(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9864],{91231:function(e,t,r){Promise.resolve().then(r.bind(r,46882))},6053:function(e,t,r){"use strict";r.d(t,{Z:function(){return E}});var o=r(2265),n=r(42744),l=r.n(n),s=r(54925),a=r(29810),c=r(18606),i=r(65823),d=r(79934),u=r(57499),g=r(58489),h=r(47861),p=r(11303),k=r(12711),f=r(78387);let m=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:o,componentCls:n,calc:l}=e,s=l(o).sub(r).equal(),a=l(t).sub(r).equal();return{[n]:Object.assign(Object.assign({},(0,p.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:s,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:"".concat((0,g.bf)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorBorder),borderRadius:e.borderRadiusSM,opacity:1,transition:"all ".concat(e.motionDurationMid),textAlign:"start",position:"relative",["&".concat(n,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},["".concat(n,"-close-icon")]:{marginInlineStart:a,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:"all ".concat(e.motionDurationMid),"&:hover":{color:e.colorTextHeading}},["&".concat(n,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(e.iconCls,"-close, ").concat(e.iconCls,"-close:hover")]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(n,"-checkable-checked):hover")]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(e.iconCls," + span, > span + ").concat(e.iconCls)]:{marginInlineStart:s}}),["".concat(n,"-borderless")]:{borderColor:"transparent",background:e.tagBorderlessBg}}},S=e=>{let{lineWidth:t,fontSizeIcon:r,calc:o}=e,n=e.fontSizeSM;return(0,k.IX)(e,{tagFontSize:n,tagLineHeight:(0,g.bf)(o(e.lineHeightSM).mul(n).equal()),tagIconSize:o(r).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},w=e=>({defaultBg:new h.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var x=(0,f.I$)("Tag",e=>m(S(e)),w),y=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let v=o.forwardRef((e,t)=>{let{prefixCls:r,style:n,className:s,checked:a,onChange:c,onClick:i}=e,d=y(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:g,tag:h}=o.useContext(u.E_),p=g("tag",r),[k,f,m]=x(p),S=l()(p,"".concat(p,"-checkable"),{["".concat(p,"-checkable-checked")]:a},null==h?void 0:h.className,s,f,m);return k(o.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},n),null==h?void 0:h.style),className:S,onClick:e=>{null==c||c(!a),null==i||i(e)}})))});var b=r(82303);let C=e=>(0,b.Z)(e,(t,r)=>{let{textColor:o,lightBorderColor:n,lightColor:l,darkColor:s}=r;return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:o,background:l,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:s,borderColor:s},["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}});var O=(0,f.bk)(["Tag","preset"],e=>C(S(e)),w);let j=(e,t,r)=>{let o="string"!=typeof r?r:r.charAt(0).toUpperCase()+r.slice(1);return{["".concat(e.componentCls).concat(e.componentCls,"-").concat(t)]:{color:e["color".concat(r)],background:e["color".concat(o,"Bg")],borderColor:e["color".concat(o,"Border")],["&".concat(e.componentCls,"-borderless")]:{borderColor:"transparent"}}}};var T=(0,f.bk)(["Tag","status"],e=>{let t=S(e);return[j(t,"success","Success"),j(t,"processing","Info"),j(t,"error","Error"),j(t,"warning","Warning")]},w),W=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let D=o.forwardRef((e,t)=>{let{prefixCls:r,className:n,rootClassName:g,style:h,children:p,icon:k,color:f,onClose:m,bordered:S=!0,visible:w}=e,y=W(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:v,direction:b,tag:C}=o.useContext(u.E_),[j,D]=o.useState(!0),E=(0,s.Z)(y,["closeIcon","closable"]);o.useEffect(()=>{void 0!==w&&D(w)},[w]);let M=(0,a.o2)(f),P=(0,a.yT)(f),I=M||P,N=Object.assign(Object.assign({backgroundColor:f&&!I?f:void 0},null==C?void 0:C.style),h),A=v("tag",r),[B,z,Z]=x(A),_=l()(A,null==C?void 0:C.className,{["".concat(A,"-").concat(f)]:I,["".concat(A,"-has-color")]:f&&!I,["".concat(A,"-hidden")]:!j,["".concat(A,"-rtl")]:"rtl"===b,["".concat(A,"-borderless")]:!S},n,g,z,Z),F=e=>{e.stopPropagation(),null==m||m(e),e.defaultPrevented||D(!1)},[,L]=(0,c.Z)((0,c.w)(e),(0,c.w)(C),{closable:!1,closeIconRender:e=>{let t=o.createElement("span",{className:"".concat(A,"-close-icon"),onClick:F},e);return(0,i.wm)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),F(t)},className:l()(null==e?void 0:e.className,"".concat(A,"-close-icon"))}))}}),R="function"==typeof y.onClick||p&&"a"===p.type,Q=k||null,H=Q?o.createElement(o.Fragment,null,Q,p&&o.createElement("span",null,p)):p,q=o.createElement("span",Object.assign({},E,{ref:t,className:_,style:N}),H,L,M&&o.createElement(O,{key:"preset",prefixCls:A}),P&&o.createElement(T,{key:"status",prefixCls:A}));return B(R?o.createElement(d.Z,{component:"Tag"},q):q)});D.CheckableTag=v;var E=D},46882:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return w}});var o=r(57437),n=r(2265),l=r(23656),s=r(6053),a=r(65270),c=r(94734),i=r(89511),d=r(50574),u=r(32779);function g(){console.warn("⚠️ [clearSchedulingData] clearAllScheduledWorkOrders 函数已废弃，建议使用dataAccessManager重新实现");let{useProductionStore:e}=r(64513),t=e.getState(),o=t.productionWorkOrders;Object.entries(o.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{})).forEach(e=>{let[t,r]=e});let n=o.filter(e=>e.workstation||e.workstationCode||e.plannedStartTime||e.plannedEndTime);if(0===n.length)return{success:!0,clearedWorkOrders:0,message:"没有已排程的工单需要清理"};let l=[];return n.forEach(e=>{t.updateProductionWorkOrder(e.id,{workstation:void 0,workstationCode:void 0,plannedStartTime:void 0,plannedEndTime:void 0,status:"pending"}),l.push(e.id)}),{success:!0,clearedWorkOrders:l.length,clearedWorkOrderIds:l,message:"成功清理 ".concat(l.length," 个工单的排程信息")}}async function h(){try{var e,t;let{dataAccessManager:o}=await Promise.resolve().then(r.bind(r,32779)),n=await o.workstations.getWorkstations();if("success"!==n.status||!(null===(e=n.data)||void 0===e?void 0:e.items))throw Error("获取工位数据失败");let l=n.data.items,s=0,a=[];for(let e of l){let o=(null===(t=e.batchNumberQueue)||void 0===t?void 0:t.length)||0;if(o>0)try{let{dataAccessManager:t}=await Promise.resolve().then(r.bind(r,32779)),n=await t.workstations.update(e.id,{batchNumberQueue:[],currentMoldNumber:null,currentBatchNumber:null,lastEndTime:null});"success"===n.status?(s+=o,a.push(e.code)):console.error("❌ 清理工位 ".concat(e.code," 失败:"),n.message)}catch(t){console.error("❌ 清理工位 ".concat(e.code," 异常:"),t)}}return{success:!0,totalClearedItems:s,clearedWorkstations:a,message:"成功清理 ".concat(s," 个队列任务，涉及 ").concat(a.length," 个工位")}}catch(e){return console.error("❌ [clearSchedulingData] 清理工位队列异常:",e),{success:!1,totalClearedItems:0,clearedWorkstations:[],message:"清理工位队列时发生异常"}}}async function p(){try{let{dataAccessManager:e}=await Promise.resolve().then(r.bind(r,32779)),t=await e.workstations.resetAllWorkstationsToIdle();if("success"===t.status&&t.data)return console.log("✅ [clearSchedulingData] 已重置 ".concat(t.data.resetCount," 个工位状态")),{success:!0,resetWorkstations:t.data.resetCount,message:"成功重置 ".concat(t.data.resetCount," 个工位状态")};return console.error("❌ [clearSchedulingData] 重置工位状态失败:",t.message),{success:!1,resetWorkstations:0,message:t.message||"重置工位状态失败"}}catch(e){return console.error("❌ [clearSchedulingData] 重置工位状态异常:",e),{success:!1,resetWorkstations:0,message:"重置工位状态时发生异常"}}}async function k(){try{let e=await g(),t=await h(),r=await p();return{success:!0,workOrderResult:e,queueResult:t,statusResult:r,summary:{clearedWorkOrders:e.clearedWorkOrders,clearedQueueItems:t.totalClearedItems,resetWorkstations:r.resetWorkstations}}}catch(e){return console.error("❌ [clearSchedulingData] 完整清理异常:",e),{success:!1,error:e instanceof Error?e.message:"未知错误"}}}window.clearAllScheduledWorkOrders=g,window.clearAllWorkstationQueues=h,window.resetAllWorkstationStatus=p,window.performCompleteCleanup=k;var f=r(29255);let{Title:m,Text:S}=l.default;function w(){let[e,t]=(0,n.useState)([]),[r,l]=(0,n.useState)([]),[g,h]=(0,n.useState)(0),p=async()=>{try{let e=await u.dataAccessManager.workstations.getAll();if("success"===e.status&&e.data){let r=Array.isArray(e.data)?e.data:e.data.items;t(r)}let r=await u.dataAccessManager.productionWorkOrders.getAll();if("success"===r.status&&r.data){let e=Array.isArray(r.data)?r.data:r.data.items;l(e)}}catch(e){console.error("加载数据失败:",e)}};(0,n.useEffect)(()=>{p()},[g]);let w=()=>{h(e=>e+1)},x=[{title:"工位编码",dataIndex:"code",key:"code",width:100},{title:"工位名称",dataIndex:"name",key:"name",width:200},{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>(0,o.jsx)(s.Z,{color:"active"===e?"green":"default",children:"active"===e?"启用":"停用"})},{title:"当前模具",dataIndex:"currentMoldNumber",key:"currentMoldNumber",width:120,render:e=>e||(0,o.jsx)(S,{type:"secondary",children:"无"})},{title:"当前批次",dataIndex:"currentBatchNumber",key:"currentBatchNumber",width:150,render:e=>e||(0,o.jsx)(S,{type:"secondary",children:"无"})},{title:"队列任务数",dataIndex:"batchNumberQueue",key:"queueCount",width:100,render:e=>(0,o.jsx)(s.Z,{color:(null==e?void 0:e.length)>0?"orange":"default",children:(null==e?void 0:e.length)||0})},{title:"最后结束时间",dataIndex:"lastEndTime",key:"lastEndTime",width:180,render:e=>e?new Date(e).toLocaleString():(0,o.jsx)(S,{type:"secondary",children:"无"})}],y=[{title:"批次号",dataIndex:"batchNumber",key:"batchNumber",width:150},{title:"产品名称",dataIndex:"productName",key:"productName",width:200},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>{let t=f.ux[e]||{antdColor:"default",text:e};return(0,o.jsx)(s.Z,{color:t.antdColor,children:t.text})}},{title:"分配工位",dataIndex:"workstationCode",key:"workstationCode",width:100,render:e=>e||(0,o.jsx)(S,{type:"secondary",children:"未分配"})},{title:"预计开始时间",dataIndex:"plannedStartTime",key:"plannedStartTime",width:180,render:e=>e?new Date(e).toLocaleString():(0,o.jsx)(S,{type:"secondary",children:"未排程"})},{title:"预计结束时间",dataIndex:"plannedEndTime",key:"plannedEndTime",width:180,render:e=>e?new Date(e).toLocaleString():(0,o.jsx)(S,{type:"secondary",children:"未排程"})}],v={total:e.length,active:e.filter(e=>"active"===e.status).length,withQueue:e.filter(e=>{var t;return(null===(t=e.batchNumberQueue)||void 0===t?void 0:t.length)>0}).length,withMold:e.filter(e=>e.currentMoldNumber).length},b={total:r.length,pending:r.filter(e=>"pending"===e.status).length,scheduled:r.filter(e=>e.workstationCode||e.plannedStartTime).length,inProgress:r.filter(e=>"in_progress"===e.status).length};return(0,o.jsxs)("div",{style:{padding:"24px",maxWidth:"1400px",margin:"0 auto"},children:[(0,o.jsx)(m,{level:2,children:"\uD83D\uDCCA 系统数据状态查看"}),(0,o.jsxs)(a.Z,{style:{marginBottom:"24px"},children:[(0,o.jsx)(c.ZP,{type:"primary",onClick:w,children:"\uD83D\uDD04 刷新数据"}),(0,o.jsx)(c.ZP,{danger:!0,onClick:()=>{k(),w()},children:"\uD83E\uDDF9 执行完整清理"})]}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"24px"},children:[(0,o.jsx)(i.Z,{size:"small",children:(0,o.jsxs)("div",{style:{textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#1890ff"},children:v.active}),(0,o.jsxs)("div",{children:["启用工位 / ",v.total]})]})}),(0,o.jsx)(i.Z,{size:"small",children:(0,o.jsxs)("div",{style:{textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#fa8c16"},children:v.withQueue}),(0,o.jsx)("div",{children:"有队列任务的工位"})]})}),(0,o.jsx)(i.Z,{size:"small",children:(0,o.jsxs)("div",{style:{textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#52c41a"},children:b.pending}),(0,o.jsx)("div",{children:"待开始工单"})]})}),(0,o.jsx)(i.Z,{size:"small",children:(0,o.jsxs)("div",{style:{textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#722ed1"},children:b.scheduled}),(0,o.jsx)("div",{children:"已排程工单"})]})})]}),(0,o.jsxs)(i.Z,{style:{marginBottom:"24px"},children:[(0,o.jsx)(m,{level:4,children:"\uD83C\uDFED 工位状态"}),(0,o.jsx)(d.Z,{columns:x,dataSource:e,rowKey:"id",size:"small",scroll:{x:1e3},pagination:!1})]}),(0,o.jsxs)(i.Z,{children:[(0,o.jsx)(m,{level:4,children:"\uD83D\uDCCB 生产工单状态"}),(0,o.jsx)(d.Z,{columns:y,dataSource:r,rowKey:"id",size:"small",scroll:{x:1200},pagination:{pageSize:10}})]})]})}},29255:function(e,t,r){"use strict";r.d(t,{Fi:function(){return o},ux:function(){return n}});let o={in_plan:{color:"blue",text:"计划中"},planned:{color:"cyan",text:"已计划"},in_progress:{color:"orange",text:"生产中"},completed:{color:"green",text:"已完成"},cancelled:{color:"red",text:"已取消"}},n={pending:{color:"default",text:"待开始",antdColor:"default"},scheduled:{color:"blue",text:"已排程",antdColor:"blue"},in_progress:{color:"processing",text:"进行中",antdColor:"processing"},completed:{color:"success",text:"已完成",antdColor:"success"},paused:{color:"warning",text:"暂停",antdColor:"warning"},cancelled:{color:"error",text:"已取消",antdColor:"error"},exception:{color:"error",text:"异常",antdColor:"error"}}},64513:function(e,t,r){"use strict";r.r(t),r.d(t,{useProductionStore:function(){return s}});var o=r(94660),n=r(74810);let l={loading:{orders:!1,workOrders:!1,workstations:!1,reports:!1,calculations:!1,scheduling:!1,general:!1},schedulingState:{isPreviewMode:!1,workstationSnapshot:null,lastTransaction:null,selectedDate:null,viewMode:"day",selectedWorkstation:null,draggedTask:null},errors:{orders:null,workOrders:null,workstations:null,reports:null,calculations:null,scheduling:null,general:null},filters:{orderStatus:[],workstationTypes:[],dateRange:null,searchText:""},selections:{selectedOrders:[],selectedWorkOrders:[],selectedWorkstations:[]}},s=(0,o.Ue)()((0,n.tJ)((e,t)=>({...l,setLoading:(t,r)=>e(e=>({loading:{...e.loading,[t]:r}})),setLoadingMultiple:t=>e(e=>({loading:{...e.loading,...t}})),clearAllLoading:()=>e(e=>({loading:Object.keys(e.loading).reduce((e,t)=>({...e,[t]:!1}),{})})),setError:(t,r)=>e(e=>({errors:{...e.errors,[t]:r}})),clearError:t=>e(e=>({errors:{...e.errors,[t]:null}})),clearAllErrors:()=>e(e=>({errors:Object.keys(e.errors).reduce((e,t)=>({...e,[t]:null}),{})})),isInPreviewMode:()=>t().schedulingState.isPreviewMode,enablePreviewMode:()=>e(e=>({schedulingState:{...e.schedulingState,isPreviewMode:!0}})),disablePreviewMode:()=>e(e=>({schedulingState:{...e.schedulingState,isPreviewMode:!1}})),setSchedulingDate:t=>e(e=>({schedulingState:{...e.schedulingState,selectedDate:t}})),setSchedulingViewMode:t=>e(e=>({schedulingState:{...e.schedulingState,viewMode:t}})),setSelectedWorkstation:t=>e(e=>({schedulingState:{...e.schedulingState,selectedWorkstation:t}})),setDraggedTask:t=>e(e=>({schedulingState:{...e.schedulingState,draggedTask:t}})),applySchedulingTransaction:t=>e(e=>({schedulingState:{...e.schedulingState,lastTransaction:t,isPreviewMode:!1}})),rollbackSchedulingTransaction:()=>e(e=>({schedulingState:{...e.schedulingState,isPreviewMode:!1,workstationSnapshot:null,lastTransaction:null}})),createWorkstationSnapshot:()=>{let t={id:"snapshot-"+Date.now(),timestamp:new Date().toISOString(),workstations:[],metadata:{reason:"scheduling_preview",createdBy:"system"}};return e(e=>({schedulingState:{...e.schedulingState,workstationSnapshot:t}})),t},restoreWorkstationSnapshot:t=>e(e=>({schedulingState:{...e.schedulingState,workstationSnapshot:t}})),setOrderStatusFilter:t=>e(e=>({filters:{...e.filters,orderStatus:t}})),setWorkstationTypesFilter:t=>e(e=>({filters:{...e.filters,workstationTypes:t}})),setDateRangeFilter:t=>e(e=>({filters:{...e.filters,dateRange:t}})),setSearchText:t=>e(e=>({filters:{...e.filters,searchText:t}})),clearFilters:()=>e(e=>({filters:{orderStatus:[],workstationTypes:[],dateRange:null,searchText:""}})),setSelectedOrders:t=>e(e=>({selections:{...e.selections,selectedOrders:t}})),setSelectedWorkOrders:t=>e(e=>({selections:{...e.selections,selectedWorkOrders:t}})),setSelectedWorkstations:t=>e(e=>({selections:{...e.selections,selectedWorkstations:t}})),toggleOrderSelection:t=>e(e=>{let r=e.selections.selectedOrders,o=r.includes(t)?r.filter(e=>e!==t):[...r,t];return{selections:{...e.selections,selectedOrders:o}}}),toggleWorkOrderSelection:t=>e(e=>{let r=e.selections.selectedWorkOrders,o=r.includes(t)?r.filter(e=>e!==t):[...r,t];return{selections:{...e.selections,selectedWorkOrders:o}}}),toggleWorkstationSelection:t=>e(e=>{let r=e.selections.selectedWorkstations,o=r.includes(t)?r.filter(e=>e!==t):[...r,t];return{selections:{...e.selections,selectedWorkstations:o}}}),clearAllSelections:()=>e(e=>({selections:{selectedOrders:[],selectedWorkOrders:[],selectedWorkstations:[]}})),resetSchedulingState:()=>e(e=>({schedulingState:{isPreviewMode:!1,workstationSnapshot:null,lastTransaction:null,selectedDate:null,viewMode:"day",selectedWorkstation:null,draggedTask:null}})),resetFilters:()=>e(e=>({filters:{orderStatus:[],workstationTypes:[],dateRange:null,searchText:""}})),resetSelections:()=>e(e=>({selections:{selectedOrders:[],selectedWorkOrders:[],selectedWorkstations:[]}})),resetAllUIState:()=>e(()=>({...l}))}),{name:"production-ui-store",partialize:e=>({schedulingState:{viewMode:e.schedulingState.viewMode,isPreviewMode:e.schedulingState.isPreviewMode},filters:{orderStatus:e.filters.orderStatus,workstationTypes:e.filters.workstationTypes,searchText:e.filters.searchText}}),version:1,migrate:(e,t)=>{if(0===t){var r,o,n,l;return{schedulingState:{viewMode:(null===(r=e.schedulingState)||void 0===r?void 0:r.viewMode)||"day",isPreviewMode:!1},filters:{orderStatus:(null===(o=e.filters)||void 0===o?void 0:o.orderStatus)||[],workstationTypes:(null===(n=e.filters)||void 0===n?void 0:n.workstationTypes)||[],searchText:(null===(l=e.filters)||void 0===l?void 0:l.searchText)||""}}}return e}}))}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,574,128,5166,2897,3656,2779,2971,4938,1744],function(){return e(e.s=91231)}),_N_E=e.O()}]);