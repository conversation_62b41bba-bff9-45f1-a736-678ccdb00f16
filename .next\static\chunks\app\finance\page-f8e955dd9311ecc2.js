(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9536],{76486:function(e,s,l){Promise.resolve().then(l.bind(l,45693))},45693:function(e,s,l){"use strict";l.r(s);var t=l(57437),a=l(2265),r=l(50030),i=l(27296),c=l(39992),n=l(89198),d=l(6053),x=l(65270),o=l(94734),h=l(38302),p=l(28683),j=l(89511),u=l(86155),m=l(2012),f=l(50574),y=l(47628),Z=l(75123),g=l(96619),v=l(58992),w=l(75216),N=l(65362),k=l(34021),b=l(40856),I=l(75393),S=l(51769),O=l(74898),P=l(96);let{RangePicker:z}=r.default,{Option:C}=i.default,{TextArea:_}=c.default;s.default=()=>{let[e,s]=(0,a.useState)(!1),[l,q]=(0,a.useState)("records"),[F]=n.Z.useForm(),T=[{id:"1",date:"2024-01-15",type:"income",category:"销售收入",amount:125600,description:"销售订单SO-2024-001收款",reference:"SO-2024-001",status:"approved"},{id:"2",date:"2024-01-14",type:"expense",category:"采购支出",amount:89500,description:"采购原材料付款",reference:"PO-2024-002",status:"approved"},{id:"3",date:"2024-01-13",type:"expense",category:"运营费用",amount:15600,description:"办公用品采购",status:"pending"},{id:"4",date:"2024-01-12",type:"income",category:"其他收入",amount:8900,description:"设备租赁收入",status:"approved"}],E={income:{color:"green",text:"收入",icon:(0,t.jsx)(g.Z,{})},expense:{color:"red",text:"支出",icon:(0,t.jsx)(v.Z,{})}},K={pending:{color:"orange",text:"待审核"},approved:{color:"green",text:"已审核"},rejected:{color:"red",text:"已拒绝"}},D=T.filter(e=>"income"===e.type&&"approved"===e.status).reduce((e,s)=>e+s.amount,0),J=T.filter(e=>"expense"===e.type&&"approved"===e.status).reduce((e,s)=>e+s.amount,0),L=D-J;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"page-header",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(b.Z,{className:"text-2xl text-yellow-600 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"page-title",children:"财务管理"}),(0,t.jsx)("p",{className:"page-description",children:"管理财务收支、成本核算和财务报表"})]})]})}),(0,t.jsxs)(h.Z,{gutter:[16,16],children:[(0,t.jsx)(p.Z,{xs:24,sm:6,children:(0,t.jsx)(j.Z,{children:(0,t.jsx)(u.Z,{title:"本月收入",value:D,precision:2,prefix:"\xa5",valueStyle:{color:"#52c41a"}})})}),(0,t.jsx)(p.Z,{xs:24,sm:6,children:(0,t.jsx)(j.Z,{children:(0,t.jsx)(u.Z,{title:"本月支出",value:J,precision:2,prefix:"\xa5",valueStyle:{color:"#ff4d4f"}})})}),(0,t.jsx)(p.Z,{xs:24,sm:6,children:(0,t.jsx)(j.Z,{children:(0,t.jsx)(u.Z,{title:"净利润",value:L,precision:2,prefix:"\xa5",valueStyle:{color:L>=0?"#52c41a":"#ff4d4f"}})})}),(0,t.jsx)(p.Z,{xs:24,sm:6,children:(0,t.jsx)(j.Z,{children:(0,t.jsx)(u.Z,{title:"待审核记录",value:T.filter(e=>"pending"===e.status).length,suffix:"条",valueStyle:{color:"#fa8c16"}})})})]}),(0,t.jsx)(j.Z,{children:(0,t.jsx)(m.default,{activeKey:l,onChange:q,items:[{key:"records",label:"财务记录",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,t.jsx)(c.default,{placeholder:"搜索描述或关联单据",prefix:(0,t.jsx)(I.Z,{}),className:"w-full sm:w-64"}),(0,t.jsxs)(i.default,{placeholder:"记录类型",className:"w-full sm:w-32",children:[(0,t.jsx)(C,{value:"",children:"全部"}),(0,t.jsx)(C,{value:"income",children:"收入"}),(0,t.jsx)(C,{value:"expense",children:"支出"})]}),(0,t.jsxs)(i.default,{placeholder:"审核状态",className:"w-full sm:w-32",children:[(0,t.jsx)(C,{value:"",children:"全部"}),(0,t.jsx)(C,{value:"pending",children:"待审核"}),(0,t.jsx)(C,{value:"approved",children:"已审核"}),(0,t.jsx)(C,{value:"rejected",children:"已拒绝"})]}),(0,t.jsx)(z,{className:"w-full sm:w-auto"})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(o.ZP,{icon:(0,t.jsx)(S.Z,{}),children:"导出"}),(0,t.jsx)(o.ZP,{type:"primary",icon:(0,t.jsx)(O.Z,{}),onClick:()=>{s(!0)},children:"新增记录"})]})]}),(0,t.jsx)(f.Z,{columns:[{title:"日期",dataIndex:"date",key:"date",width:100},{title:"类型",dataIndex:"type",key:"type",width:80,render:e=>{let s=E[e];return(0,t.jsx)(d.Z,{color:s.color,icon:s.icon,children:s.text})}},{title:"分类",dataIndex:"category",key:"category",width:120},{title:"金额",dataIndex:"amount",key:"amount",width:120,render:(e,s)=>{let l="income"===s.type?"#52c41a":"#ff4d4f",a="income"===s.type?"+":"-";return(0,t.jsxs)("span",{style:{color:l},children:[a,"\xa5",e.toLocaleString()]})}},{title:"描述",dataIndex:"description",key:"description",width:200,ellipsis:!0},{title:"关联单据",dataIndex:"reference",key:"reference",width:120,render:e=>e||"-"},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>{let s=K[e];return(0,t.jsx)(d.Z,{color:s.color,children:s.text})}},{title:"操作",key:"action",width:150,render:(e,s)=>(0,t.jsxs)(x.Z,{size:"small",children:[(0,t.jsx)(o.ZP,{type:"text",icon:(0,t.jsx)(w.Z,{}),size:"small",children:"查看"}),(0,t.jsx)(o.ZP,{type:"text",icon:(0,t.jsx)(N.Z,{}),size:"small",children:"编辑"}),(0,t.jsx)(o.ZP,{type:"text",danger:!0,icon:(0,t.jsx)(k.Z,{}),size:"small",children:"删除"})]})}],dataSource:T,rowKey:"id",pagination:{total:T.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>"第 ".concat(s[0],"-").concat(s[1]," 条/共 ").concat(e," 条")},scroll:{x:1e3}})]})},{key:"reports",label:"财务报表",children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)(h.Z,{gutter:[16,16],children:[(0,t.jsx)(p.Z,{xs:24,lg:12,children:(0,t.jsx)(j.Z,{title:"收支趋势",className:"h-96",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-400",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(P.Z,{className:"text-4xl mb-4"}),(0,t.jsx)("p",{children:"收支趋势图表"}),(0,t.jsx)("p",{className:"text-sm",children:"（图表组件将在后续集成）"})]})})})}),(0,t.jsx)(p.Z,{xs:24,lg:12,children:(0,t.jsx)(j.Z,{title:"分类统计",className:"h-96",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64 text-gray-400",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(P.Z,{className:"text-4xl mb-4"}),(0,t.jsx)("p",{children:"分类统计图表"}),(0,t.jsx)("p",{className:"text-sm",children:"（图表组件将在后续集成）"})]})})})})]})})}]})}),(0,t.jsx)(y.Z,{title:"新增财务记录",open:e,onOk:()=>{F.validateFields().then(e=>{s(!1),F.resetFields()})},onCancel:()=>{s(!1),F.resetFields()},width:800,okText:"确认",cancelText:"取消",children:(0,t.jsxs)(n.Z,{form:F,layout:"vertical",initialValues:{date:new Date().toISOString().split("T")[0],type:"expense"},children:[(0,t.jsxs)(h.Z,{gutter:16,children:[(0,t.jsx)(p.Z,{span:12,children:(0,t.jsx)(n.Z.Item,{label:"记录类型",name:"type",rules:[{required:!0,message:"请选择记录类型"}],children:(0,t.jsxs)(i.default,{placeholder:"请选择记录类型",children:[(0,t.jsx)(C,{value:"income",children:"收入"}),(0,t.jsx)(C,{value:"expense",children:"支出"})]})})}),(0,t.jsx)(p.Z,{span:12,children:(0,t.jsx)(n.Z.Item,{label:"日期",name:"date",rules:[{required:!0,message:"请选择日期"}],children:(0,t.jsx)(r.default,{className:"w-full"})})})]}),(0,t.jsxs)(h.Z,{gutter:16,children:[(0,t.jsx)(p.Z,{span:12,children:(0,t.jsx)(n.Z.Item,{label:"分类",name:"category",rules:[{required:!0,message:"请输入分类"}],children:(0,t.jsx)(c.default,{placeholder:"请输入分类"})})}),(0,t.jsx)(p.Z,{span:12,children:(0,t.jsx)(n.Z.Item,{label:"金额",name:"amount",rules:[{required:!0,message:"请输入金额"}],children:(0,t.jsx)(Z.Z,{className:"w-full",placeholder:"请输入金额",prefix:"\xa5",min:0,precision:2})})})]}),(0,t.jsx)(n.Z.Item,{label:"关联单据",name:"reference",children:(0,t.jsx)(c.default,{placeholder:"请输入关联单据号（可选）"})}),(0,t.jsx)(n.Z.Item,{label:"描述",name:"description",rules:[{required:!0,message:"请输入描述"}],children:(0,t.jsx)(_,{placeholder:"请输入详细描述",rows:3})})]})})]})}}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,9198,9992,1157,30,1510,2971,4938,1744],function(){return e(e.s=76486)}),_N_E=e.O()}]);