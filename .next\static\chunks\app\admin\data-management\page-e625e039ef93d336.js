(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1763],{16370:function(e,s,t){Promise.resolve().then(t.bind(t,26278))},26278:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return B}});var r=t(57437),l=t(2265),a=t(57416),o=t(89511),i=t(38302),n=t(28683),d=t(86155),c=t(65270),g=t(94734),x=t(28116),p=t(40796),u=t(59189),m=t(99617),h=t(43043),f=t(63420),j=t(64370),y=t(88123),b=t(34021),Z=t(63260),S=t(57613),v=t(93011);function M(){let{message:e,modal:s}=a.Z.useApp(),[t,M]=(0,l.useState)(null),[B,w]=(0,l.useState)(!1),{materials:C,productModels:R,clearAll:k}=(0,S.rm)(),I=()=>{M(S.Tx.getStorageInfo())};return(0,l.useEffect)(()=>{I()},[C,R]),(0,r.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:v.ZB.spacing.lg},children:[(0,r.jsx)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{style:{fontSize:"24px",fontWeight:"bold",color:v.ZB.colors.gray[900],margin:0},children:"数据管理"}),(0,r.jsx)("p",{style:{color:v.ZB.colors.gray[600],marginTop:v.ZB.spacing.xs,margin:0},children:"管理ERP系统的本地数据存储"})]})}),(0,r.jsx)(o.Z,{title:(0,r.jsxs)("span",{children:[(0,r.jsx)(f.Z,{style:{marginRight:8}}),"存储统计"]}),children:(0,r.jsxs)(i.Z,{gutter:16,children:[(0,r.jsx)(n.Z,{span:6,children:(0,r.jsx)(d.Z,{title:"物料数量",value:(null==t?void 0:t.materialsCount)||0,suffix:"个",valueStyle:{color:"#1890ff"}})}),(0,r.jsx)(n.Z,{span:6,children:(0,r.jsx)(d.Z,{title:"产品数量",value:(null==t?void 0:t.productModelsCount)||0,suffix:"个",valueStyle:{color:"#52c41a"}})}),(0,r.jsx)(n.Z,{span:6,children:(0,r.jsx)(d.Z,{title:"存储大小",value:((null==t?void 0:t.storageSize)||0)/1024,precision:2,suffix:"KB",valueStyle:{color:"#722ed1"}})}),(0,r.jsx)(n.Z,{span:6,children:(0,r.jsx)(d.Z,{title:"数据状态",value:(null==t?void 0:t.materialsCount)>0||(null==t?void 0:t.productModelsCount)>0?"有数据":"无数据",valueStyle:{color:(null==t?void 0:t.materialsCount)>0||(null==t?void 0:t.productModelsCount)>0?"#52c41a":"#ff4d4f"}})})]})}),(0,r.jsx)(o.Z,{title:"数据操作",children:(0,r.jsxs)(c.Z,{direction:"vertical",size:"large",style:{width:"100%"},children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{style:{fontSize:"18px",fontWeight:500,marginBottom:v.ZB.spacing.xs,margin:0},children:"导出数据"}),(0,r.jsx)("p",{style:{color:v.ZB.colors.gray[600],marginBottom:v.ZB.spacing.sm,margin:0},children:"将当前的物料和产品数据导出为JSON文件，可用于备份或迁移。"}),(0,r.jsx)(g.ZP,{type:"primary",icon:(0,r.jsx)(j.Z,{}),onClick:()=>{try{S.Tx.exportData(),e.success("数据导出成功")}catch(s){e.error("数据导出失败")}},disabled:!(null==t?void 0:t.materialsCount)&&!(null==t?void 0:t.productModelsCount),children:"导出数据"})]}),(0,r.jsx)(x.Z,{}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{style:{fontSize:"18px",fontWeight:500,marginBottom:v.ZB.spacing.xs,margin:0},children:"导入数据"}),(0,r.jsx)("p",{style:{color:v.ZB.colors.gray[600],marginBottom:v.ZB.spacing.sm,margin:0},children:"从JSON文件导入物料和产品数据。导入的数据将与现有数据合并。"}),(0,r.jsx)(p.Z,{accept:".json",beforeUpload:s=>(w(!0),S.Tx.importData(s).then(()=>{e.success("数据导入成功"),I()}).catch(s=>{e.error("数据导入失败: ".concat(s.message))}).finally(()=>{w(!1)}),!1),showUploadList:!1,children:(0,r.jsx)(g.ZP,{icon:(0,r.jsx)(y.Z,{}),loading:B,children:"选择文件导入"})})]}),(0,r.jsx)(x.Z,{}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{style:{fontSize:"18px",fontWeight:500,marginBottom:v.ZB.spacing.xs,margin:0},children:"清除数据"}),(0,r.jsx)("p",{style:{color:v.ZB.colors.gray[600],marginBottom:v.ZB.spacing.sm,margin:0},children:"清除所有本地存储的数据。此操作不可恢复，请谨慎使用。"}),(0,r.jsx)(g.ZP,{danger:!0,icon:(0,r.jsx)(b.Z,{}),onClick:()=>{s.confirm({title:"确认清除数据",icon:(0,r.jsx)(h.Z,{}),content:"此操作将清除所有本地存储的主数据，包括物料和产品信息。此操作不可恢复，确定要继续吗？",okText:"确认清除",okType:"danger",cancelText:"取消",onOk(){try{S.Tx.clearLocalStorage(),k(),e.success("数据清除成功"),I()}catch(s){e.error("数据清除失败")}}})},disabled:!(null==t?void 0:t.materialsCount)&&!(null==t?void 0:t.productModelsCount),children:"清除所有数据"})]})]})}),(0,r.jsxs)(o.Z,{title:(0,r.jsxs)("span",{children:[(0,r.jsx)(Z.Z,{style:{marginRight:8}}),"技术说明"]}),children:[(0,r.jsx)(u.Z,{message:"数据持久化机制",description:(0,r.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:v.ZB.spacing.xs},children:[(0,r.jsxs)("p",{style:{margin:0},children:["• ",(0,r.jsx)("strong",{children:"存储方式"}),": 使用浏览器的localStorage进行本地存储"]}),(0,r.jsxs)("p",{style:{margin:0},children:["• ",(0,r.jsx)("strong",{children:"数据范围"}),": 包括物料主数据和产品型号库数据"]}),(0,r.jsxs)("p",{style:{margin:0},children:["• ",(0,r.jsx)("strong",{children:"持久化"}),": 页面刷新后数据不会丢失"]}),(0,r.jsxs)("p",{style:{margin:0},children:["• ",(0,r.jsx)("strong",{children:"容量限制"}),": localStorage通常支持5-10MB的数据存储"]}),(0,r.jsxs)("p",{style:{margin:0},children:["• ",(0,r.jsx)("strong",{children:"数据安全"}),": 数据仅存储在本地浏览器中，不会上传到服务器"]})]}),type:"info",showIcon:!0}),(0,r.jsx)(x.Z,{}),(0,r.jsxs)(m.Z,{title:"存储详情",bordered:!0,size:"small",children:[(0,r.jsx)(m.Z.Item,{label:"存储键名",children:"master-data-storage"}),(0,r.jsx)(m.Z.Item,{label:"存储位置",children:"浏览器localStorage"}),(0,r.jsx)(m.Z.Item,{label:"数据格式",children:"JSON"}),(0,r.jsx)(m.Z.Item,{label:"版本控制",children:"支持"}),(0,r.jsx)(m.Z.Item,{label:"自动备份",children:"否"}),(0,r.jsx)(m.Z.Item,{label:"跨设备同步",children:"否"})]})]}),(0,r.jsx)(o.Z,{title:"使用建议",children:(0,r.jsx)(u.Z,{message:"数据管理最佳实践",description:(0,r.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:v.ZB.spacing.xs},children:[(0,r.jsxs)("p",{style:{margin:0},children:["• ",(0,r.jsx)("strong",{children:"定期备份"}),": 建议定期导出数据进行备份"]}),(0,r.jsxs)("p",{style:{margin:0},children:["• ",(0,r.jsx)("strong",{children:"版本管理"}),": 重要变更前先导出当前数据"]}),(0,r.jsxs)("p",{style:{margin:0},children:["• ",(0,r.jsx)("strong",{children:"数据验证"}),": 导入数据前请确认文件格式正确"]}),(0,r.jsxs)("p",{style:{margin:0},children:["• ",(0,r.jsx)("strong",{children:"清理策略"}),": 定期清理无用数据以节省存储空间"]}),(0,r.jsxs)("p",{style:{margin:0},children:["• ",(0,r.jsx)("strong",{children:"迁移准备"}),": 如需更换设备，请先导出数据"]})]}),type:"warning",showIcon:!0})})]})}function B(){return(0,r.jsx)(a.Z,{children:(0,r.jsx)(M,{})})}},57613:function(e,s,t){"use strict";t.d(s,{Tx:function(){return o},rm:function(){return a}});var r=t(94660),l=t(74810);let a=(0,r.Ue)()((0,l.mW)((0,l.tJ)(e=>({materials:[],selectedMaterial:null,materialsLoading:!1,productModels:[],selectedProductModel:null,productModelsLoading:!1,setMaterials:s=>e({materials:s}),setSelectedMaterial:s=>e({selectedMaterial:s}),setMaterialsLoading:s=>e({materialsLoading:s}),setProductModels:s=>{e({productModels:s})},setSelectedProductModel:s=>e({selectedProductModel:s}),setProductModelsLoading:s=>e({productModelsLoading:s}),clearAll:()=>e({materials:[],selectedMaterial:null,materialsLoading:!1,productModels:[],selectedProductModel:null,productModelsLoading:!1}),initializeFromStorage:()=>{}}),{name:"master-data-store",partialize:e=>({materials:e.materials,productModels:e.productModels}),version:1,migrate:(e,s)=>0===s?{materials:e.materials||[],productModels:e.productModels||[]}:e}),{name:"master-data-store-devtools"})),o={clearLocalStorage:()=>{localStorage.removeItem("master-data-storage")},exportData:()=>{let e=a.getState(),s={materials:e.materials,productModels:e.productModels,exportTime:new Date().toISOString(),version:"1.0"},t=new Blob([JSON.stringify(s,null,2)],{type:"application/json"}),r=URL.createObjectURL(t),l=document.createElement("a");l.href=r,l.download="master-data-".concat(new Date().toISOString().split("T")[0],".json"),document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(r)},importData:e=>new Promise((s,t)=>{let r=new FileReader;r.onload=e=>{try{var r;let t=JSON.parse(null===(r=e.target)||void 0===r?void 0:r.result),l=a.getState();t.materials&&Array.isArray(t.materials)&&l.setMaterials(t.materials),t.productModels&&Array.isArray(t.productModels)&&l.setProductModels(t.productModels),s()}catch(e){t(e)}},r.onerror=()=>t(Error("文件读取失败")),r.readAsText(e)}),getStorageInfo:()=>{var e;let s=a.getState(),t=localStorage.getItem("master-data-storage");return{materialsCount:s.materials.length,productModelsCount:s.productModels.length,storageSize:t?new Blob([t]).size:0,lastUpdated:t?null===(e=JSON.parse(t).state)||void 0===e?void 0:e.updatedAt:null}}}},93011:function(e,s,t){"use strict";t.d(s,{ZB:function(){return r}});let r={spacing:{xs:4,sm:8,md:16,lg:24,xl:32,xxl:48},shadows:{sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1)"},borderRadius:{sm:4,md:8,lg:12,xl:16},colors:{primary:{50:"#f0f9ff",100:"#e0f2fe",500:"#0ea5e9",600:"#0284c7",700:"#0369a1"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827"},success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"},createSpacing:e=>({padding:r.spacing[e]}),createMargin:e=>({margin:r.spacing[e]}),createShadow:e=>({boxShadow:r.shadows[e]}),createBorderRadius:e=>({borderRadius:r.borderRadius[e]}),cardStyle:{borderRadius:12,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",background:"#ffffff",border:"1px solid #f0f0f0"},buttonStyle:{borderRadius:8,transition:"all 0.2s ease-in-out"},layoutStyle:{minHeight:"100vh",background:"#f9fafb"},sidebarStyle:{background:"#ffffff",boxShadow:"2px 0 8px 0 rgba(29, 35, 41, 0.05)",borderRight:"1px solid #f0f0f0"},headerStyle:{background:"#ffffff",borderBottom:"1px solid #f0f0f0",boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.05)"},breakpoints:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px",xxl:"1536px"},transitions:{fast:"all 0.15s ease-in-out",normal:"all 0.2s ease-in-out",slow:"all 0.3s ease-in-out"}};r.spacing.xs,r.spacing.sm,r.spacing.md,r.spacing.lg,r.spacing.xl,r.spacing.xs,r.spacing.sm,r.spacing.md,r.spacing.lg,r.colors.gray[50],r.colors.gray[100],r.colors.gray[200],r.colors.gray[200],r.shadows.sm,r.shadows.md,r.shadows.lg,r.borderRadius.sm,r.borderRadius.md,r.borderRadius.lg,r.transitions.normal}},function(e){e.O(0,[9444,8454,6254,7661,9511,364,6245,2217,4863,7416,5166,9617,7660,2971,4938,1744],function(){return e(e.s=16370)}),_N_E=e.O()}]);