(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3656],{65362:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var l=n(13428),o=n(2265),r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"},a=n(46614),i=o.forwardRef(function(e,t){return o.createElement(a.Z,(0,l.Z)({},e,{ref:t,icon:r}))})},2390:function(e,t,n){"use strict";var l=n(25809),o={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,r,a,i,c,s,u,d,p=!1;t||(t={}),a=t.debug||!1;try{if(c=l(),s=document.createRange(),u=document.getSelection(),(d=document.createElement("span")).textContent=e,d.ariaHidden="true",d.style.all="unset",d.style.position="fixed",d.style.top=0,d.style.clip="rect(0, 0, 0, 0)",d.style.whiteSpace="pre",d.style.webkitUserSelect="text",d.style.MozUserSelect="text",d.style.msUserSelect="text",d.style.userSelect="text",d.addEventListener("copy",function(n){if(n.stopPropagation(),t.format){if(n.preventDefault(),void 0===n.clipboardData){a&&console.warn("unable to use e.clipboardData"),a&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var l=o[t.format]||o.default;window.clipboardData.setData(l,e)}else n.clipboardData.clearData(),n.clipboardData.setData(t.format,e)}t.onCopy&&(n.preventDefault(),t.onCopy(n.clipboardData))}),document.body.appendChild(d),s.selectNodeContents(d),u.addRange(s),!document.execCommand("copy"))throw Error("copy command was unsuccessful");p=!0}catch(l){a&&console.error("unable to copy using execCommand: ",l),a&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),p=!0}catch(l){a&&console.error("unable to copy using clipboardData: ",l),a&&console.error("falling back to prompt"),n="message"in t?t.message:"Copy to clipboard: #{key}, Enter",r=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",i=n.replace(/#{\s*key\s*}/g,r),window.prompt(i,e)}}finally{u&&("function"==typeof u.removeRange?u.removeRange(s):u.removeAllRanges()),d&&document.body.removeChild(d),c()}return p}},23656:function(e,t,n){"use strict";n.d(t,{default:function(){return ex}});var l=n(2265),o=n(16141),r=n(65362),a=n(42744),i=n.n(a),c=n(11288),s=n(79173),u=n(19836),d=n(73310),p=n(54925),f=n(17146),m=n(82998),g=n(57499),b=n(70595),y=n(78634),v=n(13428),h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"},x=n(46614),O=l.forwardRef(function(e,t){return l.createElement(x.Z,(0,v.Z)({},e,{ref:t,icon:h}))}),E=n(89017),w=n(65823),S=n(30128),j=n(11303),C=n(78387),k=n(62363),R=n(58489);let T=(e,t,n,l)=>{let{titleMarginBottom:o,fontWeightStrong:r}=l;return{marginBottom:o,color:n,fontWeight:r,fontSize:e,lineHeight:t}},Z=e=>{let t={};return[1,2,3,4,5].forEach(n=>{t["\n      h".concat(n,"&,\n      div&-h").concat(n,",\n      div&-h").concat(n," > textarea,\n      h").concat(n,"\n    ")]=T(e["fontSizeHeading".concat(n)],e["lineHeightHeading".concat(n)],e.colorTextHeading,e)}),t},I=e=>{let{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,j.Nd)(e)),{userSelect:"text",["&[disabled], &".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},D=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:k.EV[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),H=e=>{let{componentCls:t,paddingSM:n}=e;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(n).mul(-1).equal(),marginBottom:"calc(1em - ".concat((0,R.bf)(n),")")},["".concat(t,"-edit-content-confirm")]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},M=e=>({["".concat(e.componentCls,"-copy-success")]:{"\n    &,\n    &:hover,\n    &:focus":{color:e.colorSuccess}},["".concat(e.componentCls,"-copy-icon-only")]:{marginInlineStart:0}}),z=()=>({"\n  a&-ellipsis,\n  span&-ellipsis\n  ":{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),B=e=>{let{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,["&".concat(t,"-secondary")]:{color:e.colorTextDescription},["&".concat(t,"-success")]:{color:e.colorSuccessText},["&".concat(t,"-warning")]:{color:e.colorWarningText},["&".concat(t,"-danger")]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},["&".concat(t,"-disabled")]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},"\n        div&,\n        p\n      ":{marginBottom:"1em"}},Z(e)),{["\n      & + h1".concat(t,",\n      & + h2").concat(t,",\n      & + h3").concat(t,",\n      & + h4").concat(t,",\n      & + h5").concat(t,"\n      ")]:{marginTop:n},"\n      div,\n      ul,\n      li,\n      p,\n      h1,\n      h2,\n      h3,\n      h4,\n      h5":{"\n        + h1,\n        + h2,\n        + h3,\n        + h4,\n        + h5\n        ":{marginTop:n}}}),D(e)),I(e)),{["\n        ".concat(t,"-expand,\n        ").concat(t,"-collapse,\n        ").concat(t,"-edit,\n        ").concat(t,"-copy\n      ")]:Object.assign(Object.assign({},(0,j.Nd)(e)),{marginInlineStart:e.marginXXS})}),H(e)),M(e)),z()),{"&-rtl":{direction:"rtl"}})}};var P=(0,C.I$)("Typography",e=>[B(e)],()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"})),N=e=>{let{prefixCls:t,"aria-label":n,className:o,style:r,direction:a,maxLength:c,autoSize:s=!0,value:u,onSave:d,onCancel:p,onEnd:f,component:m,enterIcon:g=l.createElement(O,null)}=e,b=l.useRef(null),y=l.useRef(!1),v=l.useRef(null),[h,x]=l.useState(u);l.useEffect(()=>{x(u)},[u]),l.useEffect(()=>{var e;if(null===(e=b.current)||void 0===e?void 0:e.resizableTextArea){let{textArea:e}=b.current.resizableTextArea;e.focus();let{length:t}=e.value;e.setSelectionRange(t,t)}},[]);let j=()=>{d(h.trim())},[C,k,R]=P(t),T=i()(t,"".concat(t,"-edit-content"),{["".concat(t,"-rtl")]:"rtl"===a,["".concat(t,"-").concat(m)]:!!m},o,k,R);return C(l.createElement("div",{className:T,style:r},l.createElement(S.Z,{ref:b,maxLength:c,value:h,onChange:e=>{let{target:t}=e;x(t.value.replace(/[\n\r]/g,""))},onKeyDown:e=>{let{keyCode:t}=e;y.current||(v.current=t)},onKeyUp:e=>{let{keyCode:t,ctrlKey:n,altKey:l,metaKey:o,shiftKey:r}=e;v.current!==t||y.current||n||l||o||r||(t===E.Z.ENTER?(j(),null==f||f()):t===E.Z.ESC&&p())},onCompositionStart:()=>{y.current=!0},onCompositionEnd:()=>{y.current=!1},onBlur:()=>{j()},"aria-label":n,rows:1,autoSize:s}),null!==g?(0,w.Tm)(g,{className:"".concat(t,"-edit-content-confirm")}):null))},L=n(2390),A=n.n(L),W=n(28788),F=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return t&&null==e?[]:Array.isArray(e)?e:[e]},U=e=>{let{copyConfig:t,children:n}=e,[o,r]=l.useState(!1),[a,i]=l.useState(!1),c=l.useRef(null),s=()=>{c.current&&clearTimeout(c.current)},u={};return t.format&&(u.format=t.format),l.useEffect(()=>s,[]),{copied:o,copyLoading:a,onClick:(0,W.Z)(e=>{var l,o,a,d;return l=void 0,o=void 0,a=void 0,d=function*(){var l;null==e||e.preventDefault(),null==e||e.stopPropagation(),i(!0);try{let o="function"==typeof t.text?yield t.text():t.text;A()(o||F(n,!0).join("")||"",u),i(!1),r(!0),s(),c.current=setTimeout(()=>{r(!1)},3e3),null===(l=t.onCopy)||void 0===l||l.call(t,e)}catch(e){throw i(!1),e}},new(a||(a=Promise))(function(e,t){function n(e){try{i(d.next(e))}catch(e){t(e)}}function r(e){try{i(d.throw(e))}catch(e){t(e)}}function i(t){var l;t.done?e(t.value):((l=t.value)instanceof a?l:new a(function(e){e(l)})).then(n,r)}i((d=d.apply(l,o||[])).next())})})}};function V(e,t){return l.useMemo(()=>{let n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"==typeof e?e:null)]},[e])}var _=e=>{let t=(0,l.useRef)(void 0);return(0,l.useEffect)(()=>{t.current=e}),t.current},q=(e,t,n)=>(0,l.useMemo)(()=>!0===e?{title:null!=t?t:n}:(0,l.isValidElement)(e)?{title:e}:"object"==typeof e?Object.assign({title:null!=t?t:n},e):{title:e},[e,t,n]),X=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,l=Object.getOwnPropertySymbols(e);o<l.length;o++)0>t.indexOf(l[o])&&Object.prototype.propertyIsEnumerable.call(e,l[o])&&(n[l[o]]=e[l[o]]);return n};let G=l.forwardRef((e,t)=>{let{prefixCls:n,component:o="article",className:r,rootClassName:a,setContentRef:c,children:s,direction:u,style:d}=e,p=X(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:m,direction:b,className:y,style:v}=(0,g.dj)("typography"),h=c?(0,f.sQ)(t,c):t,x=m("typography",n),[O,E,w]=P(x),S=i()(x,y,{["".concat(x,"-rtl")]:"rtl"===(null!=u?u:b)},r,a,E,w),j=Object.assign(Object.assign({},v),d);return O(l.createElement(o,Object.assign({className:S,style:j,ref:h},p),s))});var K=n(67883),Q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"},$=l.forwardRef(function(e,t){return l.createElement(x.Z,(0,v.Z)({},e,{ref:t,icon:Q}))}),J=n(7898);function Y(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function ee(e,t,n){return!0===e||void 0===e?t:e||n&&t}let et=e=>["string","number"].includes(typeof e);var en=e=>{let{prefixCls:t,copied:n,locale:o,iconOnly:r,tooltips:a,icon:c,tabIndex:s,onCopy:u,loading:d}=e,p=Y(a),f=Y(c),{copied:m,copy:g}=null!=o?o:{},b=n?m:g,v=ee(p[n?1:0],b),h="string"==typeof v?v:b;return l.createElement(y.Z,{title:v},l.createElement("button",{type:"button",className:i()("".concat(t,"-copy"),{["".concat(t,"-copy-success")]:n,["".concat(t,"-copy-icon-only")]:r}),onClick:u,"aria-label":h,tabIndex:s},n?ee(f[1],l.createElement(K.Z,null),!0):ee(f[0],d?l.createElement(J.Z,null):l.createElement($,null),!0)))};let el=l.forwardRef((e,t)=>{let{style:n,children:o}=e,r=l.useRef(null);return l.useImperativeHandle(t,()=>({isExceed:()=>{let e=r.current;return e.scrollHeight>e.clientHeight},getHeight:()=>r.current.clientHeight})),l.createElement("span",{"aria-hidden":!0,ref:r,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},n)},o)}),eo=e=>e.reduce((e,t)=>e+(et(t)?String(t).length:1),0);function er(e,t){let n=0,l=[];for(let o=0;o<e.length;o+=1){if(n===t)return l;let r=e[o],a=n+(et(r)?String(r).length:1);if(a>t){let e=t-n;return l.push(String(r).slice(0,e)),l}l.push(r),n=a}return e}let ea={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function ei(e){let{enableMeasure:t,width:n,text:r,children:a,rows:i,expanded:c,miscDeps:d,onEllipsis:p}=e,f=l.useMemo(()=>(0,s.Z)(r),[r]),m=l.useMemo(()=>eo(f),[r]),g=l.useMemo(()=>a(f,!1),[r]),[b,y]=l.useState(null),v=l.useRef(null),h=l.useRef(null),x=l.useRef(null),O=l.useRef(null),E=l.useRef(null),[w,S]=l.useState(!1),[j,C]=l.useState(0),[k,R]=l.useState(0),[T,Z]=l.useState(null);(0,u.Z)(()=>{t&&n&&m?C(1):C(0)},[n,r,i,t,f]),(0,u.Z)(()=>{var e,t,n,l;if(1===j)C(2),Z(h.current&&getComputedStyle(h.current).whiteSpace);else if(2===j){let o=!!(null===(e=x.current)||void 0===e?void 0:e.isExceed());C(o?3:4),y(o?[0,m]:null),S(o);let r=(null===(t=x.current)||void 0===t?void 0:t.getHeight())||0;R(Math.max(r,(1===i?0:(null===(n=O.current)||void 0===n?void 0:n.getHeight())||0)+((null===(l=E.current)||void 0===l?void 0:l.getHeight())||0))+1),p(o)}},[j]);let I=b?Math.ceil((b[0]+b[1])/2):0;(0,u.Z)(()=>{var e;let[t,n]=b||[0,0];if(t!==n){let l=((null===(e=v.current)||void 0===e?void 0:e.getHeight())||0)>k,o=I;n-t==1&&(o=l?t:n),y(l?[t,o]:[o,n])}},[b,I]);let D=l.useMemo(()=>{if(!t)return a(f,!1);if(3!==j||!b||b[0]!==b[1]){let e=a(f,!1);return[4,0].includes(j)?e:l.createElement("span",{style:Object.assign(Object.assign({},ea),{WebkitLineClamp:i})},e)}return a(c?f:er(f,b[0]),w)},[c,j,b,f].concat((0,o.Z)(d))),H={width:n,margin:0,padding:0,whiteSpace:"nowrap"===T?"normal":"inherit"};return l.createElement(l.Fragment,null,D,2===j&&l.createElement(l.Fragment,null,l.createElement(el,{style:Object.assign(Object.assign(Object.assign({},H),ea),{WebkitLineClamp:i}),ref:x},g),l.createElement(el,{style:Object.assign(Object.assign(Object.assign({},H),ea),{WebkitLineClamp:i-1}),ref:O},g),l.createElement(el,{style:Object.assign(Object.assign(Object.assign({},H),ea),{WebkitLineClamp:1}),ref:E},a([],!0))),3===j&&b&&b[0]!==b[1]&&l.createElement(el,{style:Object.assign(Object.assign({},H),{top:400}),ref:v},a(er(f,I),!0)),1===j&&l.createElement("span",{style:{whiteSpace:"inherit"},ref:h}))}var ec=e=>{let{enableEllipsis:t,isEllipsis:n,children:o,tooltipProps:r}=e;return(null==r?void 0:r.title)&&t?l.createElement(y.Z,Object.assign({open:!!n&&void 0},r),o):o},es=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,l=Object.getOwnPropertySymbols(e);o<l.length;o++)0>t.indexOf(l[o])&&Object.prototype.propertyIsEnumerable.call(e,l[o])&&(n[l[o]]=e[l[o]]);return n};let eu=["delete","mark","code","underline","strong","keyboard","italic"],ed=l.forwardRef((e,t)=>{var n;let{prefixCls:a,className:v,style:h,type:x,disabled:O,children:E,ellipsis:w,editable:S,copyable:j,component:C,title:k}=e,R=es(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:T,direction:Z}=l.useContext(g.E_),[I]=(0,b.Z)("Text"),D=l.useRef(null),H=l.useRef(null),M=T("typography",a),z=(0,p.Z)(R,eu),[B,P]=V(S),[L,A]=(0,d.Z)(!1,{value:P.editing}),{triggerType:W=["icon"]}=P,F=e=>{var t;e&&(null===(t=P.onStart)||void 0===t||t.call(P)),A(e)},X=_(L);(0,u.Z)(()=>{var e;!L&&X&&(null===(e=H.current)||void 0===e||e.focus())},[L]);let K=e=>{null==e||e.preventDefault(),F(!0)},[Q,$]=V(j),{copied:J,copyLoading:Y,onClick:ee}=U({copyConfig:$,children:E}),[el,eo]=l.useState(!1),[er,ea]=l.useState(!1),[ed,ep]=l.useState(!1),[ef,em]=l.useState(!1),[eg,eb]=l.useState(!0),[ey,ev]=V(w,{expandable:!1,symbol:e=>e?null==I?void 0:I.collapse:null==I?void 0:I.expand}),[eh,ex]=(0,d.Z)(ev.defaultExpanded||!1,{value:ev.expanded}),eO=ey&&(!eh||"collapsible"===ev.expandable),{rows:eE=1}=ev,ew=l.useMemo(()=>eO&&(void 0!==ev.suffix||ev.onEllipsis||ev.expandable||B||Q),[eO,ev,B,Q]);(0,u.Z)(()=>{ey&&!ew&&(eo((0,m.G)("webkitLineClamp")),ea((0,m.G)("textOverflow")))},[ew,ey]);let[eS,ej]=l.useState(eO),eC=l.useMemo(()=>!ew&&(1===eE?er:el),[ew,er,el]);(0,u.Z)(()=>{ej(eC&&eO)},[eC,eO]);let ek=eO&&(eS?ef:ed),eR=eO&&1===eE&&eS,eT=eO&&eE>1&&eS,eZ=(e,t)=>{var n;ex(t.expanded),null===(n=ev.onExpand)||void 0===n||n.call(ev,e,t)},[eI,eD]=l.useState(0),eH=e=>{var t;ep(e),ed!==e&&(null===(t=ev.onEllipsis)||void 0===t||t.call(ev,e))};l.useEffect(()=>{let e=D.current;if(ey&&eS&&e){let t=function(e){let t=document.createElement("em");e.appendChild(t);let n=e.getBoundingClientRect(),l=t.getBoundingClientRect();return e.removeChild(t),n.left>l.left||l.right>n.right||n.top>l.top||l.bottom>n.bottom}(e);ef!==t&&em(t)}},[ey,eS,E,eT,eg,eI]),l.useEffect(()=>{let e=D.current;if("undefined"==typeof IntersectionObserver||!e||!eS||!eO)return;let t=new IntersectionObserver(()=>{eb(!!e.offsetParent)});return t.observe(e),()=>{t.disconnect()}},[eS,eO]);let eM=q(ev.tooltip,P.text,E),ez=l.useMemo(()=>{if(ey&&!eS)return[P.text,E,k,eM.title].find(et)},[ey,eS,k,eM.title,ek]);if(L)return l.createElement(N,{value:null!==(n=P.text)&&void 0!==n?n:"string"==typeof E?E:"",onSave:e=>{var t;null===(t=P.onChange)||void 0===t||t.call(P,e),F(!1)},onCancel:()=>{var e;null===(e=P.onCancel)||void 0===e||e.call(P),F(!1)},onEnd:P.onEnd,prefixCls:M,className:v,style:h,direction:Z,component:C,maxLength:P.maxLength,autoSize:P.autoSize,enterIcon:P.enterIcon});let eB=()=>{let{expandable:e,symbol:t}=ev;return e?l.createElement("button",{type:"button",key:"expand",className:"".concat(M,"-").concat(eh?"collapse":"expand"),onClick:e=>eZ(e,{expanded:!eh}),"aria-label":eh?I.collapse:null==I?void 0:I.expand},"function"==typeof t?t(eh):t):null},eP=()=>{if(!B)return;let{icon:e,tooltip:t,tabIndex:n}=P,o=(0,s.Z)(t)[0]||(null==I?void 0:I.edit),a="string"==typeof o?o:"";return W.includes("icon")?l.createElement(y.Z,{key:"edit",title:!1===t?"":o},l.createElement("button",{type:"button",ref:H,className:"".concat(M,"-edit"),onClick:K,"aria-label":a,tabIndex:n},e||l.createElement(r.Z,{role:"button"}))):null},eN=()=>Q?l.createElement(en,Object.assign({key:"copy"},$,{prefixCls:M,copied:J,locale:I,onCopy:ee,loading:Y,iconOnly:null==E})):null,eL=e=>[e&&eB(),eP(),eN()],eA=e=>[e&&!eh&&l.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),ev.suffix,eL(e)];return l.createElement(c.Z,{onResize:e=>{let{offsetWidth:t}=e;eD(t)},disabled:!eO},n=>l.createElement(ec,{tooltipProps:eM,enableEllipsis:eO,isEllipsis:ek},l.createElement(G,Object.assign({className:i()({["".concat(M,"-").concat(x)]:x,["".concat(M,"-disabled")]:O,["".concat(M,"-ellipsis")]:ey,["".concat(M,"-ellipsis-single-line")]:eR,["".concat(M,"-ellipsis-multiple-line")]:eT},v),prefixCls:a,style:Object.assign(Object.assign({},h),{WebkitLineClamp:eT?eE:void 0}),component:C,ref:(0,f.sQ)(n,D,t),direction:Z,onClick:W.includes("text")?K:void 0,"aria-label":null==ez?void 0:ez.toString(),title:k},z),l.createElement(ei,{enableMeasure:eO&&!eS,text:E,rows:eE,width:eI,onEllipsis:eH,expanded:eh,miscDeps:[J,eh,Y,B,Q,I].concat((0,o.Z)(eu.map(t=>e[t])))},(t,n)=>(function(e,t){let{mark:n,code:o,underline:r,delete:a,strong:i,keyboard:c,italic:s}=e,u=t;function d(e,t){t&&(u=l.createElement(e,{},u))}return d("strong",i),d("u",r),d("del",a),d("code",o),d("mark",n),d("kbd",c),d("i",s),u})(e,l.createElement(l.Fragment,null,t.length>0&&n&&!eh&&ez?l.createElement("span",{key:"show-content","aria-hidden":!0},t):t,eA(n)))))))});var ep=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,l=Object.getOwnPropertySymbols(e);o<l.length;o++)0>t.indexOf(l[o])&&Object.prototype.propertyIsEnumerable.call(e,l[o])&&(n[l[o]]=e[l[o]]);return n};let ef=l.forwardRef((e,t)=>{var{ellipsis:n,rel:o}=e,r=ep(e,["ellipsis","rel"]);let a=Object.assign(Object.assign({},r),{rel:void 0===o&&"_blank"===r.target?"noopener noreferrer":o});return delete a.navigate,l.createElement(ed,Object.assign({},a,{ref:t,ellipsis:!!n,component:"a"}))}),em=l.forwardRef((e,t)=>l.createElement(ed,Object.assign({ref:t},e,{component:"div"})));var eg=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,l=Object.getOwnPropertySymbols(e);o<l.length;o++)0>t.indexOf(l[o])&&Object.prototype.propertyIsEnumerable.call(e,l[o])&&(n[l[o]]=e[l[o]]);return n},eb=l.forwardRef((e,t)=>{var{ellipsis:n}=e,o=eg(e,["ellipsis"]);let r=l.useMemo(()=>n&&"object"==typeof n?(0,p.Z)(n,["expandable","rows"]):n,[n]);return l.createElement(ed,Object.assign({ref:t},o,{ellipsis:r,component:"span"}))}),ey=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,l=Object.getOwnPropertySymbols(e);o<l.length;o++)0>t.indexOf(l[o])&&Object.prototype.propertyIsEnumerable.call(e,l[o])&&(n[l[o]]=e[l[o]]);return n};let ev=[1,2,3,4,5],eh=l.forwardRef((e,t)=>{let{level:n=1}=e,o=ey(e,["level"]),r=ev.includes(n)?"h".concat(n):"h1";return l.createElement(ed,Object.assign({ref:t},o,{component:r}))});G.Text=eb,G.Link=ef,G.Title=eh,G.Paragraph=em;var ex=G},25809:function(e){e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],l=0;l<e.rangeCount;l++)n.push(e.getRangeAt(l));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}}}]);