(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1086],{70596:function(e,t,s){Promise.resolve().then(s.bind(s,80077))},80077:function(e,t,s){"use strict";s.r(t);var r=s(57437),a=s(2265),i=s(27296),n=s(23656),l=s(89198),d=s(55175),c=s(28683),o=s(89511),m=s(9427),u=s(94734),x=s(6053),h=s(38302),p=s(86155),j=s(50574),f=s(47628),Z=s(59189),N=s(75123),g=s(57084),v=s(87304),y=s(6063),w=s(13412);let{Option:b}=i.default,{Title:T}=n.default;t.default=()=>{var e,t,s;let[i]=l.Z.useForm(),[n,b]=(0,a.useState)(!1),[k,S]=(0,a.useState)(!1),[D,I]=(0,a.useState)(""),[M,A]=(0,a.useState)(0),[C]=(0,a.useState)([{id:"W1",code:"A1",name:"成型机A线-1号工位",description:"主要用于圆形餐盘生产",status:"active",currentMoldNumber:"M001",currentBatchNumber:"PC202412050001",batchNumberQueue:["PC202412050003"],lastEndTime:"2024-12-05T18:00:00Z",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T14:00:00Z",version:1,lastModifiedBy:"system",lastModifiedAt:"2024-01-15T14:00:00Z"},{id:"W2",code:"B1",name:"成型机B线-1号工位",description:"主要用于方形餐盒生产",status:"active",currentMoldNumber:"M002",currentBatchNumber:"PC202412050002",batchNumberQueue:[],lastEndTime:"2024-12-05T16:30:00Z",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T15:00:00Z",version:1,lastModifiedBy:"system",lastModifiedAt:"2024-01-15T15:00:00Z"},{id:"W3",code:"C1",name:"成型机C线-1号工位",description:"备用工位",status:"inactive",currentMoldNumber:null,currentBatchNumber:null,batchNumberQueue:[],lastEndTime:null,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-10T08:00:00Z",version:1,lastModifiedBy:"system",lastModifiedAt:"2024-01-10T08:00:00Z"}]),[B,P]=(0,a.useState)([{id:"report-001",workstationId:"W1",operatorId:"OP001",operatorName:"张师傅",reportTime:"2024-01-15T14:00:00Z",completedMolds:300,reportType:"normal",isValidated:!0,remark:"正常生产",createdAt:"2024-01-15T14:00:00Z"},{id:"report-002",workstationId:"W2",operatorId:"OP002",operatorName:"李师傅",reportTime:"2024-01-15T15:00:00Z",completedMolds:400,reportType:"normal",isValidated:!0,remark:"正常生产",createdAt:"2024-01-15T15:00:00Z"}]),E=e=>{let t=new Date().toDateString();return B.filter(s=>s.workstationId===e&&new Date(s.reportTime).toDateString()===t).reduce((e,t)=>e+t.completedMolds,0)},O=e=>{let t=new Date().toDateString();return B.filter(s=>s.workstationId===e&&new Date(s.reportTime).toDateString()===t).length},_=e=>{I(e),S(!0)},W=async()=>{if(D){b(!0);try{if(!C.find(e=>e.id===D))throw Error("工位不存在");let e={id:"report-".concat(Date.now()),workstationId:D,operatorId:"unknown",operatorName:"未知操作员",reportTime:new Date().toISOString(),completedMolds:M,reportType:"normal",isValidated:!1,remark:"手动报工",createdAt:new Date().toISOString()};P(t=>[...t,e]),d.ZP.success("报工成功！"),S(!1),A(0),i.resetFields()}catch(e){d.ZP.error("报工失败，请重试")}finally{b(!1)}}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)(T,{level:2,children:"工作报告"}),(0,r.jsx)("p",{className:"text-gray-600",children:"实时监控生产进度，记录工作完成情况"})]}),(0,r.jsxs)(h.Z,{gutter:[16,16],className:"mb-6",children:[(0,r.jsx)(c.Z,{xs:24,sm:6,children:(0,r.jsx)(o.Z,{children:(0,r.jsx)(p.Z,{title:"活跃工位",value:C.filter(e=>"active"===e.status).length,suffix:"个",valueStyle:{color:"#52c41a"},prefix:(0,r.jsx)(g.Z,{})})})}),(0,r.jsx)(c.Z,{xs:24,sm:6,children:(0,r.jsx)(o.Z,{children:(0,r.jsx)(p.Z,{title:"今日报工",value:B.filter(e=>new Date(e.reportTime).toDateString()===new Date().toDateString()).length,suffix:"次",valueStyle:{color:"#1890ff"},prefix:(0,r.jsx)(v.Z,{})})})}),(0,r.jsx)(c.Z,{xs:24,sm:6,children:(0,r.jsx)(o.Z,{children:(0,r.jsx)(p.Z,{title:"今日产量",value:B.filter(e=>new Date(e.reportTime).toDateString()===new Date().toDateString()).reduce((e,t)=>e+t.completedMolds,0),suffix:"模",valueStyle:{color:"#722ed1"},prefix:(0,r.jsx)(y.Z,{})})})}),(0,r.jsx)(c.Z,{xs:24,sm:6,children:(0,r.jsx)(o.Z,{children:(0,r.jsx)(p.Z,{title:"空闲工位",value:C.filter(e=>"inactive"===e.status).length,suffix:"个",valueStyle:{color:"#fa8c16"},prefix:(0,r.jsx)(w.Z,{})})})})]}),(0,r.jsx)(o.Z,{title:"工位状态",className:"mb-6",children:(0,r.jsx)(h.Z,{gutter:[16,16],children:C.map(e=>{let t=E(e.id),s=O(e.id);return(0,r.jsx)(c.Z,{xs:24,lg:8,children:(0,r.jsx)(o.Z,{title:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{children:e.name}),(0,r.jsx)(m.Z,{status:"active"===e.status?"processing":"default",text:"active"===e.status?"运行中":"空闲"})]}),extra:(0,r.jsx)(u.ZP,{type:"primary",size:"small",icon:(0,r.jsx)(g.Z,{}),onClick:()=>_(e.id),disabled:"active"!==e.status,children:"报工"}),className:"mb-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"负责人:"}),(0,r.jsx)("span",{className:"font-medium",children:"未分配"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"当前批次:"}),(0,r.jsx)("span",{className:"font-medium",children:e.currentBatchNumber||"无"})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"今日产量:"}),(0,r.jsxs)("span",{className:"font-medium text-blue-600",children:[t," 模"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"报工次数:"}),(0,r.jsxs)("span",{className:"font-medium",children:[s," 次"]})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"状态:"}),(0,r.jsx)("span",{className:"font-medium ".concat("active"===e.status?"text-green-600":"text-red-600"),children:"active"===e.status?"运行中":"停用"})]})]})})},e.id)})})}),(0,r.jsx)(o.Z,{title:"报工记录",children:(0,r.jsx)(j.Z,{columns:[{title:"报工时间",dataIndex:"reportTime",key:"reportTime",width:180,render:e=>new Date(e).toLocaleString()},{title:"工位",dataIndex:"workstationId",key:"workstationId",width:120,render:e=>{let t=C.find(t=>t.id===e);return t?t.name:e}},{title:"操作员",dataIndex:"operatorName",key:"operatorName",width:100},{title:"报工模数",dataIndex:"completedMolds",key:"completedMolds",width:120,render:e=>(0,r.jsxs)("span",{className:"font-medium text-blue-600",children:[e," 模"]})},{title:"类型",dataIndex:"reportType",key:"reportType",width:100,render:e=>(0,r.jsx)(x.Z,{color:"normal"===e?"green":"orange",children:"normal"===e?"正常":"异常"})},{title:"备注",dataIndex:"remark",key:"remark",ellipsis:!0}],dataSource:B,rowKey:"id",pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>"共 ".concat(e," 条记录")}})}),(0,r.jsx)(f.Z,{title:"工作报告",open:k,onCancel:()=>S(!1),onOk:W,confirmLoading:n,width:600,children:D&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(Z.Z,{message:"报工信息确认",description:"请确认报工信息后提交",type:"info",showIcon:!0}),(0,r.jsxs)(h.Z,{gutter:16,children:[(0,r.jsx)(c.Z,{span:12,children:(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"工位信息"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"工位:"})," ",null===(e=C.find(e=>e.id===D))||void 0===e?void 0:e.name]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"负责人:"})," 未分配"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"设备:"})," ",null===(t=C.find(e=>e.id===D))||void 0===t?void 0:t.name]})]})}),(0,r.jsx)(c.Z,{span:12,children:(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"生产信息"}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"当前批次:"})," ",(null===(s=C.find(e=>e.id===D))||void 0===s?void 0:s.currentBatchNumber)||"无"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"今日产量:"})," ",E(D)," 模"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"报工次数:"})," ",O(D)," 次"]})]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"报工模数"}),(0,r.jsx)(N.Z,{value:M,onChange:e=>A(e||0),min:1,max:2e3,className:"w-full",placeholder:"请输入完成的模数",addonAfter:"模"})]})]})})]})}}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9427,1157,3656,303,2971,4938,1744],function(){return e(e.s=70596)}),_N_E=e.O()}]);