(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3007],{65362:function(e,t,l){"use strict";l.d(t,{Z:function(){return n}});var s=l(13428),r=l(2265),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"},c=l(46614),n=r.forwardRef(function(e,t){return r.createElement(c.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},51769:function(e,t,l){"use strict";l.d(t,{Z:function(){return n}});var s=l(13428),r=l(2265),a={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"},c=l(46614),n=r.forwardRef(function(e,t){return r.createElement(c.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},6063:function(e,t,l){"use strict";l.d(t,{Z:function(){return n}});var s=l(13428),r=l(2265),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"},c=l(46614),n=r.forwardRef(function(e,t){return r.createElement(c.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},6371:function(e,t,l){"use strict";l.d(t,{Z:function(){return n}});var s=l(13428),r=l(2265),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"},c=l(46614),n=r.forwardRef(function(e,t){return r.createElement(c.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},34747:function(e,t,l){Promise.resolve().then(l.bind(l,16716))},47628:function(e,t,l){"use strict";l.d(t,{Z:function(){return y}});var s=l(73465),r=l(99486),a=l(5832),c=l(2265),n=l(42744),i=l.n(n),o=l(33746),d=l(21467),u=l(57499),h=l(92935),x=l(1601),m=l(19704),f=l(42203),j=function(e,t){var l={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(l[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,s=Object.getOwnPropertySymbols(e);r<s.length;r++)0>t.indexOf(s[r])&&Object.prototype.propertyIsEnumerable.call(e,s[r])&&(l[s[r]]=e[s[r]]);return l},Z=(0,d.i)(e=>{let{prefixCls:t,className:l,closeIcon:s,closable:r,type:a,title:n,children:d,footer:Z}=e,p=j(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:v}=c.useContext(u.E_),g=v(),y=t||v("modal"),C=(0,h.Z)(g),[b,w,R]=(0,f.ZP)(y,C),S="".concat(y,"-confirm"),I={};return I=a?{closable:null!=r&&r,title:"",footer:"",children:c.createElement(x.O,Object.assign({},e,{prefixCls:y,confirmPrefixCls:S,rootPrefixCls:g,content:d}))}:{closable:null==r||r,title:n,footer:null!==Z&&c.createElement(m.$,Object.assign({},e)),children:d},b(c.createElement(o.s,Object.assign({prefixCls:y,className:i()(w,"".concat(y,"-pure-panel"),a&&S,a&&"".concat(S,"-").concat(a),l,R,C)},p,{closeIcon:(0,m.b)(y,s),closable:r},I)))}),p=l(36245);function v(e){return(0,s.ZP)((0,s.uW)(e))}let g=a.Z;g.useModal=p.Z,g.info=function(e){return(0,s.ZP)((0,s.cw)(e))},g.success=function(e){return(0,s.ZP)((0,s.vq)(e))},g.error=function(e){return(0,s.ZP)((0,s.AQ)(e))},g.warning=v,g.warn=v,g.confirm=function(e){return(0,s.ZP)((0,s.Au)(e))},g.destroyAll=function(){for(;r.Z.length;){let e=r.Z.pop();e&&e()}},g.config=s.ai,g._InternalPanelDoNotUseOrYouWillBeFired=Z;var y=g},16716:function(e,t,l){"use strict";l.r(t),l.d(t,{default:function(){return D}});var s=l(57437),r=l(2265),a=l(27296),c=l(39992),n=l(57416),i=l(89198),o=l(6053),d=l(34863),u=l(9427),h=l(65270),x=l(94734),m=l(92503),f=l(38302),j=l(28683),Z=l(89511),p=l(86155),v=l(50574),g=l(47628),y=l(2012),C=l(99617),b=l(6063),w=l(75216),R=l(65362),S=l(34021),I=l(6371),N=l(75393),E=l(51769),L=l(74898),O=l(13428),P={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"},k=l(46614),_=r.forwardRef(function(e,t){return r.createElement(k.Z,(0,O.Z)({},e,{ref:t,icon:P}))}),A={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"},T=r.forwardRef(function(e,t){return r.createElement(k.Z,(0,O.Z)({},e,{ref:t,icon:A}))}),z=l(32779),H=l(50538);let{Option:M}=a.default,{TextArea:F}=c.default;function B(){let{message:e}=n.Z.useApp(),[t,l]=(0,r.useState)([]),[O,P]=(0,r.useState)(!1),[k,A]=(0,r.useState)(null),[F,B]=(0,r.useState)(!1),[D,K]=(0,r.useState)(!1),[U,V]=(0,r.useState)(null),[X,$]=(0,r.useState)("basic"),[q]=i.Z.useForm(),[W,Q]=(0,r.useState)(""),[Y,J]=(0,r.useState)(""),[G,ee]=(0,r.useState)(""),et=()=>{let l=t.map(e=>e.customerCode).filter(e=>/^KH\d{4,5}$/.test(e));if(0===l.length)return"KH0001";let s=Math.max(...l.map(e=>{let t=e.match(/^KH(\d{4,5})$/);return t?parseInt(t[1],10):0}))+1;return s>99999?(e.warning("编码已达到最大值KH99999，请手动输入编码"),""):s<=9999?"KH".concat(s.toString().padStart(4,"0")):"KH".concat(s.toString())},el=(e,l)=>!t.some(t=>t.customerCode===e&&t.id!==l);(0,r.useEffect)(()=>{console.log("客户数据已加载",{customers:t.length})},[t.length]);let es=e=>{switch(e){case"A":return"gold";case"B":return"blue";case"C":return"green";default:return"default"}},er=e=>{switch(e){case"important":return"red";case"general":return"orange";case"small":return"cyan";default:return"default"}},ea=e=>{switch(e){case"important":return"重要客户";case"general":return"一般客户";case"small":return"小客户";default:return"未分类"}},ec=(e,t)=>t>0?e/t*100:0,en=[{title:"客户编码",dataIndex:"customerCode",key:"customerCode",width:120,fixed:"left"},{title:"客户名称",dataIndex:"customerName",key:"customerName",width:200,fixed:"left"},{title:"客户等级",dataIndex:"customerLevel",key:"customerLevel",width:100,render:e=>(0,s.jsxs)(o.Z,{color:es(e),icon:(0,s.jsx)(b.Z,{}),children:[e,"类"]})},{title:"客户分类",dataIndex:"customerCategory",key:"customerCategory",width:120,render:e=>(0,s.jsx)(o.Z,{color:er(e),children:ea(e)})},{title:"联系人",dataIndex:"contactPerson",key:"contactPerson",width:100},{title:"联系电话",dataIndex:"contactPhone",key:"contactPhone",width:130},{title:"年销售额",dataIndex:"annualSalesAmount",key:"annualSalesAmount",width:120,render:e=>"\xa5".concat((e||0).toLocaleString())},{title:"信用额度",key:"creditInfo",width:150,render:(e,t)=>{let l=t.creditLimit||0,r=t.usedCredit||0,a=ec(r,l);return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{children:["\xa5",l.toLocaleString()]}),(0,s.jsx)(d.Z,{percent:a,size:"small",status:a>80?"exception":"normal",showInfo:!1}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["已用: \xa5",r.toLocaleString()]})]})}},{title:"所属业务员",dataIndex:"salesRepresentative",key:"salesRepresentative",width:120},{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>(0,s.jsx)(u.Z,{status:"active"===e?"success":"default",text:"active"===e?"正常":"停用"})},{title:"操作",key:"action",width:200,fixed:"right",render:(e,t)=>(0,s.jsxs)(h.Z,{size:"small",children:[(0,s.jsx)(x.ZP,{type:"link",icon:(0,s.jsx)(w.Z,{}),onClick:()=>eh(t),children:"详情"}),(0,s.jsx)(x.ZP,{type:"link",icon:(0,s.jsx)(R.Z,{}),onClick:()=>eu(t),children:"编辑"}),(0,s.jsx)(m.Z,{title:"确定要删除这个客户吗？",onConfirm:()=>ex(t.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(x.ZP,{type:"link",danger:!0,icon:(0,s.jsx)(S.Z,{}),children:"删除"})})]})}],ei=t.filter(e=>{let t=!W||e.customerName.toLowerCase().includes(W.toLowerCase())||e.customerCode.toLowerCase().includes(W.toLowerCase())||(e.contactPerson||"").toLowerCase().includes(W.toLowerCase()),l=!Y||e.customerLevel===Y,s=!G||e.customerCategory===G;return t&&l&&s}),eo=async()=>{try{P(!0);let e=await (0,H.Ro)(()=>z.dataAccessManager.customers.getAll(),"获取客户数据");e&&e.items&&l(e.items)}catch(t){console.error("刷新客户数据失败:",t),e.error("刷新客户数据失败")}finally{P(!1)}};(0,r.useEffect)(()=>{eo()},[]);let ed={total:t.length,aLevel:t.filter(e=>"A"===e.customerLevel).length,bLevel:t.filter(e=>"B"===e.customerLevel).length,cLevel:t.filter(e=>"C"===e.customerLevel).length,totalSales:t.reduce((e,t)=>e+(t.annualSalesAmount||0),0),totalCredit:t.reduce((e,t)=>e+(t.creditLimit||0),0),usedCredit:t.reduce((e,t)=>e+(t.usedCredit||0),0)},eu=e=>{V(e),B(!0),$("basic"),q.setFieldsValue(e)},eh=e=>{A(e),K(!0)},ex=async t=>{try{P(!0),await (0,H.Ro)(()=>z.dataAccessManager.customers.delete(t),"删除客户")&&(await eo(),e.success("客户删除成功"))}catch(t){console.error("删除客户失败:",t),e.error("删除客户失败，请稍后重试")}finally{P(!1)}},em=async()=>{try{let t=await q.validateFields();if(P(!0),U){if(!await (0,H.Ro)(()=>z.dataAccessManager.customers.update(U.id,t),"更新客户信息"))return;await eo(),e.success("客户信息更新成功")}else{let l={...t,creditLimit:t.creditLimit||0,usedCredit:t.usedCredit||0,annualSalesAmount:t.annualSalesAmount||0,discountRate:t.discountRate||0};if(!await (0,H.Ro)(()=>z.dataAccessManager.customers.create(l),"创建客户"))return;await eo(),e.success("客户创建成功")}B(!1),$("basic"),q.resetFields()}catch(t){e.error("操作失败，请稍后重试"),console.error("客户操作失败:",t)}finally{P(!1)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"page-header",children:[(0,s.jsx)("h1",{className:"page-title",children:"客户管理"}),(0,s.jsx)("p",{className:"page-description",children:"管理客户档案、信用额度和客户关系"})]}),(0,s.jsxs)(f.Z,{gutter:[16,16],children:[(0,s.jsx)(j.Z,{xs:24,sm:6,children:(0,s.jsx)(Z.Z,{children:(0,s.jsx)(p.Z,{title:"客户总数",value:ed.total,suffix:"个",prefix:(0,s.jsx)(I.Z,{})})})}),(0,s.jsx)(j.Z,{xs:24,sm:6,children:(0,s.jsx)(Z.Z,{children:(0,s.jsx)(p.Z,{title:"年销售总额",value:ed.totalSales,precision:0,prefix:"\xa5",valueStyle:{color:"#3f8600"}})})}),(0,s.jsx)(j.Z,{xs:24,sm:6,children:(0,s.jsx)(Z.Z,{children:(0,s.jsx)(p.Z,{title:"信用总额度",value:ed.totalCredit,precision:0,prefix:"\xa5",valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(j.Z,{xs:24,sm:6,children:(0,s.jsx)(Z.Z,{children:(0,s.jsx)(p.Z,{title:"信用使用率",value:ed.totalCredit>0?ed.usedCredit/ed.totalCredit*100:0,precision:1,suffix:"%",valueStyle:{color:ed.totalCredit>0&&ed.usedCredit/ed.totalCredit>.8?"#cf1322":"#1890ff"}})})})]}),(0,s.jsxs)(f.Z,{gutter:[16,16],children:[(0,s.jsx)(j.Z,{xs:24,sm:8,children:(0,s.jsx)(Z.Z,{children:(0,s.jsx)(p.Z,{title:"A级客户",value:ed.aLevel,suffix:"个",valueStyle:{color:"#faad14"}})})}),(0,s.jsx)(j.Z,{xs:24,sm:8,children:(0,s.jsx)(Z.Z,{children:(0,s.jsx)(p.Z,{title:"B级客户",value:ed.bLevel,suffix:"个",valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(j.Z,{xs:24,sm:8,children:(0,s.jsx)(Z.Z,{children:(0,s.jsx)(p.Z,{title:"C级客户",value:ed.cLevel,suffix:"个",valueStyle:{color:"#52c41a"}})})})]}),(0,s.jsx)(Z.Z,{children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,s.jsx)(c.default,{placeholder:"搜索客户名称、编码或联系人",prefix:(0,s.jsx)(N.Z,{}),value:W,onChange:e=>Q(e.target.value),className:"w-full sm:w-64"}),(0,s.jsxs)(a.default,{placeholder:"客户等级",value:Y,onChange:J,className:"w-full sm:w-32",allowClear:!0,children:[(0,s.jsx)(M,{value:"A",children:"A级"}),(0,s.jsx)(M,{value:"B",children:"B级"}),(0,s.jsx)(M,{value:"C",children:"C级"})]}),(0,s.jsxs)(a.default,{placeholder:"客户分类",value:G,onChange:ee,className:"w-full sm:w-32",allowClear:!0,children:[(0,s.jsx)(M,{value:"important",children:"重要客户"}),(0,s.jsx)(M,{value:"general",children:"一般客户"}),(0,s.jsx)(M,{value:"small",children:"小客户"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(x.ZP,{icon:(0,s.jsx)(E.Z,{}),children:"导出"}),(0,s.jsx)(x.ZP,{type:"primary",icon:(0,s.jsx)(L.Z,{}),onClick:()=>{V(null),B(!0),$("basic"),q.resetFields();let e=et();e&&q.setFieldsValue({customerCode:e})},children:"新建客户"})]})]})}),(0,s.jsx)(Z.Z,{title:"客户列表",children:(0,s.jsx)(v.Z,{columns:en,dataSource:ei,rowKey:"id",loading:O,pagination:{total:ei.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条")},scroll:{x:1400}})}),(0,s.jsx)(g.Z,{title:U?"编辑客户":"新建客户",open:F,onOk:em,onCancel:()=>{B(!1),$("basic"),q.resetFields()},width:900,okText:"确认",cancelText:"取消",children:(0,s.jsx)(i.Z,{form:q,layout:"vertical",initialValues:{status:"active"},children:(0,s.jsx)(y.default,{activeKey:X,onChange:$,items:[{key:"basic",label:"基础信息",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(f.Z,{gutter:16,children:[(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(i.Z.Item,{name:"customerCode",label:"客户编码",rules:[{required:!0,message:"请输入客户编码"},{pattern:/^KH\d{4,5}$/,message:"格式：KHXXXX或KHXXXXX（如：KH0001）"},{validator:(e,t)=>t?el(t,null==U?void 0:U.id)?Promise.resolve():Promise.reject(Error("客户编码已存在，请使用其他编码")):Promise.resolve()}],children:(0,s.jsx)(c.default,{placeholder:"如：KH0001（自动生成）"})})}),(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(i.Z.Item,{name:"customerName",label:"客户名称",rules:[{required:!0,message:"请输入客户名称"}],children:(0,s.jsx)(c.default,{placeholder:"请输入客户名称"})})})]}),(0,s.jsxs)(f.Z,{gutter:16,children:[(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(i.Z.Item,{name:"contactPerson",label:"联系人",children:(0,s.jsx)(c.default,{placeholder:"请输入联系人"})})}),(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(i.Z.Item,{name:"contactPhone",label:"联系电话",rules:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式"}],children:(0,s.jsx)(c.default,{placeholder:"请输入联系电话"})})})]}),(0,s.jsxs)(f.Z,{gutter:16,children:[(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(i.Z.Item,{name:"referrer",label:"介绍人",children:(0,s.jsx)(c.default,{placeholder:"请输入介绍人"})})}),(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(i.Z.Item,{name:"salesRepresentative",label:"所属业务员",rules:[{required:!0,message:"请输入所属业务员"}],children:(0,s.jsx)(c.default,{placeholder:"请输入所属业务员"})})})]})]})},{key:"financial",label:"财务信息",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(f.Z,{gutter:16,children:[(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(i.Z.Item,{name:"bankName",label:"开户行",children:(0,s.jsx)(c.default,{placeholder:"请输入开户行"})})}),(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(i.Z.Item,{name:"bankAccount",label:"开户账号",children:(0,s.jsx)(c.default,{placeholder:"请输入开户账号"})})})]}),(0,s.jsx)(f.Z,{gutter:16,children:(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(i.Z.Item,{name:"taxNumber",label:"单位税号",children:(0,s.jsx)(c.default,{placeholder:"请输入单位税号"})})})}),(0,s.jsx)("div",{className:"text-gray-500 text-center py-8",children:(0,s.jsx)("p",{children:"更多财务信息功能开发中..."})})]})}]})})}),(0,s.jsx)(g.Z,{title:"客户详情",open:D,onCancel:()=>K(!1),footer:[(0,s.jsx)(x.ZP,{onClick:()=>K(!1),children:"关闭"},"close")],width:800,children:k&&k.customerCode&&k.customerName&&(0,s.jsx)(y.default,{defaultActiveKey:"basic",items:[{key:"basic",label:"基本信息",children:(0,s.jsxs)(C.Z,{column:2,bordered:!0,children:[(0,s.jsx)(C.Z.Item,{label:"客户编码",children:k.customerCode}),(0,s.jsx)(C.Z.Item,{label:"客户名称",children:k.customerName}),(0,s.jsx)(C.Z.Item,{label:"客户等级",children:(0,s.jsxs)(o.Z,{color:es(k.customerLevel||"C"),children:[k.customerLevel,"类"]})}),(0,s.jsx)(C.Z.Item,{label:"客户分类",children:(0,s.jsx)(o.Z,{color:er(k.customerCategory||"general"),children:ea(k.customerCategory||"general")})}),(0,s.jsx)(C.Z.Item,{label:"联系人",children:(0,s.jsxs)(h.Z,{children:[(0,s.jsx)(I.Z,{}),k.contactPerson]})}),(0,s.jsx)(C.Z.Item,{label:"联系电话",children:(0,s.jsxs)(h.Z,{children:[(0,s.jsx)(_,{}),k.contactPhone]})}),(0,s.jsx)(C.Z.Item,{label:"联系邮箱",children:(0,s.jsxs)(h.Z,{children:[(0,s.jsx)(T,{}),k.contactEmail||"未填写"]})}),(0,s.jsx)(C.Z.Item,{label:"地址",children:k.address}),(0,s.jsx)(C.Z.Item,{label:"税号",children:k.taxNumber||"未填写"}),(0,s.jsx)(C.Z.Item,{label:"付款条件",children:k.paymentTerms}),(0,s.jsxs)(C.Z.Item,{label:"年销售额",children:["\xa5",(k.annualSalesAmount||0).toLocaleString()]}),(0,s.jsxs)(C.Z.Item,{label:"专属折扣率",children:[(100*(k.discountRate||0)).toFixed(1),"%"]}),(0,s.jsx)(C.Z.Item,{label:"所属业务员",children:k.salesRepresentative}),(0,s.jsx)(C.Z.Item,{label:"状态",children:(0,s.jsx)(u.Z,{status:"active"===k.status?"success":"default",text:"active"===k.status?"正常":"停用"})}),(0,s.jsx)(C.Z.Item,{label:"创建时间",span:2,children:k.createdAt}),(0,s.jsx)(C.Z.Item,{label:"更新时间",span:2,children:k.updatedAt}),(0,s.jsx)(C.Z.Item,{label:"备注",span:2,children:k.remark||"无"})]})},{key:"credit",label:"信用信息",children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(f.Z,{gutter:16,children:[(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(Z.Z,{children:(0,s.jsx)(p.Z,{title:"信用额度",value:k.creditLimit||0,precision:0,prefix:"\xa5",valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(j.Z,{span:12,children:(0,s.jsx)(Z.Z,{children:(0,s.jsx)(p.Z,{title:"已用额度",value:k.usedCredit||0,precision:0,prefix:"\xa5",valueStyle:{color:"#cf1322"}})})})]}),(0,s.jsxs)("div",{style:{marginTop:16},children:[(0,s.jsx)("h4",{children:"信用使用情况"}),(0,s.jsx)(d.Z,{percent:ec(k.usedCredit||0,k.creditLimit||0),status:ec(k.usedCredit||0,k.creditLimit||0)>80?"exception":"normal",strokeColor:{"0%":"#108ee9","100%":"#87d068"}}),(0,s.jsxs)("p",{style:{marginTop:8,color:"#666"},children:["可用额度: \xa5",((k.creditLimit||0)-(k.usedCredit||0)).toLocaleString()]})]})]})},{key:"products",label:"产品偏好",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{children:"常购产品型号"}),(0,s.jsxs)("div",{style:{marginTop:16},children:[(k.preferredProducts||[]).map(e=>(0,s.jsx)(o.Z,{color:"blue",style:{marginBottom:8},children:e},e)),(!k.preferredProducts||0===k.preferredProducts.length)&&(0,s.jsx)("span",{style:{color:"#999"},children:"暂无产品偏好数据"})]})]})}]})})]})}function D(){return(0,s.jsx)(n.Z,{children:(0,s.jsx)(B,{})})}},50538:function(e,t,l){"use strict";l.d(t,{Ro:function(){return s}});let s=async(e,t)=>{try{let l=await e();if("success"===l.status)return l.data||null;return l.code,l.message,l.message,t&&a(String(l.code||"UNKNOWN_ERROR")),null}catch(e){return e instanceof Error?e.message:String(e),null}},r={ERR_NOT_FOUND:"资源不存在",ERR_UNAUTHORIZED:"未授权访问",ERR_FORBIDDEN:"禁止访问",ERR_INTERNAL_ERROR:"内部服务器错误",ERR_PRODUCT_NOT_FOUND:"产品不存在",ERR_PRODUCT_CODE_EXISTS:"产品编码已存在",ERR_CUSTOMER_NOT_FOUND:"客户不存在",ERR_INVENTORY_INSUFFICIENT:"库存不足",ERR_ORDER_NOT_FOUND:"订单不存在",ERR_ORDER_NUMBER_EXISTS:"订单号已存在",DUPLICATE_ORDER_NUMBER:"订单号重复"},a=e=>r[e]||"操作失败"}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,9427,4863,7416,8236,9617,2897,2779,2971,4938,1744],function(){return e(e.s=34747)}),_N_E=e.O()}]);