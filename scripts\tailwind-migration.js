#!/usr/bin/env node

/**
 * Tailwind CSS 迁移脚本
 * 自动将项目中的 Tailwind 类名替换为内联样式
 */

const fs = require('fs')
const path = require('path')
const glob = require('glob')

// Tailwind 类名到内联样式的映射
const tailwindToStyle = {
  // 间距
  'p-1': 'padding: 4px',
  'p-2': 'padding: 8px',
  'p-3': 'padding: 12px',
  'p-4': 'padding: 16px',
  'p-6': 'padding: 24px',
  'p-8': 'padding: 32px',
  'px-2': 'paddingLeft: 8px; paddingRight: 8px',
  'px-3': 'paddingLeft: 12px; paddingRight: 12px',
  'px-4': 'paddingLeft: 16px; paddingRight: 16px',
  'px-6': 'paddingLeft: 24px; paddingRight: 24px',
  'py-2': 'paddingTop: 8px; paddingBottom: 8px',
  'py-3': 'paddingTop: 12px; paddingBottom: 12px',
  'py-4': 'paddingTop: 16px; paddingBottom: 16px',
  
  'm-1': 'margin: 4px',
  'm-2': 'margin: 8px',
  'm-3': 'margin: 12px',
  'm-4': 'margin: 16px',
  'mx-2': 'marginLeft: 8px; marginRight: 8px',
  'mx-3': 'marginLeft: 12px; marginRight: 12px',
  'mx-4': 'marginLeft: 16px; marginRight: 16px',
  'my-2': 'marginTop: 8px; marginBottom: 8px',
  'my-3': 'marginTop: 12px; marginBottom: 12px',
  'my-4': 'marginTop: 16px; marginBottom: 16px',
  'mt-1': 'marginTop: 4px',
  'mt-2': 'marginTop: 8px',
  'mt-3': 'marginTop: 12px',
  'mt-4': 'marginTop: 16px',
  'mb-1': 'marginBottom: 4px',
  'mb-2': 'marginBottom: 8px',
  'mb-3': 'marginBottom: 12px',
  'mb-4': 'marginBottom: 16px',
  'ml-1': 'marginLeft: 4px',
  'ml-2': 'marginLeft: 8px',
  'ml-3': 'marginLeft: 12px',
  'mr-1': 'marginRight: 4px',
  'mr-2': 'marginRight: 8px',
  'mr-3': 'marginRight: 12px',
  
  // 布局
  'flex': 'display: flex',
  'flex-col': 'flexDirection: column',
  'flex-row': 'flexDirection: row',
  'items-center': 'alignItems: center',
  'items-start': 'alignItems: flex-start',
  'items-end': 'alignItems: flex-end',
  'justify-center': 'justifyContent: center',
  'justify-between': 'justifyContent: space-between',
  'justify-start': 'justifyContent: flex-start',
  'justify-end': 'justifyContent: flex-end',
  
  // 尺寸
  'w-full': 'width: 100%',
  'w-1/2': 'width: 50%',
  'w-1/3': 'width: 33.333333%',
  'w-2/3': 'width: 66.666667%',
  'w-1/4': 'width: 25%',
  'w-3/4': 'width: 75%',
  'h-full': 'height: 100%',
  'h-screen': 'height: 100vh',
  'h-64': 'height: 256px',
  'h-96': 'height: 384px',
  'min-h-screen': 'minHeight: 100vh',
  'max-w-full': 'maxWidth: 100%',
  
  // 文本
  'text-xs': 'fontSize: 12px',
  'text-sm': 'fontSize: 14px',
  'text-base': 'fontSize: 16px',
  'text-lg': 'fontSize: 18px',
  'text-xl': 'fontSize: 20px',
  'text-2xl': 'fontSize: 24px',
  'text-3xl': 'fontSize: 30px',
  'text-4xl': 'fontSize: 36px',
  'font-medium': 'fontWeight: 500',
  'font-semibold': 'fontWeight: 600',
  'font-bold': 'fontWeight: bold',
  'text-center': 'textAlign: center',
  'text-left': 'textAlign: left',
  'text-right': 'textAlign: right',
  
  // 颜色
  'text-gray-400': 'color: #9ca3af',
  'text-gray-500': 'color: #6b7280',
  'text-gray-600': 'color: #4b5563',
  'text-gray-700': 'color: #374151',
  'text-gray-800': 'color: #1f2937',
  'text-gray-900': 'color: #111827',
  'text-white': 'color: #ffffff',
  'text-blue-500': 'color: #3b82f6',
  'text-green-500': 'color: #10b981',
  'text-red-500': 'color: #ef4444',
  'text-purple-500': 'color: #8b5cf6',
  'text-primary-600': 'color: #0284c7',
  
  'bg-white': 'background: #ffffff',
  'bg-gray-50': 'background: #f9fafb',
  'bg-gray-100': 'background: #f3f4f6',
  'bg-gray-200': 'background: #e5e7eb',
  'bg-blue-50': 'background: #eff6ff',
  'bg-green-50': 'background: #f0fdf4',
  
  // 边框
  'border': 'border: 1px solid #e5e7eb',
  'border-b': 'borderBottom: 1px solid #e5e7eb',
  'border-t': 'borderTop: 1px solid #e5e7eb',
  'border-l': 'borderLeft: 1px solid #e5e7eb',
  'border-r': 'borderRight: 1px solid #e5e7eb',
  'border-gray-200': 'borderColor: #e5e7eb',
  'border-gray-300': 'borderColor: #d1d5db',
  'border-none': 'border: none',
  
  // 圆角
  'rounded': 'borderRadius: 4px',
  'rounded-md': 'borderRadius: 8px',
  'rounded-lg': 'borderRadius: 12px',
  'rounded-xl': 'borderRadius: 16px',
  'rounded-full': 'borderRadius: 9999px',
  
  // 阴影
  'shadow': 'boxShadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1)',
  'shadow-sm': 'boxShadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  'shadow-md': 'boxShadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1)',
  'shadow-lg': 'boxShadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1)',
  
  // 其他
  'cursor-pointer': 'cursor: pointer',
  'overflow-auto': 'overflow: auto',
  'overflow-hidden': 'overflowHidden',
  'transition-colors': 'transition: background-color 0.2s ease',
  'transition-all': 'transition: all 0.2s ease',
  'duration-200': 'transitionDuration: 200ms',
  'duration-300': 'transitionDuration: 300ms',
  'space-y-2': 'gap: 8px',
  'space-y-3': 'gap: 12px',
  'space-y-4': 'gap: 16px',
  'space-y-6': 'gap: 24px',
  'space-x-1': 'gap: 4px',
  'space-x-2': 'gap: 8px',
  'space-x-3': 'gap: 12px',
  'space-x-4': 'gap: 16px',
  'hidden': 'display: none',
  'block': 'display: block',
  'inline': 'display: inline',
  'inline-block': 'display: inline-block'
}

// 需要处理的文件模式
const filePatterns = [
  'src/**/*.tsx',
  'src/**/*.ts',
  '!src/**/*.test.ts',
  '!src/**/*.test.tsx',
  '!src/**/*.d.ts'
]

// 获取所有需要处理的文件
function getFilesToProcess() {
  const files = []
  filePatterns.forEach(pattern => {
    const matchedFiles = glob.sync(pattern)
    files.push(...matchedFiles)
  })
  return [...new Set(files)] // 去重
}

// 处理单个文件
function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8')
    let newContent = content
    let hasChanges = false
    
    // 检查是否已经导入了 styleHelpers
    const hasStyleHelpers = content.includes('styleHelpers')
    
    // 如果文件包含 className 且没有导入 styleHelpers，则添加导入
    if (content.includes('className') && !hasStyleHelpers) {
      const importMatch = content.match(/import.*from ['"]@\/.*['"]/)
      if (importMatch) {
        const importIndex = content.indexOf(importMatch[0]) + importMatch[0].length
        newContent = content.slice(0, importIndex) + 
          '\nimport { styleHelpers } from \'@/utils/styles/antdHelpers\'' +
          content.slice(importIndex)
        hasChanges = true
      }
    }
    
    // 替换简单的 Tailwind 类名
    Object.entries(tailwindToStyle).forEach(([className, style]) => {
      const regex = new RegExp(`className="([^"]*\\s)?${className}(\\s[^"]*)?"`,'g')
      if (regex.test(newContent)) {
        // 这里需要更复杂的逻辑来处理 className 到 style 的转换
        // 暂时只标记有变化
        hasChanges = true
      }
    })
    
    if (hasChanges) {
      console.log(`处理文件: ${filePath}`)
      // 这里可以写入文件，但为了安全起见，先只输出
      // fs.writeFileSync(filePath, newContent, 'utf8')
      return true
    }
    
    return false
  } catch (error) {
    console.error(`处理文件 ${filePath} 时出错:`, error.message)
    return false
  }
}

// 主函数
function main() {
  console.log('开始 Tailwind CSS 迁移...')
  
  const files = getFilesToProcess()
  console.log(`找到 ${files.length} 个文件需要处理`)
  
  let processedCount = 0
  
  files.forEach(file => {
    if (processFile(file)) {
      processedCount++
    }
  })
  
  console.log(`迁移完成！处理了 ${processedCount} 个文件`)
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = {
  tailwindToStyle,
  processFile,
  getFilesToProcess
}
