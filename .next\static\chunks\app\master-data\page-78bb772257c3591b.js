(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2649],{8349:function(e,s,c){"use strict";c.d(s,{Z:function(){return i}});var r=c(13428),t=c(2265),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"}}]},name:"appstore",theme:"outlined"},l=c(46614),i=t.forwardRef(function(e,s){return t.createElement(l.Z,(0,r.Z)({},e,{ref:s,icon:a}))})},88652:function(e,s,c){"use strict";c.d(s,{Z:function(){return i}});var r=c(13428),t=c(2265),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M120 160H72c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8zm833 0h-48c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8zM200 736h112c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8H200c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm321 0h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm126 0h178c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8H647c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm-255 0h48c4.4 0 8-3.6 8-8V168c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v560c0 4.4 3.6 8 8 8zm-79 64H201c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h112c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm257 0h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm256 0H648c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h178c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zm-385 0h-48c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"}}]},name:"barcode",theme:"outlined"},l=c(46614),i=t.forwardRef(function(e,s){return t.createElement(l.Z,(0,r.Z)({},e,{ref:s,icon:a}))})},63420:function(e,s,c){"use strict";c.d(s,{Z:function(){return i}});var r=c(13428),t=c(2265),a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-600 72h560v208H232V136zm560 480H232V408h560v208zm0 272H232V680h560v208zM304 240a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0zm0 272a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"database",theme:"outlined"},l=c(46614),i=t.forwardRef(function(e,s){return t.createElement(l.Z,(0,r.Z)({},e,{ref:s,icon:a}))})},98532:function(e,s,c){Promise.resolve().then(c.bind(c,2086))},2086:function(e,s,c){"use strict";c.r(s);var r=c(57437);c(2265);var t=c(38302),a=c(28683),l=c(89511),i=c(86155),n=c(94734),d=c(63420),x=c(8349),o=c(88652),h=c(74898),m=c(24033);s.default=()=>{let e=(0,m.useRouter)();return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"page-header",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.Z,{className:"text-2xl text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"page-title",children:"基础数据管理"}),(0,r.jsx)("p",{className:"page-description",children:"管理物料主数据和产品型号库等基础信息"})]})]})}),(0,r.jsxs)(t.Z,{gutter:[16,16],children:[(0,r.jsx)(a.Z,{xs:24,sm:12,lg:6,children:(0,r.jsx)(l.Z,{children:(0,r.jsx)(i.Z,{title:"物料总数",value:156,suffix:"种",valueStyle:{color:"#1890ff"},prefix:(0,r.jsx)(x.Z,{})})})}),(0,r.jsx)(a.Z,{xs:24,sm:12,lg:6,children:(0,r.jsx)(l.Z,{children:(0,r.jsx)(i.Z,{title:"产品型号",value:89,suffix:"个",valueStyle:{color:"#52c41a"},prefix:(0,r.jsx)(o.Z,{})})})}),(0,r.jsx)(a.Z,{xs:24,sm:12,lg:6,children:(0,r.jsx)(l.Z,{children:(0,r.jsx)(i.Z,{title:"活跃物料",value:142,suffix:"种",valueStyle:{color:"#722ed1"}})})}),(0,r.jsx)(a.Z,{xs:24,sm:12,lg:6,children:(0,r.jsx)(l.Z,{children:(0,r.jsx)(i.Z,{title:"活跃型号",value:76,suffix:"个",valueStyle:{color:"#fa8c16"}})})})]}),(0,r.jsxs)(t.Z,{gutter:[16,16],children:[(0,r.jsx)(a.Z,{xs:24,lg:12,children:(0,r.jsx)(l.Z,{title:"物料主数据管理",className:"h-64",extra:(0,r.jsx)(n.ZP,{type:"primary",icon:(0,r.jsx)(h.Z,{}),onClick:()=>e.push("/master-data/materials"),children:"管理物料"}),children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>e.push("/master-data/materials"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.Z,{className:"text-xl text-blue-500 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"物料信息管理"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"管理物料编码、名称、价格等基础信息"})]})]})}),(0,r.jsx)("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.Z,{className:"text-xl text-green-500 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"批量导入导出"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"支持Excel批量导入导出物料数据"})]})]})})]})})}),(0,r.jsx)(a.Z,{xs:24,lg:12,children:(0,r.jsx)(l.Z,{title:"产品型号库管理",className:"h-64",extra:(0,r.jsx)(n.ZP,{type:"primary",icon:(0,r.jsx)(h.Z,{}),onClick:()=>e.push("/master-data/product-models"),children:"管理型号"}),children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>e.push("/master-data/product-models"),children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(o.Z,{className:"text-xl text-purple-500 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"型号信息管理"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"管理产品型号、模具关联和计件单价"})]})]})}),(0,r.jsx)("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.Z,{className:"text-xl text-orange-500 mr-3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:"模具关联管理"}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:"管理成型模具和热压模具关联信息"})]})]})})]})})})]})]})}},24033:function(e,s,c){e.exports=c(15313)}},function(e){e.O(0,[9444,8454,9511,364,2971,4938,1744],function(){return e(e.s=98532)}),_N_E=e.O()}]);