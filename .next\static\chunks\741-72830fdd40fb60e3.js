"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[741],{83539:function(t,e,n){n.d(e,{Z:function(){return l}});var o=n(13428),c=n(2265),r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M304 280h56c4.4 0 8-3.6 8-8 0-28.3 5.9-53.2 17.1-73.5 10.6-19.4 26-34.8 45.4-45.4C450.9 142 475.7 136 504 136h16c28.3 0 53.2 5.9 73.5 17.1 19.4 10.6 34.8 26 45.4 45.4C650 218.9 656 243.7 656 272c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-40-8.8-76.7-25.9-108.1a184.31 184.31 0 00-74-74C596.7 72.8 560 64 520 64h-16c-40 0-76.7 8.8-108.1 25.9a184.31 184.31 0 00-74 74C304.8 195.3 296 232 296 272c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M940 512H792V412c76.8 0 139-62.2 139-139 0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8a63 63 0 01-63 63H232a63 63 0 01-63-63c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 76.8 62.2 139 139 139v100H84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h148v96c0 6.5.2 13 .7 19.3C164.1 728.6 116 796.7 116 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-44.2 23.9-82.9 59.6-103.7a273 273 0 0022.7 49c24.3 41.5 59 76.2 100.5 100.5S460.5 960 512 960s99.8-13.9 141.3-38.2a281.38 281.38 0 00123.2-149.5A120 120 0 01836 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-79.3-48.1-147.4-116.7-176.7.4-6.4.7-12.8.7-19.3v-96h148c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM716 680c0 36.8-9.7 72-27.8 102.9-17.7 30.3-43 55.6-73.3 73.3C584 874.3 548.8 884 512 884s-72-9.7-102.9-27.8c-30.3-17.7-55.6-43-73.3-73.3A202.75 202.75 0 01308 680V412h408v268z"}}]},name:"bug",theme:"outlined"},a=n(46614),l=c.forwardRef(function(t,e){return c.createElement(a.Z,(0,o.Z)({},t,{ref:e,icon:r}))})},80782:function(t,e,n){n.d(e,{Z:function(){return l}});var o=n(13428),c=n(2265),r={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"},a=n(46614),l=c.forwardRef(function(t,e){return c.createElement(a.Z,(0,o.Z)({},t,{ref:e,icon:r}))})},43043:function(t,e,n){n.d(e,{Z:function(){return l}});var o=n(13428),c=n(2265),r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"},a=n(46614),l=c.forwardRef(function(t,e){return c.createElement(a.Z,(0,o.Z)({},t,{ref:e,icon:r}))})},57084:function(t,e,n){n.d(e,{Z:function(){return l}});var o=n(13428),c=n(2265),r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"},a=n(46614),l=c.forwardRef(function(t,e){return c.createElement(a.Z,(0,o.Z)({},t,{ref:e,icon:r}))})},28683:function(t,e,n){var o=n(90791);e.Z=o.Z},28116:function(t,e,n){n.d(e,{Z:function(){return m}});var o=n(2265),c=n(42744),r=n.n(c),a=n(57499),l=n(10693),i=n(58489),s=n(11303),d=n(78387),f=n(12711);let u=t=>{let{componentCls:e}=t;return{[e]:{"&-horizontal":{["&".concat(e)]:{"&-sm":{marginBlock:t.marginXS},"&-md":{marginBlock:t.margin}}}}}},p=t=>{let{componentCls:e,sizePaddingEdgeHorizontal:n,colorSplit:o,lineWidth:c,textPaddingInline:r,orientationMargin:a,verticalMarginInline:l}=t;return{[e]:Object.assign(Object.assign({},(0,s.Wf)(t)),{borderBlockStart:"".concat((0,i.bf)(c)," solid ").concat(o),"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:l,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:"".concat((0,i.bf)(c)," solid ").concat(o)},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:"".concat((0,i.bf)(t.marginLG)," 0")},["&-horizontal".concat(e,"-with-text")]:{display:"flex",alignItems:"center",margin:"".concat((0,i.bf)(t.dividerHorizontalWithTextGutterMargin)," 0"),color:t.colorTextHeading,fontWeight:500,fontSize:t.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:"0 ".concat(o),"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:"".concat((0,i.bf)(c)," solid transparent"),borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},["&-horizontal".concat(e,"-with-text-start")]:{"&::before":{width:"calc(".concat(a," * 100%)")},"&::after":{width:"calc(100% - ".concat(a," * 100%)")}},["&-horizontal".concat(e,"-with-text-end")]:{"&::before":{width:"calc(100% - ".concat(a," * 100%)")},"&::after":{width:"calc(".concat(a," * 100%)")}},["".concat(e,"-inner-text")]:{display:"inline-block",paddingBlock:0,paddingInline:r},"&-dashed":{background:"none",borderColor:o,borderStyle:"dashed",borderWidth:"".concat((0,i.bf)(c)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dashed")]:{"&::before, &::after":{borderStyle:"dashed none none"}},["&-vertical".concat(e,"-dashed")]:{borderInlineStartWidth:c,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:o,borderStyle:"dotted",borderWidth:"".concat((0,i.bf)(c)," 0 0")},["&-horizontal".concat(e,"-with-text").concat(e,"-dotted")]:{"&::before, &::after":{borderStyle:"dotted none none"}},["&-vertical".concat(e,"-dotted")]:{borderInlineStartWidth:c,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},["&-plain".concat(e,"-with-text")]:{color:t.colorText,fontWeight:"normal",fontSize:t.fontSize},["&-horizontal".concat(e,"-with-text-start").concat(e,"-no-default-orientation-margin-start")]:{"&::before":{width:0},"&::after":{width:"100%"},["".concat(e,"-inner-text")]:{paddingInlineStart:n}},["&-horizontal".concat(e,"-with-text-end").concat(e,"-no-default-orientation-margin-end")]:{"&::before":{width:"100%"},"&::after":{width:0},["".concat(e,"-inner-text")]:{paddingInlineEnd:n}}})}};var g=(0,d.I$)("Divider",t=>{let e=(0,f.IX)(t,{dividerHorizontalWithTextGutterMargin:t.margin,sizePaddingEdgeHorizontal:0});return[p(e),u(e)]},t=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:t.marginXS}),{unitless:{orientationMargin:!0}}),b=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,o=Object.getOwnPropertySymbols(t);c<o.length;c++)0>e.indexOf(o[c])&&Object.prototype.propertyIsEnumerable.call(t,o[c])&&(n[o[c]]=t[o[c]]);return n};let h={small:"sm",middle:"md"};var m=t=>{let{getPrefixCls:e,direction:n,className:c,style:i}=(0,a.dj)("divider"),{prefixCls:s,type:d="horizontal",orientation:f="center",orientationMargin:u,className:p,rootClassName:m,children:y,dashed:v,variant:x="solid",plain:C,style:O,size:w}=t,S=b(t,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),k=e("divider",s),[j,I,E]=g(k),z=h[(0,l.Z)(w)],M=!!y,B=o.useMemo(()=>"left"===f?"rtl"===n?"end":"start":"right"===f?"rtl"===n?"start":"end":f,[n,f]),Z="start"===B&&null!=u,P="end"===B&&null!=u,N=r()(k,c,I,E,"".concat(k,"-").concat(d),{["".concat(k,"-with-text")]:M,["".concat(k,"-with-text-").concat(B)]:M,["".concat(k,"-dashed")]:!!v,["".concat(k,"-").concat(x)]:"solid"!==x,["".concat(k,"-plain")]:!!C,["".concat(k,"-rtl")]:"rtl"===n,["".concat(k,"-no-default-orientation-margin-start")]:Z,["".concat(k,"-no-default-orientation-margin-end")]:P,["".concat(k,"-").concat(z)]:!!z},p,m),W=o.useMemo(()=>"number"==typeof u?u:/^\d+$/.test(u)?Number(u):u,[u]);return j(o.createElement("div",Object.assign({className:N,style:Object.assign(Object.assign({},i),O)},S,{role:"separator"}),y&&"vertical"!==d&&o.createElement("span",{className:"".concat(k,"-inner-text"),style:{marginInlineStart:Z?W:void 0,marginInlineEnd:P?W:void 0}},y)))}},24305:function(t,e,n){let o=(0,n(2265).createContext)({});e.Z=o},90791:function(t,e,n){var o=n(2265),c=n(42744),r=n.n(c),a=n(57499),l=n(24305),i=n(37148),s=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,o=Object.getOwnPropertySymbols(t);c<o.length;c++)0>e.indexOf(o[c])&&Object.prototype.propertyIsEnumerable.call(t,o[c])&&(n[o[c]]=t[o[c]]);return n};function d(t){return"number"==typeof t?"".concat(t," ").concat(t," auto"):/^\d+(\.\d+)?(px|em|rem|%)$/.test(t)?"0 0 ".concat(t):t}let f=["xs","sm","md","lg","xl","xxl"],u=o.forwardRef((t,e)=>{let{getPrefixCls:n,direction:c}=o.useContext(a.E_),{gutter:u,wrap:p}=o.useContext(l.Z),{prefixCls:g,span:b,order:h,offset:m,push:y,pull:v,className:x,children:C,flex:O,style:w}=t,S=s(t,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),k=n("col",g),[j,I,E]=(0,i.cG)(k),z={},M={};f.forEach(e=>{let n={},o=t[e];"number"==typeof o?n.span=o:"object"==typeof o&&(n=o||{}),delete S[e],M=Object.assign(Object.assign({},M),{["".concat(k,"-").concat(e,"-").concat(n.span)]:void 0!==n.span,["".concat(k,"-").concat(e,"-order-").concat(n.order)]:n.order||0===n.order,["".concat(k,"-").concat(e,"-offset-").concat(n.offset)]:n.offset||0===n.offset,["".concat(k,"-").concat(e,"-push-").concat(n.push)]:n.push||0===n.push,["".concat(k,"-").concat(e,"-pull-").concat(n.pull)]:n.pull||0===n.pull,["".concat(k,"-rtl")]:"rtl"===c}),n.flex&&(M["".concat(k,"-").concat(e,"-flex")]=!0,z["--".concat(k,"-").concat(e,"-flex")]=d(n.flex))});let B=r()(k,{["".concat(k,"-").concat(b)]:void 0!==b,["".concat(k,"-order-").concat(h)]:h,["".concat(k,"-offset-").concat(m)]:m,["".concat(k,"-push-").concat(y)]:y,["".concat(k,"-pull-").concat(v)]:v},x,M,I,E),Z={};if(u&&u[0]>0){let t=u[0]/2;Z.paddingLeft=t,Z.paddingRight=t}return O&&(Z.flex=d(O),!1!==p||Z.minWidth||(Z.minWidth=0)),j(o.createElement("div",Object.assign({},S,{style:Object.assign(Object.assign(Object.assign({},Z),w),z),className:B,ref:e}),C))});e.Z=u},59094:function(t,e,n){n.d(e,{Z:function(){return p}});var o=n(2265),c=n(42744),r=n.n(c),a=n(43313),l=n(57499),i=n(65471),s=n(24305),d=n(37148),f=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,o=Object.getOwnPropertySymbols(t);c<o.length;c++)0>e.indexOf(o[c])&&Object.prototype.propertyIsEnumerable.call(t,o[c])&&(n[o[c]]=t[o[c]]);return n};function u(t,e){let[n,c]=o.useState("string"==typeof t?t:""),r=()=>{if("string"==typeof t&&c(t),"object"==typeof t)for(let n=0;n<a.c4.length;n++){let o=a.c4[n];if(!e||!e[o])continue;let r=t[o];if(void 0!==r){c(r);return}}};return o.useEffect(()=>{r()},[JSON.stringify(t),e]),n}var p=o.forwardRef((t,e)=>{let{prefixCls:n,justify:c,align:p,className:g,style:b,children:h,gutter:m=0,wrap:y}=t,v=f(t,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:x,direction:C}=o.useContext(l.E_),O=(0,i.Z)(!0,null),w=u(p,O),S=u(c,O),k=x("row",n),[j,I,E]=(0,d.VM)(k),z=function(t,e){let n=[void 0,void 0],o=Array.isArray(t)?t:[t,void 0],c=e||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return o.forEach((t,e)=>{if("object"==typeof t&&null!==t)for(let o=0;o<a.c4.length;o++){let r=a.c4[o];if(c[r]&&void 0!==t[r]){n[e]=t[r];break}}else n[e]=t}),n}(m,O),M=r()(k,{["".concat(k,"-no-wrap")]:!1===y,["".concat(k,"-").concat(S)]:S,["".concat(k,"-").concat(w)]:w,["".concat(k,"-rtl")]:"rtl"===C},g,I,E),B={},Z=null!=z[0]&&z[0]>0?-(z[0]/2):void 0;Z&&(B.marginLeft=Z,B.marginRight=Z);let[P,N]=z;B.rowGap=N;let W=o.useMemo(()=>({gutter:[P,N],wrap:y}),[P,N,y]);return j(o.createElement(s.Z.Provider,{value:W},o.createElement("div",Object.assign({},v,{className:M,style:Object.assign(Object.assign({},B),b),ref:e}),h)))})},37148:function(t,e,n){n.d(e,{VM:function(){return d},cG:function(){return u},hd:function(){return f}});var o=n(58489),c=n(78387),r=n(12711);let a=t=>{let{componentCls:e}=t;return{[e]:{position:"relative",maxWidth:"100%",minHeight:1}}},l=(t,e)=>{let{prefixCls:n,componentCls:o,gridColumns:c}=t,r={};for(let t=c;t>=0;t--)0===t?(r["".concat(o).concat(e,"-").concat(t)]={display:"none"},r["".concat(o,"-push-").concat(t)]={insetInlineStart:"auto"},r["".concat(o,"-pull-").concat(t)]={insetInlineEnd:"auto"},r["".concat(o).concat(e,"-push-").concat(t)]={insetInlineStart:"auto"},r["".concat(o).concat(e,"-pull-").concat(t)]={insetInlineEnd:"auto"},r["".concat(o).concat(e,"-offset-").concat(t)]={marginInlineStart:0},r["".concat(o).concat(e,"-order-").concat(t)]={order:0}):(r["".concat(o).concat(e,"-").concat(t)]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:"0 0 ".concat(t/c*100,"%"),maxWidth:"".concat(t/c*100,"%")}],r["".concat(o).concat(e,"-push-").concat(t)]={insetInlineStart:"".concat(t/c*100,"%")},r["".concat(o).concat(e,"-pull-").concat(t)]={insetInlineEnd:"".concat(t/c*100,"%")},r["".concat(o).concat(e,"-offset-").concat(t)]={marginInlineStart:"".concat(t/c*100,"%")},r["".concat(o).concat(e,"-order-").concat(t)]={order:t});return r["".concat(o).concat(e,"-flex")]={flex:"var(--".concat(n).concat(e,"-flex)")},r},i=(t,e)=>l(t,e),s=(t,e,n)=>({["@media (min-width: ".concat((0,o.bf)(e),")")]:Object.assign({},i(t,n))}),d=(0,c.I$)("Grid",t=>{let{componentCls:e}=t;return{[e]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},()=>({})),f=t=>({xs:t.screenXSMin,sm:t.screenSMMin,md:t.screenMDMin,lg:t.screenLGMin,xl:t.screenXLMin,xxl:t.screenXXLMin}),u=(0,c.I$)("Grid",t=>{let e=(0,r.IX)(t,{gridColumns:24}),n=f(e);return delete n.xs,[a(e),i(e,""),i(e,"-xs"),Object.keys(n).map(t=>s(e,n[t],"-".concat(t))).reduce((t,e)=>Object.assign(Object.assign({},t),e),{})]},()=>({}))},38302:function(t,e,n){var o=n(59094);e.Z=o.Z},6053:function(t,e,n){n.d(e,{Z:function(){return M}});var o=n(2265),c=n(42744),r=n.n(c),a=n(54925),l=n(29810),i=n(18606),s=n(65823),d=n(79934),f=n(57499),u=n(58489),p=n(47861),g=n(11303),b=n(12711),h=n(78387);let m=t=>{let{paddingXXS:e,lineWidth:n,tagPaddingHorizontal:o,componentCls:c,calc:r}=t,a=r(o).sub(n).equal(),l=r(e).sub(n).equal();return{[c]:Object.assign(Object.assign({},(0,g.Wf)(t)),{display:"inline-block",height:"auto",marginInlineEnd:t.marginXS,paddingInline:a,fontSize:t.tagFontSize,lineHeight:t.tagLineHeight,whiteSpace:"nowrap",background:t.defaultBg,border:"".concat((0,u.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder),borderRadius:t.borderRadiusSM,opacity:1,transition:"all ".concat(t.motionDurationMid),textAlign:"start",position:"relative",["&".concat(c,"-rtl")]:{direction:"rtl"},"&, a, a:hover":{color:t.defaultColor},["".concat(c,"-close-icon")]:{marginInlineStart:l,fontSize:t.tagIconSize,color:t.colorIcon,cursor:"pointer",transition:"all ".concat(t.motionDurationMid),"&:hover":{color:t.colorTextHeading}},["&".concat(c,"-has-color")]:{borderColor:"transparent",["&, a, a:hover, ".concat(t.iconCls,"-close, ").concat(t.iconCls,"-close:hover")]:{color:t.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",["&:not(".concat(c,"-checkable-checked):hover")]:{color:t.colorPrimary,backgroundColor:t.colorFillSecondary},"&:active, &-checked":{color:t.colorTextLightSolid},"&-checked":{backgroundColor:t.colorPrimary,"&:hover":{backgroundColor:t.colorPrimaryHover}},"&:active":{backgroundColor:t.colorPrimaryActive}},"&-hidden":{display:"none"},["> ".concat(t.iconCls," + span, > span + ").concat(t.iconCls)]:{marginInlineStart:a}}),["".concat(c,"-borderless")]:{borderColor:"transparent",background:t.tagBorderlessBg}}},y=t=>{let{lineWidth:e,fontSizeIcon:n,calc:o}=t,c=t.fontSizeSM;return(0,b.IX)(t,{tagFontSize:c,tagLineHeight:(0,u.bf)(o(t.lineHeightSM).mul(c).equal()),tagIconSize:o(n).sub(o(e).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:t.defaultBg})},v=t=>({defaultBg:new p.t(t.colorFillQuaternary).onBackground(t.colorBgContainer).toHexString(),defaultColor:t.colorText});var x=(0,h.I$)("Tag",t=>m(y(t)),v),C=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,o=Object.getOwnPropertySymbols(t);c<o.length;c++)0>e.indexOf(o[c])&&Object.prototype.propertyIsEnumerable.call(t,o[c])&&(n[o[c]]=t[o[c]]);return n};let O=o.forwardRef((t,e)=>{let{prefixCls:n,style:c,className:a,checked:l,onChange:i,onClick:s}=t,d=C(t,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:u,tag:p}=o.useContext(f.E_),g=u("tag",n),[b,h,m]=x(g),y=r()(g,"".concat(g,"-checkable"),{["".concat(g,"-checkable-checked")]:l},null==p?void 0:p.className,a,h,m);return b(o.createElement("span",Object.assign({},d,{ref:e,style:Object.assign(Object.assign({},c),null==p?void 0:p.style),className:y,onClick:t=>{null==i||i(!l),null==s||s(t)}})))});var w=n(82303);let S=t=>(0,w.Z)(t,(e,n)=>{let{textColor:o,lightBorderColor:c,lightColor:r,darkColor:a}=n;return{["".concat(t.componentCls).concat(t.componentCls,"-").concat(e)]:{color:o,background:r,borderColor:c,"&-inverse":{color:t.colorTextLightSolid,background:a,borderColor:a},["&".concat(t.componentCls,"-borderless")]:{borderColor:"transparent"}}}});var k=(0,h.bk)(["Tag","preset"],t=>S(y(t)),v);let j=(t,e,n)=>{let o="string"!=typeof n?n:n.charAt(0).toUpperCase()+n.slice(1);return{["".concat(t.componentCls).concat(t.componentCls,"-").concat(e)]:{color:t["color".concat(n)],background:t["color".concat(o,"Bg")],borderColor:t["color".concat(o,"Border")],["&".concat(t.componentCls,"-borderless")]:{borderColor:"transparent"}}}};var I=(0,h.bk)(["Tag","status"],t=>{let e=y(t);return[j(e,"success","Success"),j(e,"processing","Info"),j(e,"error","Error"),j(e,"warning","Warning")]},v),E=function(t,e){var n={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&0>e.indexOf(o)&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var c=0,o=Object.getOwnPropertySymbols(t);c<o.length;c++)0>e.indexOf(o[c])&&Object.prototype.propertyIsEnumerable.call(t,o[c])&&(n[o[c]]=t[o[c]]);return n};let z=o.forwardRef((t,e)=>{let{prefixCls:n,className:c,rootClassName:u,style:p,children:g,icon:b,color:h,onClose:m,bordered:y=!0,visible:v}=t,C=E(t,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:O,direction:w,tag:S}=o.useContext(f.E_),[j,z]=o.useState(!0),M=(0,a.Z)(C,["closeIcon","closable"]);o.useEffect(()=>{void 0!==v&&z(v)},[v]);let B=(0,l.o2)(h),Z=(0,l.yT)(h),P=B||Z,N=Object.assign(Object.assign({backgroundColor:h&&!P?h:void 0},null==S?void 0:S.style),p),W=O("tag",n),[L,T,H]=x(W),R=r()(W,null==S?void 0:S.className,{["".concat(W,"-").concat(h)]:P,["".concat(W,"-has-color")]:h&&!P,["".concat(W,"-hidden")]:!j,["".concat(W,"-rtl")]:"rtl"===w,["".concat(W,"-borderless")]:!y},c,u,T,H),A=t=>{t.stopPropagation(),null==m||m(t),t.defaultPrevented||z(!1)},[,G]=(0,i.Z)((0,i.w)(t),(0,i.w)(S),{closable:!1,closeIconRender:t=>{let e=o.createElement("span",{className:"".concat(W,"-close-icon"),onClick:A},t);return(0,s.wm)(t,e,t=>({onClick:e=>{var n;null===(n=null==t?void 0:t.onClick)||void 0===n||n.call(t,e),A(e)},className:r()(null==t?void 0:t.className,"".concat(W,"-close-icon"))}))}}),X="function"==typeof C.onClick||g&&"a"===g.type,_=b||null,V=_?o.createElement(o.Fragment,null,_,g&&o.createElement("span",null,g)):g,$=o.createElement("span",Object.assign({},M,{ref:e,className:R,style:N}),V,G,B&&o.createElement(k,{key:"preset",prefixCls:W}),Z&&o.createElement(I,{key:"status",prefixCls:W}));return L(X?o.createElement(d.Z,{component:"Tag"},$):$)});z.CheckableTag=O;var M=z}}]);