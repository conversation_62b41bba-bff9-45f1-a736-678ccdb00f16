"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1865],{74424:function(e,t,r){r.d(t,{Xe:function(){return l}});var a=r(57437);r(2265);var s=r(23656),n=r(6053),i=r(34863);let{Text:o}=s.default,l={datetime:e=>{if(!e)return l.empty("未设置");try{let t="string"==typeof e?new Date(e):e;return(0,a.jsx)("span",{children:t.toLocaleString()})}catch(e){return l.empty("无效时间")}},date:e=>{if(!e)return l.empty("未设置");try{let t="string"==typeof e?new Date(e):e;return(0,a.jsx)("span",{children:t.toLocaleDateString()})}catch(e){return l.empty("无效日期")}},currency:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"\xa5";return null==e?l.empty("0.00"):(0,a.jsxs)("span",{style:{fontWeight:"bold",color:"#3f8600"},children:[t,e.toLocaleString()]})},quantity:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"个";return null==e?l.empty("0"):(0,a.jsxs)("span",{children:[e.toLocaleString()," ",t]})},status:(e,t)=>{if(!e)return l.empty("未知状态");let r=t[e]||{color:"default",text:e};return(0,a.jsx)(n.Z,{color:r.color,icon:r.icon,children:r.text})},progress:(e,t)=>{if(0===t)return l.empty("无数据");let r=Math.round(e/t*100);return(0,a.jsx)(i.Z,{percent:r,size:"small",status:100===r?"success":"active",format:()=>"".concat(e,"/").concat(t)})},empty:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"无";return(0,a.jsx)(o,{type:"secondary",children:e})},highlight:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info";return e?(0,a.jsx)(o,{style:{color:{success:"#52c41a",warning:"#faad14",error:"#ff4d4f",info:"#1890ff"}[t],fontWeight:"bold"},children:e}):l.empty()}}},66326:function(e,t,r){r.d(t,{ZP:function(){return g}});var a=r(57437),s=r(2265),n=r(23656),i=r(65270),o=r(94734),l=r(28116),c=r(99617),u=r(47628),d=r(74424);let{Title:h}=n.default;var g=function(e){let{open:t,order:r,onClose:n,config:g,className:R,style:m}=e,y=(0,s.useMemo)(()=>{if(!r)return"";let e="function"==typeof g.title?g.title(r):g.title,t=g.getStatus(r);return g.statusConfig[t],(0,a.jsxs)(i.Z,{children:[(0,a.jsx)("span",{children:e}),d.Xe.status(t,g.statusConfig)]})},[r,g]),E=(0,s.useMemo)(()=>r&&g.actions?g.actions.filter(e=>!e.hidden||!e.hidden(r)).map(e=>{let t="function"==typeof e.loading?e.loading(r):e.loading,s="function"==typeof e.disabled?e.disabled(r):e.disabled,n="function"==typeof e.text?e.text(r):e.text;return(0,a.jsx)(o.ZP,{type:e.type,icon:e.icon,loading:t,disabled:s,danger:e.danger,style:e.style,onClick:()=>e.onClick(r),children:n},e.key)}):[],[r,g.actions]),p=(e,t)=>"string"==typeof t&&t.includes(".")?t.split(".").reduce((e,t)=>null==e?void 0:e[t],e):e[t],v=e=>null==e||""===e?d.Xe.empty():"number"==typeof e&&e>1e9?d.Xe.datetime(new Date(e)):"string"==typeof e&&/^\d{4}-\d{2}-\d{2}/.test(e)?d.Xe.datetime(e):(0,a.jsx)("span",{children:String(e)}),f=(0,s.useMemo)(()=>{let e=[...E,(0,a.jsx)(o.ZP,{onClick:n,children:"关闭"},"close")];return g.customFooter&&r?g.customFooter(r,e):e},[E,n,g.customFooter,r]);return r?(0,a.jsx)(u.Z,{title:y,open:t,onCancel:n,footer:f,width:g.width||800,className:R,style:m,children:(0,a.jsxs)("div",{style:{padding:"16px 0"},children:[g.sections.map((e,t)=>{if(!r||e.hidden&&e.hidden(r))return null;let s=e.fields.filter(e=>!e.hidden||!e.hidden(r));return 0!==s.length||e.customContent?(0,a.jsxs)("div",{children:[t>0&&(0,a.jsx)(l.Z,{}),(0,a.jsx)(h,{level:5,children:e.title}),s.length>0&&(0,a.jsx)(c.Z,{column:e.columns||2,bordered:!1!==e.bordered,size:e.size||"small",children:s.map((e,t)=>{let s=p(r,e.key),n=e.render?e.render(s,r):v(s);return(0,a.jsx)(c.Z.Item,{label:e.label,span:e.span,children:n},t)})}),e.customContent&&e.customContent(r)]},t):null}),g.customContent&&g.customContent(r)]})}):null}},40810:function(e,t,r){r.d(t,{e:function(){return n}});var a=r(2265),s=r(32779);let n=e=>{let t={interval:6e4,enabled:!0,showDetails:!1,...e},[r,n]=(0,a.useState)({metrics:null,cacheStats:null,isMonitoring:!1,lastUpdateTime:null,error:null}),i=(0,a.useCallback)(async()=>{try{let e=s.dataAccessManager.getPerformanceMetrics(),r=s.dataAccessManager.getCacheStatistics();n(t=>({...t,metrics:e,cacheStats:r,lastUpdateTime:Date.now(),error:null})),t.showDetails&&console.log("DataAccessManager监控数据:",{响应时间:e.averageResponseTime+"ms",缓存命中率:(100*r.hitRate).toFixed(2)+"%",缓存大小:r.size,总请求数:e.totalCalls})}catch(e){n(t=>({...t,error:e instanceof Error?e.message:"监控数据获取失败"})),console.error("DataAccessManager监控数据获取失败:",e)}},[t.showDetails]),o=(0,a.useCallback)((e,t)=>{try{switch(e){case"service":if(t){let e=s.dataAccessManager.clearServiceCache(t);console.log("清理了 ".concat(e," 个").concat(t,"服务缓存"))}break;case"dataType":t&&(s.dataAccessManager.clearDataTypeCache(t),console.log("清理了".concat(t,"数据类型缓存")));break;default:s.dataAccessManager.clearAllCache(),console.log("清理了所有缓存")}i()}catch(e){console.error("缓存清理失败:",e)}},[i]),l=(0,a.useCallback)(()=>{try{return[]}catch(e){return console.error("获取性能告警失败:",e),[]}},[]),c=(0,a.useCallback)(e=>{if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]},[]),u=(0,a.useCallback)(e=>(100*e).toFixed(2)+"%",[]);return(0,a.useEffect)(()=>{if(!t.enabled)return;n(e=>({...e,isMonitoring:!0})),i();let e=setInterval(i,t.interval);return()=>{clearInterval(e),n(e=>({...e,isMonitoring:!1}))}},[t.enabled,t.interval,i]),{...r,updateMonitoringData:i,clearCache:o,getPerformanceAlerts:l,formatMemorySize:c,formatPercentage:u,isHealthy:!!r.cacheStats&&r.cacheStats.hitRate>.3,needsOptimization:!!r.metrics&&r.metrics.averageResponseTime>1e3}}},62416:function(e,t,r){r.d(t,{Yy:function(){return l},y1:function(){return o}});var a=r(2265),s=r(68143),n=r.n(s),i=r(40810);let o=(e,t,r,s)=>{var o,l;let{isMonitoring:c}=(0,i.e)({interval:6e4,enabled:!1}),u=(0,a.useRef)(null),d=(0,a.useCallback)(n()(e,t,{leading:null!==(o=null==s?void 0:s.leading)&&void 0!==o&&o,trailing:null===(l=null==s?void 0:s.trailing)||void 0===l||l,maxWait:null==s?void 0:s.maxWait}),r);return(0,a.useEffect)(()=>(u.current=d,()=>{u.current&&(u.current.cancel(),u.current=null)}),[d]),(0,a.useEffect)(()=>()=>{u.current&&(u.current.cancel(),u.current=null)},[]),d},l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:300,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];return o(e,t,[e,...r],{leading:!1,trailing:!0})}},71910:function(e,t,r){r.d(t,{el:function(){return b}});var a,s,n,i,o,l,c,u,d=r(2265),h=r(92104),g=r(40810);(a=o||(o={})).CONNECTION_ERROR="connection_error",a.LISTENER_ERROR="listener_error",a.PUBLISH_ERROR="publish_error",a.SERIALIZATION_ERROR="serialization_error",a.TIMEOUT_ERROR="timeout_error",a.PERMISSION_ERROR="permission_error",a.NETWORK_ERROR="network_error",a.CONFIG_ERROR="config_error",(s=l||(l={})).LOW="low",s.MEDIUM="medium",s.HIGH="high",s.CRITICAL="critical",(n=c||(c={})).FIXED="fixed",n.EXPONENTIAL="exponential",n.LINEAR="linear",n.CUSTOM="custom",(i=u||(u={})).NONE="none",i.POLLING="polling",i.BACKUP_SERVICE="backup_service",i.LOCAL_CACHE="local_cache",i.SILENT_FAIL="silent_fail";let R={FAST:{strategy:c.EXPONENTIAL,maxRetries:3,baseDelay:100,maxDelay:1e3,backoffMultiplier:2,jitterFactor:.1},STANDARD:{strategy:c.EXPONENTIAL,maxRetries:5,baseDelay:500,maxDelay:5e3,backoffMultiplier:2,jitterFactor:.2},SLOW:{strategy:c.EXPONENTIAL,maxRetries:3,baseDelay:2e3,maxDelay:3e4,backoffMultiplier:3,jitterFactor:.3},LINEAR:{strategy:c.LINEAR,maxRetries:10,baseDelay:1e3,maxDelay:1e4,backoffMultiplier:1,jitterFactor:.1},FIXED:{strategy:c.FIXED,maxRetries:5,baseDelay:3e3,maxDelay:3e3,backoffMultiplier:1,jitterFactor:0}},m={POLLING:{strategy:u.POLLING,triggerErrorTypes:[o.CONNECTION_ERROR,o.NETWORK_ERROR,o.TIMEOUT_ERROR],timeout:3e4,options:{pollingInterval:5e3,maxPollingAttempts:10}},LOCAL_CACHE:{strategy:u.LOCAL_CACHE,triggerErrorTypes:[o.CONNECTION_ERROR,o.NETWORK_ERROR],timeout:6e4,options:{cacheExpiry:3e5,maxCacheSize:1e3}},BACKUP_SERVICE:{strategy:u.BACKUP_SERVICE,triggerErrorTypes:[o.CONNECTION_ERROR,o.TIMEOUT_ERROR],timeout:15e3,options:{backupEndpoint:"/api/backup/events",healthCheckInterval:1e4}},SILENT_FAIL:{strategy:u.SILENT_FAIL,triggerErrorTypes:[o.PERMISSION_ERROR,o.CONFIG_ERROR],timeout:0,options:{logLevel:"warn"}}},y={[o.CONNECTION_ERROR]:{enabled:!0,retry:R.STANDARD,fallback:m.POLLING,reporting:{enabled:!0,batchSize:10,interval:3e4},monitoring:{enabled:!0,interval:5e3,thresholds:{maxErrorRate:.1,maxResponseTime:5e3,maxMemoryUsage:.8}}},[o.LISTENER_ERROR]:{enabled:!0,retry:R.FAST,fallback:m.SILENT_FAIL,reporting:{enabled:!0,batchSize:5,interval:1e4},monitoring:{enabled:!0,interval:1e3,thresholds:{maxErrorRate:.05,maxResponseTime:1e3,maxMemoryUsage:.7}}},[o.PUBLISH_ERROR]:{enabled:!0,retry:R.STANDARD,fallback:m.LOCAL_CACHE,reporting:{enabled:!0,batchSize:20,interval:6e4},monitoring:{enabled:!0,interval:2e3,thresholds:{maxErrorRate:.02,maxResponseTime:2e3,maxMemoryUsage:.6}}},[o.SERIALIZATION_ERROR]:{enabled:!0,retry:R.FAST,fallback:m.SILENT_FAIL,reporting:{enabled:!0,batchSize:1,interval:5e3},monitoring:{enabled:!0,interval:1e3,thresholds:{maxErrorRate:.01,maxResponseTime:500,maxMemoryUsage:.5}}},[o.TIMEOUT_ERROR]:{enabled:!0,retry:R.SLOW,fallback:m.POLLING,reporting:{enabled:!0,batchSize:15,interval:45e3},monitoring:{enabled:!0,interval:3e3,thresholds:{maxErrorRate:.15,maxResponseTime:1e4,maxMemoryUsage:.8}}},[o.PERMISSION_ERROR]:{enabled:!0,retry:{...R.FAST,maxRetries:1},fallback:m.SILENT_FAIL,reporting:{enabled:!0,batchSize:1,interval:1e3},monitoring:{enabled:!0,interval:1e3,thresholds:{maxErrorRate:.01,maxResponseTime:1e3,maxMemoryUsage:.5}}},[o.NETWORK_ERROR]:{enabled:!0,retry:R.STANDARD,fallback:m.LOCAL_CACHE,reporting:{enabled:!0,batchSize:25,interval:6e4},monitoring:{enabled:!0,interval:5e3,thresholds:{maxErrorRate:.2,maxResponseTime:8e3,maxMemoryUsage:.8}}},[o.CONFIG_ERROR]:{enabled:!0,retry:{...R.FAST,maxRetries:1},fallback:m.SILENT_FAIL,reporting:{enabled:!0,batchSize:1,interval:1e3},monitoring:{enabled:!0,interval:1e3,thresholds:{maxErrorRate:.01,maxResponseTime:500,maxMemoryUsage:.5}}}};l.LOW,l.MEDIUM,l.HIGH,l.CRITICAL;class E{static calculateRetryDelay(e,t,r){let a;switch(t.strategy){case c.FIXED:a=t.baseDelay;break;case c.LINEAR:a=t.baseDelay*e;break;case c.EXPONENTIAL:a=t.baseDelay*Math.pow(t.backoffMultiplier,e-1);break;case c.CUSTOM:a=t.customDelayFn?t.customDelayFn(e,t.baseDelay):t.baseDelay;break;default:a=t.baseDelay}if(t.jitterFactor>0){let e=a*t.jitterFactor*(Math.random()-.5);a+=e}return Math.min(a,t.maxDelay)}static shouldTriggerFallback(e,t,r){return!!r.triggerErrorTypes.includes(e)&&t>=(({[o.CONNECTION_ERROR]:3,[o.NETWORK_ERROR]:5,[o.TIMEOUT_ERROR]:2,[o.LISTENER_ERROR]:10,[o.PUBLISH_ERROR]:3,[o.SERIALIZATION_ERROR]:1,[o.PERMISSION_ERROR]:1,[o.CONFIG_ERROR]:1})[e]||3)}static calculateListenerHealthScore(e,t,r,a){let s=e+t;return 0===s?100:Math.round(e/s*40+Math.max(0,30-r/100*30)+Math.max(0,20-20*a)+Math.max(0,10-t/s*100))}static recommendStrategy(e,t){let r=t.filter(e=>Date.now()-e.timestamp<3e5&&!e.success).length/Math.max(1,t.length),a=y[e],s=a.retry,n=a.fallback,i=.8;return r>.5?(s={...s,maxRetries:Math.max(1,s.maxRetries-2),baseDelay:2*s.baseDelay},i=.6):r<.1&&(s={...s,maxRetries:s.maxRetries+1,baseDelay:Math.max(100,s.baseDelay/2)},i=.9),{retryConfig:s,fallbackConfig:n,confidence:i}}}class p{async handleError(e,t,r){let a=Date.now(),s=this.createEventError(e,t),n=r?{...this.config,...r}:this.getConfigForErrorType(s.type);this.recordError(s),t.listenerId&&this.updateListenerState(t.listenerId,!1,Date.now()-a);let i=await this.executeErrorHandling(s,n);return this.recordHandlingResult(s,i),this.checkAndGenerateAlerts(s,i),i}createEventError(e,t){let r=this.classifyError(e),a=this.determineSeverity(r,t);return{id:this.generateErrorId(),type:r,severity:a,message:e.message,originalError:e,context:t,timestamp:Date.now(),retryable:this.isRetryable(r),retryCount:0,maxRetries:this.getConfigForErrorType(r).retry.maxRetries,stack:e.stack}}classifyError(e){let t=e.message.toLowerCase(),r=e.name.toLowerCase();return t.includes("network")||t.includes("fetch")?o.NETWORK_ERROR:t.includes("timeout")||r.includes("timeout")?o.TIMEOUT_ERROR:t.includes("connection")||t.includes("websocket")?o.CONNECTION_ERROR:t.includes("permission")||t.includes("unauthorized")?o.PERMISSION_ERROR:t.includes("serialize")||t.includes("json")?o.SERIALIZATION_ERROR:t.includes("config")||t.includes("configuration")?o.CONFIG_ERROR:t.includes("listener")||t.includes("callback")?o.LISTENER_ERROR:o.PUBLISH_ERROR}determineSeverity(e,t){let r={[o.CONNECTION_ERROR]:l.HIGH,[o.NETWORK_ERROR]:l.MEDIUM,[o.TIMEOUT_ERROR]:l.MEDIUM,[o.LISTENER_ERROR]:l.LOW,[o.PUBLISH_ERROR]:l.MEDIUM,[o.SERIALIZATION_ERROR]:l.LOW,[o.PERMISSION_ERROR]:l.HIGH,[o.CONFIG_ERROR]:l.CRITICAL}[e];"critical-service"===t.component&&(r=l.CRITICAL);let a=this.getRecentErrors(e,3e5);return a.length>10?r=l.CRITICAL:a.length>5&&(r=l.HIGH),r}async executeErrorHandling(e,t){let r=Date.now();return this.shouldRetry(e,t.retry)?this.scheduleRetry(e,t.retry):this.shouldFallback(e,t.fallback)?await this.executeFallback(e,t.fallback):{success:!1,action:"ignore",message:"错误已记录但无法处理: ".concat(e.message),duration:Date.now()-r}}shouldRetry(e,t){return e.retryable&&e.retryCount<t.maxRetries&&e.retryCount<e.maxRetries}scheduleRetry(e,t){let r=E.calculateRetryDelay(e.retryCount+1,t,e.type),a=Date.now()+r,s="".concat(e.id,"_retry");this.retryTimers.has(s)&&clearTimeout(this.retryTimers.get(s));let n=setTimeout(()=>{this.executeRetry(e),this.retryTimers.delete(s)},r);return this.retryTimers.set(s,n),{success:!0,action:"retry",nextRetryAt:a,message:"已安排在 ".concat(r,"ms 后重试 (第 ").concat(e.retryCount+1," 次)"),duration:0}}async executeRetry(e){e.retryCount++;try{this.emitRetryEvent(e)}catch(t){await this.handleError(t,e.context)}}shouldFallback(e,t){return E.shouldTriggerFallback(e.type,this.getErrorCount(e.type,e.context.listenerId),t)}async executeFallback(e,t){let r=Date.now(),a="".concat(e.context.listenerId||"global","_").concat(e.type);this.fallbackStates.set(a,{active:!0,strategy:t.strategy,startTime:Date.now()});try{switch(t.strategy){case"polling":await this.activatePollingFallback(e,t);break;case"local_cache":await this.activateLocalCacheFallback(e,t);break;case"backup_service":await this.activateBackupServiceFallback(e,t);break;case"silent_fail":console.warn("[EventErrorHandler] 静默失败: ".concat(e.message))}return{success:!0,action:"fallback",fallbackStrategy:t.strategy,message:"已激活降级策略: ".concat(t.strategy),duration:Date.now()-r}}catch(e){return{success:!1,action:"escalate",message:"降级策略执行失败: ".concat(e.message),duration:Date.now()-r}}}async activatePollingFallback(e,t){let{pollingInterval:r=5e3,maxPollingAttempts:a=10}=t.options;console.log("[EventErrorHandler] 激活轮询降级，间隔: ".concat(r,"ms")),this.emitFallbackEvent("polling_activated",{errorType:e.type,pollingInterval:r,maxPollingAttempts:a})}async activateLocalCacheFallback(e,t){console.log("[EventErrorHandler] 激活本地缓存降级"),this.emitFallbackEvent("local_cache_activated",{errorType:e.type,cacheOptions:t.options})}async activateBackupServiceFallback(e,t){console.log("[EventErrorHandler] 激活备用服务降级"),this.emitFallbackEvent("backup_service_activated",{errorType:e.type,backupEndpoint:t.options.backupEndpoint})}getConfigForErrorType(e){return y[e]||this.config}isRetryable(e){return![o.PERMISSION_ERROR,o.CONFIG_ERROR,o.SERIALIZATION_ERROR].includes(e)}generateErrorId(){return"err_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}recordError(e){this.errorHistory.length>1e3&&(this.errorHistory=this.errorHistory.slice(-500))}recordHandlingResult(e,t){this.errorHistory.push({error:e,handlingResult:t,timestamp:Date.now()})}updateListenerState(e,t,r){let a=this.listenerStates.get(e);a||(a={id:e,errorCount:0,successCount:0,lastActivity:Date.now(),averageResponseTime:0,memoryUsage:0,health:{listenerId:e,status:"healthy",lastActivity:Date.now(),errorCount:0,successCount:0,averageResponseTime:0,memoryUsage:0,healthScore:100}},this.listenerStates.set(e,a)),t?a.successCount++:a.errorCount++,a.lastActivity=Date.now();let s=a.successCount+a.errorCount;a.averageResponseTime=(a.averageResponseTime*(s-1)+r)/s,a.health.healthScore=E.calculateListenerHealthScore(a.successCount,a.errorCount,a.averageResponseTime,a.memoryUsage),a.health.healthScore>80?a.health.status="healthy":a.health.healthScore>60?a.health.status="warning":a.health.healthScore>20?a.health.status="error":a.health.status="dead"}getRecentErrors(e,t){let r=Date.now()-t;return this.errorHistory.filter(t=>t.error.type===e&&t.timestamp>r)}getErrorCount(e,t){return this.errorHistory.filter(r=>r.error.type===e&&(!t||r.error.context.listenerId===t)).length}checkAndGenerateAlerts(e,t){e.severity===l.CRITICAL&&this.generateAlert({id:"alert_".concat(Date.now()),type:"error",level:"critical",message:"严重错误: ".concat(e.message),metrics:{errorCount:this.getErrorCount(e.type)},suggestedActions:["检查系统状态","联系技术支持"],timestamp:Date.now(),acknowledged:!1});let r=this.getRecentErrors(e.type,3e5);r.length>10&&this.generateAlert({id:"alert_freq_".concat(Date.now()),type:"performance",level:"warning",message:"错误频率过高: ".concat(e.type," (").concat(r.length," 次/5分钟)"),metrics:{errorRate:r.length/5},suggestedActions:["检查网络连接","检查服务状态"],timestamp:Date.now(),acknowledged:!1})}generateAlert(e){this.alerts.push(e),this.alerts.length>100&&(this.alerts=this.alerts.slice(-50)),console.warn("[EventErrorHandler] ".concat(e.level.toUpperCase(),": ").concat(e.message))}startMonitoring(){setInterval(()=>{this.performHealthCheck(),this.cleanupExpiredStates()},this.config.monitoring.interval)}performHealthCheck(){this.listenerStates.forEach((e,t)=>{Date.now()-e.lastActivity>3e5&&(e.health.status="dead",e.health.healthScore=0)})}cleanupExpiredStates(){let e=Date.now();this.fallbackStates.forEach((t,r)=>{e-t.startTime>36e5&&this.fallbackStates.delete(r)}),this.retryTimers.forEach((e,t)=>{})}emitRetryEvent(e){console.log("[EventErrorHandler] 重试事件: ".concat(e.type," - ").concat(e.message))}emitFallbackEvent(e,t){console.log("[EventErrorHandler] 降级事件: ".concat(e),t)}getErrorStatistics(){let e=Date.now(),t=this.errorHistory.filter(t=>e-t.timestamp<864e5),r={};return Object.values(o).forEach(e=>{r[e]=t.filter(t=>t.error.type===e).length}),{totalErrors:t.length,errorsByType:r,activeListeners:this.listenerStates.size,healthyListeners:Array.from(this.listenerStates.values()).filter(e=>"healthy"===e.health.status).length,activeFallbacks:this.fallbackStates.size,pendingRetries:this.retryTimers.size}}getListenerHealthStates(){return Array.from(this.listenerStates.values()).map(e=>e.health)}getAlerts(){return[...this.alerts]}clearAlerts(){this.alerts=[]}getErrorHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:100;return this.errorHistory.slice(-e)}async manualRetry(e){let t=this.errorHistory.find(t=>t.error.id===e);return t?(await this.executeRetry(t.error),{success:!0,action:"retry",message:"已手动触发重试",duration:0}):{success:!1,action:"ignore",message:"未找到指定的错误记录",duration:0}}deactivateFallback(e,t){let r="".concat(e,"_").concat(t),a=this.fallbackStates.delete(r);return a&&this.emitFallbackEvent("fallback_deactivated",{listenerId:e,errorType:t}),a}resetListenerState(e){let t=this.listenerStates.get(e);return!!t&&(t.errorCount=0,t.successCount=0,t.averageResponseTime=0,t.health.healthScore=100,t.health.status="healthy",t.health.errorCount=0,t.health.successCount=0,!0)}getConfig(){return{...this.config}}updateConfig(e){this.config={...this.config,...e}}destroy(){this.retryTimers.forEach(e=>clearTimeout(e)),this.retryTimers.clear(),this.errorHistory=[],this.listenerStates.clear(),this.fallbackStates.clear(),this.alerts=[],console.log("[EventErrorHandler] 错误处理器已销毁")}constructor(e){this.errorHistory=[],this.listenerStates=new Map,this.retryTimers=new Map,this.fallbackStates=new Map,this.alerts=[],this.config={enabled:!0,retry:y[o.CONNECTION_ERROR].retry,fallback:y[o.CONNECTION_ERROR].fallback,reporting:{enabled:!0,batchSize:10,interval:3e4},monitoring:{enabled:!0,interval:5e3,thresholds:{maxErrorRate:.1,maxResponseTime:5e3,maxMemoryUsage:.8}},...e},this.config.monitoring.enabled&&this.startMonitoring()}}class v{registerListener(e){if(this.isDestroyed)throw Error("EventListenerManager has been destroyed");this.validateListenerConfig(e),this.listeners.has(e.id)&&(console.warn("[EventListenerManager] 监听器 ".concat(e.id," 已存在，将被替换")),this.unregisterListener(e.id));let t={id:e.id,config:e,registeredAt:Date.now(),lastActivity:Date.now(),eventCount:0,errorCount:0,averageResponseTime:0,isActive:!0},r=this.wrapListenerCallback(t);return t.config.callback=r,this.listeners.set(e.id,t),this.updateMetrics(),console.log("[EventListenerManager] 注册监听器: ".concat(e.id)),e.id}unregisterListener(e){let t=this.listeners.get(e);if(!t)return!1;if(t.cleanup)try{t.cleanup()}catch(t){console.error("[EventListenerManager] 清理监听器 ".concat(e," 时出错:"),t)}return t.isActive=!1,this.listeners.delete(e),this.updateMetrics(),console.log("[EventListenerManager] 注销监听器: ".concat(e)),!0}async publishEvent(e){let t;if(this.isDestroyed)throw Error("EventListenerManager has been destroyed");let r=Date.now(),a=this.generateEventId(),s=!0;try{let n=this.getTargetListeners(e);if(0===n.length)return console.warn("[EventListenerManager] 没有找到事件 ".concat(e.type," 的监听器")),!1;let i=n.map(t=>this.publishToListener(t,e)),o=(await Promise.allSettled(i)).filter(e=>"rejected"===e.status);return o.length>0&&(s=!1,t=Error("".concat(o.length," 个监听器发布失败"))),this.recordEventPublish({id:a,type:e.type,timestamp:r,success:s,duration:Date.now()-r,targetListeners:n.map(e=>e.id),error:t}),s}catch(n){return t=n,s=!1,this.recordEventPublish({id:a,type:e.type,timestamp:r,success:!1,duration:Date.now()-r,targetListeners:[],error:t}),await this.errorHandler.handleError(t,{eventType:e.type,component:"EventListenerManager"}),!1}}getTargetListeners(e){let t=Array.from(this.listeners.values());return e.targetListeners&&e.targetListeners.length>0?t.filter(t=>e.targetListeners.includes(t.id)&&t.isActive&&t.config.enabled):t.filter(t=>t.config.eventTypes.includes(e.type)&&t.isActive&&t.config.enabled)}async publishToListener(e,t){let r=Date.now();try{var a;let s=(null===(a=t.options)||void 0===a?void 0:a.timeout)||e.config.timeout||5e3,n=new Promise((e,t)=>{setTimeout(()=>t(Error("Listener timeout")),s)}),i=Promise.resolve(e.config.callback(t.data));await Promise.race([i,n]),this.updateListenerStats(e,!0,Date.now()-r)}catch(a){throw this.updateListenerStats(e,!1,Date.now()-r),await this.errorHandler.handleError(a,{eventType:t.type,listenerId:e.id,component:"EventListenerManager"}),a}}wrapListenerCallback(e){let t=e.config.callback;return async r=>{let a=Date.now();try{e.lastActivity=Date.now();let s=await t(r);return this.updateListenerStats(e,!0,Date.now()-a),s}catch(r){this.updateListenerStats(e,!1,Date.now()-a);let t={listenerId:e.id,component:"EventListener",metadata:{eventTypes:e.config.eventTypes,priority:e.config.priority}};throw await this.errorHandler.handleError(r,t),r}}}updateListenerStats(e,t,r){e.eventCount++,e.lastActivity=Date.now(),t?e.averageResponseTime=(e.averageResponseTime*(e.eventCount-1)+r)/e.eventCount:e.errorCount++}validateListenerConfig(e){if(!e.id)throw Error("监听器ID不能为空");if(!e.eventTypes||0===e.eventTypes.length)throw Error("监听器必须指定至少一个事件类型");if("function"!=typeof e.callback)throw Error("监听器回调必须是函数");if(e.priority<0||e.priority>10)throw Error("监听器优先级必须在0-10之间");if(e.timeout<0)throw Error("监听器超时时间不能为负数")}recordEventPublish(e){this.eventHistory.push(e),this.eventHistory.length>1e3&&(this.eventHistory=this.eventHistory.slice(-500)),this.updateMetrics()}updateMetrics(){let e=Date.now(),t=this.eventHistory.filter(t=>e-t.timestamp<3e5);this.metrics.totalEvents=t.length,this.metrics.successfulEvents=t.filter(e=>e.success).length,this.metrics.failedEvents=t.filter(e=>!e.success).length,this.metrics.errorRate=this.metrics.totalEvents>0?this.metrics.failedEvents/this.metrics.totalEvents:0,this.metrics.averageResponseTime=t.length>0?t.reduce((e,t)=>e+t.duration,0)/t.length:0,this.metrics.activeListeners=Array.from(this.listeners.values()).filter(e=>e.isActive).length,this.metrics.deadListeners=Array.from(this.listeners.values()).filter(t=>!t.isActive||e-t.lastActivity>3e5).length,this.metrics.memoryUsage=this.estimateMemoryUsage()}estimateMemoryUsage(){return(1024*this.listeners.size+512*this.eventHistory.length)/1048576}generateEventId(){return"evt_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}startMonitoring(){let e=setInterval(()=>{if(this.isDestroyed){clearInterval(e);return}this.performHealthCheck(),this.cleanupInactiveListeners(),this.updateMetrics()},3e4)}performHealthCheck(){let e=Date.now();this.listeners.forEach((t,r)=>{let a=e-t.lastActivity;a>3e5&&t.isActive&&console.warn("[EventListenerManager] 监听器 ".concat(r," 长时间无活动 (").concat(Math.round(a/1e3),"秒)"))})}cleanupInactiveListeners(){let e=Date.now(),t=[];this.listeners.forEach((r,a)=>{(!r.isActive||e-r.lastActivity>36e5)&&t.push(a)}),t.forEach(e=>{console.log("[EventListenerManager] 清理非活跃监听器: ".concat(e)),this.unregisterListener(e)})}getListenerHealthStates(){return Array.from(this.listeners.values()).map(e=>{let t;let r=e.eventCount,a=Math.round(60*(r>0?(r-e.errorCount)/r:1)+Math.max(0,30-e.averageResponseTime/100*30)+(e.isActive?10:0));return t=e.isActive?a>80?"healthy":a>60?"warning":"error":"dead",{listenerId:e.id,status:t,lastActivity:e.lastActivity,errorCount:e.errorCount,successCount:e.eventCount-e.errorCount,averageResponseTime:e.averageResponseTime,memoryUsage:0,healthScore:a}})}getMetrics(){return{...this.metrics}}getDebugInfo(){let e=this.getListenerHealthStates(),t=this.eventHistory.slice(-20);return{listeners:Array.from(this.listeners.values()).map(t=>({id:t.id,eventTypes:t.config.eventTypes,status:t.isActive?"active":"inactive",health:e.find(e=>e.listenerId===t.id)})),recentEvents:t.map(e=>({type:e.type,timestamp:e.timestamp,success:e.success,duration:e.duration})),errorStats:this.errorHandler.getErrorStatistics().errorsByType,metrics:this.metrics,alerts:this.errorHandler.getAlerts()}}getListeners(){return Array.from(this.listeners.values()).map(e=>({id:e.id,eventTypes:e.config.eventTypes,isActive:e.isActive,registeredAt:e.registeredAt,eventCount:e.eventCount,errorCount:e.errorCount}))}resetListenerStats(e){let t=this.listeners.get(e);return!!t&&(t.eventCount=0,t.errorCount=0,t.averageResponseTime=0,t.lastActivity=Date.now(),!0)}destroy(){this.isDestroyed||(Array.from(this.listeners.keys()).forEach(e=>this.unregisterListener(e)),this.listeners.clear(),this.eventHistory=[],this.errorHandler.destroy(),this.isDestroyed=!0,console.log("[EventListenerManager] 事件监听器管理器已销毁"))}constructor(e){this.listeners=new Map,this.eventHistory=[],this.isDestroyed=!1,this.errorHandler=e||new p,this.metrics={totalEvents:0,successfulEvents:0,failedEvents:0,errorRate:0,averageResponseTime:0,activeListeners:0,deadListeners:0,memoryUsage:0,networkUsage:0},this.startMonitoring()}}let f=new v(new p),b=(e,t,r)=>{let{isMonitoring:a}=(0,g.e)({interval:6e4,enabled:!1}),s=(0,d.useRef)(null),n={autoCleanup:!0,trackMemory:!0,useNewManager:!1,priority:5,timeout:5e3,...r};(0,d.useEffect)(()=>{if(s.current=t,!n.useNewManager)return h.dataChangeNotifier.registerListener(e,t),n.trackMemory&&a&&console.log("Event listener registered: ".concat(e),{manager:"legacy"}),()=>{n.autoCleanup&&h.dataChangeNotifier.unregisterListener(e),s.current=null};{let r={id:e,eventTypes:["order_created","order_updated","order_deleted","production_order_created","production_order_updated","production_order_deleted","production_work_order_created","production_work_order_updated","production_work_order_deleted"],callback:async e=>{var r,a,s,n,i,o,l,c,u;switch(e.type||e.eventType){case"order_created":null===(r=t.onOrderCreated)||void 0===r||r.call(t,e.data);break;case"order_updated":null===(a=t.onOrderUpdated)||void 0===a||a.call(t,e.data);break;case"order_deleted":null===(s=t.onOrderDeleted)||void 0===s||s.call(t,e.data);break;case"production_order_created":null===(n=t.onProductionOrderCreated)||void 0===n||n.call(t,e.data);break;case"production_order_updated":null===(i=t.onProductionOrderUpdated)||void 0===i||i.call(t,e.data);break;case"production_order_deleted":null===(o=t.onProductionOrderDeleted)||void 0===o||o.call(t,e.data);break;case"production_work_order_created":null===(l=t.onProductionWorkOrderCreated)||void 0===l||l.call(t,e.data);break;case"production_work_order_updated":null===(c=t.onProductionWorkOrderUpdated)||void 0===c||c.call(t,e.data);break;case"production_work_order_deleted":null===(u=t.onProductionWorkOrderDeleted)||void 0===u||u.call(t,e.data)}},priority:n.priority,enabled:!0,timeout:n.timeout};return f.registerListener(r),n.trackMemory&&a&&console.log("Event listener registered: ".concat(e),{manager:"new",priority:n.priority,timeout:n.timeout}),()=>{n.autoCleanup&&f.unregisterListener(e),s.current=null}}},[e,n.autoCleanup,n.trackMemory,n.useNewManager,n.priority,n.timeout,a]),(0,d.useEffect)(()=>()=>{s.current&&(h.dataChangeNotifier.unregisterListener(e),s.current=null)},[e])}},50538:function(e,t,r){r.d(t,{Ro:function(){return a}});let a=async(e,t)=>{try{let r=await e();if("success"===r.status)return r.data||null;return r.code,r.message,r.message,t&&n(String(r.code||"UNKNOWN_ERROR")),null}catch(e){return e instanceof Error?e.message:String(e),null}},s={ERR_NOT_FOUND:"资源不存在",ERR_UNAUTHORIZED:"未授权访问",ERR_FORBIDDEN:"禁止访问",ERR_INTERNAL_ERROR:"内部服务器错误",ERR_PRODUCT_NOT_FOUND:"产品不存在",ERR_PRODUCT_CODE_EXISTS:"产品编码已存在",ERR_CUSTOMER_NOT_FOUND:"客户不存在",ERR_INVENTORY_INSUFFICIENT:"库存不足",ERR_ORDER_NOT_FOUND:"订单不存在",ERR_ORDER_NUMBER_EXISTS:"订单号已存在",DUPLICATE_ORDER_NUMBER:"订单号重复"},n=e=>s[e]||"操作失败"}}]);