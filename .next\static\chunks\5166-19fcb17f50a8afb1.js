"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5166],{99808:function(e,t,n){/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,o=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!a(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),a=r[0].inst,c=r[1];return s(function(){a.value=n,a.getSnapshot=t,u(a)&&c({inst:a})},[e,n,t]),o(function(){return u(a)&&c({inst:a}),e(function(){u(a)&&c({inst:a})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},53176:function(e,t,n){/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=n(2265),a=n(26272),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=a.useSyncExternalStore,s=r.useRef,l=r.useEffect,u=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,a){var d=s(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;var v=o(e,(d=u(function(){function e(e){if(!l){if(l=!0,o=e,e=r(e),void 0!==a&&f.hasValue){var t=f.value;if(a(t,e))return s=t}return s=e}if(t=s,i(o,e))return t;var n=r(e);return void 0!==a&&a(t,n)?(o=e,t):(o=e,s=n)}var o,s,l=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,n,r,a]))[0],d[1]);return l(function(){f.hasValue=!0,f.value=v},[v]),c(v),v}},26272:function(e,t,n){e.exports=n(99808)},65401:function(e,t,n){e.exports=n(53176)},94660:function(e,t,n){n.d(t,{Ue:function(){return f}});let r=e=>{let t;let n=new Set,r=(e,r)=>{let a="function"==typeof e?e(t):e;if(!Object.is(a,t)){let e=t;t=(null!=r?r:"object"!=typeof a||null===a)?a:Object.assign({},t,a),n.forEach(n=>n(t,e))}},a=()=>t,i={setState:r,getState:a,getInitialState:()=>o,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},o=t=e(r,a,i);return i},a=e=>e?r(e):r;var i=n(2265),o=n(65401);let{useDebugValue:s}=i,{useSyncExternalStoreWithSelector:l}=o,u=!1,c=e=>e,d=e=>{"function"!=typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");let t="function"==typeof e?a(e):e,n=(e,n)=>(function(e,t=c,n){n&&!u&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),u=!0);let r=l(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return s(r),r})(t,e,n);return Object.assign(n,t),n},f=e=>e?d(e):d},74810:function(e,t,n){n.d(t,{mW:function(){return o},tJ:function(){return d}});let r=new Map,a=e=>{let t=r.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},i=(e,t,n)=>{if(void 0===e)return{type:"untracked",connection:t.connect(n)};let a=r.get(n.name);if(a)return{type:"tracked",store:e,...a};let i={connection:t.connect(n),stores:{}};return r.set(n.name,i),{type:"tracked",store:e,...i}},o=(e,t={})=>(n,r,o)=>{let l;let{enabled:u,anonymousActionType:c,store:d,...f}=t;try{l=(null==u||u)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!l)return u&&console.warn("[zustand devtools middleware] Please install/enable Redux devtools extension"),e(n,r,o);let{connection:v,...p}=i(d,l,f),g=!0;o.setState=(e,t,i)=>{let s=n(e,t);if(!g)return s;let l=void 0===i?{type:c||"anonymous"}:"string"==typeof i?{type:i}:i;return void 0===d?null==v||v.send(l,r()):null==v||v.send({...l,type:`${d}/${l.type}`},{...a(f.name),[d]:o.getState()}),s};let m=(...e)=>{let t=g;g=!1,n(...e),g=t},S=e(o.setState,r,o);if("untracked"===p.type?null==v||v.init(S):(p.stores[p.store]=o,null==v||v.init(Object.fromEntries(Object.entries(p.stores).map(([e,t])=>[e,e===p.store?S:t.getState()])))),o.dispatchFromDevtools&&"function"==typeof o.dispatch){let e=!1,t=o.dispatch;o.dispatch=(...n)=>{"__setState"!==n[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...n)}}return v.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload){console.error("[zustand devtools middleware] Unsupported action format");return}return s(e.payload,e=>{if("__setState"===e.type){if(void 0===d){m(e.state);return}1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[d];if(null==t)return;JSON.stringify(o.getState())!==JSON.stringify(t)&&m(t);return}o.dispatchFromDevtools&&"function"==typeof o.dispatch&&o.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(m(S),void 0===d)return null==v?void 0:v.init(o.getState());return null==v?void 0:v.init(a(f.name));case"COMMIT":if(void 0===d){null==v||v.init(o.getState());break}return null==v?void 0:v.init(a(f.name));case"ROLLBACK":return s(e.state,e=>{if(void 0===d){m(e),null==v||v.init(o.getState());return}m(e[d]),null==v||v.init(a(f.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return s(e.state,e=>{if(void 0===d){m(e);return}JSON.stringify(o.getState())!==JSON.stringify(e[d])&&m(e[d])});case"IMPORT_STATE":{let{nextLiftedState:n}=e.payload,r=null==(t=n.computedStates.slice(-1)[0])?void 0:t.state;if(!r)return;void 0===d?m(r):m(r[d]),null==v||v.send(null,n);break}case"PAUSE_RECORDING":return g=!g}return}}),S},s=(e,t)=>{let n;try{n=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==n&&t(n)},l=e=>t=>{try{let n=e(t);if(n instanceof Promise)return n;return{then:e=>l(e)(n),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>l(t)(e)}}},u=(e,t)=>(n,r,a)=>{let i,o,s={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},u=!1,c=new Set,d=new Set;try{i=s.getStorage()}catch(e){}if(!i)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...e)},r,a);let f=l(s.serialize),v=()=>{let e;let t=f({state:s.partialize({...r()}),version:s.version}).then(e=>i.setItem(s.name,e)).catch(t=>{e=t});if(e)throw e;return t},p=a.setState;a.setState=(e,t)=>{p(e,t),v()};let g=e((...e)=>{n(...e),v()},r,a),m=()=>{var e;if(!i)return;u=!1,c.forEach(e=>e(r()));let t=(null==(e=s.onRehydrateStorage)?void 0:e.call(s,r()))||void 0;return l(i.getItem.bind(i))(s.name).then(e=>{if(e)return s.deserialize(e)}).then(e=>{if(e){if("number"!=typeof e.version||e.version===s.version)return e.state;if(s.migrate)return s.migrate(e.state,e.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}}).then(e=>{var t;return n(o=s.merge(e,null!=(t=r())?t:g),!0),v()}).then(()=>{null==t||t(o,void 0),u=!0,d.forEach(e=>e(o))}).catch(e=>{null==t||t(void 0,e)})};return a.persist={setOptions:e=>{s={...s,...e},e.getStorage&&(i=e.getStorage())},clearStorage:()=>{null==i||i.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>m(),hasHydrated:()=>u,onHydrate:e=>(c.add(e),()=>{c.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},m(),o||g},c=(e,t)=>(n,r,a)=>{let i,o={storage:function(e,t){let n;try{n=e()}catch(e){return}return{getItem:e=>{var r;let a=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),i=null!=(r=n.getItem(e))?r:null;return i instanceof Promise?i.then(a):a(i)},setItem:(e,r)=>n.setItem(e,JSON.stringify(r,null==t?void 0:t.replacer)),removeItem:e=>n.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},s=!1,u=new Set,c=new Set,d=o.storage;if(!d)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...e)},r,a);let f=()=>{let e=o.partialize({...r()});return d.setItem(o.name,{state:e,version:o.version})},v=a.setState;a.setState=(e,t)=>{v(e,t),f()};let p=e((...e)=>{n(...e),f()},r,a);a.getInitialState=()=>p;let g=()=>{var e,t;if(!d)return;s=!1,u.forEach(e=>{var t;return e(null!=(t=r())?t:p)});let a=(null==(t=o.onRehydrateStorage)?void 0:t.call(o,null!=(e=r())?e:p))||void 0;return l(d.getItem.bind(d))(o.name).then(e=>{if(e){if("number"!=typeof e.version||e.version===o.version)return[!1,e.state];if(o.migrate)return[!0,o.migrate(e.state,e.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[a,s]=e;if(n(i=o.merge(s,null!=(t=r())?t:p),!0),a)return f()}).then(()=>{null==a||a(i,void 0),i=r(),s=!0,c.forEach(e=>e(i))}).catch(e=>{null==a||a(void 0,e)})};return a.persist={setOptions:e=>{o={...o,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>g(),hasHydrated:()=>s,onHydrate:e=>(u.add(e),()=>{u.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},o.skipHydration||g(),i||p},d=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?(console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),u(e,t)):c(e,t)}}]);