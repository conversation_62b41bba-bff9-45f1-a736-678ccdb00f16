(()=>{var e={};e.id=1086,e.ids=[1086],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},70621:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var n=r(50482),o=r(69108),s=r(62563),a=r.n(s),l=r(68300),i={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let c=["",{children:["production",{children:["work-report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26425)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\work-report\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,18223)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\work-report\\page.tsx"],u="/production/work-report/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/production/work-report/page",pathname:"/production/work-report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},51085:(e,t,r)=>{Promise.resolve().then(r.bind(r,2335))},3745:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(65651),o=r(3729);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var a=r(49809);let l=o.forwardRef(function(e,t){return o.createElement(a.Z,(0,n.Z)({},e,{ref:t,icon:s}))})},39593:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(65651),o=r(3729);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm-88-532h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8zm224 0h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8z"}}]},name:"pause-circle",theme:"outlined"};var a=r(49809);let l=o.forwardRef(function(e,t){return o.createElement(a.Z,(0,n.Z)({},e,{ref:t,icon:s}))})},98507:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(65651),o=r(3729);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"};var a=r(49809);let l=o.forwardRef(function(e,t){return o.createElement(a.Z,(0,n.Z)({},e,{ref:t,icon:s}))})},12391:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(65651),o=r(3729);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"};var a=r(49809);let l=o.forwardRef(function(e,t){return o.createElement(a.Z,(0,n.Z)({},e,{ref:t,icon:s}))})},14223:(e,t,r)=>{"use strict";r.d(t,{Z:()=>z});var n=r(3729),o=r(33795),s=r(57629),a=r(32066),l=r(2523),i=r(29513),c=r(34132),d=r.n(c),u=r(27335),p=r(7305),m=r(67862),g=r(29545),f=r(84893),h=r(92959),x=r(22989),b=r(13165);let v=(e,t,r,n,o)=>({background:e,border:`${(0,h.bf)(n.lineWidth)} ${n.lineType} ${t}`,[`${o}-icon`]:{color:r}}),y=e=>{let{componentCls:t,motionDurationSlow:r,marginXS:n,marginSM:o,fontSize:s,fontSizeLG:a,lineHeight:l,borderRadiusLG:i,motionEaseInOutCirc:c,withDescriptionIconSize:d,colorText:u,colorTextHeading:p,withDescriptionPadding:m,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,x.Wf)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:i,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:n,lineHeight:0},"&-description":{display:"none",fontSize:s,lineHeight:l},"&-message":{color:p},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${r} ${c}, opacity ${r} ${c},
        padding-top ${r} ${c}, padding-bottom ${r} ${c},
        margin-bottom ${r} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:m,[`${t}-icon`]:{marginInlineEnd:o,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:n,color:p,fontSize:a},[`${t}-description`]:{display:"block",color:u}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},j=e=>{let{componentCls:t,colorSuccess:r,colorSuccessBorder:n,colorSuccessBg:o,colorWarning:s,colorWarningBorder:a,colorWarningBg:l,colorError:i,colorErrorBorder:c,colorErrorBg:d,colorInfo:u,colorInfoBorder:p,colorInfoBg:m}=e;return{[t]:{"&-success":v(o,n,r,e,t),"&-info":v(m,p,u,e,t),"&-warning":v(l,a,s,e,t),"&-error":Object.assign(Object.assign({},v(d,c,i,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},w=e=>{let{componentCls:t,iconCls:r,motionDurationMid:n,marginXS:o,fontSizeIcon:s,colorIcon:a,colorIconHover:l}=e;return{[t]:{"&-action":{marginInlineStart:o},[`${t}-close-icon`]:{marginInlineStart:o,padding:0,overflow:"hidden",fontSize:s,lineHeight:(0,h.bf)(s),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${r}-close`]:{color:a,transition:`color ${n}`,"&:hover":{color:l}}},"&-close-text":{color:a,transition:`color ${n}`,"&:hover":{color:l}}}}},C=(0,b.I$)("Alert",e=>[y(e),j(e),w(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var k=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let Z={success:o.Z,info:i.Z,error:s.Z,warning:l.Z},$=e=>{let{icon:t,prefixCls:r,type:o}=e,s=Z[o]||null;return t?(0,g.wm)(t,n.createElement("span",{className:`${r}-icon`},t),()=>({className:d()(`${r}-icon`,t.props.className)})):n.createElement(s,{className:`${r}-icon`})},N=e=>{let{isClosable:t,prefixCls:r,closeIcon:o,handleClose:s,ariaProps:l}=e,i=!0===o||void 0===o?n.createElement(a.Z,null):o;return t?n.createElement("button",Object.assign({type:"button",onClick:s,className:`${r}-close-icon`,tabIndex:0},l),i):null},S=n.forwardRef((e,t)=>{let{description:r,prefixCls:o,message:s,banner:a,className:l,rootClassName:i,style:c,onMouseEnter:g,onMouseLeave:h,onClick:x,afterClose:b,showIcon:v,closable:y,closeText:j,closeIcon:w,action:Z,id:S}=e,O=k(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[E,I]=n.useState(!1),P=n.useRef(null);n.useImperativeHandle(t,()=>({nativeElement:P.current}));let{getPrefixCls:T,direction:M,closable:D,closeIcon:z,className:B,style:A}=(0,f.dj)("alert"),q=T("alert",o),[H,_,R]=C(q),L=t=>{var r;I(!0),null===(r=e.onClose)||void 0===r||r.call(e,t)},V=n.useMemo(()=>void 0!==e.type?e.type:a?"warning":"info",[e.type,a]),W=n.useMemo(()=>"object"==typeof y&&!!y.closeIcon||!!j||("boolean"==typeof y?y:!1!==w&&null!=w||!!D),[j,w,y,D]),F=!!a&&void 0===v||v,U=d()(q,`${q}-${V}`,{[`${q}-with-description`]:!!r,[`${q}-no-icon`]:!F,[`${q}-banner`]:!!a,[`${q}-rtl`]:"rtl"===M},B,l,i,R,_),G=(0,p.Z)(O,{aria:!0,data:!0}),Q=n.useMemo(()=>"object"==typeof y&&y.closeIcon?y.closeIcon:j||(void 0!==w?w:"object"==typeof D&&D.closeIcon?D.closeIcon:z),[w,y,j,z]),J=n.useMemo(()=>{let e=null!=y?y:D;if("object"==typeof e){let{closeIcon:t}=e;return k(e,["closeIcon"])}return{}},[y,D]);return H(n.createElement(u.ZP,{visible:!E,motionName:`${q}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:b},({className:t,style:o},a)=>n.createElement("div",Object.assign({id:S,ref:(0,m.sQ)(P,a),"data-show":!E,className:d()(U,t),style:Object.assign(Object.assign(Object.assign({},A),c),o),onMouseEnter:g,onMouseLeave:h,onClick:x,role:"alert"},G),F?n.createElement($,{description:r,icon:e.icon,prefixCls:q,type:V}):null,n.createElement("div",{className:`${q}-content`},s?n.createElement("div",{className:`${q}-message`},s):null,r?n.createElement("div",{className:`${q}-description`},r):null),Z?n.createElement("div",{className:`${q}-action`},Z):null,n.createElement(N,{isClosable:W,prefixCls:q,closeIcon:Q,handleClose:L,ariaProps:J}))))});var O=r(31475),E=r(24142),I=r(61792),P=r(50804),T=r(6392),M=r(94977);let D=function(e){function t(){var e,r,n;return(0,O.Z)(this,t),r=t,n=arguments,r=(0,I.Z)(r),(e=(0,T.Z)(this,(0,P.Z)()?Reflect.construct(r,n||[],(0,I.Z)(this).constructor):r.apply(this,n))).state={error:void 0,info:{componentStack:""}},e}return(0,M.Z)(t,e),(0,E.Z)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:r,children:o}=this.props,{error:s,info:a}=this.state,l=(null==a?void 0:a.componentStack)||null,i=void 0===e?(s||"").toString():e;return s?n.createElement(S,{id:r,type:"error",message:i,description:n.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?l:t)}):o}}])}(n.Component);S.ErrorBoundary=D;let z=S},16407:(e,t,r)=>{"use strict";r.d(t,{ZP:()=>w});var n=r(72375),o=r(3729),s=r.n(o),a=r(13743),l=r(84893),i=r(90263),c=r(73101),d=r(727),u=r(11779),p=r(47190);let m=null,g=e=>e(),f=[],h={};function x(){let{getContainer:e,duration:t,rtl:r,maxCount:n,top:o}=h,s=(null==e?void 0:e())||document.body;return{getContainer:()=>s,duration:t,rtl:r,maxCount:n,top:o}}let b=s().forwardRef((e,t)=>{let{messageConfig:r,sync:n}=e,{getPrefixCls:i}=(0,o.useContext)(l.E_),c=h.prefixCls||i("message"),d=(0,o.useContext)(a.J),[p,m]=(0,u.K)(Object.assign(Object.assign(Object.assign({},r),{prefixCls:c}),d.message));return s().useImperativeHandle(t,()=>{let e=Object.assign({},p);return Object.keys(e).forEach(t=>{e[t]=(...e)=>(n(),p[t].apply(p,e))}),{instance:e,sync:n}}),m}),v=s().forwardRef((e,t)=>{let[r,n]=s().useState(x),o=()=>{n(x)};s().useEffect(o,[]);let a=(0,i.w6)(),l=a.getRootPrefixCls(),c=a.getIconPrefixCls(),d=a.getTheme(),u=s().createElement(b,{ref:t,sync:o,messageConfig:r});return s().createElement(i.ZP,{prefixCls:l,iconPrefixCls:c,theme:d},a.holderRender?a.holderRender(u):u)});function y(){if(!m){let e=document.createDocumentFragment(),t={fragment:e};m=t,g(()=>{(0,c.q)()(s().createElement(v,{ref:e=>{let{instance:r,sync:n}=e||{};Promise.resolve().then(()=>{!t.instance&&r&&(t.instance=r,t.sync=n,y())})}}),e)});return}m.instance&&(f.forEach(e=>{let{type:t,skipped:r}=e;if(!r)switch(t){case"open":g(()=>{let t=m.instance.open(Object.assign(Object.assign({},h),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":g(()=>{null==m||m.instance.destroy(e.key)});break;default:g(()=>{var r;let o=(r=m.instance)[t].apply(r,(0,n.Z)(e.args));null==o||o.then(e.resolve),e.setCloseFn(o)})}}),f=[])}let j={open:function(e){let t=(0,p.J)(t=>{let r;let n={type:"open",config:e,resolve:t,setCloseFn:e=>{r=e}};return f.push(n),()=>{r?g(()=>{r()}):n.skipped=!0}});return y(),t},destroy:e=>{f.push({type:"destroy",key:e}),y()},config:function(e){h=Object.assign(Object.assign({},h),e),g(()=>{var e;null===(e=null==m?void 0:m.sync)||void 0===e||e.call(m)})},useMessage:u.Z,_InternalPanelDoNotUseOrYouWillBeFired:d.ZP};["success","info","warning","error","loading"].forEach(e=>{j[e]=(...t)=>(function(e,t){(0,i.w6)();let r=(0,p.J)(r=>{let n;let o={type:e,args:t,resolve:r,setCloseFn:e=>{n=e}};return f.push(o),()=>{n?g(()=>{n()}):o.skipped=!0}});return y(),r})(e,t)});let w=j},90377:(e,t,r)=>{"use strict";r.d(t,{Z:()=>E});var n=r(3729),o=r(34132),s=r.n(o),a=r(24773),l=r(22624),i=r(46164),c=r(29545),d=r(30605),u=r(84893),p=r(92959),m=r(55002),g=r(22989),f=r(96373),h=r(13165);let x=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:n,componentCls:o,calc:s}=e,a=s(n).sub(r).equal(),l=s(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,g.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:l,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},b=e=>{let{lineWidth:t,fontSizeIcon:r,calc:n}=e,o=e.fontSizeSM;return(0,f.IX)(e,{tagFontSize:o,tagLineHeight:(0,p.bf)(n(e.lineHeightSM).mul(o).equal()),tagIconSize:n(r).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new m.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),y=(0,h.I$)("Tag",e=>x(b(e)),v);var j=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let w=n.forwardRef((e,t)=>{let{prefixCls:r,style:o,className:a,checked:l,onChange:i,onClick:c}=e,d=j(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=n.useContext(u.E_),g=p("tag",r),[f,h,x]=y(g),b=s()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:l},null==m?void 0:m.className,a,h,x);return f(n.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},o),null==m?void 0:m.style),className:b,onClick:e=>{null==i||i(!l),null==c||c(e)}})))});var C=r(78701);let k=e=>(0,C.Z)(e,(t,{textColor:r,lightBorderColor:n,lightColor:o,darkColor:s})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:o,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:s,borderColor:s},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),Z=(0,h.bk)(["Tag","preset"],e=>k(b(e)),v),$=(e,t,r)=>{let n=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},N=(0,h.bk)(["Tag","status"],e=>{let t=b(e);return[$(t,"success","Success"),$(t,"processing","Info"),$(t,"error","Error"),$(t,"warning","Warning")]},v);var S=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};let O=n.forwardRef((e,t)=>{let{prefixCls:r,className:o,rootClassName:p,style:m,children:g,icon:f,color:h,onClose:x,bordered:b=!0,visible:v}=e,j=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:C,tag:k}=n.useContext(u.E_),[$,O]=n.useState(!0),E=(0,a.Z)(j,["closeIcon","closable"]);n.useEffect(()=>{void 0!==v&&O(v)},[v]);let I=(0,l.o2)(h),P=(0,l.yT)(h),T=I||P,M=Object.assign(Object.assign({backgroundColor:h&&!T?h:void 0},null==k?void 0:k.style),m),D=w("tag",r),[z,B,A]=y(D),q=s()(D,null==k?void 0:k.className,{[`${D}-${h}`]:T,[`${D}-has-color`]:h&&!T,[`${D}-hidden`]:!$,[`${D}-rtl`]:"rtl"===C,[`${D}-borderless`]:!b},o,p,B,A),H=e=>{e.stopPropagation(),null==x||x(e),e.defaultPrevented||O(!1)},[,_]=(0,i.Z)((0,i.w)(e),(0,i.w)(k),{closable:!1,closeIconRender:e=>{let t=n.createElement("span",{className:`${D}-close-icon`,onClick:H},e);return(0,c.wm)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),H(t)},className:s()(null==e?void 0:e.className,`${D}-close-icon`)}))}}),R="function"==typeof j.onClick||g&&"a"===g.type,L=f||null,V=L?n.createElement(n.Fragment,null,L,g&&n.createElement("span",null,g)):g,W=n.createElement("span",Object.assign({},E,{ref:t,className:q,style:M}),V,_,I&&n.createElement(Z,{key:"preset",prefixCls:D}),P&&n.createElement(N,{key:"status",prefixCls:D}));return z(R?n.createElement(d.Z,{component:"Tag"},W):W)});O.CheckableTag=w;let E=O},2335:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>$});var n=r(95344),o=r(3729),s=r(97854),a=r(90152),l=r(7618),i=r(16407),c=r(27976),d=r(83984),u=r(39470),p=r(11157),m=r(90377),g=r(63724),f=r(43896),h=r(36527),x=r(16408),b=r(14223),v=r(6025),y=r(98507),j=r(3745),w=r(12391),C=r(39593);let{Option:k}=s.default,{Title:Z}=a.default,$=()=>{let[e]=l.Z.useForm(),[t,r]=(0,o.useState)(!1),[s,a]=(0,o.useState)(!1),[k,$]=(0,o.useState)(""),[N,S]=(0,o.useState)(0),[O]=(0,o.useState)([{id:"W1",code:"A1",name:"成型机A线-1号工位",description:"主要用于圆形餐盘生产",status:"active",currentMoldNumber:"M001",currentBatchNumber:"PC202412050001",batchNumberQueue:["PC202412050003"],lastEndTime:"2024-12-05T18:00:00Z",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T14:00:00Z",version:1,lastModifiedBy:"system",lastModifiedAt:"2024-01-15T14:00:00Z"},{id:"W2",code:"B1",name:"成型机B线-1号工位",description:"主要用于方形餐盒生产",status:"active",currentMoldNumber:"M002",currentBatchNumber:"PC202412050002",batchNumberQueue:[],lastEndTime:"2024-12-05T16:30:00Z",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-15T15:00:00Z",version:1,lastModifiedBy:"system",lastModifiedAt:"2024-01-15T15:00:00Z"},{id:"W3",code:"C1",name:"成型机C线-1号工位",description:"备用工位",status:"inactive",currentMoldNumber:null,currentBatchNumber:null,batchNumberQueue:[],lastEndTime:null,createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-10T08:00:00Z",version:1,lastModifiedBy:"system",lastModifiedAt:"2024-01-10T08:00:00Z"}]),[E,I]=(0,o.useState)([{id:"report-001",workstationId:"W1",operatorId:"OP001",operatorName:"张师傅",reportTime:"2024-01-15T14:00:00Z",completedMolds:300,reportType:"normal",isValidated:!0,remark:"正常生产",createdAt:"2024-01-15T14:00:00Z"},{id:"report-002",workstationId:"W2",operatorId:"OP002",operatorName:"李师傅",reportTime:"2024-01-15T15:00:00Z",completedMolds:400,reportType:"normal",isValidated:!0,remark:"正常生产",createdAt:"2024-01-15T15:00:00Z"}]),P=e=>{let t=new Date().toDateString();return E.filter(r=>r.workstationId===e&&new Date(r.reportTime).toDateString()===t).reduce((e,t)=>e+t.completedMolds,0)},T=e=>{let t=new Date().toDateString();return E.filter(r=>r.workstationId===e&&new Date(r.reportTime).toDateString()===t).length},M=e=>{$(e),a(!0)},D=async()=>{if(k){r(!0);try{if(!O.find(e=>e.id===k))throw Error("工位不存在");let t={id:`report-${Date.now()}`,workstationId:k,operatorId:"unknown",operatorName:"未知操作员",reportTime:new Date().toISOString(),completedMolds:N,reportType:"normal",isValidated:!1,remark:"手动报工",createdAt:new Date().toISOString()};I(e=>[...e,t]),i.ZP.success("报工成功！"),a(!1),S(0),e.resetFields()}catch(e){i.ZP.error("报工失败，请重试")}finally{r(!1)}}};return(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"mb-6",children:[n.jsx(Z,{level:2,children:"工作报告"}),n.jsx("p",{className:"text-gray-600",children:"实时监控生产进度，记录工作完成情况"})]}),(0,n.jsxs)(g.Z,{gutter:[16,16],className:"mb-6",children:[n.jsx(c.Z,{xs:24,sm:6,children:n.jsx(d.Z,{children:n.jsx(f.Z,{title:"活跃工位",value:O.filter(e=>"active"===e.status).length,suffix:"个",valueStyle:{color:"#52c41a"},prefix:n.jsx(y.Z,{})})})}),n.jsx(c.Z,{xs:24,sm:6,children:n.jsx(d.Z,{children:n.jsx(f.Z,{title:"今日报工",value:E.filter(e=>new Date(e.reportTime).toDateString()===new Date().toDateString()).length,suffix:"次",valueStyle:{color:"#1890ff"},prefix:n.jsx(j.Z,{})})})}),n.jsx(c.Z,{xs:24,sm:6,children:n.jsx(d.Z,{children:n.jsx(f.Z,{title:"今日产量",value:E.filter(e=>new Date(e.reportTime).toDateString()===new Date().toDateString()).reduce((e,t)=>e+t.completedMolds,0),suffix:"模",valueStyle:{color:"#722ed1"},prefix:n.jsx(w.Z,{})})})}),n.jsx(c.Z,{xs:24,sm:6,children:n.jsx(d.Z,{children:n.jsx(f.Z,{title:"空闲工位",value:O.filter(e=>"inactive"===e.status).length,suffix:"个",valueStyle:{color:"#fa8c16"},prefix:n.jsx(C.Z,{})})})})]}),n.jsx(d.Z,{title:"工位状态",className:"mb-6",children:n.jsx(g.Z,{gutter:[16,16],children:O.map(e=>{let t=P(e.id),r=T(e.id);return n.jsx(c.Z,{xs:24,lg:8,children:n.jsx(d.Z,{title:(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[n.jsx("span",{children:e.name}),n.jsx(u.Z,{status:"active"===e.status?"processing":"default",text:"active"===e.status?"运行中":"空闲"})]}),extra:n.jsx(p.ZP,{type:"primary",size:"small",icon:n.jsx(y.Z,{}),onClick:()=>M(e.id),disabled:"active"!==e.status,children:"报工"}),className:"mb-4",children:(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[n.jsx("span",{className:"text-gray-600",children:"负责人:"}),n.jsx("span",{className:"font-medium",children:"未分配"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[n.jsx("span",{className:"text-gray-600",children:"当前批次:"}),n.jsx("span",{className:"font-medium",children:e.currentBatchNumber||"无"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[n.jsx("span",{className:"text-gray-600",children:"今日产量:"}),(0,n.jsxs)("span",{className:"font-medium text-blue-600",children:[t," 模"]})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[n.jsx("span",{className:"text-gray-600",children:"报工次数:"}),(0,n.jsxs)("span",{className:"font-medium",children:[r," 次"]})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[n.jsx("span",{className:"text-gray-600",children:"状态:"}),n.jsx("span",{className:`font-medium ${"active"===e.status?"text-green-600":"text-red-600"}`,children:"active"===e.status?"运行中":"停用"})]})]})})},e.id)})})}),n.jsx(d.Z,{title:"报工记录",children:n.jsx(h.Z,{columns:[{title:"报工时间",dataIndex:"reportTime",key:"reportTime",width:180,render:e=>new Date(e).toLocaleString()},{title:"工位",dataIndex:"workstationId",key:"workstationId",width:120,render:e=>{let t=O.find(t=>t.id===e);return t?t.name:e}},{title:"操作员",dataIndex:"operatorName",key:"operatorName",width:100},{title:"报工模数",dataIndex:"completedMolds",key:"completedMolds",width:120,render:e=>(0,n.jsxs)("span",{className:"font-medium text-blue-600",children:[e," 模"]})},{title:"类型",dataIndex:"reportType",key:"reportType",width:100,render:e=>n.jsx(m.Z,{color:"normal"===e?"green":"orange",children:"normal"===e?"正常":"异常"})},{title:"备注",dataIndex:"remark",key:"remark",ellipsis:!0}],dataSource:E,rowKey:"id",pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:e=>`共 ${e} 条记录`}})}),n.jsx(x.Z,{title:"工作报告",open:s,onCancel:()=>a(!1),onOk:D,confirmLoading:t,width:600,children:k&&(0,n.jsxs)("div",{className:"space-y-4",children:[n.jsx(b.Z,{message:"报工信息确认",description:"请确认报工信息后提交",type:"info",showIcon:!0}),(0,n.jsxs)(g.Z,{gutter:16,children:[n.jsx(c.Z,{span:12,children:(0,n.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[n.jsx("h4",{className:"font-medium mb-2",children:"工位信息"}),(0,n.jsxs)("p",{children:[n.jsx("strong",{children:"工位:"})," ",O.find(e=>e.id===k)?.name]}),(0,n.jsxs)("p",{children:[n.jsx("strong",{children:"负责人:"})," 未分配"]}),(0,n.jsxs)("p",{children:[n.jsx("strong",{children:"设备:"})," ",O.find(e=>e.id===k)?.name]})]})}),n.jsx(c.Z,{span:12,children:(0,n.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[n.jsx("h4",{className:"font-medium mb-2",children:"生产信息"}),(0,n.jsxs)("p",{children:[n.jsx("strong",{children:"当前批次:"})," ",O.find(e=>e.id===k)?.currentBatchNumber||"无"]}),(0,n.jsxs)("p",{children:[n.jsx("strong",{children:"今日产量:"})," ",P(k)," 模"]}),(0,n.jsxs)("p",{children:[n.jsx("strong",{children:"报工次数:"})," ",T(k)," 次"]})]})})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-medium mb-2",children:"报工模数"}),n.jsx(v.Z,{value:N,onChange:e=>S(e||0),min:1,max:2e3,className:"w-full",placeholder:"请输入完成的模数",addonAfter:"模"})]})]})})]})}},18223:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(25036),o=r(38834);function s({children:e}){return n.jsx(o.Z,{children:e})}},26425:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>s,__esModule:()=>o,default:()=>a});let n=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\production\work-report\page.tsx`),{__esModule:o,$$typeof:s}=n,a=n.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,2345,152,6274,996,6133],()=>r(70621));module.exports=n})();