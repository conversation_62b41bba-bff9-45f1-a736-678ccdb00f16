(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1887],{73707:function(e,t,a){Promise.resolve().then(a.bind(a,17206))},17206:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return N}});var s=a(57437),r=a(2265),n=a(23656),o=a(57416),i=a(55175),c=a(6053),l=a(89511),d=a(65270),u=a(94734),h=a(38302),v=a(28683),g=a(86155),k=a(59189),w=a(50574),m=a(43043),p=a(78466),f=a(51554),I=a(87304),x=a(32779),y=a(3587);function j(e){return/^PC\d{8}\d{4}$/.test(e)&&14===e.length}async function W(){let e=await x.dataAccessManager.workstations.getActiveWorkstations();if("success"!==e.status||!e.data)return{totalWorkstations:0,workstationsWithQueues:0,totalQueueItems:0,validBatchNumbers:0,invalidWorkOrderIds:0,otherInvalidItems:0,details:[]};let t=e.data,a=0,s=0,r=0,n=0,o=t.map(e=>{let t=e.batchNumberQueue||[],o=[],i=[],c=[];return t.forEach(e=>{(a++,j(e))?(o.push(e),s++):/^wo_\d+_[a-z0-9]+$/.test(e)?(i.push(e),r++):(c.push(e),n++)}),{workstationId:e.id,workstationCode:e.code,workstationName:e.name,queueLength:t.length,validItems:o,invalidWorkOrderIds:i,otherInvalidItems:c}});return{totalWorkstations:t.length,workstationsWithQueues:t.filter(e=>{var t;return((null===(t=e.batchNumberQueue)||void 0===t?void 0:t.length)||0)>0}).length,totalQueueItems:a,validBatchNumbers:s,invalidWorkOrderIds:r,otherInvalidItems:n,details:o}}async function S(){var e;console.warn("⚠️ [workstationQueueCleaner] cleanAllWorkstationQueues 函数已废弃，建议使用dataAccessManager重新实现");let t=await x.dataAccessManager.workstations.getWorkstations();if("success"!==t.status||!(null===(e=t.data)||void 0===e?void 0:e.items))throw Error("获取工位数据失败");let a=t.data.items,s=0,r=0,n=[],o=a.map(async e=>{let t=e.batchNumberQueue||[];if(0===t.length)return null;let a=t.filter(e=>j(e)),n=t.filter(e=>!j(e));if(n.length>0)try{let o=await y.r.updateWorkstation(e.id,{batchNumberQueue:a},{source:"system",operation:"queue_management",userId:"system",reason:"清理工位队列中的无效数据"},e.version);if(o.success)return s++,r+=n.length,{workstationId:e.id,workstationCode:e.code,originalQueueLength:t.length,cleanedQueueLength:a.length,removedItems:n};console.error("❌ 工位 ".concat(e.code," 队列清理失败:"),o.error)}catch(t){console.error("❌ 工位 ".concat(e.code," 队列清理异常:"),t)}return null});return n.push(...(await Promise.all(o)).filter(e=>null!==e)),{totalWorkstations:a.length,cleanedWorkstations:s,removedItems:r,cleanupDetails:n}}let{Title:Z,Text:D,Paragraph:b}=n.default;function C(){let{modal:e}=o.Z.useApp(),[t,a]=(0,r.useState)(null),[n,x]=(0,r.useState)(null),[y,j]=(0,r.useState)(!1),[D,C]=(0,r.useState)(!1),N=()=>{j(!0);try{let e=W();a(e),i.ZP.success("队列数据分析完成")}catch(e){i.ZP.error("分析失败: "+e.message)}finally{j(!1)}};return(0,r.useEffect)(()=>{N()},[]),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)(Z,{level:2,children:[(0,s.jsx)(p.Z,{})," 工位队列数据清理工具"]}),(0,s.jsx)(b,{children:"此工具用于分析和清理工位队列中的无效数据。工位队列应该只包含有效的批次号格式（PC开头的14位格式）， 而不应包含工单ID格式（wo_开头的格式）。"}),(0,s.jsx)(l.Z,{className:"mb-6",children:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(u.ZP,{type:"primary",icon:(0,s.jsx)(f.Z,{}),onClick:N,loading:y,children:"分析队列数据"}),(0,s.jsx)(u.ZP,{type:"primary",danger:!0,icon:(0,s.jsx)(p.Z,{}),onClick:()=>{e.confirm({title:"确认清理队列数据",content:(0,s.jsxs)("div",{children:[(0,s.jsx)(b,{children:"此操作将清理所有工位队列中的无效数据（工单ID格式），只保留有效的批次号格式数据。"}),(0,s.jsxs)(b,{type:"warning",children:[(0,s.jsx)(m.Z,{})," 此操作不可撤销，请确认继续。"]})]}),onOk:async()=>{C(!0);try{let e=await S();x(e),i.ZP.success("清理完成！共清理 ".concat((null==e?void 0:e.removedItems)||0," 个无效项目")),N()}catch(e){i.ZP.error("清理失败: "+e.message)}finally{C(!1)}}})},loading:D,disabled:!t||0===t.invalidWorkOrderIds,children:"清理无效数据"})]})}),t&&(0,s.jsxs)(l.Z,{title:"队列数据分析结果",className:"mb-6",children:[(0,s.jsxs)(h.Z,{gutter:16,children:[(0,s.jsx)(v.Z,{span:6,children:(0,s.jsx)(g.Z,{title:"总工位数",value:t.totalWorkstations,prefix:(0,s.jsx)(I.Z,{})})}),(0,s.jsx)(v.Z,{span:6,children:(0,s.jsx)(g.Z,{title:"有队列的工位",value:t.workstationsWithQueues,valueStyle:{color:"#1890ff"}})}),(0,s.jsx)(v.Z,{span:6,children:(0,s.jsx)(g.Z,{title:"有效批次号",value:t.validBatchNumbers,valueStyle:{color:"#52c41a"}})}),(0,s.jsx)(v.Z,{span:6,children:(0,s.jsx)(g.Z,{title:"无效工单ID",value:t.invalidWorkOrderIds,valueStyle:{color:t.invalidWorkOrderIds>0?"#ff4d4f":"#52c41a"}})})]}),t.invalidWorkOrderIds>0&&(0,s.jsx)(k.Z,{className:"mt-4",message:"发现无效数据",description:"检测到 ".concat(t.invalidWorkOrderIds," 个工单ID格式的无效数据，建议立即清理。"),type:"warning",showIcon:!0}),0===t.invalidWorkOrderIds&&(0,s.jsx)(k.Z,{className:"mt-4",message:"数据格式正确",description:"所有工位队列数据格式正确，无需清理。",type:"success",showIcon:!0})]}),t&&(0,s.jsx)(l.Z,{title:"工位队列详细分析",className:"mb-6",children:(0,s.jsx)(w.Z,{columns:[{title:"工位编码",dataIndex:"workstationCode",key:"workstationCode",width:100},{title:"工位名称",dataIndex:"workstationName",key:"workstationName",width:200},{title:"队列长度",dataIndex:"queueLength",key:"queueLength",width:100,render:e=>(0,s.jsx)(c.Z,{color:e>0?"blue":"default",children:e})},{title:"有效批次号",dataIndex:"validItems",key:"validItems",width:120,render:e=>(0,s.jsx)(c.Z,{color:"green",children:e.length})},{title:"无效工单ID",dataIndex:"invalidWorkOrderIds",key:"invalidWorkOrderIds",width:120,render:e=>(0,s.jsx)(c.Z,{color:e.length>0?"red":"default",children:e.length})},{title:"其他无效项",dataIndex:"otherInvalidItems",key:"otherInvalidItems",width:120,render:e=>(0,s.jsx)(c.Z,{color:e.length>0?"orange":"default",children:e.length})}],dataSource:t.details,rowKey:"workstationId",pagination:{pageSize:10},scroll:{x:800}})}),n&&(0,s.jsxs)(l.Z,{title:"清理结果",className:"mb-6",children:[(0,s.jsxs)(h.Z,{gutter:16,className:"mb-4",children:[(0,s.jsx)(v.Z,{span:8,children:(0,s.jsx)(g.Z,{title:"清理工位数",value:n.cleanedWorkstations,valueStyle:{color:"#52c41a"}})}),(0,s.jsx)(v.Z,{span:8,children:(0,s.jsx)(g.Z,{title:"清理项目数",value:n.removedItems,valueStyle:{color:"#ff4d4f"}})}),(0,s.jsx)(v.Z,{span:8,children:(0,s.jsx)(g.Z,{title:"清理完成率",value:n.totalWorkstations>0?Math.round(n.cleanedWorkstations/n.totalWorkstations*100):0,suffix:"%",valueStyle:{color:"#1890ff"}})})]}),n.cleanupDetails.length>0&&(0,s.jsx)(w.Z,{columns:[{title:"工位编码",dataIndex:"workstationCode",key:"workstationCode",width:100},{title:"原队列长度",dataIndex:"originalQueueLength",key:"originalQueueLength",width:120},{title:"清理后长度",dataIndex:"cleanedQueueLength",key:"cleanedQueueLength",width:120},{title:"清理项目",dataIndex:"removedItems",key:"removedItems",render:e=>(0,s.jsx)("div",{style:{maxWidth:300},children:e.map((e,t)=>(0,s.jsx)(c.Z,{color:"red",style:{marginBottom:4},children:e.length>20?"".concat(e.substring(0,20),"..."):e},t))})}],dataSource:n.cleanupDetails,rowKey:"workstationId",pagination:{pageSize:10},scroll:{x:800}})]})]})}function N(){return(0,s.jsx)(o.Z,{children:(0,s.jsx)(C,{})})}},3587:function(e,t,a){"use strict";a.d(t,{J:function(){return r},r:function(){return n}});var s=a(40518);class r{static getInstance(){return r.instance||(r.instance=new r),r.instance}async updateWorkstation(e,t,a,s){let r=Date.now();try{var n;console.log("\uD83D\uDD04 [WorkstationUpdateService] 开始更新工位 ".concat(e),{source:a.source,operation:a.operation,expectedVersion:s,reason:a.reason});let o=await this.validatePermissions(e,a);if(!o.allowed)return{success:!1,error:"权限不足: ".concat(o.reason)};let i=await this.validateBusinessRules(e,t,a);if(!i.valid)return{success:!1,error:"业务规则验证失败: ".concat(i.reason),warnings:i.warnings};let c=this.getModifiedBy(a),l=await this.workstationService.update(e,t,s,c);if("success"!==l.status){if(l.code&&"VERSION_CONFLICT"===l.code.toString()){let e=l.data;return{success:!1,error:l.message||"版本冲突",conflictInfo:{expectedVersion:s||0,currentVersion:(null==e?void 0:e.currentVersion)||0,lastModifiedBy:"unknown",lastModifiedAt:new Date().toISOString()}}}return{success:!1,error:l.message||"更新失败"}}return l.data&&await this.logUpdateOperation(e,t,a,l.data,r),await this.clearWorkstationCaches(e,a.operation),await this.clearDataAccessManagerCaches(),l.data&&await this.triggerPostUpdateActions(e,t,a,l.data),console.log("✅ [WorkstationUpdateService] 工位 ".concat(e," 更新成功"),{duration:Date.now()-r,newVersion:null===(n=l.data)||void 0===n?void 0:n.version}),{success:!0,workstation:l.data,warnings:i.warnings}}catch(t){return console.error("❌ [WorkstationUpdateService] 工位 ".concat(e," 更新失败:"),t),{success:!1,error:t instanceof Error?t.message:"未知错误"}}}async batchUpdateWorkstations(e,t){console.log("\uD83D\uDD04 [WorkstationUpdateService] 开始批量更新 ".concat(e.length," 个工位"));let a=[],s=0,r=0,n=0,o=0,i=0;for(let c of e){let e=await this.updateWorkstation(c.workstationId,c.updates,t,c.expectedVersion);a.push({workstationId:c.workstationId,result:e}),e.success?s++:(r++,e.conflictInfo?n++:o++),e.warnings&&e.warnings.length>0&&i++}let c={totalCount:e.length,successCount:s,failureCount:r,results:a,summary:{conflicts:n,errors:o,warnings:i}};return console.log("✅ [WorkstationUpdateService] 批量更新完成",c.summary),c}async validatePermissions(e,t){switch(t.source){case"user":if(!t.userId)return{allowed:!1,reason:"用户操作必须提供用户ID"};break;case"system":case"scheduling":case"batch":break;default:return{allowed:!1,reason:"未知的更新来源"}}return{allowed:!0}}async validateBusinessRules(e,t,a){let s=[],r=await this.workstationService.getWorkstationById(e);if("success"!==r.status)return{valid:!1,reason:"无法获取当前工位状态"};let n=r.data;if(!n)return{valid:!1,reason:"工位数据不存在"};if(t.status&&t.status!==n.status){let e=this.validateStatusTransition(n.status,t.status,a);if(!e.valid)return{valid:!1,reason:e.reason};e.warnings&&s.push(...e.warnings)}if(this.hasProductionStateChanges(t)){let e=this.validateProductionStateChanges(n,t,a);if(!e.valid)return{valid:!1,reason:e.reason};e.warnings&&s.push(...e.warnings)}return{valid:!0,warnings:s.length>0?s:void 0}}validateStatusTransition(e,t,a){var s;return(null===(s=({active:["inactive"],inactive:["active"]})[e])||void 0===s?void 0:s.includes(t))?{valid:!0}:{valid:!1,reason:"不允许从状态 ".concat(e," 转换到 ").concat(t)}}hasProductionStateChanges(e){return!(void 0===e.currentMoldNumber&&void 0===e.currentBatchNumber&&void 0===e.batchNumberQueue&&void 0===e.lastEndTime)}validateProductionStateChanges(e,t,a){let s=[];return"scheduling"!==a.source&&"system"!==a.source?{valid:!1,reason:"只有排程系统可以修改工位的生产状态"}:(t.currentMoldNumber&&t.currentBatchNumber&&s.push("请确认模具编号与批次号匹配"),{valid:!0,warnings:s.length>0?s:void 0})}getModifiedBy(e){switch(e.source){case"user":return e.userId||"unknown_user";case"system":return"system";case"scheduling":return"scheduling_service";case"batch":return"batch_operation";default:return"unknown"}}async logUpdateOperation(e,t,a,s,r){console.log("\uD83D\uDCDD [WorkstationUpdateService] 审计日志:",{timestamp:new Date().toISOString(),workstationId:e,operation:a.operation,source:a.source,userId:a.userId,reason:a.reason,updates:Object.keys(t),newVersion:s.version,duration:Date.now()-r,metadata:a.metadata})}async clearWorkstationCaches(e,t){try{let{dataAccessManager:t}=a(32779);t.clearDataTypeCache("workstations",[e]),t.clearServiceCache("WorkstationService")}catch(t){console.error("[WorkstationUpdateService] 清除工位 ".concat(e," 缓存失败:"),t)}}async clearDataAccessManagerCaches(){try{let{dataAccessManager:e}=await Promise.resolve().then(a.bind(a,32779)),t=0;["WorkstationService:*","WorkstationService.*"].forEach(a=>{let s=e.clearServiceCache("WorkstationService");t+=s});let s=e.clearDataTypeCache("workstations");t+=s,console.log("[WorkstationUpdateService] 清理DataAccessManager缓存: ".concat(t," 个条目"))}catch(e){console.error("[WorkstationUpdateService] 清理DataAccessManager缓存失败:",e)}}async triggerPostUpdateActions(e,t,a,s){console.log("\uD83D\uDD14 [WorkstationUpdateService] 触发后置处理: ".concat(e))}constructor(){this.workstationService=s.D.getInstance()}}let n=r.getInstance()}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,7416,2897,3656,4112,2779,2971,4938,1744],function(){return e(e.s=73707)}),_N_E=e.O()}]);