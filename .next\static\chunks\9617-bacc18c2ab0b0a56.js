"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9617],{99617:function(e,t,n){n.d(t,{Z:function(){return I}});var l=n(2265),a=n(42744),o=n.n(a),c=n(43313),i=n(57499),s=n(10693),r=n(65471),d={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1};let b=l.createContext({});var m=n(79173),g=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(n[l[a]]=e[l[a]]);return n};let p=e=>(0,m.Z)(e).map(e=>Object.assign(Object.assign({},null==e?void 0:e.props),{key:e.key}));var u=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(n[l[a]]=e[l[a]]);return n},f=(e,t)=>{let[n,a]=(0,l.useMemo)(()=>{let n,l,a,o;return n=[],l=[],a=!1,o=0,t.filter(e=>e).forEach(t=>{let{filled:c}=t,i=u(t,["filled"]);if(c){l.push(i),n.push(l),l=[],o=0;return}let s=e-o;(o+=t.span||1)>=e?(o>e?(a=!0,l.push(Object.assign(Object.assign({},i),{span:s}))):l.push(i),n.push(l),l=[],o=0):l.push(i)}),l.length>0&&n.push(l),[n=n.map(t=>{let n=t.reduce((e,t)=>e+(t.span||1),0);if(n<e){let l=t[t.length-1];l.span=e-(n-(l.span||1))}return t}),a]},[t,e]);return n},O=e=>{let{itemPrefixCls:t,component:n,span:a,className:c,style:i,labelStyle:s,contentStyle:r,bordered:d,label:m,content:g,colon:p,type:u,styles:f}=e,{classNames:O}=l.useContext(b);return d?l.createElement(n,{className:o()({["".concat(t,"-item-label")]:"label"===u,["".concat(t,"-item-content")]:"content"===u,["".concat(null==O?void 0:O.label)]:"label"===u,["".concat(null==O?void 0:O.content)]:"content"===u},c),style:i,colSpan:a},null!=m&&l.createElement("span",{style:Object.assign(Object.assign({},s),null==f?void 0:f.label)},m),null!=g&&l.createElement("span",{style:Object.assign(Object.assign({},s),null==f?void 0:f.content)},g)):l.createElement(n,{className:o()("".concat(t,"-item"),c),style:i,colSpan:a},l.createElement("div",{className:"".concat(t,"-item-container")},(m||0===m)&&l.createElement("span",{className:o()("".concat(t,"-item-label"),null==O?void 0:O.label,{["".concat(t,"-item-no-colon")]:!p}),style:Object.assign(Object.assign({},s),null==f?void 0:f.label)},m),(g||0===g)&&l.createElement("span",{className:o()("".concat(t,"-item-content"),null==O?void 0:O.content),style:Object.assign(Object.assign({},r),null==f?void 0:f.content)},g)))};function y(e,t,n){let{colon:a,prefixCls:o,bordered:c}=t,{component:i,type:s,showLabel:r,showContent:d,labelStyle:b,contentStyle:m,styles:g}=n;return e.map((e,t)=>{let{label:n,children:p,prefixCls:u=o,className:f,style:y,labelStyle:j,contentStyle:h,span:v=1,key:x,styles:S}=e;return"string"==typeof i?l.createElement(O,{key:"".concat(s,"-").concat(x||t),className:f,style:y,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},b),null==g?void 0:g.label),j),null==S?void 0:S.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},m),null==g?void 0:g.content),h),null==S?void 0:S.content)},span:v,colon:a,component:i,itemPrefixCls:u,bordered:c,label:r?n:null,content:d?p:null,type:s}):[l.createElement(O,{key:"label-".concat(x||t),className:f,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},b),null==g?void 0:g.label),y),j),null==S?void 0:S.label),span:1,colon:a,component:i[0],itemPrefixCls:u,bordered:c,label:n,type:"label"}),l.createElement(O,{key:"content-".concat(x||t),className:f,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},m),null==g?void 0:g.content),y),h),null==S?void 0:S.content),span:2*v-1,component:i[1],itemPrefixCls:u,bordered:c,content:p,type:"content"})]})}var j=e=>{let t=l.useContext(b),{prefixCls:n,vertical:a,row:o,index:c,bordered:i}=e;return a?l.createElement(l.Fragment,null,l.createElement("tr",{key:"label-".concat(c),className:"".concat(n,"-row")},y(o,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),l.createElement("tr",{key:"content-".concat(c),className:"".concat(n,"-row")},y(o,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):l.createElement("tr",{key:c,className:"".concat(n,"-row")},y(o,e,Object.assign({component:i?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))},h=n(58489),v=n(11303),x=n(78387),S=n(12711);let E=e=>{let{componentCls:t,labelBg:n}=e;return{["&".concat(t,"-bordered")]:{["> ".concat(t,"-view")]:{border:"".concat((0,h.bf)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit),"> table":{tableLayout:"auto"},["".concat(t,"-row")]:{borderBottom:"".concat((0,h.bf)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit),"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:e.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:e.borderRadiusLG}},["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat((0,h.bf)(e.padding)," ").concat((0,h.bf)(e.paddingLG)),borderInlineEnd:"".concat((0,h.bf)(e.lineWidth)," ").concat(e.lineType," ").concat(e.colorSplit),"&:last-child":{borderInlineEnd:"none"}},["> ".concat(t,"-item-label")]:{color:e.colorTextSecondary,backgroundColor:n,"&::after":{display:"none"}}}},["&".concat(t,"-middle")]:{["".concat(t,"-row")]:{["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat((0,h.bf)(e.paddingSM)," ").concat((0,h.bf)(e.paddingLG))}}},["&".concat(t,"-small")]:{["".concat(t,"-row")]:{["> ".concat(t,"-item-label, > ").concat(t,"-item-content")]:{padding:"".concat((0,h.bf)(e.paddingXS)," ").concat((0,h.bf)(e.padding))}}}}}},w=e=>{let{componentCls:t,extraColor:n,itemPaddingBottom:l,itemPaddingEnd:a,colonMarginRight:o,colonMarginLeft:c,titleMarginBottom:i}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,v.Wf)(e)),E(e)),{"&-rtl":{direction:"rtl"},["".concat(t,"-header")]:{display:"flex",alignItems:"center",marginBottom:i},["".concat(t,"-title")]:Object.assign(Object.assign({},v.vS),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),["".concat(t,"-extra")]:{marginInlineStart:"auto",color:n,fontSize:e.fontSize},["".concat(t,"-view")]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},["".concat(t,"-row")]:{"> th, > td":{paddingBottom:l,paddingInlineEnd:a},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},["".concat(t,"-item-label")]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:"".concat((0,h.bf)(c)," ").concat((0,h.bf)(o))},["&".concat(t,"-item-no-colon::after")]:{content:'""'}},["".concat(t,"-item-no-label")]:{"&::after":{margin:0,content:'""'}},["".concat(t,"-item-content")]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},["".concat(t,"-item")]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",["".concat(t,"-item-label")]:{display:"inline-flex",alignItems:"baseline"},["".concat(t,"-item-content")]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{["".concat(t,"-row")]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{["".concat(t,"-row")]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}};var N=(0,x.I$)("Descriptions",e=>w((0,S.IX)(e,{})),e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText})),C=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(n[l[a]]=e[l[a]]);return n};let k=e=>{let{prefixCls:t,title:n,extra:a,column:m,colon:u=!0,bordered:O,layout:y,children:h,className:v,rootClassName:x,style:S,size:E,labelStyle:w,contentStyle:k,styles:I,items:P,classNames:B}=e,L=C(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:M,direction:z,className:T,style:W,classNames:G,styles:H}=(0,i.dj)("descriptions"),R=M("descriptions",t),X=(0,r.Z)(),Z=l.useMemo(()=>{var e;return"number"==typeof m?m:null!==(e=(0,c.m9)(X,Object.assign(Object.assign({},d),m)))&&void 0!==e?e:3},[X,m]),_=function(e,t,n){let a=l.useMemo(()=>t||p(n),[t,n]);return l.useMemo(()=>a.map(t=>{var{span:n}=t,l=g(t,["span"]);return"filled"===n?Object.assign(Object.assign({},l),{filled:!0}):Object.assign(Object.assign({},l),{span:"number"==typeof n?n:(0,c.m9)(e,n)})}),[a,e])}(X,P,h),A=(0,s.Z)(E),F=f(Z,_),[D,$,q]=N(R),J=l.useMemo(()=>({labelStyle:w,contentStyle:k,styles:{content:Object.assign(Object.assign({},H.content),null==I?void 0:I.content),label:Object.assign(Object.assign({},H.label),null==I?void 0:I.label)},classNames:{label:o()(G.label,null==B?void 0:B.label),content:o()(G.content,null==B?void 0:B.content)}}),[w,k,I,B,G,H]);return D(l.createElement(b.Provider,{value:J},l.createElement("div",Object.assign({className:o()(R,T,G.root,null==B?void 0:B.root,{["".concat(R,"-").concat(A)]:A&&"default"!==A,["".concat(R,"-bordered")]:!!O,["".concat(R,"-rtl")]:"rtl"===z},v,x,$,q),style:Object.assign(Object.assign(Object.assign(Object.assign({},W),H.root),null==I?void 0:I.root),S)},L),(n||a)&&l.createElement("div",{className:o()("".concat(R,"-header"),G.header,null==B?void 0:B.header),style:Object.assign(Object.assign({},H.header),null==I?void 0:I.header)},n&&l.createElement("div",{className:o()("".concat(R,"-title"),G.title,null==B?void 0:B.title),style:Object.assign(Object.assign({},H.title),null==I?void 0:I.title)},n),a&&l.createElement("div",{className:o()("".concat(R,"-extra"),G.extra,null==B?void 0:B.extra),style:Object.assign(Object.assign({},H.extra),null==I?void 0:I.extra)},a)),l.createElement("div",{className:"".concat(R,"-view")},l.createElement("table",null,l.createElement("tbody",null,F.map((e,t)=>l.createElement(j,{key:t,index:t,colon:u,prefixCls:R,vertical:"vertical"===y,bordered:O,row:e}))))))))};k.Item=e=>{let{children:t}=e;return t};var I=k}}]);