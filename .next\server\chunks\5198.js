"use strict";exports.id=5198,exports.ids=[5198],exports.modules={55198:(t,e,a)=>{a.d(e,{SchedulingStateManager:()=>i});var r=a(37637);class i{static{this.changeHistory=[]}static async createWorkstationSnapshot(t){try{let e=await r.dataAccessManager.workstations.getWorkstations();if("success"!==e.status||!e.data?.items)throw Error("获取工位数据失败");let a=e.data.items,i={id:`snapshot_${Date.now()}`,timestamp:new Date().toISOString(),operation:"snapshot",description:t||"排程前工位状态快照",affectedWorkstations:a.map(t=>t.id),affectedWorkOrders:[]};return this.changeHistory.push(i),console.log(`📸 [SchedulingStateManager] 创建工位状态快照: ${a.length} 个工位`),a}catch(t){throw console.error("❌ [SchedulingStateManager] 创建工位状态快照失败:",t),t}}static createVirtualWorkstationStates(t){return t.map(t=>({...JSON.parse(JSON.stringify(t)),isVirtual:!0,originalState:{...t}}))}static calculateVirtualStateChanges(t,e){let a=e.reduce((t,e)=>{let a=e.workstationCode;return t[a]||(t[a]=[]),t[a].push(e),t},{}),r=t.map(t=>{let e=a[t.code]||[];if(0===e.length)return t;let r=e.sort((t,e)=>new Date(t.plannedEndTime).getTime()-new Date(e.plannedEndTime).getTime()),i=r[r.length-1],n={...t,lastEndTime:i.plannedEndTime,currentMoldNumber:i.formingMoldNumber,currentBatchNumber:i.batchNumber,batchNumberQueue:[...t.batchNumberQueue||[],...e.map(t=>t.batchNumber)]};return console.log(`🔄 [SchedulingStateManager] 虚拟工位状态更新: ${t.code}`),n}),i={id:`virtual_calc_${Date.now()}`,timestamp:new Date().toISOString(),operation:"virtual_calculation",description:"虚拟工位状态计算完成",affectedWorkstations:[...new Set(e.map(t=>t.workstationCode))],affectedWorkOrders:e.map(t=>t.workOrderId)};return this.changeHistory.push(i),r}static async applySchedulingToActualState(t){throw console.warn("⚠️ [SchedulingStateManager] applySchedulingToActualState 方法已废弃，请使用 SameMoldPrioritySchedulingService.applySchedulingResults"),Error("此方法已废弃，请使用 SameMoldPrioritySchedulingService.applySchedulingResults")}static rollbackStateChanges(t){console.warn("⚠️ [SchedulingStateManager] rollbackStateChanges 方法已废弃"),console.warn("排程回滚应该通过重新计算和应用来实现，而不是依赖快照恢复");let e={id:`rollback_attempt_${Date.now()}`,timestamp:new Date().toISOString(),operation:"virtual_calculation",description:"尝试回滚操作（已废弃）",affectedWorkstations:[],affectedWorkOrders:[]};this.changeHistory.push(e)}static calculateWorkstationUpdates(t){return console.warn("⚠️ [SchedulingStateManager] calculateWorkstationUpdates 方法已废弃"),[]}static getChangeHistory(){return[...this.changeHistory]}static clearChangeHistory(){this.changeHistory=[]}static getCurrentSchedulingState(){return{changeHistoryCount:this.changeHistory.length,lastChangeTime:this.changeHistory.length>0?this.changeHistory[this.changeHistory.length-1].timestamp:null,operations:this.changeHistory.map(t=>({id:t.id,operation:t.operation,description:t.description,timestamp:t.timestamp}))}}}}};