(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8522],{96619:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(13428),i=a(2265),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"},l=a(46614),r=i.forwardRef(function(e,t){return i.createElement(l.Z,(0,s.Z)({},e,{ref:t,icon:o}))})},51554:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(13428),i=a(2265),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"},l=a(46614),r=i.forwardRef(function(e,t){return i.createElement(l.Z,(0,s.Z)({},e,{ref:t,icon:o}))})},72973:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(13428),i=a(2265),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M251.2 387H320v68.8c0 1.8 1.8 3.2 4 3.2h48c2.2 0 4-1.4 4-3.3V387h68.8c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H376v-68.8c0-1.8-1.8-3.2-4-3.2h-48c-2.2 0-4 1.4-4 3.2V331h-68.8c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm328 0h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 265h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 104h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm-195.7-81l61.2-74.9c4.3-5.2.7-13.1-5.9-13.1H388c-2.3 0-4.5 1-5.9 2.9l-34 41.6-34-41.6a7.85 7.85 0 00-5.9-2.9h-50.9c-6.6 0-10.2 7.9-5.9 13.1l61.2 74.9-62.7 76.8c-4.4 5.2-.8 13.1 5.8 13.1h50.8c2.3 0 4.5-1 5.9-2.9l35.5-43.5 35.5 43.5c1.5 1.8 3.7 2.9 5.9 2.9h50.8c6.6 0 10.2-7.9 5.9-13.1L383.5 675zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-36 732H180V180h664v664z"}}]},name:"calculator",theme:"outlined"},l=a(46614),r=i.forwardRef(function(e,t){return i.createElement(l.Z,(0,s.Z)({},e,{ref:t,icon:o}))})},40856:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(13428),i=a(2265),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"},l=a(46614),r=i.forwardRef(function(e,t){return i.createElement(l.Z,(0,s.Z)({},e,{ref:t,icon:o}))})},64370:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(13428),i=a(2265),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"},l=a(46614),r=i.forwardRef(function(e,t){return i.createElement(l.Z,(0,s.Z)({},e,{ref:t,icon:o}))})},43043:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(13428),i=a(2265),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"},l=a(46614),r=i.forwardRef(function(e,t){return i.createElement(l.Z,(0,s.Z)({},e,{ref:t,icon:o}))})},13924:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(13428),i=a(2265),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM504 618H320c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM312 490v48c0 4.4 3.6 8 8 8h384c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H320c-4.4 0-8 3.6-8 8z"}}]},name:"file-text",theme:"outlined"},l=a(46614),r=i.forwardRef(function(e,t){return i.createElement(l.Z,(0,s.Z)({},e,{ref:t,icon:o}))})},96:function(e,t,a){"use strict";a.d(t,{Z:function(){return r}});var s=a(13428),i=a(2265),o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 518H506V160c0-4.4-3.6-8-8-8h-26a398.46 398.46 0 00-282.8 117.1 398.19 398.19 0 00-85.7 127.1A397.61 397.61 0 0072 552a398.46 398.46 0 00117.1 282.8c36.7 36.7 79.5 65.6 127.1 85.7A397.61 397.61 0 00472 952a398.46 398.46 0 00282.8-117.1c36.7-36.7 65.6-79.5 85.7-127.1A397.61 397.61 0 00872 552v-26c0-4.4-3.6-8-8-8zM705.7 787.8A331.59 331.59 0 01470.4 884c-88.1-.4-170.9-34.9-233.2-97.2C174.5 724.1 140 640.7 140 552c0-88.7 34.5-172.1 97.2-234.8 54.6-54.6 124.9-87.9 200.8-95.5V586h364.3c-7.7 76.3-41.3 147-96.6 201.8zM952 462.4l-2.6-28.2c-8.5-92.1-49.4-179-115.2-244.6A399.4 399.4 0 00589 74.6L560.7 72c-4.7-.4-8.7 3.2-8.7 7.9V464c0 4.4 3.6 8 8 8l384-1c4.7 0 8.4-4 8-8.6zm-332.2-58.2V147.6a332.24 332.24 0 01166.4 89.8c45.7 45.6 77 103.6 90 166.1l-256.4.7z"}}]},name:"pie-chart",theme:"outlined"},l=a(46614),r=i.forwardRef(function(e,t){return i.createElement(l.Z,(0,s.Z)({},e,{ref:t,icon:o}))})},34841:function(e,t,a){Promise.resolve().then(a.bind(a,83731))},83731:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return T}});var s=a(57437),i=a(2265),o=a(27296),l=a(50030),r=a(57416),c=a(89198),n=a(65270),d=a(94734),m=a(9427),u=a(38302),h=a(28683),x=a(89511),p=a(86155),f=a(34863),g=a(2012),j=a(50574),v=a(59189),Z=a(37044),C=a(47628),M=a(39992),y=a(75123),w=a(13924),N=a(87304),b=a(72973),P=a(64370),I=a(82765),k=a(51554),S=a(40856),A=a(96619),R=a(43043),z=a(96);class V{static calculateFormingCost(e,t){let a=e.reduce((e,t)=>e+t.completedMolds,0),s=t.formingPiecePrice,i=new Map;e.forEach(e=>{let t=i.get(e.operatorId)||{molds:0,operatorName:e.operatorName};t.molds+=e.completedMolds,i.set(e.operatorId,t)});let o=Array.from(i.entries()).map(e=>{let[t,a]=e;return{operatorId:t,operatorName:a.operatorName,completedMolds:a.molds,pieceRate:s,wage:a.molds*s}});return{totalMolds:a,pieceRate:s,totalWage:o.reduce((e,t)=>e+t.wage,0),operatorRecords:o}}static calculateHotPressCost(e,t){let a=e.filter(e=>e.isActive),s=a.reduce((e,t)=>e+t.bindingMolds,0),i=t.hotPressPiecePrice,o=a.map(e=>({employeeId:e.employeeId,employeeName:e.employeeName,bindingMolds:e.bindingMolds,pieceRate:i,wage:e.bindingMolds*i}));return{totalMolds:s,pieceRate:i,totalWage:o.reduce((e,t)=>e+t.wage,0),employeeBindings:o}}static calculateMaterialCost(e,t,a,s){let i=t*a/1e6;return{materialCode:"MAT-".concat(e),materialName:"".concat(e,"原料"),usedQuantity:i,unitPrice:s,totalCost:i*s}}static executeFullCostCalculation(e,t,a,s){let i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:250,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:1500,l=this.calculateFormingCost(a,t),r=this.calculateHotPressCost(s,t),c=this.calculateMaterialCost(t.modelCode,l.totalMolds,i,o),n=l.totalWage+r.totalWage+c.totalCost,d=l.totalMolds>0?n/l.totalMolds:0,m=l.totalMolds*t.productPrice,u=m-n;return{id:"cost-".concat(e,"-").concat(Date.now()),calculationDate:new Date().toISOString().split("T")[0],productionOrderId:e,productModelCode:t.modelCode,totalMolds:l.totalMolds,formingCost:l,hotPressCost:r,materialCost:c,totalCost:Math.round(100*n)/100,unitCost:Math.round(100*d)/100,totalRevenue:Math.round(1e3*m)/1e3,totalProfit:Math.round(1e3*u)/1e3,profitMargin:Math.round(100*(m>0?u/m*100:0))/100,createdAt:new Date().toISOString()}}static performCostReconciliation(e,t,a,s,i,o){let l=t.reduce((e,t)=>e+t.bindingMolds,0),r=a+s,c=l-r,n=r>0?100*Math.abs(c/r):0,d=n<=5;return{id:"reconciliation-".concat(e,"-").concat(Date.now()),reconciliationDate:new Date().toISOString().split("T")[0],productModelCode:e,hotPressQuantityTotal:l,warehouseQuantity:a,onSiteQuantity:s,bindingQuantityTotal:l,quantityVariance:c,variancePercentage:Math.round(100*n)/100,isVarianceAcceptable:d,varianceReason:d?void 0:"数量差异超过5%阈值，实际差异".concat(n.toFixed(2),"%"),materialTheoreticalUsage:i,materialActualUsage:o,materialVariance:o-i,status:"pending",createdAt:new Date().toISOString()}}static generateCostAnalysisReport(e){let t=e.reduce((e,t)=>e+t.totalMolds,0),a=e.reduce((e,t)=>e+t.totalCost,0),s=e.reduce((e,t)=>e+t.formingCost.totalWage,0),i=e.reduce((e,t)=>e+t.hotPressCost.totalWage,0),o=e.reduce((e,t)=>e+t.materialCost.totalCost,0),l={formingCostRatio:a>0?s/a*100:0,hotPressCostRatio:a>0?i/a*100:0,materialCostRatio:a>0?o/a*100:0},r=new Map;e.forEach(e=>{let t=e.calculationDate,a=r.get(t)||{totalCost:0,totalMolds:0};a.totalCost+=e.totalCost,a.totalMolds+=e.totalMolds,r.set(t,a)});let c=Array.from(r.entries()).map(e=>{let[t,a]=e;return{period:t,unitCost:a.totalMolds>0?a.totalCost/a.totalMolds:0,totalMolds:a.totalMolds}}).sort((e,t)=>e.period.localeCompare(t.period));return{totalProduction:t,totalCost:Math.round(100*a)/100,averageUnitCost:Math.round(100*(t>0?a/t:0))/100,costBreakdown:{formingCostRatio:Math.round(100*l.formingCostRatio)/100,hotPressCostRatio:Math.round(100*l.hotPressCostRatio)/100,materialCostRatio:Math.round(100*l.materialCostRatio)/100},trends:c}}static detectCostAnomalies(e,t){let a=e.unitCost,s=t>0?(a-t)/t*100:0;return Math.abs(s)>20?{hasAnomaly:!0,anomalyType:s>0?"high_cost":"low_efficiency",anomalyMessage:"单位成本异常：当前".concat(a.toFixed(2),"元/模，偏离历史平均").concat(Math.abs(s).toFixed(1),"%"),suggestions:s>0?["检查材料用量是否超标","核实计件工资计算","分析生产效率"]:["确认数据准确性","分析效率提升原因"]}:{hasAnomaly:!1}}}let{Option:F}=o.default,{RangePicker:D}=l.default;var T=()=>{let{modal:e}=r.Z.useApp(),[t]=c.Z.useForm(),[a,l]=(0,i.useState)(!1),[D,T]=(0,i.useState)(!1),[H,B]=(0,i.useState)(!1),[E,Q]=(0,i.useState)("calculations"),[O]=(0,i.useState)([{id:"1",modelCode:"JJS-0001",modelName:"精密机械组件A",formingMold:"M-JX-05",formingMoldQuantity:4,hotPressMold:"M-RY-12",hotPressMoldQuantity:2,piecesPerMold:4,formingPiecePrice:8,hotPressPiecePrice:2.5,productPrice:.15,productWeight:12.5,boxSpecification:"30\xd720\xd715 cm",packingQuantity:100,status:"active",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"2",modelCode:"JJS-0002",modelName:"电子控制器B",formingMold:"M-JX-08",formingMoldQuantity:6,hotPressMold:"M-RY-15",hotPressMoldQuantity:3,piecesPerMold:6,formingPiecePrice:12,hotPressPiecePrice:3.5,productPrice:.258,productWeight:18.6,boxSpecification:"25\xd715\xd78 cm",packingQuantity:80,status:"active",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"}]),[U,q]=(0,i.useState)([{id:"cost-001",calculationDate:"2024-01-15",productionOrderId:"order-001",productModelCode:"JJS-0001",totalMolds:1500,formingCost:{totalMolds:1500,pieceRate:8,totalWage:12e3,operatorRecords:[{operatorId:"OP001",operatorName:"张师傅",completedMolds:800,pieceRate:8,wage:6400},{operatorId:"OP002",operatorName:"李师傅",completedMolds:700,pieceRate:8,wage:5600}]},hotPressCost:{totalMolds:1500,pieceRate:2.5,totalWage:3750,employeeBindings:[{employeeId:"EMP001",employeeName:"王师傅",bindingMolds:800,pieceRate:2.5,wage:2e3},{employeeId:"EMP002",employeeName:"赵师傅",bindingMolds:700,pieceRate:2.5,wage:1750}]},materialCost:{materialCode:"MAT-CP-202",materialName:"CP-202原料",usedQuantity:.375,unitPrice:1500,totalCost:562.5},totalCost:16312.5,unitCost:10.875,createdAt:"2024-01-15T18:00:00Z"}]),[W,J]=(0,i.useState)([{id:"reconciliation-001",reconciliationDate:"2024-01-15",productModelCode:"JJS-0001",hotPressQuantityTotal:1500,warehouseQuantity:1450,onSiteQuantity:45,bindingQuantityTotal:1500,quantityVariance:5,variancePercentage:.33,isVarianceAcceptable:!0,materialTheoreticalUsage:375,materialActualUsage:380,materialVariance:5,status:"pending",createdAt:"2024-01-15T18:00:00Z"}]),_=V.generateCostAnalysisReport(U),L=async a=>{l(!0);try{let e=O.find(e=>e.modelCode===a.productModelCode);if(!e)throw Error("产品型号不存在");let s=[{id:"report-001",workstationId:"W1",operatorId:a.operatorId||"OP001",operatorName:a.operatorName||"操作员",reportTime:new Date().toISOString(),completedMolds:a.totalMolds,reportType:"normal",isValidated:!0,createdAt:new Date().toISOString()}],i=[{id:"binding-001",employeeId:a.employeeId||"EMP001",employeeName:a.employeeName||"员工",employeeCode:"E001",taskId:"task-001",batchNumber:"BATCH-001",bindingTime:new Date().toISOString(),scanMethod:"qr_code",bindingMolds:a.totalMolds,isActive:!0,createdAt:new Date().toISOString()}],o=V.executeFullCostCalculation(a.productionOrderId,e,s,i,a.materialUsageRate||250,a.materialUnitPrice||1500);q(e=>[o,...e]),T(!1),t.resetFields()}catch(t){e.error({title:"计算失败",content:"成本计算过程中发生错误，请重试"})}finally{l(!1)}},K=async a=>{l(!0);try{let e=[{id:"binding-001",employeeId:"EMP001",employeeName:"员工1",employeeCode:"E001",taskId:"task-001",batchNumber:"BATCH-001",bindingTime:new Date().toISOString(),scanMethod:"qr_code",bindingMolds:a.hotPressQuantity,isActive:!0,createdAt:new Date().toISOString()}],s=V.performCostReconciliation(a.productModelCode,e,a.warehouseQuantity,a.onSiteQuantity,a.theoreticalMaterialUsage,a.actualMaterialUsage);J(e=>[s,...e]),B(!1),t.resetFields()}catch(t){e.error({title:"对账失败",content:"成本对账过程中发生错误，请重试"})}finally{l(!1)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"page-header",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(b.Z,{className:"text-2xl text-purple-600 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"page-title",children:"成本计算"}),(0,s.jsx)("p",{className:"page-description",children:"生产成本计算和对账系统"})]})]}),(0,s.jsxs)(n.Z,{children:[(0,s.jsx)(d.ZP,{icon:(0,s.jsx)(P.Z,{}),children:"导出报表"}),(0,s.jsx)(d.ZP,{icon:(0,s.jsx)(I.Z,{}),onClick:()=>window.location.reload(),children:"刷新数据"})]})]})}),(0,s.jsxs)(u.Z,{gutter:[16,16],children:[(0,s.jsx)(h.Z,{xs:24,sm:6,children:(0,s.jsx)(x.Z,{children:(0,s.jsx)(p.Z,{title:"总生产量",value:_.totalProduction,suffix:"模",valueStyle:{color:"#1890ff"},prefix:(0,s.jsx)(k.Z,{})})})}),(0,s.jsx)(h.Z,{xs:24,sm:6,children:(0,s.jsx)(x.Z,{children:(0,s.jsx)(p.Z,{title:"总成本",value:_.totalCost,suffix:"元",valueStyle:{color:"#52c41a"},prefix:(0,s.jsx)(S.Z,{}),precision:2})})}),(0,s.jsx)(h.Z,{xs:24,sm:6,children:(0,s.jsx)(x.Z,{children:(0,s.jsx)(p.Z,{title:"平均单位成本",value:_.averageUnitCost,suffix:"元/模",valueStyle:{color:"#722ed1"},prefix:(0,s.jsx)(A.Z,{}),precision:3})})}),(0,s.jsx)(h.Z,{xs:24,sm:6,children:(0,s.jsx)(x.Z,{children:(0,s.jsx)(p.Z,{title:"待审核对账",value:W.filter(e=>"pending"===e.status).length,suffix:"项",valueStyle:{color:"#fa8c16"},prefix:(0,s.jsx)(R.Z,{})})})})]}),(0,s.jsx)(x.Z,{title:"成本结构分析",children:(0,s.jsxs)(u.Z,{gutter:[16,16],children:[(0,s.jsx)(h.Z,{xs:24,lg:12,children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsx)("span",{children:"成型成本"}),(0,s.jsxs)("span",{className:"font-medium",children:[_.costBreakdown.formingCostRatio.toFixed(1),"%"]})]}),(0,s.jsx)(f.Z,{percent:_.costBreakdown.formingCostRatio,strokeColor:"#1890ff",showInfo:!1})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsx)("span",{children:"热压成本"}),(0,s.jsxs)("span",{className:"font-medium",children:[_.costBreakdown.hotPressCostRatio.toFixed(1),"%"]})]}),(0,s.jsx)(f.Z,{percent:_.costBreakdown.hotPressCostRatio,strokeColor:"#52c41a",showInfo:!1})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsx)("span",{children:"材料成本"}),(0,s.jsxs)("span",{className:"font-medium",children:[_.costBreakdown.materialCostRatio.toFixed(1),"%"]})]}),(0,s.jsx)(f.Z,{percent:_.costBreakdown.materialCostRatio,strokeColor:"#faad14",showInfo:!1})]})]})}),(0,s.jsx)(h.Z,{xs:24,lg:12,children:(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[(0,s.jsx)("h4",{className:"font-medium mb-3",children:"成本分析建议"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,s.jsx)("li",{children:"• 成型成本占比最高，建议优化生产效率"}),(0,s.jsx)("li",{children:"• 材料成本相对稳定，关注原料价格波动"}),(0,s.jsx)("li",{children:"• 热压成本占比较低，工艺效率良好"}),(0,s.jsx)("li",{children:"• 建议定期对比历史数据，识别成本趋势"})]})]})})]})}),(0,s.jsx)(x.Z,{children:(0,s.jsx)(g.default,{activeKey:E,onChange:Q,tabBarExtraContent:(0,s.jsxs)(n.Z,{children:[(0,s.jsx)(d.ZP,{type:"primary",icon:(0,s.jsx)(b.Z,{}),onClick:()=>T(!0),children:"新建成本计算"}),(0,s.jsx)(d.ZP,{icon:(0,s.jsx)(z.Z,{}),onClick:()=>B(!0),children:"成本对账"})]}),items:[{key:"calculations",label:"成本计算记录",children:(0,s.jsx)(j.Z,{columns:[{title:"计算日期",dataIndex:"calculationDate",key:"calculationDate",width:100},{title:"产品型号",dataIndex:"productModelCode",key:"productModelCode",width:100},{title:"总模数",dataIndex:"totalMolds",key:"totalMolds",width:80,render:e=>"".concat(e," 模")},{title:"成型成本",key:"formingCost",width:100,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"font-medium",children:["\xa5",t.formingCost.totalWage.toFixed(2)]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["\xa5",t.formingCost.pieceRate,"/模"]})]})},{title:"热压成本",key:"hotPressCost",width:100,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"font-medium",children:["\xa5",t.hotPressCost.totalWage.toFixed(2)]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["\xa5",t.hotPressCost.pieceRate,"/模"]})]})},{title:"材料成本",key:"materialCost",width:100,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"font-medium",children:["\xa5",t.materialCost.totalCost.toFixed(2)]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[t.materialCost.usedQuantity.toFixed(3),"吨"]})]})},{title:"总成本",dataIndex:"totalCost",key:"totalCost",width:100,render:e=>(0,s.jsxs)("div",{className:"font-medium text-blue-600",children:["\xa5",e.toFixed(2)]})},{title:"总收入",dataIndex:"totalRevenue",key:"totalRevenue",width:100,render:e=>(0,s.jsx)("div",{className:"font-medium text-green-600",children:e?"\xa5".concat(e.toFixed(3)):"-"})},{title:"总利润",dataIndex:"totalProfit",key:"totalProfit",width:100,render:e=>(0,s.jsx)("div",{className:"font-medium ".concat(e&&e>=0?"text-green-600":"text-red-600"),children:e?"\xa5".concat(e.toFixed(3)):"-"})},{title:"利润率",dataIndex:"profitMargin",key:"profitMargin",width:100,render:e=>(0,s.jsx)("div",{className:"font-medium ".concat(e&&e>=0?"text-green-600":"text-red-600"),children:e?"".concat(e.toFixed(1),"%"):"-"})},{title:"单位成本",dataIndex:"unitCost",key:"unitCost",width:100,render:e=>(0,s.jsxs)("div",{className:"font-medium text-purple-600",children:["\xa5",e.toFixed(3),"/模"]})},{title:"操作",key:"action",width:100,render:(e,t)=>(0,s.jsx)(n.Z,{size:"small",children:(0,s.jsx)(d.ZP,{type:"text",size:"small",icon:(0,s.jsx)(w.Z,{}),children:"详情"})})}],dataSource:U,rowKey:"id",pagination:{pageSize:10},scroll:{x:1e3}})},{key:"reconciliations",label:"对账记录",children:(0,s.jsx)(j.Z,{columns:[{title:"对账日期",dataIndex:"reconciliationDate",key:"reconciliationDate",width:100},{title:"产品型号",dataIndex:"productModelCode",key:"productModelCode",width:100},{title:"数量对账",key:"quantityReconciliation",width:150,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("span",{children:["热压: ",t.hotPressQuantityTotal]}),(0,s.jsx)("span",{className:"mx-2",children:"|"}),(0,s.jsxs)("span",{children:["入库: ",t.warehouseQuantity]})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["差异: ",t.quantityVariance," (",t.variancePercentage,"%)"]})]})},{title:"材料对账",key:"materialReconciliation",width:120,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-sm",children:["差异: ",t.materialVariance,"kg"]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["实际: ",t.materialActualUsage,"kg"]})]})},{title:"对账状态",key:"reconciliationStatus",width:120,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsx)(m.Z,{status:t.isVarianceAcceptable?"success":"error",text:t.isVarianceAcceptable?"正常":"异常"}),(0,s.jsx)("div",{className:"text-xs text-gray-500 mt-1",children:"pending"===t.status?"待审核":"reviewed"===t.status?"已审核":"已批准"})]})},{title:"操作",key:"action",width:100,render:(e,t)=>(0,s.jsx)(n.Z,{size:"small",children:(0,s.jsx)(d.ZP,{type:"text",size:"small",icon:(0,s.jsx)(N.Z,{}),children:"审核"})})}],dataSource:W,rowKey:"id",pagination:{pageSize:10},scroll:{x:800}})},{key:"trends",label:"成本趋势",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(v.Z,{message:"成本趋势分析",description:"基于历史数据分析成本变化趋势，帮助优化生产成本控制",type:"info",showIcon:!0}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[(0,s.jsx)("h4",{className:"font-medium mb-3",children:"趋势数据"}),(0,s.jsx)(Z.Z,{dataSource:_.trends,renderItem:e=>(0,s.jsx)(Z.Z.Item,{children:(0,s.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,s.jsx)("span",{children:e.period}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"font-medium",children:["\xa5",e.unitCost.toFixed(3),"/模"]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[e.totalMolds," 模"]})]})]})})})]})]})}]})}),(0,s.jsx)(C.Z,{title:"新建成本计算",open:D,onCancel:()=>T(!1),onOk:()=>t.submit(),confirmLoading:a,width:800,children:(0,s.jsxs)(c.Z,{form:t,layout:"vertical",onFinish:L,children:[(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(h.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"生产订单ID",name:"productionOrderId",rules:[{required:!0,message:"请输入生产订单ID"}],children:(0,s.jsx)(M.default,{placeholder:"请输入生产订单ID"})})}),(0,s.jsx)(h.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"产品型号",name:"productModelCode",rules:[{required:!0,message:"请选择产品型号"}],children:(0,s.jsx)(o.default,{placeholder:"请选择产品型号",children:O.map(e=>(0,s.jsxs)(F,{value:e.modelCode,children:[e.modelCode," - ",e.modelName]},e.modelCode))})})})]}),(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(h.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"总模数",name:"totalMolds",rules:[{required:!0,message:"请输入总模数"}],children:(0,s.jsx)(y.Z,{className:"w-full",placeholder:"请输入总模数",min:1,addonAfter:"模"})})}),(0,s.jsx)(h.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"材料使用率",name:"materialUsageRate",initialValue:250,children:(0,s.jsx)(y.Z,{className:"w-full",placeholder:"材料使用率",min:1,addonAfter:"克/模"})})})]}),(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(h.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"材料单价",name:"materialUnitPrice",initialValue:1500,children:(0,s.jsx)(y.Z,{className:"w-full",placeholder:"材料单价",min:1,addonAfter:"元/吨"})})}),(0,s.jsx)(h.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"操作员姓名",name:"operatorName",children:(0,s.jsx)(M.default,{placeholder:"操作员姓名（可选）"})})})]})]})}),(0,s.jsx)(C.Z,{title:"成本对账",open:H,onCancel:()=>B(!1),onOk:()=>t.submit(),confirmLoading:a,width:600,children:(0,s.jsxs)(c.Z,{form:t,layout:"vertical",onFinish:K,children:[(0,s.jsx)(c.Z.Item,{label:"产品型号",name:"productModelCode",rules:[{required:!0,message:"请选择产品型号"}],children:(0,s.jsx)(o.default,{placeholder:"请选择产品型号",children:O.map(e=>(0,s.jsxs)(F,{value:e.modelCode,children:[e.modelCode," - ",e.modelName]},e.modelCode))})}),(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(h.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"热压数量",name:"hotPressQuantity",rules:[{required:!0,message:"请输入热压数量"}],children:(0,s.jsx)(y.Z,{className:"w-full",placeholder:"热压数量",min:1,addonAfter:"模"})})}),(0,s.jsx)(h.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"入库数量",name:"warehouseQuantity",rules:[{required:!0,message:"请输入入库数量"}],children:(0,s.jsx)(y.Z,{className:"w-full",placeholder:"入库数量",min:0,addonAfter:"模"})})})]}),(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(h.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"场地暂存",name:"onSiteQuantity",rules:[{required:!0,message:"请输入场地暂存数量"}],children:(0,s.jsx)(y.Z,{className:"w-full",placeholder:"场地暂存数量",min:0,addonAfter:"模"})})}),(0,s.jsx)(h.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"理论材料用量",name:"theoreticalMaterialUsage",rules:[{required:!0,message:"请输入理论材料用量"}],children:(0,s.jsx)(y.Z,{className:"w-full",placeholder:"理论材料用量",min:0,addonAfter:"kg"})})})]}),(0,s.jsx)(c.Z.Item,{label:"实际材料用量",name:"actualMaterialUsage",rules:[{required:!0,message:"请输入实际材料用量"}],children:(0,s.jsx)(y.Z,{className:"w-full",placeholder:"实际材料用量",min:0,addonAfter:"kg"})})]})})]})}}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,9427,1157,4863,7416,30,5424,7829,2971,4938,1744],function(){return e(e.s=34841)}),_N_E=e.O()}]);