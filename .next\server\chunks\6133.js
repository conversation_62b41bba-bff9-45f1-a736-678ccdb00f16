exports.id=6133,exports.ids=[6133],exports.modules={8282:(e,s,a)=>{Promise.resolve().then(a.bind(a,95883))},95883:(e,s,a)=>{"use strict";a.r(s),a.d(s,{MainLayout:()=>D});var t=a(95344),r=a(3729),l=a(32063),o=a(43158),i=a(67023);let n=(0,o.Ue)()((0,i.mW)(e=>({user:null,isAuthenticated:!1,sidebarCollapsed:!1,currentModule:"dashboard",loading:!1,setUser:s=>e({user:s}),setAuthenticated:s=>e({isAuthenticated:s}),toggleSidebar:()=>e(e=>({sidebarCollapsed:!e.sidebarCollapsed})),setSidebarCollapsed:s=>e({sidebarCollapsed:s}),setCurrentModule:s=>e({currentModule:s}),setLoading:s=>e({loading:s}),logout:()=>e({user:null,isAuthenticated:!1,currentModule:"dashboard"})}),{name:"app-store"}));var d=a(98033),c=a(68222),p=a(71978),g=a(22254),u=a(32535),x=a(92561),f=a(6670),b=a(2778),h=a(21754),y=a(75397),m=a(55362),w=a(27385),k=a(27224),Z=a(77746),j=a(35720),v=a(59147),B=a(11689);let{Sider:S}=l.default,C=[{key:"dashboard",label:"仪表板",icon:t.jsx(u.Z,{}),path:"/dashboard"},{key:"sales",label:"销售管理",icon:t.jsx(x.Z,{}),children:[{key:"sales-orders",label:"订单管理",path:"/sales/orders"},{key:"sales-credit",label:"信用管理",path:"/sales/credit"},{key:"sales-delivery",label:"发货管理",path:"/sales/delivery"},{key:"sales-finance",label:"财务协同",children:[{key:"sales-invoices",label:"开票管理",path:"/sales/invoices"},{key:"sales-payments",label:"收款管理",path:"/sales/payments"}]},{key:"sales-after-sales",label:"售后服务",path:"/sales/after-sales"},{key:"sales-analytics",label:"决策分析",path:"/sales/analytics"}]},{key:"procurement",label:"采购管理",icon:t.jsx(f.Z,{}),path:"/procurement"},{key:"warehouse",label:"仓库管理",icon:t.jsx(b.Z,{}),children:[{key:"warehouse-products",label:"产品库存",path:"/warehouse/products"},{key:"warehouse-materials",label:"原料库存",path:"/warehouse/materials"},{key:"warehouse-overview",label:"库存总览",path:"/warehouse"}]},{key:"production",label:"生产管理",icon:t.jsx(h.Z,{}),children:[{key:"production-orders",label:"生产订单管理",path:"/production/orders"}]},{key:"finance",label:"财务管理",icon:t.jsx(y.Z,{}),path:"/finance"},{key:"master-data",label:"主数据管理",icon:t.jsx(m.Z,{}),children:[{key:"employees",label:"员工信息",icon:t.jsx(w.Z,{}),path:"/master-data/employees"},{key:"master-data-customers",label:"客户档案",icon:t.jsx(k.Z,{}),path:"/sales/customers"},{key:"materials",label:"原料数据",icon:t.jsx(Z.Z,{}),path:"/master-data/materials"},{key:"product-models",label:"产品数据",icon:t.jsx(j.Z,{}),path:"/master-data/product-models"}]}],E=()=>{let e=(0,g.useRouter)(),s=(0,g.usePathname)(),{sidebarCollapsed:a,toggleSidebar:l,setCurrentModule:o}=n(),[i,u]=(0,r.useState)(!1),[x,f]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=()=>{u(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,r.useEffect)(()=>{let e=()=>{i&&f(!x)};return window.addEventListener("toggleMobileSidebar",e),()=>window.removeEventListener("toggleMobileSidebar",e)},[i,x]);let b=s=>{s.path&&(e.push(s.path),o(s.key),i&&f(!1))},h=e=>e.map(e=>e.children?{key:e.key,icon:e.icon,label:e.label,children:h(e.children)}:{key:e.key,icon:e.icon,label:e.label,onClick:()=>b(e)}),y=()=>{let e=a=>{for(let t of a){if(t.path===s)return t;if(t.children){let s=e(t.children);if(s)return s}}},a=e(C);return a?[a.key]:["dashboard"]},m=()=>{i?f(!x):l()},w=()=>(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{style:{height:64,display:"flex",alignItems:"center",justifyContent:"center",borderBottom:"1px solid #f0f0f0"},children:i||!a?t.jsx("div",{style:{fontSize:"20px",fontWeight:"bold",color:d.ZB.colors.primary[600]},children:"ERP系统"}):t.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:d.ZB.colors.primary[600]},children:"E"})}),!i&&t.jsx("div",{style:{padding:d.ZB.spacing.md,borderBottom:"1px solid #f0f0f0"},children:t.jsx("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",width:32,height:32,borderRadius:d.ZB.borderRadius.lg,cursor:"pointer",transition:"background-color 0.2s ease",backgroundColor:"transparent"},onClick:m,onMouseEnter:e=>{e.currentTarget.style.backgroundColor=d.ZB.colors.gray[100]},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:a?t.jsx(v.Z,{}):t.jsx(B.Z,{})})}),t.jsx(c.Z,{mode:"inline",selectedKeys:y(),style:{border:"none"},items:h(C)})]});return i?t.jsx(p.Z,{title:null,placement:"left",closable:!1,onClose:()=>f(!1),open:x,styles:{body:{padding:0}},width:256,children:t.jsx(w,{})}):t.jsx(S,{trigger:null,collapsible:!0,collapsed:a,width:256,collapsedWidth:80,style:{position:"fixed",left:0,top:0,bottom:0,zIndex:50,boxShadow:d.ZB.shadows.lg,background:"#ffffff",borderRight:"1px solid #f0f0f0",display:i?"none":"block"},children:t.jsx(w,{})})};var R=a(11157),L=a(39470),M=a(83512),z=a(90185),I=a(10707),W=a(46472),P=a(61064),A=a(4288),T=a(10592),U=a(2014);let{Header:H}=l.default,$=()=>{let{user:e,logout:s}=n(),[a,l]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=()=>{l(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let o=[{key:"profile",icon:t.jsx(w.Z,{}),label:"个人资料"},{key:"settings",icon:t.jsx(W.Z,{}),label:"系统设置"},{type:"divider"},{key:"logout",icon:t.jsx(P.Z,{}),label:"退出登录",onClick:s}];return(0,t.jsxs)(H,{style:{background:"#ffffff",borderBottom:"1px solid #f0f0f0",padding:`0 ${a?d.ZB.spacing.sm:d.ZB.spacing.lg}px`,display:"flex",alignItems:"center",justifyContent:"space-between",boxShadow:d.ZB.shadows.sm},children:[(0,t.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:d.ZB.spacing.sm},children:[a&&t.jsx(R.ZP,{type:"text",icon:t.jsx(A.Z,{}),onClick:()=>{let e=new CustomEvent("toggleMobileSidebar");window.dispatchEvent(e)},style:{display:"flex",alignItems:"center",justifyContent:"center",width:40,height:40}}),t.jsx("h1",{style:{fontSize:a?"16px":"18px",fontWeight:600,color:d.ZB.colors.gray[900],margin:0,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:a?"ERP系统":"企业资源规划系统"})]}),(0,t.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:a?d.ZB.spacing.xs:d.ZB.spacing.md},children:[t.jsx(L.Z,{count:5,size:"small",children:t.jsx(R.ZP,{type:"text",icon:t.jsx(T.Z,{}),style:{display:"flex",alignItems:"center",justifyContent:"center",width:a?32:40,height:a?32:40}})}),t.jsx(M.Z,{menu:{items:o},trigger:["click"],placement:"bottomRight",children:(0,t.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:a?d.ZB.spacing.xs:d.ZB.spacing.sm,cursor:"pointer",padding:`${d.ZB.spacing.xs}px ${a?d.ZB.spacing.xs:d.ZB.spacing.sm}px`,borderRadius:d.ZB.borderRadius.lg,transition:"background-color 0.2s ease",backgroundColor:"transparent"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor=d.ZB.colors.gray[50]},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="transparent"},children:[t.jsx(z.Z,{size:a?"small":"default",icon:t.jsx(w.Z,{}),src:e?.avatar}),!a&&(0,t.jsxs)(I.Z,{children:[t.jsx("span",{style:{color:d.ZB.colors.gray[700],fontWeight:500},children:e?.name||"管理员"}),t.jsx(U.Z,{style:{color:d.ZB.colors.gray[400],fontSize:"12px"}})]})]})})]})]})},{Content:q}=l.default,D=({children:e})=>{let{sidebarCollapsed:s}=n(),[a,o]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{o(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,t.jsxs)(l.default,{style:{minHeight:"100vh"},children:[t.jsx(E,{}),(0,t.jsxs)(l.default,{style:{transition:"all 0.3s ease",marginLeft:a?0:s?80:256},children:[t.jsx($,{}),t.jsx(q,{style:{padding:a?d.ZB.spacing.sm:d.ZB.spacing.lg,background:d.ZB.colors.gray[50],overflow:"auto"},children:e})]})]})}},98033:(e,s,a)=>{"use strict";a.d(s,{ZB:()=>t});let t={spacing:{xs:4,sm:8,md:16,lg:24,xl:32,xxl:48},shadows:{sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1)"},borderRadius:{sm:4,md:8,lg:12,xl:16},colors:{primary:{50:"#f0f9ff",100:"#e0f2fe",500:"#0ea5e9",600:"#0284c7",700:"#0369a1"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827"},success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"},createSpacing:e=>({padding:t.spacing[e]}),createMargin:e=>({margin:t.spacing[e]}),createShadow:e=>({boxShadow:t.shadows[e]}),createBorderRadius:e=>({borderRadius:t.borderRadius[e]}),cardStyle:{borderRadius:12,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",background:"#ffffff",border:"1px solid #f0f0f0"},buttonStyle:{borderRadius:8,transition:"all 0.2s ease-in-out"},layoutStyle:{minHeight:"100vh",background:"#f9fafb"},sidebarStyle:{background:"#ffffff",boxShadow:"2px 0 8px 0 rgba(29, 35, 41, 0.05)",borderRight:"1px solid #f0f0f0"},headerStyle:{background:"#ffffff",borderBottom:"1px solid #f0f0f0",boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.05)"},breakpoints:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px",xxl:"1536px"},transitions:{fast:"all 0.15s ease-in-out",normal:"all 0.2s ease-in-out",slow:"all 0.3s ease-in-out"}};t.spacing.xs,t.spacing.sm,t.spacing.md,t.spacing.lg,t.spacing.xl,t.spacing.xs,t.spacing.sm,t.spacing.md,t.spacing.lg,t.colors.gray[50],t.colors.gray[100],t.colors.gray[200],t.colors.gray[200],t.shadows.sm,t.shadows.md,t.shadows.lg,t.borderRadius.sm,t.borderRadius.md,t.borderRadius.lg,t.transitions.normal},38834:(e,s,a)=>{"use strict";a.d(s,{Z:()=>i});var t=a(86843);let r=(0,t.createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\components\layout\MainLayout.tsx`),{__esModule:l,$$typeof:o}=r;r.default;let i=(0,t.createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\components\layout\MainLayout.tsx#MainLayout`)}};