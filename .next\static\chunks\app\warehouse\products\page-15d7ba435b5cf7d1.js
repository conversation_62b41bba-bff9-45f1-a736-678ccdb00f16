(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2735],{99645:function(e,t,s){Promise.resolve().then(s.bind(s,99661))},99661:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return A}});var l=s(57437),r=s(2265),c=s(27296),n=s(50030),i=s(57416),a=s(89198),o=s(9427),d=s(34863),x=s(65270),u=s(78634),h=s(94734),j=s(38302),m=s(28683),p=s(89511),f=s(86155),v=s(39992),Z=s(50574),g=s(47628),y=s(75123),S=s(23455),C=s(90060),k=s(43043),N=s(87304),w=s(81453),R=s(65362),P=s(75216),E=s(78740),_=s(75393),b=s(59160),O=s(51554),I=s(51769),T=s(74898),U=s(74548),D=s.n(U),L=s(32779),F=s(50538);let{Option:M}=c.default,{RangePicker:z}=n.default,V=()=>{let{message:e,modal:t}=i.Z.useApp(),[s,n]=(0,r.useState)(!1),[U,z]=(0,r.useState)([]),[V,A]=(0,r.useState)(!1),[q,B]=(0,r.useState)(null),[H,K]=(0,r.useState)(""),[Q,W]=(0,r.useState)(void 0),[X,J]=(0,r.useState)([]),[Y,G]=(0,r.useState)({totalValue:0,totalProducts:0,lowStockCount:0,outOfStockCount:0}),[$]=a.Z.useForm(),ee=async()=>{n(!0);try{let e=await (0,F.Ro)(()=>L.dataAccessManager.inventory.getAll(),"获取库存数据");if(e&&e.items){let t=e.items.map(e=>({id:e.id||"inv-".concat(e.productCode),productCode:e.productCode,productName:e.productName||e.productCode,productModel:e.productModel||"",category:e.category||"未分类",currentStock:e.currentStock||0,reservedStock:e.reservedStock||0,availableStock:(e.currentStock||0)-(e.reservedStock||0),safetyStock:e.safetyStock||0,unitCost:e.unitCost||0,totalValue:(e.currentStock||0)*(e.unitCost||0),lastUpdated:e.lastUpdated||new Date().toISOString(),location:e.location||"默认仓库",supplier:e.supplier||"",status:et(e)}));J(t);let s=es(t);G(s)}}catch(t){console.error("加载库存数据失败:",t),e.error("加载库存数据失败")}finally{n(!1)}},et=e=>{let t=e.currentStock||0,s=e.safetyStock||0;return 0===t?"shortage":t<s?"warning":t>3*s?"excess":"normal"},es=e=>({totalValue:e.reduce((e,t)=>e+t.totalValue,0),totalProducts:e.length,lowStockCount:e.filter(e=>"warning"===e.status).length,outOfStockCount:e.filter(e=>"shortage"===e.status).length});(0,r.useEffect)(()=>{ee()},[]);let el=e=>{let{currentStock:t,safetyStock:s,status:r}=e,c=3*s;switch(r){case"shortage":return{color:"red",text:"库存不足",icon:(0,l.jsx)(S.Z,{}),percentage:c>0?t/c*100:0};case"excess":return{color:"orange",text:"库存过多",icon:(0,l.jsx)(C.Z,{}),percentage:c>0?t/c*100:0};case"warning":return{color:"volcano",text:"库存预警",icon:(0,l.jsx)(k.Z,{}),percentage:c>0?t/c*100:0};default:return{color:"green",text:"正常",icon:(0,l.jsx)(N.Z,{}),percentage:c>0?t/c*100:0}}},er=e=>{ec(e)},ec=e=>{t.info({title:"产品库存详情",width:600,content:(0,l.jsx)("div",{className:"space-y-4",children:(0,l.jsxs)(j.Z,{gutter:[16,16],children:[(0,l.jsx)(m.Z,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"产品编码:"})," ",e.productCode]})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"产品名称:"})," ",e.productName]})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"产品型号:"})," ",e.productModel]})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"分类:"})," ",e.category]})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"当前库存:"})," ",e.currentStock]})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"安全库存:"})," ",e.safetyStock]})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"库位:"})," ",e.location]})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"可用库存:"})," ",e.availableStock]})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"预留库存:"})," ",e.reservedStock]})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"供应商:"})," ",e.supplier]})})]})})})},en=e=>{B(e),$.setFieldsValue({productCode:e.productCode,productName:e.productName,currentStock:e.currentStock,adjustmentType:"adjustment"}),A(!0)},ei=async()=>{try{n(!0),await ee(),e.success("数据同步成功")}catch(t){console.error("数据同步失败:",t),e.error("数据同步失败，请稍后重试")}finally{n(!1)}},ea=async()=>{try{n(!0);let t=X.filter(e=>!e.productCode||!e.productName||e.currentStock<0||e.unitCost<0);0===t.length?e.success("数据验证通过，所有产品数据一致"):(e.warning("发现 ".concat(t.length," 个数据不一致问题")),console.log("数据验证问题:",t))}catch(t){console.error("数据验证失败:",t),e.error("数据验证失败，请稍后重试")}finally{n(!1)}},eo={totalProducts:X.length,totalValue:Y.totalValue,lowStockCount:Y.lowStockCount,highStockCount:X.filter(e=>"excess"===e.status).length,normalStockCount:X.filter(e=>"normal"===e.status).length,validPriceCount:X.filter(e=>e.unitCost>0).length,invalidPriceCount:X.filter(e=>0===e.unitCost).length};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("div",{className:"page-header",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(E.Z,{className:"text-2xl text-blue-600 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"page-title",children:"产品库存管理"}),(0,l.jsx)("p",{className:"page-description",children:"管理成品库存，包括产品入库、出库、库存查询、库存预警等"})]})]})}),(0,l.jsxs)(j.Z,{gutter:[16,16],children:[(0,l.jsx)(m.Z,{xs:24,sm:6,children:(0,l.jsx)(p.Z,{children:(0,l.jsx)(f.Z,{title:"产品总数",value:eo.totalProducts,suffix:"种",prefix:(0,l.jsx)(E.Z,{}),valueStyle:{color:"#1890ff"}})})}),(0,l.jsx)(m.Z,{xs:24,sm:6,children:(0,l.jsx)(p.Z,{children:(0,l.jsx)(f.Z,{title:"库存总值",value:eo.totalValue,precision:2,prefix:"\xa5",valueStyle:{color:"#52c41a"}})})}),(0,l.jsx)(m.Z,{xs:24,sm:6,children:(0,l.jsx)(p.Z,{children:(0,l.jsx)(f.Z,{title:"库存不足",value:eo.lowStockCount,suffix:"种",valueStyle:{color:"#ff4d4f"},prefix:(0,l.jsx)(S.Z,{})})})}),(0,l.jsx)(m.Z,{xs:24,sm:6,children:(0,l.jsx)(p.Z,{children:(0,l.jsx)(f.Z,{title:"库存过多",value:eo.highStockCount,suffix:"种",valueStyle:{color:"#fa8c16"},prefix:(0,l.jsx)(C.Z,{})})})})]}),(0,l.jsxs)(j.Z,{gutter:[16,16],children:[(0,l.jsx)(m.Z,{xs:24,sm:6,children:(0,l.jsx)(p.Z,{children:(0,l.jsx)(f.Z,{title:"有效价格",value:eo.validPriceCount,suffix:"/ ".concat(eo.totalProducts),valueStyle:{color:eo.validPriceCount===eo.totalProducts?"#52c41a":"#fa8c16"},prefix:(0,l.jsx)(N.Z,{})})})}),(0,l.jsx)(m.Z,{xs:24,sm:6,children:(0,l.jsx)(p.Z,{children:(0,l.jsx)(f.Z,{title:"价格缺失",value:eo.invalidPriceCount,suffix:"种",valueStyle:{color:eo.invalidPriceCount>0?"#ff4d4f":"#52c41a"},prefix:(0,l.jsx)(k.Z,{})})})}),(0,l.jsx)(m.Z,{xs:24,sm:12,children:(0,l.jsx)(p.Z,{children:(0,l.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{style:{fontSize:"14px",color:"#666"},children:"数据一致性"}),(0,l.jsxs)("div",{style:{fontSize:"24px",fontWeight:"bold",color:eo.validPriceCount===eo.totalProducts?"#52c41a":"#fa8c16"},children:[eo.totalProducts>0?Math.round(eo.validPriceCount/eo.totalProducts*100):0,"%"]})]}),(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#999"},children:[(0,l.jsxs)("div",{children:["✅ 价格有效: ",eo.validPriceCount]}),(0,l.jsxs)("div",{children:["❌ 价格缺失: ",eo.invalidPriceCount]})]})]})})})]}),(0,l.jsx)(p.Z,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,l.jsx)(v.default,{placeholder:"搜索产品编码、名称或型号",prefix:(0,l.jsx)(_.Z,{}),value:H,onChange:e=>K(e.target.value),className:"w-full sm:w-64"}),(0,l.jsxs)(c.default,{placeholder:"库存状态",value:Q,onChange:W,className:"w-full sm:w-32",allowClear:!0,children:[(0,l.jsx)(M,{value:"",children:"全部状态"}),(0,l.jsx)(M,{value:"normal",children:"正常"}),(0,l.jsx)(M,{value:"warning",children:"库存预警"}),(0,l.jsx)(M,{value:"shortage",children:"库存不足"}),(0,l.jsx)(M,{value:"excess",children:"库存过多"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(h.ZP,{icon:(0,l.jsx)(b.Z,{}),onClick:()=>ee(),children:"刷新"}),(0,l.jsx)(h.ZP,{icon:(0,l.jsx)(O.Z,{}),children:"库存报表"}),(0,l.jsx)(h.ZP,{icon:(0,l.jsx)(I.Z,{}),children:"导出数据"}),(0,l.jsx)(h.ZP,{icon:(0,l.jsx)(b.Z,{}),onClick:ei,loading:s,children:"同步数据"}),(0,l.jsx)(h.ZP,{icon:(0,l.jsx)(C.Z,{}),onClick:ea,loading:s,children:"数据验证"}),(0,l.jsx)(h.ZP,{type:"primary",icon:(0,l.jsx)(T.Z,{}),children:"产品入库"})]})]}),(0,l.jsx)(Z.Z,{columns:[{title:"产品编码",dataIndex:"productCode",key:"productCode",width:120,fixed:"left",render:e=>(0,l.jsx)("span",{className:"font-mono text-blue-600",children:e})},{title:"产品名称",key:"productName",width:200,render:(e,t)=>(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium",children:t.productName}),(0,l.jsxs)("div",{className:"text-gray-500 text-sm",children:["型号: ",t.productModel]}),(0,l.jsxs)("div",{className:"text-gray-400 text-xs",children:["分类: ",t.category]})]})},{title:"当前库存",dataIndex:"currentStock",key:"currentStock",width:120,render:(e,t)=>{let s=el(t);return(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"text-lg font-bold",children:e.toLocaleString()}),(0,l.jsx)(o.Z,{status:s.color,text:s.text})]})}},{title:"库存水位",key:"stockLevel",width:150,render:(e,t)=>{let s=el(t);return(0,l.jsxs)("div",{children:[(0,l.jsx)(d.Z,{percent:s.percentage,size:"small",status:"normal"===t.status?"success":"exception",format:()=>"".concat(t.currentStock,"/").concat(3*t.safetyStock)}),(0,l.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["安全库存: ",t.safetyStock]})]})}},{title:"库存价值",key:"value",width:120,render:(e,t)=>(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"font-medium",children:["\xa5",t.totalValue.toLocaleString()]}),(0,l.jsxs)("div",{className:"text-gray-500 text-sm",children:["单价: \xa5",t.unitCost.toLocaleString()]}),0===t.unitCost&&(0,l.jsx)("div",{className:"text-red-400 text-xs",children:"价格未设置"})]})},{title:"库位信息",key:"locationInfo",width:150,render:(e,t)=>(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"font-medium",children:t.location}),(0,l.jsxs)("div",{className:"text-gray-500 text-sm",children:["供应商: ",t.supplier]})]})},{title:"可用库存",dataIndex:"availableStock",key:"availableStock",width:100,render:e=>(0,l.jsx)("span",{className:"font-medium text-blue-600",children:e.toLocaleString()})},{title:"最后更新",dataIndex:"lastUpdated",key:"lastUpdated",width:140,render:e=>(0,l.jsx)("span",{className:"text-gray-500",children:D()(e).format("MM-DD HH:mm")})},{title:"操作",key:"action",width:180,fixed:"right",render:(e,t)=>(0,l.jsxs)(x.Z,{size:"small",children:[(0,l.jsx)(u.Z,{title:"库存调整",children:(0,l.jsx)(h.ZP,{type:"text",icon:(0,l.jsx)(w.Z,{}),size:"small",onClick:()=>en(t),children:"调整"})}),(0,l.jsx)(u.Z,{title:"编辑信息",children:(0,l.jsx)(h.ZP,{type:"text",icon:(0,l.jsx)(R.Z,{}),size:"small",onClick:()=>er(t),children:"编辑"})}),(0,l.jsx)(u.Z,{title:"查看详情",children:(0,l.jsx)(h.ZP,{type:"text",icon:(0,l.jsx)(P.Z,{}),size:"small",onClick:()=>ec(t),children:"详情"})})]})}],dataSource:X.filter(e=>{let t=!H||e.productCode.toLowerCase().includes(H.toLowerCase())||e.productName.toLowerCase().includes(H.toLowerCase())||e.productModel.toLowerCase().includes(H.toLowerCase()),s=!Q||""===Q||e.status===Q;return t&&s}),rowKey:"id",loading:s,rowSelection:{selectedRowKeys:U,onChange:z,getCheckboxProps:e=>({disabled:"shortage"===e.status})},pagination:{total:X.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条")},scroll:{x:1400},size:"small"})]})}),(0,l.jsx)(g.Z,{title:"库存调整",open:V,onCancel:()=>{A(!1),B(null),$.resetFields()},footer:[(0,l.jsx)(h.ZP,{onClick:()=>A(!1),children:"取消"},"cancel"),(0,l.jsx)(h.ZP,{type:"primary",onClick:()=>{$.validateFields().then(t=>{e.success("库存调整成功"),A(!1),$.resetFields()})},children:"确认调整"},"submit")],width:600,children:(0,l.jsxs)(a.Z,{form:$,layout:"vertical",className:"space-y-4",children:[(0,l.jsxs)(j.Z,{gutter:16,children:[(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(a.Z.Item,{label:"产品编码",name:"productCode",children:(0,l.jsx)(v.default,{disabled:!0})})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(a.Z.Item,{label:"产品名称",name:"productName",children:(0,l.jsx)(v.default,{disabled:!0})})})]}),(0,l.jsxs)(j.Z,{gutter:16,children:[(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(a.Z.Item,{label:"当前库存",name:"currentStock",children:(0,l.jsx)(y.Z,{disabled:!0,style:{width:"100%"}})})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(a.Z.Item,{label:"调整类型",name:"adjustmentType",rules:[{required:!0,message:"请选择调整类型"}],children:(0,l.jsxs)(c.default,{children:[(0,l.jsx)(M,{value:"in",children:"入库"}),(0,l.jsx)(M,{value:"out",children:"出库"}),(0,l.jsx)(M,{value:"adjustment",children:"库存调整"}),(0,l.jsx)(M,{value:"transfer",children:"库位转移"})]})})})]}),(0,l.jsxs)(j.Z,{gutter:16,children:[(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(a.Z.Item,{label:"调整数量",name:"adjustmentQuantity",rules:[{required:!0,message:"请输入调整数量"},{type:"number",min:1,message:"数量必须大于0"}],children:(0,l.jsx)(y.Z,{style:{width:"100%"},placeholder:"请输入调整数量",min:1})})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(a.Z.Item,{label:"调整原因",name:"reason",rules:[{required:!0,message:"请输入调整原因"}],children:(0,l.jsxs)(c.default,{children:[(0,l.jsx)(M,{value:"盘点调整",children:"盘点调整"}),(0,l.jsx)(M,{value:"损耗调整",children:"损耗调整"}),(0,l.jsx)(M,{value:"质量问题",children:"质量问题"}),(0,l.jsx)(M,{value:"生产需要",children:"生产需要"}),(0,l.jsx)(M,{value:"销售出库",children:"销售出库"}),(0,l.jsx)(M,{value:"其他",children:"其他"})]})})})]}),(0,l.jsx)(a.Z.Item,{label:"备注",name:"remark",children:(0,l.jsx)(v.default.TextArea,{rows:3,placeholder:"请输入调整备注信息"})})]})})]})};function A(){return(0,l.jsx)(i.Z,{children:(0,l.jsx)(V,{})})}},50538:function(e,t,s){"use strict";s.d(t,{Ro:function(){return l}});let l=async(e,t)=>{try{let s=await e();if("success"===s.status)return s.data||null;return s.code,s.message,s.message,t&&c(String(s.code||"UNKNOWN_ERROR")),null}catch(e){return e instanceof Error?e.message:String(e),null}},r={ERR_NOT_FOUND:"资源不存在",ERR_UNAUTHORIZED:"未授权访问",ERR_FORBIDDEN:"禁止访问",ERR_INTERNAL_ERROR:"内部服务器错误",ERR_PRODUCT_NOT_FOUND:"产品不存在",ERR_PRODUCT_CODE_EXISTS:"产品编码已存在",ERR_CUSTOMER_NOT_FOUND:"客户不存在",ERR_INVENTORY_INSUFFICIENT:"库存不足",ERR_ORDER_NOT_FOUND:"订单不存在",ERR_ORDER_NUMBER_EXISTS:"订单号已存在",DUPLICATE_ORDER_NUMBER:"订单号重复"},c=e=>r[e]||"操作失败"}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,9427,1157,4863,7416,30,2897,9563,2779,2971,4938,1744],function(){return e(e.s=99645)}),_N_E=e.O()}]);