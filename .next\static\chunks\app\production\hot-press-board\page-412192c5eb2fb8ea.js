(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6475],{68826:function(e,t,s){"use strict";s.d(t,{Z:function(){return c}});var i=s(13428),n=s(2265),r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"},a=s(46614),c=n.forwardRef(function(e,t){return n.createElement(a.Z,(0,i.Z)({},e,{ref:t,icon:r}))})},57084:function(e,t,s){"use strict";s.d(t,{Z:function(){return c}});var i=s(13428),n=s(2265),r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"},a=s(46614),c=n.forwardRef(function(e,t){return n.createElement(a.Z,(0,i.Z)({},e,{ref:t,icon:r}))})},66887:function(e,t,s){"use strict";s.d(t,{Z:function(){return c}});var i=s(13428),n=s(2265),r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M136 384h56c4.4 0 8-3.6 8-8V200h176c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H196c-37.6 0-68 30.4-68 68v180c0 4.4 3.6 8 8 8zm512-184h176v176c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V196c0-37.6-30.4-68-68-68H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zM376 824H200V648c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v180c0 37.6 30.4 68 68 68h180c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm512-184h-56c-4.4 0-8 3.6-8 8v176H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h180c37.6 0 68-30.4 68-68V648c0-4.4-3.6-8-8-8zm16-164H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"scan",theme:"outlined"},a=s(46614),c=n.forwardRef(function(e,t){return n.createElement(a.Z,(0,i.Z)({},e,{ref:t,icon:r}))})},6063:function(e,t,s){"use strict";s.d(t,{Z:function(){return c}});var i=s(13428),n=s(2265),r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"},a=s(46614),c=n.forwardRef(function(e,t){return n.createElement(a.Z,(0,i.Z)({},e,{ref:t,icon:r}))})},71390:function(e,t,s){Promise.resolve().then(s.bind(s,32883))},47628:function(e,t,s){"use strict";s.d(t,{Z:function(){return Z}});var i=s(73465),n=s(99486),r=s(5832),a=s(2265),c=s(42744),l=s.n(c),d=s(33746),o=s(21467),m=s(57499),u=s(92935),x=s(1601),h=s(19704),g=s(42203),p=function(e,t){var s={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(s[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)0>t.indexOf(i[n])&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(s[i[n]]=e[i[n]]);return s},f=(0,o.i)(e=>{let{prefixCls:t,className:s,closeIcon:i,closable:n,type:r,title:c,children:o,footer:f}=e,j=p(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:v}=a.useContext(m.E_),y=v(),Z=t||v("modal"),b=(0,u.Z)(y),[N,w,C]=(0,g.ZP)(Z,b),T="".concat(Z,"-confirm"),k={};return k=r?{closable:null!=n&&n,title:"",footer:"",children:a.createElement(x.O,Object.assign({},e,{prefixCls:Z,confirmPrefixCls:T,rootPrefixCls:y,content:o}))}:{closable:null==n||n,title:c,footer:null!==f&&a.createElement(h.$,Object.assign({},e)),children:o},N(a.createElement(d.s,Object.assign({prefixCls:Z,className:l()(w,"".concat(Z,"-pure-panel"),r&&T,r&&"".concat(T,"-").concat(r),s,C,b)},j,{closeIcon:(0,h.b)(Z,i),closable:n},k)))}),j=s(36245);function v(e){return(0,i.ZP)((0,i.uW)(e))}let y=r.Z;y.useModal=j.Z,y.info=function(e){return(0,i.ZP)((0,i.cw)(e))},y.success=function(e){return(0,i.ZP)((0,i.vq)(e))},y.error=function(e){return(0,i.ZP)((0,i.AQ)(e))},y.warning=v,y.warn=v,y.confirm=function(e){return(0,i.ZP)((0,i.Au)(e))},y.destroyAll=function(){for(;n.Z.length;){let e=n.Z.pop();e&&e()}},y.config=i.ai,y._InternalPanelDoNotUseOrYouWillBeFired=f;var Z=y},32883:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return q}});var i=s(57437),n=s(2265),r=s(27296),a=s(39992),c=s(57416),l=s(34863),d=s(6053),o=s(9427),m=s(65270),u=s(94734),x=s(38302),h=s(28683),g=s(89511),p=s(86155),f=s(59189),j=s(50574),v=s(37044),y=s(92503),Z=s(37397),b=s(47628),N=s(68826),w=s(57084),C=s(87304),T=s(84326),k=s(6371),M=s(4e4),E=s(82765),S=s(6063),P=s(13428),D={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M632 888H392c-4.4 0-8 3.6-8 8v32c0 17.7 14.3 32 32 32h192c17.7 0 32-14.3 32-32v-32c0-4.4-3.6-8-8-8zM512 64c-181.1 0-328 146.9-328 328 0 121.4 66 227.4 164 284.1V792c0 17.7 14.3 32 32 32h264c17.7 0 32-14.3 32-32V676.1c98-56.7 164-162.7 164-284.1 0-181.1-146.9-328-328-328zm127.9 549.8L604 634.6V752H420V634.6l-35.9-20.8C305.4 568.3 256 484.5 256 392c0-141.4 114.6-256 256-256s256 114.6 256 256c0 92.5-49.4 176.3-128.1 221.8z"}}]},name:"bulb",theme:"outlined"},I=s(46614),A=n.forwardRef(function(e,t){return n.createElement(I.Z,(0,P.Z)({},e,{ref:t,icon:D}))}),z=s(34021),_=s(66887);class B{static sortTasksByPriority(e){return[...e].sort((e,t)=>new Date(e.deliveryDate).getTime()-new Date(t.deliveryDate).getTime())}static validateEmployeeBinding(e,t,s){return s.find(s=>s.employeeId===e&&s.taskId===t&&s.isActive)?{isValid:!1,message:"该员工已绑定此任务",warningType:"duplicate"}:s.filter(e=>e.taskId===t&&e.isActive).length>=3?{isValid:!1,message:"该任务已达到最大员工绑定数量（3名）",warningType:"max_employees"}:s.filter(s=>s.employeeId===e&&s.isActive&&s.taskId!==t).length>0?{isValid:!0,message:"警告：该员工同时绑定了其他批次任务",warningType:"cross_batch"}:{isValid:!0,message:"绑定验证通过"}}static createEmployeeBinding(e,t,s,i,n,r){let a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:"qr_code";return{id:"binding-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),employeeId:e,employeeName:t,employeeCode:s,taskId:i,batchNumber:n,bindingTime:new Date().toISOString(),scanMethod:a,bindingMolds:r,isActive:!0,createdAt:new Date().toISOString()}}static calculateBasicStatistics(e,t){let s=new Date().toISOString().split("T")[0],i=e.length,n=e.filter(e=>"completed"===e.status).length,r=t.filter(e=>e.isActive&&e.bindingTime.startsWith(s));return{totalTasks:i,completedTasks:n,activeEmployees:new Set(r.map(e=>e.employeeId)).size,completionRate:Math.round(100*(i>0?n/i*100:0))/100}}static performBindingErrorCheck(e,t,s){return!e||e.length<3?{hasError:!0,errorType:"invalid_employee",errorMessage:"无效的员工工牌号",suggestions:["请检查工牌号是否正确","确认工牌是否可读"]}:s.find(s=>s.employeeCode===e&&s.taskId===t&&s.isActive)?{hasError:!0,errorType:"duplicate_binding",errorMessage:"该员工已绑定此批次",suggestions:["如需重新绑定，请先解除原绑定","确认是否为其他员工操作"]}:s.filter(e=>e.taskId===t&&e.isActive).length>=3?{hasError:!0,errorType:"max_binding_exceeded",errorMessage:"该批次绑定员工已达上限（3名）",suggestions:["请选择其他批次","或等待当前员工完成后解绑"]}:{hasError:!1}}static getPriorityTasks(e){let t=e.filter(e=>"waiting"===e.status);return this.sortTasksByPriority(t).slice(0,5)}}let{Option:O}=r.default,{Search:V}=a.default,H=()=>{let{message:e,modal:t}=c.Z.useApp(),[s,r]=(0,n.useState)(!1),[P,D]=(0,n.useState)(!1),[I,O]=(0,n.useState)(null),[V,H]=(0,n.useState)(""),[q,R]=(0,n.useState)(0),[L,Q]=(0,n.useState)([{id:"task-001",taskNumber:"HP-2024-001",productModelCode:"CP-202",productName:"精密机械组件A",batchNumber:"BATCH-001",formingCompletionRate:85.5,hotPressQuantity:1200,remainingQuantity:800,deliveryDate:"2024-01-20",status:"waiting",assignedEmployees:[],createdAt:"2024-01-15T08:00:00Z",updatedAt:"2024-01-15T08:00:00Z"},{id:"task-002",taskNumber:"HP-2024-002",productModelCode:"CP-305",productName:"电子控制器B",batchNumber:"BATCH-002",formingCompletionRate:92,hotPressQuantity:800,remainingQuantity:600,deliveryDate:"2024-01-18",status:"in_progress",assignedEmployees:["EMP001","EMP002"],startTime:"2024-01-15T09:00:00Z",createdAt:"2024-01-15T09:00:00Z",updatedAt:"2024-01-15T09:00:00Z"}]),[W,F]=(0,n.useState)([{id:"binding-001",employeeId:"EMP001",employeeName:"王师傅",employeeCode:"W001",taskId:"task-002",batchNumber:"BATCH-002",bindingTime:"2024-01-15T09:00:00Z",scanMethod:"qr_code",bindingMolds:400,isActive:!0,createdAt:"2024-01-15T09:00:00Z"},{id:"binding-002",employeeId:"EMP002",employeeName:"李师傅",employeeCode:"L002",taskId:"task-002",batchNumber:"BATCH-002",bindingTime:"2024-01-15T10:00:00Z",scanMethod:"qr_code",bindingMolds:400,isActive:!0,createdAt:"2024-01-15T10:00:00Z"}]),[K]=(0,n.useState)([{id:"eq-001",equipmentCode:"HP-001",equipmentName:"热压机A",equipmentType:"hot_press_machine",status:"running",currentLoad:80,maxCapacity:1e3,efficiency:95,lastMaintenanceDate:"2024-01-01",nextMaintenanceDate:"2024-02-01",operatorId:"EMP001",operatorName:"王师傅"},{id:"eq-002",equipmentCode:"HP-002",equipmentName:"热压机B",equipmentType:"hot_press_machine",status:"running",currentLoad:60,maxCapacity:1200,efficiency:88,lastMaintenanceDate:"2024-01-05",nextMaintenanceDate:"2024-02-05"}]),U=B.calculateBasicStatistics(L,W),Y=B.getPriorityTasks(L),$=async()=>{if(I&&V){r(!0);try{let s=B.performBindingErrorCheck(V,I.id,W);if(s.hasError){var e;t.error({title:"绑定失败",content:s.errorMessage,footer:(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{children:"建议："}),(0,i.jsx)("ul",{children:null===(e=s.suggestions)||void 0===e?void 0:e.map((e,t)=>(0,i.jsxs)("li",{children:["• ",e]},t))})]})});return}let n=B.validateEmployeeBinding(V,I.id,W);if(!n.isValid){t.error({title:"绑定验证失败",content:n.message});return}let r=B.createEmployeeBinding(V,"员工".concat(V),V,I.id,I.batchNumber,q||I.remainingQuantity);F(e=>[...e,r]),Q(e=>e.map(e=>{if(e.id===I.id){let t={...e};return t.assignedEmployees.includes(V)||t.assignedEmployees.push(V),"waiting"===t.status&&(t.status="in_progress",t.startTime=new Date().toISOString()),t.updatedAt=new Date().toISOString(),t}return e})),D(!1),H(""),R(0),O(null)}catch(e){t.error({title:"绑定失败",content:"系统错误，请重试"})}finally{r(!1)}}},G=e=>{F(t=>t.map(t=>t.id===e?{...t,isActive:!1,unbindingTime:new Date().toISOString()}:t))};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsx)("div",{className:"page-header",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(M.Z,{className:"text-2xl text-orange-600 mr-3"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"page-title",children:"热压机生产看板"}),(0,i.jsx)("p",{className:"page-description",children:"热压机生产任务看板和员工绑定系统"})]})]}),(0,i.jsx)(u.ZP,{icon:(0,i.jsx)(E.Z,{}),onClick:()=>window.location.reload(),children:"刷新看板"})]})}),(0,i.jsxs)(x.Z,{gutter:[16,16],children:[(0,i.jsx)(h.Z,{xs:24,sm:6,children:(0,i.jsx)(g.Z,{children:(0,i.jsx)(p.Z,{title:"任务完成率",value:U.completionRate,suffix:"%",valueStyle:{color:"#52c41a"},prefix:(0,i.jsx)(S.Z,{})})})}),(0,i.jsx)(h.Z,{xs:24,sm:6,children:(0,i.jsx)(g.Z,{children:(0,i.jsx)(p.Z,{title:"待处理任务",value:U.totalTasks-U.completedTasks,suffix:"个",valueStyle:{color:"#1890ff"},prefix:(0,i.jsx)(N.Z,{})})})}),(0,i.jsx)(h.Z,{xs:24,sm:6,children:(0,i.jsx)(g.Z,{children:(0,i.jsx)(p.Z,{title:"已完成任务",value:U.completedTasks,suffix:"个",valueStyle:{color:"#52c41a"},prefix:(0,i.jsx)(M.Z,{})})})}),(0,i.jsx)(h.Z,{xs:24,sm:6,children:(0,i.jsx)(g.Z,{children:(0,i.jsx)(p.Z,{title:"活跃员工",value:U.activeEmployees,suffix:"人",valueStyle:{color:"#722ed1"},prefix:(0,i.jsx)(T.Z,{})})})})]}),Y.length>0&&(0,i.jsx)(x.Z,{gutter:[16,16],children:(0,i.jsx)(h.Z,{xs:24,children:(0,i.jsx)(f.Z,{message:"优先任务提醒",description:(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{children:["当前有 ",Y.length," 个优先任务需要处理："]}),(0,i.jsxs)("ul",{className:"mb-0",children:[Y.slice(0,3).map((e,t)=>(0,i.jsxs)("li",{children:["• ",e.batchNumber," - ",e.productName," (交货日期: ",new Date(e.deliveryDate).toLocaleDateString(),")"]},t)),Y.length>3&&(0,i.jsxs)("li",{children:["• 还有 ",Y.length-3," 个任务..."]})]})]}),type:"info",showIcon:!0,icon:(0,i.jsx)(A,{})})})}),(0,i.jsx)(g.Z,{title:"设备状态",children:(0,i.jsx)(x.Z,{gutter:[16,16],children:K.map(e=>(0,i.jsx)(h.Z,{xs:24,sm:12,lg:8,children:(0,i.jsx)(g.Z,{size:"small",className:"border-l-4 border-l-blue-500",children:(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("h4",{className:"font-medium",children:e.equipmentName}),(0,i.jsx)(d.Z,{color:"running"===e.status?"green":"idle"===e.status?"default":"maintenance"===e.status?"orange":"red",children:"running"===e.status?"运行中":"idle"===e.status?"空闲":"maintenance"===e.status?"维护中":"故障"})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,i.jsxs)("div",{children:["负荷: ",e.currentLoad,"%"]}),(0,i.jsxs)("div",{children:["效率: ",e.efficiency,"%"]}),(0,i.jsxs)("div",{children:["产能: ",e.maxCapacity," 模/天"]})]}),e.operatorName&&(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:["操作员: ",e.operatorName]})]})})},e.id))})}),(0,i.jsx)(g.Z,{title:"生产任务看板",extra:(0,i.jsx)("div",{className:"text-sm text-gray-500",children:"按交货日期排序 | 自动更新"}),children:(0,i.jsx)(j.Z,{columns:[{title:"任务信息",key:"taskInfo",width:200,render:(e,t)=>(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium",children:t.taskNumber}),(0,i.jsx)("div",{className:"text-sm text-gray-500",children:t.productName}),(0,i.jsxs)("div",{className:"text-xs text-gray-400",children:["批次: ",t.batchNumber]})]})},{title:"成型完成率",dataIndex:"formingCompletionRate",key:"formingCompletionRate",width:120,render:e=>(0,i.jsx)("div",{children:(0,i.jsx)(l.Z,{percent:e,size:"small",format:()=>"".concat(e,"%"),strokeColor:e>=90?"#52c41a":e>=70?"#faad14":"#ff4d4f"})}),sorter:(e,t)=>t.formingCompletionRate-e.formingCompletionRate},{title:"数量信息",key:"quantity",width:120,render:(e,t)=>(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsxs)("div",{children:["已热压: ",t.hotPressQuantity]}),(0,i.jsxs)("div",{children:["剩余: ",t.remainingQuantity]})]})},{title:"交货日期",dataIndex:"deliveryDate",key:"deliveryDate",width:100,render:e=>{let t=new Date(e),s=new Date,n=Math.ceil((t.getTime()-s.getTime())/864e5);return(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{children:e}),(0,i.jsx)("div",{className:"text-xs ".concat(n<=1?"text-red-500":n<=3?"text-orange-500":"text-gray-500"),children:n<=0?"已逾期":"".concat(n,"天后")})]})},sorter:(e,t)=>new Date(e.deliveryDate).getTime()-new Date(t.deliveryDate).getTime()},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>{let t={waiting:{color:"default",text:"等待中",icon:(0,i.jsx)(N.Z,{})},in_progress:{color:"blue",text:"进行中",icon:(0,i.jsx)(w.Z,{})},completed:{color:"green",text:"已完成",icon:(0,i.jsx)(C.Z,{})}}[e];return(0,i.jsx)(d.Z,{color:t.color,icon:t.icon,children:t.text})}},{title:"绑定员工",dataIndex:"assignedEmployees",key:"assignedEmployees",width:120,render:e=>(0,i.jsxs)("div",{children:[(0,i.jsx)(o.Z,{count:e.length,showZero:!0,children:(0,i.jsx)(T.Z,{className:"text-lg"})}),(0,i.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[e.length,"/3 人"]})]})},{title:"操作",key:"action",width:120,render:(e,t)=>(0,i.jsx)(m.Z,{size:"small",children:(0,i.jsx)(u.ZP,{type:"primary",size:"small",icon:(0,i.jsx)(k.Z,{}),onClick:()=>{O(t),D(!0)},disabled:t.assignedEmployees.length>=3,children:"绑定"})})}],dataSource:B.sortTasksByPriority(L),rowKey:"id",pagination:{pageSize:10},scroll:{x:1e3},rowClassName:e=>{let t=new Date,s=Math.ceil((new Date(e.deliveryDate).getTime()-t.getTime())/864e5);return s<=3?"bg-red-50":s<=7?"bg-orange-50":""}})}),(0,i.jsx)(g.Z,{title:"员工绑定记录",children:(0,i.jsx)(v.Z,{dataSource:W.filter(e=>e.isActive),renderItem:e=>{let t=L.find(t=>t.id===e.taskId);return(0,i.jsx)(v.Z.Item,{actions:[(0,i.jsx)(y.Z,{title:"确认解除绑定？",onConfirm:()=>G(e.id),okText:"确认",cancelText:"取消",children:(0,i.jsx)(u.ZP,{type:"text",danger:!0,size:"small",icon:(0,i.jsx)(z.Z,{}),children:"解绑"})})],children:(0,i.jsx)(v.Z.Item.Meta,{avatar:(0,i.jsx)(Z.Z,{icon:(0,i.jsx)(k.Z,{})}),title:(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{children:e.employeeName}),(0,i.jsx)(d.Z,{children:e.employeeCode}),(0,i.jsx)(o.Z,{status:"qr_code"===e.scanMethod?"success":"default",text:"qr_code"===e.scanMethod?"扫码绑定":"手动绑定"})]}),description:(0,i.jsxs)("div",{className:"text-sm",children:[(0,i.jsxs)("div",{children:["任务: ",null==t?void 0:t.taskNumber," | 批次: ",e.batchNumber]}),(0,i.jsxs)("div",{children:["绑定模数: ",e.bindingMolds," | 时间: ",new Date(e.bindingTime).toLocaleString()]})]})})})}})}),(0,i.jsx)(b.Z,{title:"员工绑定",open:P,onCancel:()=>D(!1),onOk:$,confirmLoading:s,width:600,children:I&&(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)(f.Z,{message:"员工绑定确认",description:"请扫描员工工牌或手动输入工牌号进行绑定",type:"info",showIcon:!0}),(0,i.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"任务信息"}),(0,i.jsxs)(x.Z,{gutter:16,children:[(0,i.jsxs)(h.Z,{span:12,children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"任务号:"})," ",I.taskNumber]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"产品:"})," ",I.productName]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"批次:"})," ",I.batchNumber]})]}),(0,i.jsxs)(h.Z,{span:12,children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"剩余数量:"})," ",I.remainingQuantity," 模"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"交货日期:"})," ",I.deliveryDate]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"已绑定员工:"})," ",I.assignedEmployees.length,"/3 人"]})]})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"员工工牌"}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)(a.default,{value:V,onChange:e=>H(e.target.value),placeholder:"请输入或扫描员工工牌号",className:"flex-1"}),(0,i.jsx)(u.ZP,{icon:(0,i.jsx)(_.Z,{}),onClick:()=>{let e=["W001","L002","Z003","S004"];H(e[Math.floor(Math.random()*e.length)])},type:"primary",children:"扫码"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"绑定模数"}),(0,i.jsx)(a.default,{type:"number",value:q,onChange:e=>R(Number(e.target.value)),placeholder:"请输入绑定模数（可选，默认为剩余数量）",addonAfter:"模"}),(0,i.jsxs)("div",{className:"mt-1 text-sm text-gray-500",children:["留空将自动设置为剩余数量 (",I.remainingQuantity," 模)"]})]}),V&&(0,i.jsxs)("div",{className:"bg-blue-50 p-3 rounded",children:[(0,i.jsx)("h4",{className:"font-medium mb-2 text-blue-800",children:"绑定预览"}),(0,i.jsxs)("div",{className:"text-sm text-blue-700",children:[(0,i.jsxs)("p",{children:["员工工牌: ",V]}),(0,i.jsxs)("p",{children:["绑定模数: ",q||I.remainingQuantity," 模"]}),(0,i.jsxs)("p",{children:["绑定时间: ",new Date().toLocaleString()]})]})]}),I.assignedEmployees.length>0&&(0,i.jsxs)("div",{children:[(0,i.jsx)("h4",{className:"font-medium mb-2",children:"当前绑定员工"}),(0,i.jsx)("div",{className:"space-y-2",children:W.filter(e=>e.taskId===I.id&&e.isActive).map(e=>(0,i.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("span",{className:"font-medium",children:e.employeeName}),(0,i.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:["(",e.employeeCode,")"]})]}),(0,i.jsxs)("div",{className:"text-sm text-gray-600",children:[e.bindingMolds," 模"]})]},e.id))})]})]})})]})};var q=()=>(0,i.jsx)(c.Z,{children:(0,i.jsx)(H,{})})}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9992,9427,4863,7416,8236,2146,5424,7829,2971,4938,1744],function(){return e(e.s=71390)}),_N_E=e.O()}]);