(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[340],{34851:function(e,l,t){Promise.resolve().then(t.bind(t,18142))},18142:function(e,l,t){"use strict";t.r(l);var s=t(57437),r=t(2265),i=t(27296),n=t(39992),c=t(11330),a=t(89198),o=t(6053),d=t(78634),u=t(13102),x=t(65270),h=t(94734),j=t(92503),m=t(55175),p=t(38302),Z=t(28683),v=t(89511),g=t(86155),f=t(50574),b=t(47628),y=t(99617),I=t(59189),w=t(44753),S=t(68826),N=t(43314),C=t(87304),P=t(75216),k=t(65362),T=t(66887),A=t(34021),D=t(75393),L=t(51769),B=t(74898);let{Option:q}=i.default,{TextArea:O}=n.default,{Step:z}=c.default;l.default=()=>{let[e,l]=(0,r.useState)([]),[t,R]=(0,r.useState)(!1),[M,W]=(0,r.useState)(!1),[F,_]=(0,r.useState)(!1),[E,V]=(0,r.useState)(!1),[J,U]=(0,r.useState)(null),[K,Q]=(0,r.useState)(null),[X,Y]=(0,r.useState)(null),[G]=a.Z.useForm(),[H,$]=(0,r.useState)(""),[ee,el]=(0,r.useState)(""),[et,es]=(0,r.useState)(""),[er,ei]=(0,r.useState)("");(0,r.useEffect)(()=>{l([{id:"1",serviceNumber:"AS-2024-001",customerId:"1",customerName:"上海包装材料有限公司",contactPerson:"张经理",contactPhone:"13800138001",serviceType:"complaint",relatedOrderNumber:"SO-2024-001",productBatchNumber:"P20240115-001",issueDescription:"产品表面有轻微划痕，影响外观质量",urgencyLevel:"medium",status:"processing",assignedTo:"客服专员A",resolutionPlan:"安排质检人员现场查看，确认问题原因并提供解决方案",resolutionResult:"已确认为运输过程中造成的划痕，将重新发货",customerSatisfaction:4,traceabilityInfo:{id:"1",batchNumber:"P20240115-001",productModelCode:"CP-202",productionDate:"2024-01-15",workstation:"WS03",operator:"操作员张三",moldCode:"M-JX-05",productionOrderNumber:"PO-2024-001",qualityCheckResult:"合格",rawMaterialBatch:"RM-20240110-001",environmentalCertificate:"ENV-2024-001",createdAt:"2024-01-15T08:00:00"},createdAt:"2024-01-26T09:00:00",updatedAt:"2024-01-26T15:00:00",resolvedAt:"2024-01-26T15:00:00"},{id:"2",serviceNumber:"AS-2024-002",customerId:"2",customerName:"北京绿色包装科技公司",contactPerson:"王总",contactPhone:"13900139002",serviceType:"return",relatedOrderNumber:"SO-2024-002",productBatchNumber:"P20240118-001",issueDescription:"客户订单变更，需要退回部分产品",urgencyLevel:"low",status:"resolved",assignedTo:"客服专员B",resolutionPlan:"确认退货原因，安排物流回收",resolutionResult:"已安排物流回收，退款已处理",customerSatisfaction:5,createdAt:"2024-01-28T10:00:00",updatedAt:"2024-01-29T16:00:00",resolvedAt:"2024-01-29T16:00:00"},{id:"3",serviceNumber:"AS-2024-003",customerId:"3",customerName:"广州环保餐具厂",contactPerson:"陈主任",contactPhone:"13700137003",serviceType:"repair",productBatchNumber:"P20240120-001",issueDescription:"产品出现质量问题，需要技术支持",urgencyLevel:"high",status:"pending",assignedTo:"技术专员C",resolutionPlan:"派遣技术人员现场支持",createdAt:"2024-01-30T14:00:00",updatedAt:"2024-01-30T14:00:00"}])},[]);let en=e=>{let l={complaint:{color:"red",text:"投诉"},return:{color:"orange",text:"退货"},exchange:{color:"blue",text:"换货"},repair:{color:"purple",text:"维修"},consultation:{color:"green",text:"咨询"}}[e]||{color:"default",text:"其他"};return(0,s.jsx)(o.Z,{color:l.color,children:l.text})},ec=e=>{let l={low:{color:"green",text:"低"},medium:{color:"orange",text:"中"},high:{color:"red",text:"高"},urgent:{color:"volcano",text:"紧急"}}[e]||{color:"default",text:"未知"};return(0,s.jsx)(o.Z,{color:l.color,children:l.text})},ea=e=>{let l={pending:{color:"orange",text:"待处理",icon:(0,s.jsx)(S.Z,{})},processing:{color:"blue",text:"处理中",icon:(0,s.jsx)(N.Z,{})},resolved:{color:"green",text:"已解决",icon:(0,s.jsx)(C.Z,{})},closed:{color:"default",text:"已关闭",icon:(0,s.jsx)(C.Z,{})}}[e]||{color:"default",text:"未知",icon:null};return(0,s.jsx)(o.Z,{color:l.color,icon:l.icon,children:l.text})},eo=[{title:"服务单号",dataIndex:"serviceNumber",key:"serviceNumber",width:140,fixed:"left"},{title:"客户信息",key:"customerInfo",width:180,render:(e,l)=>(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:l.customerName}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:[l.contactPerson," | ",l.contactPhone]})]})},{title:"服务类型",dataIndex:"serviceType",key:"serviceType",width:100,render:e=>en(e)},{title:"紧急程度",dataIndex:"urgencyLevel",key:"urgencyLevel",width:100,render:e=>ec(e)},{title:"问题描述",dataIndex:"issueDescription",key:"issueDescription",width:250,ellipsis:!0,render:e=>(0,s.jsx)(d.Z,{title:e,children:e})},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>ea(e)},{title:"处理人",dataIndex:"assignedTo",key:"assignedTo",width:100},{title:"客户满意度",dataIndex:"customerSatisfaction",key:"customerSatisfaction",width:120,render:e=>e?(0,s.jsx)(u.Z,{disabled:!0,value:e}):"-"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:150,render:e=>new Date(e).toLocaleString(),sorter:(e,l)=>new Date(e.createdAt).getTime()-new Date(l.createdAt).getTime()},{title:"操作",key:"action",width:250,fixed:"right",render:(e,l)=>(0,s.jsxs)(x.Z,{size:"small",children:[(0,s.jsx)(h.ZP,{type:"link",icon:(0,s.jsx)(P.Z,{}),onClick:()=>eh(l),children:"详情"}),(0,s.jsx)(h.ZP,{type:"link",icon:(0,s.jsx)(k.Z,{}),onClick:()=>ex(l),children:"编辑"}),l.productBatchNumber&&(0,s.jsx)(h.ZP,{type:"link",icon:(0,s.jsx)(T.Z,{}),onClick:()=>ej(l),children:"追溯"}),(0,s.jsx)(j.Z,{title:"确定要删除这个服务记录吗？",onConfirm:()=>em(l.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(h.ZP,{type:"link",danger:!0,icon:(0,s.jsx)(A.Z,{}),children:"删除"})})]})}],ed=e.filter(e=>{let l=!H||e.serviceNumber.toLowerCase().includes(H.toLowerCase())||e.customerName.toLowerCase().includes(H.toLowerCase())||e.contactPerson.toLowerCase().includes(H.toLowerCase())||e.issueDescription.toLowerCase().includes(H.toLowerCase()),t=!ee||e.status===ee,s=!et||e.serviceType===et,r=!er||e.urgencyLevel===er;return l&&t&&s&&r}),eu={total:e.length,pending:e.filter(e=>"pending"===e.status).length,processing:e.filter(e=>"processing"===e.status).length,resolved:e.filter(e=>"resolved"===e.status).length,averageSatisfaction:e.filter(e=>e.customerSatisfaction).length>0?Math.round(e.filter(e=>e.customerSatisfaction).reduce((e,l)=>e+(l.customerSatisfaction||0),0)/e.filter(e=>e.customerSatisfaction).length*10)/10:0,highUrgency:e.filter(e=>"high"===e.urgencyLevel||"urgent"===e.urgencyLevel).length},ex=e=>{U(e),W(!0),G.setFieldsValue(e)},eh=e=>{Q(e),_(!0)},ej=e=>{Q(e),Y(e.traceabilityInfo||null),V(!0)},em=t=>{l(e.filter(e=>e.id!==t)),m.ZP.success("服务记录删除成功")};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"page-header",children:[(0,s.jsx)("h1",{className:"page-title",children:"售后服务管理"}),(0,s.jsx)("p",{className:"page-description",children:"质量追溯、客户投诉处理、服务记录管理"})]}),(0,s.jsxs)(p.Z,{gutter:[16,16],children:[(0,s.jsx)(Z.Z,{xs:24,sm:6,children:(0,s.jsx)(v.Z,{children:(0,s.jsx)(g.Z,{title:"服务总数",value:eu.total,suffix:"个",prefix:(0,s.jsx)(N.Z,{})})})}),(0,s.jsx)(Z.Z,{xs:24,sm:6,children:(0,s.jsx)(v.Z,{children:(0,s.jsx)(g.Z,{title:"待处理",value:eu.pending,suffix:"个",valueStyle:{color:"#faad14"}})})}),(0,s.jsx)(Z.Z,{xs:24,sm:6,children:(0,s.jsx)(v.Z,{children:(0,s.jsx)(g.Z,{title:"平均满意度",value:eu.averageSatisfaction,suffix:"/5",valueStyle:{color:eu.averageSatisfaction>=4?"#3f8600":"#cf1322"}})})}),(0,s.jsx)(Z.Z,{xs:24,sm:6,children:(0,s.jsx)(v.Z,{children:(0,s.jsx)(g.Z,{title:"高紧急度",value:eu.highUrgency,suffix:"个",valueStyle:{color:"#cf1322"}})})})]}),(0,s.jsxs)(p.Z,{gutter:[16,16],children:[(0,s.jsx)(Z.Z,{xs:24,sm:8,children:(0,s.jsx)(v.Z,{children:(0,s.jsx)(g.Z,{title:"待处理",value:eu.pending,suffix:"个",valueStyle:{color:"#faad14"}})})}),(0,s.jsx)(Z.Z,{xs:24,sm:8,children:(0,s.jsx)(v.Z,{children:(0,s.jsx)(g.Z,{title:"处理中",value:eu.processing,suffix:"个",valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(Z.Z,{xs:24,sm:8,children:(0,s.jsx)(v.Z,{children:(0,s.jsx)(g.Z,{title:"已解决",value:eu.resolved,suffix:"个",valueStyle:{color:"#52c41a"}})})})]}),(0,s.jsx)(v.Z,{children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,s.jsx)(n.default,{placeholder:"搜索服务单号、客户名称或问题描述",prefix:(0,s.jsx)(D.Z,{}),value:H,onChange:e=>$(e.target.value),className:"w-full sm:w-64"}),(0,s.jsxs)(i.default,{placeholder:"服务状态",value:ee,onChange:el,className:"w-full sm:w-32",allowClear:!0,children:[(0,s.jsx)(q,{value:"pending",children:"待处理"}),(0,s.jsx)(q,{value:"processing",children:"处理中"}),(0,s.jsx)(q,{value:"resolved",children:"已解决"}),(0,s.jsx)(q,{value:"closed",children:"已关闭"})]}),(0,s.jsxs)(i.default,{placeholder:"服务类型",value:et,onChange:es,className:"w-full sm:w-32",allowClear:!0,children:[(0,s.jsx)(q,{value:"complaint",children:"投诉"}),(0,s.jsx)(q,{value:"return",children:"退货"}),(0,s.jsx)(q,{value:"exchange",children:"换货"}),(0,s.jsx)(q,{value:"repair",children:"维修"}),(0,s.jsx)(q,{value:"consultation",children:"咨询"})]}),(0,s.jsxs)(i.default,{placeholder:"紧急程度",value:er,onChange:ei,className:"w-full sm:w-32",allowClear:!0,children:[(0,s.jsx)(q,{value:"low",children:"低"}),(0,s.jsx)(q,{value:"medium",children:"中"}),(0,s.jsx)(q,{value:"high",children:"高"}),(0,s.jsx)(q,{value:"urgent",children:"紧急"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(L.Z,{}),children:"导出"}),(0,s.jsx)(h.ZP,{type:"primary",icon:(0,s.jsx)(B.Z,{}),onClick:()=>{U(null),W(!0),G.resetFields()},children:"新建服务"})]})]})}),(0,s.jsx)(v.Z,{title:"售后服务列表",children:(0,s.jsx)(f.Z,{columns:eo,dataSource:ed,rowKey:"id",loading:t,pagination:{total:ed.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,l)=>"第 ".concat(l[0],"-").concat(l[1]," 条/共 ").concat(e," 条")},scroll:{x:1600}})}),(0,s.jsx)(b.Z,{title:J?"编辑售后服务":"新建售后服务",open:M,onOk:()=>{G.validateFields().then(t=>{let s=new Date().toISOString();if(J)l(e.map(e=>e.id===J.id?{...e,...t,updatedAt:s}:e)),m.ZP.success("服务记录更新成功");else{let r={id:Date.now().toString(),serviceNumber:"AS-".concat(new Date().getFullYear(),"-").concat(String(e.length+1).padStart(3,"0")),...t,createdAt:s,updatedAt:s};l([...e,r]),m.ZP.success("服务记录创建成功")}W(!1),G.resetFields()})},onCancel:()=>{W(!1),G.resetFields()},width:800,okText:"确认",cancelText:"取消",children:(0,s.jsxs)(a.Z,{form:G,layout:"vertical",initialValues:{serviceType:"complaint",urgencyLevel:"medium",status:"pending"},children:[(0,s.jsxs)(p.Z,{gutter:16,children:[(0,s.jsx)(Z.Z,{span:12,children:(0,s.jsx)(a.Z.Item,{name:"customerId",label:"客户ID",rules:[{required:!0,message:"请输入客户ID"}],children:(0,s.jsx)(n.default,{placeholder:"请输入客户ID"})})}),(0,s.jsx)(Z.Z,{span:12,children:(0,s.jsx)(a.Z.Item,{name:"customerName",label:"客户名称",rules:[{required:!0,message:"请输入客户名称"}],children:(0,s.jsx)(n.default,{placeholder:"请输入客户名称"})})})]}),(0,s.jsxs)(p.Z,{gutter:16,children:[(0,s.jsx)(Z.Z,{span:12,children:(0,s.jsx)(a.Z.Item,{name:"contactPerson",label:"联系人",rules:[{required:!0,message:"请输入联系人"}],children:(0,s.jsx)(n.default,{placeholder:"请输入联系人"})})}),(0,s.jsx)(Z.Z,{span:12,children:(0,s.jsx)(a.Z.Item,{name:"contactPhone",label:"联系电话",rules:[{required:!0,message:"请输入联系电话"}],children:(0,s.jsx)(n.default,{placeholder:"请输入联系电话"})})})]}),(0,s.jsxs)(p.Z,{gutter:16,children:[(0,s.jsx)(Z.Z,{span:8,children:(0,s.jsx)(a.Z.Item,{name:"serviceType",label:"服务类型",rules:[{required:!0,message:"请选择服务类型"}],children:(0,s.jsxs)(i.default,{children:[(0,s.jsx)(q,{value:"complaint",children:"投诉"}),(0,s.jsx)(q,{value:"return",children:"退货"}),(0,s.jsx)(q,{value:"exchange",children:"换货"}),(0,s.jsx)(q,{value:"repair",children:"维修"}),(0,s.jsx)(q,{value:"consultation",children:"咨询"})]})})}),(0,s.jsx)(Z.Z,{span:8,children:(0,s.jsx)(a.Z.Item,{name:"urgencyLevel",label:"紧急程度",rules:[{required:!0,message:"请选择紧急程度"}],children:(0,s.jsxs)(i.default,{children:[(0,s.jsx)(q,{value:"low",children:"低"}),(0,s.jsx)(q,{value:"medium",children:"中"}),(0,s.jsx)(q,{value:"high",children:"高"}),(0,s.jsx)(q,{value:"urgent",children:"紧急"})]})})}),(0,s.jsx)(Z.Z,{span:8,children:(0,s.jsx)(a.Z.Item,{name:"status",label:"状态",rules:[{required:!0,message:"请选择状态"}],children:(0,s.jsxs)(i.default,{children:[(0,s.jsx)(q,{value:"pending",children:"待处理"}),(0,s.jsx)(q,{value:"processing",children:"处理中"}),(0,s.jsx)(q,{value:"resolved",children:"已解决"}),(0,s.jsx)(q,{value:"closed",children:"已关闭"})]})})})]}),(0,s.jsxs)(p.Z,{gutter:16,children:[(0,s.jsx)(Z.Z,{span:12,children:(0,s.jsx)(a.Z.Item,{name:"relatedOrderNumber",label:"关联订单号",children:(0,s.jsx)(n.default,{placeholder:"请输入关联订单号"})})}),(0,s.jsx)(Z.Z,{span:12,children:(0,s.jsx)(a.Z.Item,{name:"productBatchNumber",label:"产品批号",children:(0,s.jsx)(n.default,{placeholder:"请输入产品批号"})})})]}),(0,s.jsx)(a.Z.Item,{name:"issueDescription",label:"问题描述",rules:[{required:!0,message:"请输入问题描述"}],children:(0,s.jsx)(O,{rows:3,placeholder:"请详细描述问题"})}),(0,s.jsx)(a.Z.Item,{name:"assignedTo",label:"处理人",rules:[{required:!0,message:"请输入处理人"}],children:(0,s.jsxs)(i.default,{placeholder:"请选择处理人",children:[(0,s.jsx)(q,{value:"客服专员A",children:"客服专员A"}),(0,s.jsx)(q,{value:"客服专员B",children:"客服专员B"}),(0,s.jsx)(q,{value:"技术专员C",children:"技术专员C"}),(0,s.jsx)(q,{value:"质检专员D",children:"质检专员D"})]})}),(0,s.jsx)(a.Z.Item,{name:"resolutionPlan",label:"解决方案",children:(0,s.jsx)(O,{rows:3,placeholder:"请输入解决方案"})}),(0,s.jsx)(a.Z.Item,{name:"resolutionResult",label:"处理结果",children:(0,s.jsx)(O,{rows:3,placeholder:"请输入处理结果"})}),(0,s.jsx)(a.Z.Item,{name:"customerSatisfaction",label:"客户满意度",children:(0,s.jsx)(u.Z,{})})]})}),(0,s.jsx)(b.Z,{title:"售后服务详情",open:F,onCancel:()=>_(!1),footer:[(0,s.jsx)(h.ZP,{onClick:()=>_(!1),children:"关闭"},"close")],width:900,children:K&&K.serviceNumber&&K.customerName&&(0,s.jsxs)("div",{children:[(0,s.jsxs)(y.Z,{column:2,bordered:!0,children:[(0,s.jsx)(y.Z.Item,{label:"服务单号",children:K.serviceNumber}),(0,s.jsx)(y.Z.Item,{label:"客户名称",children:K.customerName}),(0,s.jsx)(y.Z.Item,{label:"联系人",children:K.contactPerson}),(0,s.jsx)(y.Z.Item,{label:"联系电话",children:K.contactPhone}),(0,s.jsx)(y.Z.Item,{label:"服务类型",children:en(K.serviceType)}),(0,s.jsx)(y.Z.Item,{label:"紧急程度",children:ec(K.urgencyLevel)}),(0,s.jsx)(y.Z.Item,{label:"状态",children:ea(K.status)}),(0,s.jsx)(y.Z.Item,{label:"处理人",children:K.assignedTo}),(0,s.jsx)(y.Z.Item,{label:"关联订单号",children:K.relatedOrderNumber||"无"}),(0,s.jsx)(y.Z.Item,{label:"产品批号",children:K.productBatchNumber||"无"}),(0,s.jsx)(y.Z.Item,{label:"客户满意度",children:K.customerSatisfaction?(0,s.jsx)(u.Z,{disabled:!0,value:K.customerSatisfaction}):"未评价"}),(0,s.jsx)(y.Z.Item,{label:"创建时间",children:new Date(K.createdAt).toLocaleString()}),(0,s.jsx)(y.Z.Item,{label:"更新时间",children:new Date(K.updatedAt).toLocaleString()}),(0,s.jsx)(y.Z.Item,{label:"解决时间",children:K.resolvedAt?new Date(K.resolvedAt).toLocaleString():"未解决"}),(0,s.jsx)(y.Z.Item,{label:"问题描述",span:2,children:K.issueDescription}),(0,s.jsx)(y.Z.Item,{label:"解决方案",span:2,children:K.resolutionPlan||"待制定"}),(0,s.jsx)(y.Z.Item,{label:"处理结果",span:2,children:K.resolutionResult||"处理中"})]}),(0,s.jsxs)("div",{style:{marginTop:24},children:[(0,s.jsx)("h4",{children:"服务处理流程"}),(0,s.jsxs)(c.default,{size:"small",current:"pending"===K.status?0:"processing"===K.status?1:"resolved"===K.status?2:3,children:[(0,s.jsx)(z,{title:"服务创建",description:new Date(K.createdAt).toLocaleString(),icon:(0,s.jsx)(N.Z,{})}),(0,s.jsx)(z,{title:"开始处理",description:K.assignedTo,icon:(0,s.jsx)(S.Z,{})}),(0,s.jsx)(z,{title:"问题解决",description:K.resolvedAt?new Date(K.resolvedAt).toLocaleString():"处理中",icon:(0,s.jsx)(C.Z,{})}),(0,s.jsx)(z,{title:"服务关闭",description:"客户确认满意",icon:(0,s.jsx)(C.Z,{})})]})]}),K.productBatchNumber&&(0,s.jsx)("div",{style:{marginTop:24},children:(0,s.jsx)(I.Z,{message:"质量追溯",description:"产品批号: ".concat(K.productBatchNumber,"，点击追溯按钮查看详细生产信息"),type:"info",showIcon:!0,action:(0,s.jsx)(h.ZP,{size:"small",type:"primary",icon:(0,s.jsx)(T.Z,{}),onClick:()=>ej(K),children:"质量追溯"})})})]})}),(0,s.jsx)(b.Z,{title:"质量追溯信息",open:E,onCancel:()=>V(!1),footer:[(0,s.jsx)(h.ZP,{onClick:()=>V(!1),children:"关闭"},"close")],width:800,children:X&&(0,s.jsxs)("div",{children:[(0,s.jsx)(I.Z,{message:"产品质量追溯",description:"通过产品批号追溯完整的生产过程和质量信息",type:"success",showIcon:!0,style:{marginBottom:16}}),(0,s.jsxs)(y.Z,{column:2,bordered:!0,children:[(0,s.jsx)(y.Z.Item,{label:"产品批号",children:X.batchNumber}),(0,s.jsx)(y.Z.Item,{label:"产品型号",children:X.productModelCode}),(0,s.jsx)(y.Z.Item,{label:"生产日期",children:X.productionDate}),(0,s.jsx)(y.Z.Item,{label:"生产工位",children:X.workstation}),(0,s.jsx)(y.Z.Item,{label:"操作员",children:X.operator}),(0,s.jsx)(y.Z.Item,{label:"模具编码",children:X.moldCode}),(0,s.jsx)(y.Z.Item,{label:"生产订单号",children:X.productionOrderNumber}),(0,s.jsx)(y.Z.Item,{label:"质检结果",children:(0,s.jsx)(o.Z,{color:"合格"===X.qualityCheckResult?"green":"red",children:X.qualityCheckResult})}),(0,s.jsx)(y.Z.Item,{label:"原料批次",children:X.rawMaterialBatch}),(0,s.jsx)(y.Z.Item,{label:"环保证书",children:X.environmentalCertificate}),(0,s.jsx)(y.Z.Item,{label:"记录时间",span:2,children:new Date(X.createdAt).toLocaleString()})]}),(0,s.jsxs)("div",{style:{marginTop:24},children:[(0,s.jsx)("h4",{children:"生产追溯时间线"}),(0,s.jsxs)(w.Z,{children:[(0,s.jsx)(w.Z.Item,{color:"blue",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:"原料投入"}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["原料批次: ",X.rawMaterialBatch]})]})}),(0,s.jsx)(w.Z.Item,{color:"green",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:"开始生产"}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["生产日期: ",X.productionDate," | 工位: ",X.workstation," | 操作员: ",X.operator]})]})}),(0,s.jsx)(w.Z.Item,{color:"orange",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:"模具成型"}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["模具编码: ",X.moldCode]})]})}),(0,s.jsx)(w.Z.Item,{color:"purple",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:"质量检验"}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["检验结果: ",X.qualityCheckResult]})]})}),(0,s.jsx)(w.Z.Item,{color:"cyan",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:"环保认证"}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["证书编号: ",X.environmentalCertificate]})]})}),(0,s.jsx)(w.Z.Item,{color:"green",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{style:{fontWeight:"bold"},children:"产品入库"}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["批号: ",X.batchNumber]})]})})]})]})]})})]})}}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,4863,8236,9617,1330,5470,7750,2971,4938,1744],function(){return e(e.s=34851)}),_N_E=e.O()}]);