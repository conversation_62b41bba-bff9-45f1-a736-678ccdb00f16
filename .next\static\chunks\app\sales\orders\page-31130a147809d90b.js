(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[288],{40856:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var a=r(13428),n=r(2265),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"},s=r(46614),i=n.forwardRef(function(e,t){return n.createElement(s.Z,(0,a.Z)({},e,{ref:t,icon:l}))})},51769:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var a=r(13428),n=r(2265),l={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"},s=r(46614),i=n.forwardRef(function(e,t){return n.createElement(s.Z,(0,a.Z)({},e,{ref:t,icon:l}))})},78740:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var a=r(13428),n=r(2265),l={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"}}]},name:"inbox",theme:"outlined"},s=r(46614),i=n.forwardRef(function(e,t){return n.createElement(s.Z,(0,a.Z)({},e,{ref:t,icon:l}))})},49876:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var a=r(13428),n=r(2265),l={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M922.9 701.9H327.4l29.9-60.9 496.8-.9c16.8 0 31.2-12 34.2-28.6l68.8-385.1c1.8-10.1-.9-20.5-7.5-28.4a34.99 34.99 0 00-26.6-12.5l-632-2.1-5.4-25.4c-3.4-16.2-18-28-34.6-28H96.5a35.3 35.3 0 100 70.6h125.9L246 312.8l58.1 281.3-74.8 122.1a34.96 34.96 0 00-3 36.8c6 11.9 18.1 19.4 31.5 19.4h62.8a102.43 102.43 0 00-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7h161.1a102.43 102.43 0 00-20.6 61.7c0 56.6 46 102.6 102.6 102.6s102.6-46 102.6-102.6c0-22.3-7.4-44-20.6-61.7H923c19.4 0 35.3-15.8 35.3-35.3a35.42 35.42 0 00-35.4-35.2zM305.7 253l575.8 1.9-56.4 315.8-452.3.8L305.7 253zm96.9 612.7c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 01-31.6 31.6zm325.1 0c-17.4 0-31.6-14.2-31.6-31.6 0-17.4 14.2-31.6 31.6-31.6s31.6 14.2 31.6 31.6a31.6 31.6 0 01-31.6 31.6z"}}]},name:"shopping-cart",theme:"outlined"},s=r(46614),i=n.forwardRef(function(e,t){return n.createElement(s.Z,(0,a.Z)({},e,{ref:t,icon:l}))})},81453:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var a=r(13428),n=r(2265),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"},s=r(46614),i=n.forwardRef(function(e,t){return n.createElement(s.Z,(0,a.Z)({},e,{ref:t,icon:l}))})},33729:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var a=r(13428),n=r(2265),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M848 359.3H627.7L825.8 109c4.1-5.3.4-13-6.3-13H436c-2.8 0-5.5 1.5-6.9 4L170 547.5c-3.1 5.3.7 12 6.9 12h174.4l-89.4 357.6c-1.9 7.8 7.5 13.3 13.3 7.7L853.5 373c5.2-4.9 1.7-13.7-5.5-13.7zM378.2 732.5l60.3-241H281.1l189.6-327.4h224.6L487 427.4h211L378.2 732.5z"}}]},name:"thunderbolt",theme:"outlined"},s=r(46614),i=n.forwardRef(function(e,t){return n.createElement(s.Z,(0,a.Z)({},e,{ref:t,icon:l}))})},8399:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var a=r(13428),n=r(2265),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M876.6 239.5c-.5-.9-1.2-1.8-2-2.5-5-5-13.1-5-18.1 0L684.2 409.3l-67.9-67.9L788.7 169c.8-.8 1.4-1.6 2-2.5 3.6-6.1 1.6-13.9-4.5-17.5-98.2-58-226.8-44.7-311.3 39.7-67 67-89.2 162-66.5 247.4l-293 293c-3 3-2.8 7.9.3 11l169.7 169.7c3.1 3.1 8.1 3.3 11 .3l292.9-292.9c85.5 22.8 180.5.7 247.6-66.4 84.4-84.5 97.7-213.1 39.7-311.3zM786 499.8c-58.1 58.1-145.3 69.3-214.6 33.6l-8.8 8.8-.1-.1-274 274.1-79.2-79.2 230.1-230.1s0 .1.1.1l52.8-52.8c-35.7-69.3-24.5-156.5 33.6-214.6a184.2 184.2 0 01144-53.5L537 318.9a32.05 32.05 0 000 45.3l124.5 124.5a32.05 32.05 0 0045.3 0l132.8-132.8c3.7 51.8-14.4 104.8-53.6 143.9z"}}]},name:"tool",theme:"outlined"},s=r(46614),i=n.forwardRef(function(e,t){return n.createElement(s.Z,(0,a.Z)({},e,{ref:t,icon:l}))})},79029:function(e,t,r){var a=r(42242).Symbol;e.exports=a},86714:function(e,t,r){var a=r(79029),n=r(35078),l=r(76276),s=a?a.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":s&&s in Object(e)?n(e):l(e)}},13225:function(e,t,r){var a=r(20121),n=/^\s+/;e.exports=function(e){return e?e.slice(0,a(e)+1).replace(n,""):e}},66503:function(e,t,r){var a="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;e.exports=a},35078:function(e,t,r){var a=r(79029),n=Object.prototype,l=n.hasOwnProperty,s=n.toString,i=a?a.toStringTag:void 0;e.exports=function(e){var t=l.call(e,i),r=e[i];try{e[i]=void 0;var a=!0}catch(e){}var n=s.call(e);return a&&(t?e[i]=r:delete e[i]),n}},76276:function(e){var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},42242:function(e,t,r){var a=r(66503),n="object"==typeof self&&self&&self.Object===Object&&self,l=a||n||Function("return this")();e.exports=l},20121:function(e){var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},68143:function(e,t,r){var a=r(36905),n=r(14752),l=r(71573),s=Math.max,i=Math.min;e.exports=function(e,t,r){var o,d,c,u,m,h,p=0,f=!1,g=!1,x=!0;if("function"!=typeof e)throw TypeError("Expected a function");function y(t){var r=o,a=d;return o=d=void 0,p=t,u=e.apply(a,r)}function v(e){var r=e-h,a=e-p;return void 0===h||r>=t||r<0||g&&a>=c}function j(){var e,r,a,l=n();if(v(l))return w(l);m=setTimeout(j,(e=l-h,r=l-p,a=t-e,g?i(a,c-r):a))}function w(e){return(m=void 0,x&&o)?y(e):(o=d=void 0,u)}function b(){var e,r=n(),a=v(r);if(o=arguments,d=this,h=r,a){if(void 0===m)return p=e=h,m=setTimeout(j,t),f?y(e):u;if(g)return clearTimeout(m),m=setTimeout(j,t),y(h)}return void 0===m&&(m=setTimeout(j,t)),u}return t=l(t)||0,a(r)&&(f=!!r.leading,c=(g="maxWait"in r)?s(l(r.maxWait)||0,t):c,x="trailing"in r?!!r.trailing:x),b.cancel=function(){void 0!==m&&clearTimeout(m),p=0,o=h=d=m=void 0},b.flush=function(){return void 0===m?u:w(n())},b}},36905:function(e){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},12387:function(e){e.exports=function(e){return null!=e&&"object"==typeof e}},71087:function(e,t,r){var a=r(86714),n=r(12387);e.exports=function(e){return"symbol"==typeof e||n(e)&&"[object Symbol]"==a(e)}},14752:function(e,t,r){var a=r(42242);e.exports=function(){return a.Date.now()}},71573:function(e,t,r){var a=r(13225),n=r(36905),l=r(71087),s=0/0,i=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,d=/^0o[0-7]+$/i,c=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(l(e))return s;if(n(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=n(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=a(e);var r=o.test(e);return r||d.test(e)?c(e.slice(2),r?2:8):i.test(e)?s:+e}},14849:function(e,t,r){Promise.resolve().then(r.bind(r,92347))},28116:function(e,t,r){"use strict";r.d(t,{Z:function(){return x}});var a=r(2265),n=r(42744),l=r.n(n),s=r(57499),i=r(10693),o=r(58489),d=r(11303),c=r(78387),u=r(12711);let m=e=>{let{componentCls:t}=e;return{[t]:{"&-horizontal":{["&".concat(t)]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},h=e=>{let{componentCls:t,sizePaddingEdgeHorizontal:r,colorSplit:a,lineWidth:n,textPaddingInline:l,orientationMargin:s,verticalMarginInline:i}=e;return{[t]:Object.assign(Object.assign({},(0,d.Wf)(e)),{borderBlockStart:"".concat((0,o.bf)(n)," solid ").concat(a),"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:i,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:"".concat((0,o.bf)(n)," solid ").concat(a)},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:"".concat((0,o.bf)(e.marginLG)," 0")},["&-horizontal".concat(t,"-with-text")]:{display:"flex",alignItems:"center",margin:"".concat((0,o.bf)(e.dividerHorizontalWithTextGutterMargin)," 0"),color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:"0 ".concat(a),"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:"".concat((0,o.bf)(n)," solid transparent"),borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},["&-horizontal".concat(t,"-with-text-start")]:{"&::before":{width:"calc(".concat(s," * 100%)")},"&::after":{width:"calc(100% - ".concat(s," * 100%)")}},["&-horizontal".concat(t,"-with-text-end")]:{"&::before":{width:"calc(100% - ".concat(s," * 100%)")},"&::after":{width:"calc(".concat(s," * 100%)")}},["".concat(t,"-inner-text")]:{display:"inline-block",paddingBlock:0,paddingInline:l},"&-dashed":{background:"none",borderColor:a,borderStyle:"dashed",borderWidth:"".concat((0,o.bf)(n)," 0 0")},["&-horizontal".concat(t,"-with-text").concat(t,"-dashed")]:{"&::before, &::after":{borderStyle:"dashed none none"}},["&-vertical".concat(t,"-dashed")]:{borderInlineStartWidth:n,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:a,borderStyle:"dotted",borderWidth:"".concat((0,o.bf)(n)," 0 0")},["&-horizontal".concat(t,"-with-text").concat(t,"-dotted")]:{"&::before, &::after":{borderStyle:"dotted none none"}},["&-vertical".concat(t,"-dotted")]:{borderInlineStartWidth:n,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},["&-plain".concat(t,"-with-text")]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},["&-horizontal".concat(t,"-with-text-start").concat(t,"-no-default-orientation-margin-start")]:{"&::before":{width:0},"&::after":{width:"100%"},["".concat(t,"-inner-text")]:{paddingInlineStart:r}},["&-horizontal".concat(t,"-with-text-end").concat(t,"-no-default-orientation-margin-end")]:{"&::before":{width:"100%"},"&::after":{width:0},["".concat(t,"-inner-text")]:{paddingInlineEnd:r}}})}};var p=(0,c.I$)("Divider",e=>{let t=(0,u.IX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[h(t),m(t)]},e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}}),f=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(r[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(e);n<a.length;n++)0>t.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(e,a[n])&&(r[a[n]]=e[a[n]]);return r};let g={small:"sm",middle:"md"};var x=e=>{let{getPrefixCls:t,direction:r,className:n,style:o}=(0,s.dj)("divider"),{prefixCls:d,type:c="horizontal",orientation:u="center",orientationMargin:m,className:h,rootClassName:x,children:y,dashed:v,variant:j="solid",plain:w,style:b,size:Z}=e,S=f(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),P=t("divider",d),[C,k,M]=p(P),I=g[(0,i.Z)(Z)],D=!!y,N=a.useMemo(()=>"left"===u?"rtl"===r?"end":"start":"right"===u?"rtl"===r?"start":"end":u,[r,u]),A="start"===N&&null!=m,O="end"===N&&null!=m,z=l()(P,n,k,M,"".concat(P,"-").concat(c),{["".concat(P,"-with-text")]:D,["".concat(P,"-with-text-").concat(N)]:D,["".concat(P,"-dashed")]:!!v,["".concat(P,"-").concat(j)]:"solid"!==j,["".concat(P,"-plain")]:!!w,["".concat(P,"-rtl")]:"rtl"===r,["".concat(P,"-no-default-orientation-margin-start")]:A,["".concat(P,"-no-default-orientation-margin-end")]:O,["".concat(P,"-").concat(I)]:!!I},h,x),E=a.useMemo(()=>"number"==typeof m?m:/^\d+$/.test(m)?Number(m):m,[m]);return C(a.createElement("div",Object.assign({className:z,style:Object.assign(Object.assign({},o),b)},S,{role:"separator"}),y&&"vertical"!==c&&a.createElement("span",{className:"".concat(P,"-inner-text"),style:{marginInlineStart:A?E:void 0,marginInlineEnd:O?E:void 0}},y)))}},92347:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return eP}});var a=r(57437),n=r(2265),l=r(24033),s=r(27296),i=r(39992),o=r(50030),d=r(11330),c=r(57416),u=r(89198),m=r(6053),h=r(94734),p=r(78634),f=r(9427),g=r(65270),x=r(92503),y=r(75123),v=r(38302),j=r(28683),w=r(89511),b=r(86155),Z=r(50574),S=r(47628),P=r(59189),C=r(99617),k=r(81453),M=r(34021),I=r(78740),D=r(75393),N=r(51769),A=r(74898),O=r(67883),z=r(73297),E=r(33729),R=r(32779),T=r(50538),q=r(74548),L=r.n(q),V=r(63424),F=r(82765),_=r(53641);let W={orderStatusTransition:{sales:{pending:["confirmed","cancelled"],confirmed:["completed","cancelled"],completed:[],cancelled:[]},production:{not_started:["in_progress","cancelled"],in_progress:["completed","paused"],paused:["in_progress","cancelled"],completed:[],cancelled:[]}},orderChange:{allowedTypes:["quantity","delivery_date","cancel"],restrictions:{completed:[],cancelled:[],confirmed:["quantity","delivery_date","cancel"],pending:["quantity","delivery_date","cancel"]},quantityChangeLimit:{maxIncrease:2,maxDecrease:.5,warningThreshold:.2},deliveryDateChangeLimit:{maxDaysForward:90,maxDaysBackward:7,warningThreshold:30}},creditLimit:{warningThreshold:.8,criticalThreshold:1,checkRequired:!0},inventory:{checkRequired:!0,allowNegative:!1,warningThreshold:10,criticalThreshold:0}},B={required:{orderNumber:"请输入订单号",productCode:"请输入产品编码",productName:"请输入产品名称",customerName:"请输入客户名称",quantity:"请输入数量",unitPrice:"请输入单价",deliveryDate:"请选择交货日期",workstationCode:"请输入工位编码",moldNumber:"请输入模具编号"},format:{orderNumber:"订单号格式不正确",productCode:"产品编码格式不正确",customerCode:"客户编码格式不正确",phone:"手机号格式不正确",email:"邮箱格式不正确"},range:{quantity:"数量超出允许范围",price:"价格超出允许范围",deliveryDate:"交货日期超出允许范围"},business:{orderExists:"订单号已存在",productNotFound:"产品不存在",customerNotFound:"客户不存在",insufficientCredit:"客户信用额度不足",insufficientInventory:"库存不足",invalidStatusTransition:"订单状态转换无效",changeNotAllowed:"当前状态不允许此变更"}};class Y{static async validateSalesOrder(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=[],a=[],n={};if(e.orderNumber){let a=this.validateOrderNumber(e.orderNumber,"sales",t);a.isValid||(n.orderNumber=a.errors,r.push(...a.errors))}else t.skipOptionalFields||a.push("未提供订单号，将自动生成");if(!e.customerId){let e=["客户ID不能为空"];n.customerId=e,r.push(...e)}if(!e.customerName){let e=["客户名称不能为空"];n.customerName=e,r.push(...e)}if(e.items&&0!==e.items.length)for(let t=0;t<e.items.length;t++){let a=e.items[t],l=this.validateSalesOrderItem(a,t);l.isValid||(n["items[".concat(t,"]")]=l.errors,r.push(...l.errors.map(e=>"项目".concat(t+1,": ").concat(e))))}else{let e=["订单项目不能为空"];n.items=e,r.push(...e)}if(void 0!==e.totalAmount&&e.totalAmount<0){let e=["订单总金额不能为负数"];n.totalAmount=e,r.push(...e)}if(e.deliveryDate){let t=new Date(e.deliveryDate),r=new Date;r.setHours(0,0,0,0),t<r&&a.push("交期早于当前日期，请确认是否正确")}if(t.checkUniqueness&&e.orderNumber){let a=await this.checkOrderNumberUniqueness(e.orderNumber,"sales",t.excludeId);a.isValid||(n.orderNumber=[...n.orderNumber||[],...a.errors],r.push(...a.errors))}return{isValid:0===r.length,errors:r,warnings:a,fieldErrors:n}}static async validateProductionOrder(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=[],a={};if(e.orderNumber){let n=this.validateOrderNumber(e.orderNumber,"production",t);n.isValid||(a.orderNumber=n.errors,r.push(...n.errors))}else{let e=["生产订单号不能为空"];a.orderNumber=e,r.push(...e)}if(!e.productCode){let e=["产品编码不能为空"];a.productCode=e,r.push(...e)}if(!e.productName){let e=["产品名称不能为空"];a.productName=e,r.push(...e)}if(!e.plannedQuantity||e.plannedQuantity<=0){let e=["计划数量必须大于0"];a.plannedQuantity=e,r.push(...e)}if(!e.sourceOrderIds||0===e.sourceOrderIds.length){let e=["源销售订单ID不能为空"];a.sourceOrderIds=e,r.push(...e)}if(t.checkUniqueness&&e.orderNumber){let n=await this.checkOrderNumberUniqueness(e.orderNumber,"production",t.excludeId);n.isValid||(a.orderNumber=[...a.orderNumber||[],...n.errors],r.push(...n.errors))}return{isValid:0===r.length,errors:r,warnings:[],fieldErrors:a}}static validateWorkOrder(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];let t=[],r={};if(!e.batchNumber){let e=["批次号不能为空"];r.batchNumber=e,t.push(...e)}if(!e.productCode){let e=["产品编码不能为空"];r.productCode=e,t.push(...e)}if(!e.productName){let e=["产品名称不能为空"];r.productName=e,t.push(...e)}if(!e.plannedMoldCount||e.plannedMoldCount<=0){let e=["计划模数必须大于0"];r.plannedMoldCount=e,t.push(...e)}if(!e.sourceOrderId){let e=["源订单ID不能为空"];r.sourceOrderId=e,t.push(...e)}if(!e.hourlyCapacity||e.hourlyCapacity<=0){let e=["小时产能必须大于0"];r.hourlyCapacity=e,t.push(...e)}return{isValid:0===t.length,errors:t,warnings:[],fieldErrors:r}}static validateOrderNumber(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2];let r=[],a=[];if(!e||""===e.trim())return r.push("订单号不能为空"),{isValid:!1,errors:r,warnings:a};switch(t){case"sales":_.s.validateSalesOrderNumber(e)||r.push("销售订单号格式不正确，应为：XSDD + YYYYMMDD + 4位序号");break;case"production":_.s.validateProductionOrderNumber(e)||r.push("生产订单号格式不正确，应为：PO- + 销售订单号")}return{isValid:0===r.length,errors:r,warnings:a}}static validateSalesOrderItem(e,t){let r=[];return e.productCode||r.push("产品编码不能为空"),e.productName||r.push("产品名称不能为空"),(!e.quantity||e.quantity<=0)&&r.push("数量必须大于0"),(!e.unitPrice||e.unitPrice<0)&&r.push("单价不能为负数"),{isValid:0===r.length,errors:r}}static async checkOrderNumberUniqueness(e,t,r){let a=[];try{let n=null;if("sales"===t){let t=await R.dataAccessManager.orders.getByNumber(e);"success"===t.status&&t.data&&(n=t.data)}else if("production"===t){let t=await R.dataAccessManager.productionOrders.getByOrderNumber(e);"success"===t.status&&t.data&&(n=t.data)}n&&n.id!==r&&a.push("".concat("sales"===t?"销售":"生产","订单号已存在"))}catch(e){console.error("检查订单号唯一性时发生错误:",e),a.push("无法验证订单号唯一性")}return{isValid:0===a.length,errors:a}}static async validateCustomerCredit(e,t){let r=[],a=[];try{let n=await R.dataAccessManager.customers.getById(e);if("success"!==n.status||!n.data)return r.push("客户不存在"),{isValid:!1,errors:r,warnings:a};let l=n.data,s=(l.creditLimit||0)-(l.usedCredit||0);return t>s?r.push("订单金额超出客户信用额度。可用额度：\xa5".concat(s.toFixed(2),"，订单金额：\xa5").concat(t.toFixed(2))):t>.8*s&&a.push("订单金额接近客户信用额度上限。可用额度：\xa5".concat(s.toFixed(2),"，订单金额：\xa5").concat(t.toFixed(2))),{isValid:0===r.length,errors:r,warnings:a}}catch(e){return console.error("验证客户信用额度时发生错误:",e),r.push("验证客户信用额度时发生错误"),{isValid:!1,errors:r,warnings:a}}}static validateOrderChange(e,t,r,a){let n=[],l=[];if(!function(e,t){let r=W.orderChange.restrictions[e];return!!r&&r.includes(t)}(e.status,t))return n.push(function(e,t){let r=B[e];return r&&r[t]||"".concat(t,"验证失败")}("business","changeNotAllowed")),{isValid:!1,errors:n,warnings:l};let s=function(e,t){let r=W[e];return r?t&&"object"==typeof r&&r[t]?r[t]:r:null}("orderChange");switch(t){case"quantity":let i=parseInt(a);if(isNaN(i)||i<=0)n.push("新数量必须是大于0的整数");else{let e=i/parseInt(r);if(s&&"quantityChangeLimit"in s&&s.quantityChangeLimit){let{maxIncrease:t,maxDecrease:r,warningThreshold:a}=s.quantityChangeLimit;e>t?n.push("数量增加不能超过".concat((t-1)*100,"%")):e<r?n.push("数量减少不能超过".concat((1-r)*100,"%")):Math.abs(e-1)>a&&l.push("数量变更超过".concat(100*a,"%，请确认是否正确"))}}break;case"delivery_date":let o=new Date(a),d=new Date(r),c=new Date;if(c.setHours(0,0,0,0),o<c)n.push("新交期不能早于当前日期");else{let e=Math.ceil((o.getTime()-d.getTime())/864e5);if(s&&"deliveryDateChangeLimit"in s&&s.deliveryDateChangeLimit){let{maxDaysForward:t,maxDaysBackward:r,warningThreshold:a}=s.deliveryDateChangeLimit;e>t?n.push("交期延后不能超过".concat(t,"天")):e<-r?n.push("交期提前不能超过".concat(r,"天")):Math.abs(e)>a&&l.push("交期变更超过".concat(a,"天，请确认是否正确"))}}break;case"cancel":"in_progress"===e.productionStatus&&n.push("生产中的订单不能取消，请先停止生产")}return{isValid:0===n.length,errors:n,warnings:l}}static async validateSalesOrderBatch(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=[],a=0,n=0,l=0;for(let s of e){let e=await this.validateSalesOrder(s,t);r.push(e),e.isValid?a++:n++,e.warnings&&e.warnings.length>0&&l++}return{results:r,summary:{valid:a,invalid:n,warnings:l}}}static createFormValidationRules(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=[];switch(!1!==t.required&&r.push({required:!0,message:"请输入".concat(this.getFieldDisplayName(e))}),e){case"orderNumber":t.orderType&&r.push({validator:(e,r)=>{if(!r)return Promise.resolve();let a=this.validateOrderNumber(r,t.orderType);return a.isValid?Promise.resolve():Promise.reject(Error(a.errors[0]))}});break;case"positiveNumber":r.push({validator:(r,a)=>null==a?Promise.resolve():a<=0?Promise.reject(Error("".concat(this.getFieldDisplayName(e),"必须大于0"))):void 0!==t.min&&a<t.min?Promise.reject(Error("".concat(this.getFieldDisplayName(e),"不能小于").concat(t.min))):void 0!==t.max&&a>t.max?Promise.reject(Error("".concat(this.getFieldDisplayName(e),"不能大于").concat(t.max))):Promise.resolve()});break;case"email":r.push({type:"email",message:"请输入有效的邮箱地址"});break;case"phone":r.push({pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号码"})}return t.checkUniqueness&&r.push({validator:async(r,a)=>a?await t.checkUniqueness(a)?Promise.resolve():Promise.reject(Error("".concat(this.getFieldDisplayName(e),"已存在"))):Promise.resolve()}),t.customValidator&&r.push({validator:(e,r)=>{if(!r&&!t.required)return Promise.resolve();let a=t.customValidator(r);return a.isValid?Promise.resolve():Promise.reject(Error(a.errors[0]||"验证失败"))}}),r}static getFieldDisplayName(e){return({orderNumber:"订单号",productCode:"产品编码",customerCode:"客户编码",workstationCode:"工位编码",moldNumber:"模具编号",batchNumber:"批次号",email:"邮箱",phone:"电话",positiveNumber:"数值",text:"文本"})[e]||e}}var Q=r(49876),X=r(57613);let{Option:H}=s.default;var U=e=>{let{open:t,onCancel:r,onSelect:l}=e,{message:o}=c.Z.useApp(),[d,u]=(0,n.useState)(!1),[p,f]=(0,n.useState)([]),[g,x]=(0,n.useState)([]),[y,P]=(0,n.useState)(""),[C,k]=(0,n.useState)(""),[M,I]=(0,n.useState)([]),[N,A]=(0,n.useState)([]),O=async()=>{u(!0);try{let e=await R.dataAccessManager.products.getActive();if("success"===e.status&&e.data){let t=[];t=Array.isArray(e.data)?e.data:e.data&&"object"==typeof e.data&&"items"in e.data?e.data.items:[],f(t),x(t)}else o.error("获取产品数据失败")}catch(e){o.error("加载产品数据失败")}finally{u(!1)}},z=()=>{let e=[...p];if(y){let t=y.toLowerCase();e=e.filter(e=>e.modelCode.toLowerCase().includes(t)||e.modelName.toLowerCase().includes(t))}C&&(e=e.filter(e=>e.status===C)),x(e)},E=()=>{P(""),k(""),I([]),A([]),r()};return(0,n.useEffect)(()=>{t&&O()},[t]),(0,n.useEffect)(()=>{z()},[y,C,p]),(0,a.jsxs)(S.Z,{title:"选择产品",open:t,onCancel:E,width:1e3,footer:[(0,a.jsx)(h.ZP,{onClick:E,children:"取消"},"cancel"),(0,a.jsx)(h.ZP,{onClick:()=>{I([]),A([])},disabled:0===N.length,children:"清空选择"},"clear"),(0,a.jsxs)(h.ZP,{type:"primary",icon:(0,a.jsx)(Q.Z,{}),onClick:()=>{N.length>0?(l(N),E()):o.warning("请至少选择一个产品")},disabled:0===N.length,children:["确认添加 (",N.length,"个产品)"]},"select")],destroyOnHidden:!0,children:[(0,a.jsxs)(v.Z,{gutter:16,className:"mb-4",children:[(0,a.jsx)(j.Z,{span:8,children:(0,a.jsx)(w.Z,{size:"small",children:(0,a.jsx)(b.Z,{title:"可用产品",value:p.filter(e=>"active"===e.status).length,suffix:"个"})})}),(0,a.jsx)(j.Z,{span:8,children:(0,a.jsx)(w.Z,{size:"small",children:(0,a.jsx)(b.Z,{title:"筛选结果",value:g.length,suffix:"个"})})}),(0,a.jsx)(j.Z,{span:8,children:(0,a.jsx)(w.Z,{size:"small",children:(0,a.jsx)(b.Z,{title:"平均价格",value:g.length>0?g.reduce((e,t)=>e+t.productPrice,0)/g.length:0,precision:3,prefix:"\xa5"})})})]}),(0,a.jsxs)(v.Z,{gutter:16,className:"mb-4",children:[(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(i.default,{placeholder:"搜索产品编码或名称",prefix:(0,a.jsx)(D.Z,{}),value:y,onChange:e=>P(e.target.value),allowClear:!0})}),(0,a.jsx)(j.Z,{span:6,children:(0,a.jsxs)(s.default,{placeholder:"状态筛选",value:C,onChange:k,style:{width:"100%"},allowClear:!0,children:[(0,a.jsx)(H,{value:"",children:"全部状态"}),(0,a.jsx)(H,{value:"active",children:"启用"}),(0,a.jsx)(H,{value:"inactive",children:"停用"})]})}),(0,a.jsx)(j.Z,{span:6,children:(0,a.jsx)(h.ZP,{onClick:O,loading:d,children:"刷新数据"})})]}),(0,a.jsx)(Z.Z,{columns:[{title:"产品编码",dataIndex:"modelCode",key:"modelCode",width:120,fixed:"left"},{title:"产品名称",dataIndex:"modelName",key:"modelName",width:200,ellipsis:!0},{title:"产品价格",dataIndex:"productPrice",key:"productPrice",width:120,render:e=>e?"\xa5".concat(e.toFixed(3)):"\xa50.000",sorter:(e,t)=>(e.productPrice||0)-(t.productPrice||0)},{title:"产品重量",dataIndex:"productWeight",key:"productWeight",width:120,render:e=>e?"".concat(e.toFixed(2),"g"):"0.00g",sorter:(e,t)=>(e.productWeight||0)-(t.productWeight||0)},{title:"成型模具",dataIndex:"formingMold",key:"formingMold",width:120,ellipsis:!0},{title:"单模数量",dataIndex:"formingMoldQuantity",key:"formingMoldQuantity",width:100,render:e=>"".concat(e||0,"个")},{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>(0,a.jsx)(m.Z,{color:"active"===e?"green":"red",children:"active"===e?"启用":"停用"})}],dataSource:g,rowKey:"id",rowSelection:{type:"checkbox",selectedRowKeys:M,onChange:(e,t)=>{I(e),A(t)},onSelectAll:(e,t,r)=>{if(e){let e=[...N];r.forEach(t=>{e.find(e=>e.id===t.id)||e.push(t)}),A(e)}else{let e=r.map(e=>e.id);A(N.filter(t=>!e.includes(t.id)))}},onSelect:(e,t)=>{t?N.find(t=>t.id===e.id)||A([...N,e]):A(N.filter(t=>t.id!==e.id))}},loading:d,pagination:{total:g.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条")},scroll:{x:800,y:400},size:"small"}),N.length>0&&(0,a.jsx)(w.Z,{title:"已选择产品 (".concat(N.length,"个)"),size:"small",className:"mt-4",children:(0,a.jsx)("div",{style:{maxHeight:"200px",overflowY:"auto"},children:N.map((e,t)=>(0,a.jsxs)(v.Z,{gutter:16,className:t>0?"mt-2 pt-2":"",style:t>0?{borderTop:"1px solid #f0f0f0"}:{},children:[(0,a.jsxs)(j.Z,{span:6,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"编码:"})," ",e.modelCode]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"名称:"})," ",e.modelName]})]}),(0,a.jsxs)(j.Z,{span:6,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"价格:"})," \xa5",e.productPrice?e.productPrice.toFixed(3):"0.000"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"重量:"})," ",e.productWeight?e.productWeight.toFixed(2):"0.00","g"]})]}),(0,a.jsxs)(j.Z,{span:6,children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"成型模具:"})," ",e.formingMold||"-"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:"单模数量:"})," ",e.formingMoldQuantity||0,"个"]})]}),(0,a.jsx)(j.Z,{span:6,children:(0,a.jsx)(h.ZP,{type:"link",size:"small",danger:!0,onClick:()=>{let t=N.filter(t=>t.id!==e.id),r=M.filter(t=>t!==e.id);A(t),I(r)},children:"移除"})})]},e.id))})})]})},$=r(2012),J=r(8399),K=r(40856);let{Option:G}=s.default;var ee=e=>{let{open:t,onCancel:r,onSuccess:l}=e,{message:o}=c.Z.useApp(),[d]=u.Z.useForm(),[m,h]=(0,n.useState)(!1),[p,f]=(0,n.useState)([]),[g,x]=(0,n.useState)(""),w=async()=>{try{let e=await R.dataAccessManager.products.getAll();if("success"===e.status&&e.data){let t=[];Array.isArray(e.data)?t=e.data:"items"in e.data&&Array.isArray(e.data.items)&&(t=e.data.items),f(t)}}catch(e){}},b=async()=>{try{let e=await R.dataAccessManager.products.getAll();if("success"===e.status&&e.data){let t=[];if(Array.isArray(e.data))t=e.data;else{if(!("items"in e.data&&Array.isArray(e.data.items)))return"P00001";t=e.data.items}let r=t.map(e=>e.modelCode).filter(e=>/^P\d{5}$/.test(e)).map(e=>parseInt(e.substring(1),10)).sort((e,t)=>t-e),a=r.length>0?r[0]+1:1;return"P".concat(a.toString().padStart(5,"0"))}}catch(e){}return"P00001"},Z=e=>!p.some(t=>t.modelCode===e),P=e=>/^M-[A-Z]{2}-\d{2}$/.test(e),C=(e,t)=>"forming"===t?p.filter(t=>t.formingMold===e):p.filter(t=>t.hotPressMold===e),k=async()=>{try{let e=await d.validateFields();if(h(!0),!Z(e.modelCode)){o.error("产品编码已存在，请使用其他编码"),h(!1);return}let t={modelCode:e.modelCode,modelName:e.modelName,formingMold:e.formingMold||"",formingMoldQuantity:e.formingMoldQuantity||1,hotPressMold:e.hotPressMold||"",hotPressMoldQuantity:e.hotPressMoldQuantity||1,formingPiecePrice:e.formingPiecePrice||0,hotPressPiecePrice:e.hotPressPiecePrice||0,productPrice:e.productPrice,productWeight:e.productWeight,boxSpecification:e.boxSpecification||"",packingQuantity:e.packingQuantity||100,piecesPerMold:e.piecesPerMold||1,moldId:e.formingMold||"",status:e.status||"active"},r=await R.dataAccessManager.products.create(t);"success"===r.status&&r.data?(o.success("产品创建成功，已自动添加到订单中"),l(r.data),M()):o.error(r.message||"产品创建失败")}catch(e){o.error("创建产品失败")}finally{h(!1)}},M=()=>{d.resetFields(),x(""),r()},I=async()=>{if(t){await w();let e=await b();d.setFieldsValue({modelCode:e,status:"active",formingMoldQuantity:1,hotPressMoldQuantity:1,packingQuantity:100,piecesPerMold:1,formingPiecePrice:0,hotPressPiecePrice:0})}};return(0,n.useEffect)(()=>{I()},[t]),(0,a.jsx)(S.Z,{title:"新增产品",open:t,onOk:k,onCancel:M,width:900,confirmLoading:m,okText:"确认创建",cancelText:"取消",destroyOnHidden:!0,styles:{body:{maxHeight:"70vh",overflowY:"auto"}},children:(0,a.jsxs)(u.Z,{form:d,layout:"vertical",initialValues:{status:"active",formingMoldQuantity:1,hotPressMoldQuantity:1,packingQuantity:100,piecesPerMold:1,formingPiecePrice:0,hotPressPiecePrice:0},children:[(0,a.jsxs)(v.Z,{gutter:16,children:[(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"产品编码",name:"modelCode",rules:[{required:!0,message:"请输入产品编码"},{pattern:/^P\d{5}$/,message:"格式：P + 5位数字（如：P00001）"},{validator:(e,t)=>t?Z(t)?Promise.resolve():Promise.reject(Error("产品编码已存在，请使用其他编码")):Promise.resolve()}],children:(0,a.jsx)(i.default,{placeholder:"如：P00001（自动生成）"})})}),(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"产品名称",name:"modelName",rules:[{required:!0,message:"请输入产品名称"}],children:(0,a.jsx)(i.default,{placeholder:"请输入产品名称"})})})]}),(0,a.jsx)($.default,{defaultActiveKey:"mold",items:[{key:"mold",label:(0,a.jsxs)("span",{children:[(0,a.jsx)(J.Z,{}),"模具信息"]}),children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(v.Z,{gutter:16,children:[(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"成型模具编号",name:"formingMold",rules:[{required:!0,message:"请输入成型模具编号"},{validator:(e,t)=>!t||P(t)?Promise.resolve():Promise.reject(Error("模具编号格式错误，请使用格式：M-XX-XX（如：M-JX-05）"))}],extra:g&&C(g,"forming").length>0&&(0,a.jsxs)("div",{style:{marginTop:"4px"},children:[(0,a.jsxs)("span",{style:{color:"#1890ff",fontSize:"12px"},children:["\uD83D\uDCA1 此模具已被 ",C(g,"forming").length," 个产品使用："]}),(0,a.jsx)("div",{style:{fontSize:"11px",color:"#666",marginTop:"2px"},children:C(g,"forming").map(e=>e.modelName).join("、")})]}),children:(0,a.jsx)(i.default,{placeholder:"如：M-JX-05（支持多产品共享）",onChange:e=>{x(e.target.value)}})})}),(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"成型模具单模数量",name:"formingMoldQuantity",rules:[{required:!0,message:"请输入单模数量"}],children:(0,a.jsx)(y.Z,{className:"w-full",placeholder:"请输入数量",suffix:"个/模",min:1,style:{width:"100%"}})})})]}),(0,a.jsxs)(v.Z,{gutter:16,children:[(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"热压模具编号",name:"hotPressMold",rules:[{required:!0,message:"请输入热压模具编号"},{validator:(e,t)=>t?P(t)?p.some(e=>e.hotPressMold===t)?Promise.reject(Error("热压模具编号已存在，请使用其他编号")):Promise.resolve():Promise.reject(Error("模具编号格式错误，请使用格式：M-XX-XX（如：M-RY-12）")):Promise.resolve()}],children:(0,a.jsx)(i.default,{placeholder:"如：M-RY-12"})})}),(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"热压模具单模数量",name:"hotPressMoldQuantity",rules:[{required:!0,message:"请输入单模数量"}],children:(0,a.jsx)(y.Z,{className:"w-full",placeholder:"请输入数量",suffix:"个/模",min:1,style:{width:"100%"}})})})]}),(0,a.jsx)(u.Z.Item,{label:"单模出数",name:"piecesPerMold",rules:[{required:!0,message:"请输入单模出数"},{type:"number",min:1,message:"单模出数必须大于0"}],children:(0,a.jsx)(y.Z,{className:"w-full",placeholder:"用于智能排产模数计算",suffix:"个/模",min:1,precision:0,step:1,style:{width:"100%"}})})]})},{key:"price",label:(0,a.jsxs)("span",{children:[(0,a.jsx)(K.Z,{}),"计件单价"]}),children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(v.Z,{gutter:16,children:[(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"成型计件单价",name:"formingPiecePrice",rules:[{required:!0,message:"请输入成型计件单价"}],children:(0,a.jsx)(y.Z,{className:"w-full",placeholder:"请输入单价",prefix:"\xa5",suffix:"/模",min:0,precision:2,style:{width:"100%"}})})}),(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"热压计件单价",name:"hotPressPiecePrice",rules:[{required:!0,message:"请输入热压计件单价"}],children:(0,a.jsx)(y.Z,{className:"w-full",placeholder:"请输入单价",prefix:"\xa5",suffix:"/模",min:0,precision:2,style:{width:"100%"}})})})]}),(0,a.jsxs)(v.Z,{gutter:16,children:[(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"产品价格",name:"productPrice",rules:[{required:!0,message:"请输入产品价格"},{type:"number",min:.01,message:"产品价格必须大于0"}],children:(0,a.jsx)(y.Z,{className:"w-full",placeholder:"请输入产品价格",prefix:"\xa5",min:0,precision:3,step:.001,style:{width:"100%"}})})}),(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"产品重量",name:"productWeight",rules:[{required:!0,message:"请输入产品重量"},{type:"number",min:.01,message:"产品重量必须大于0"}],children:(0,a.jsx)(y.Z,{className:"w-full",placeholder:"请输入产品重量",suffix:"克",min:0,precision:2,step:.01,style:{width:"100%"}})})})]}),(0,a.jsxs)(v.Z,{gutter:16,children:[(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"箱规",name:"boxSpecification",rules:[{required:!0,message:"请输入箱规"}],children:(0,a.jsx)(i.default,{placeholder:"如：30\xd720\xd715 cm"})})}),(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"装箱数",name:"packingQuantity",rules:[{required:!0,message:"请输入装箱数"},{type:"number",min:1,message:"装箱数必须大于0"}],children:(0,a.jsx)(y.Z,{className:"w-full",placeholder:"请输入装箱数",suffix:"个/箱",min:1,precision:0,step:1,style:{width:"100%"}})})})]}),(0,a.jsx)(u.Z.Item,{label:"状态",name:"status",rules:[{required:!0,message:"请选择状态"}],children:(0,a.jsxs)(s.default,{placeholder:"请选择状态",children:[(0,a.jsx)(G,{value:"active",children:"启用"}),(0,a.jsx)(G,{value:"inactive",children:"停用"})]})})]})}]})]})})};class et{static calculateItemAmount(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{precision:a=2,roundingMode:n="round",discountRate:l=0,taxRate:s=0}=r;this.validateItemInput(e,t,l,s);let i=e*t,o=i*l,d=i-o,c=d*s;return{itemSubtotal:this.roundAmount(i,a,n),itemDiscountAmount:this.roundAmount(o,a,n),itemTaxAmount:this.roundAmount(c,a,n),itemTotalAmount:this.roundAmount(d+c,a,n)}}static calculateOrderAmount(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{precision:r=2,roundingMode:a="round",discountRate:n=0,taxRate:l=0}=t;this.validateOrderInput(e);let s=e.map(e=>this.calculateItemAmount(e.price,e.quantity,{precision:r,roundingMode:a,discountRate:n,taxRate:l})),i=s.reduce((e,t)=>e+t.itemSubtotal,0),o=s.reduce((e,t)=>e+t.itemDiscountAmount,0),d=s.reduce((e,t)=>e+t.itemTaxAmount,0),c=s.reduce((e,t)=>e+t.itemTotalAmount,0);return{subtotal:this.roundAmount(i,r,a),discountAmount:this.roundAmount(o,r,a),taxAmount:this.roundAmount(d,r,a),totalAmount:this.roundAmount(c,r,a)}}static calculateCostAmount(e,t,r){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{precision:n=2,roundingMode:l="round"}=a;this.validateCostInput(e,t,r);let s=e+t+r;return{totalCost:this.roundAmount(s,n,l),unitCost:this.roundAmount(s,n,l),formattedTotalCost:this.formatCurrency(s,n),formattedUnitCost:this.formatCurrency(s,n)}}static calculateProfitAmount(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{precision:a=2,roundingMode:n="round"}=r;if(e<0||t<0)throw Error("收入和成本不能为负数");let l=e-t,s=e>0?l/e:0;return{totalProfit:this.roundAmount(l,a,n),profitMargin:this.roundAmount(s,4,n),formattedProfit:this.formatCurrency(l,a),formattedMargin:this.formatPercentage(s,2)}}static formatCurrency(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return"number"!=typeof e||isNaN(e)?"\xa50.".concat("0".repeat(t)):"\xa5".concat(e.toFixed(t).replace(/\B(?=(\d{3})+(?!\d))/g,","))}static formatPercentage(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;return"number"!=typeof e||isNaN(e)?"0%":"".concat((100*e).toFixed(t),"%")}static roundAmount(e,t,r){let a=Math.pow(10,t);switch(r){case"up":return Math.ceil(e*a)/a;case"down":return Math.floor(e*a)/a;default:return Math.round(e*a)/a}}static validateItemInput(e,t,r,a){if("number"!=typeof e||e<0)throw Error("价格必须是非负数");if("number"!=typeof t||t<=0)throw Error("数量必须大于0");if("number"!=typeof r||r<0||r>1)throw Error("折扣率必须在0-1之间");if("number"!=typeof a||a<0||a>1)throw Error("税率必须在0-1之间")}static validateOrderInput(e){if(!Array.isArray(e)||0===e.length)throw Error("订单项目不能为空");e.forEach((e,t)=>{if(!e||"object"!=typeof e)throw Error("订单项目".concat(t+1,"格式错误"));if("number"!=typeof e.price||e.price<0)throw Error("订单项目".concat(t+1,"的价格必须是非负数"));if("number"!=typeof e.quantity||e.quantity<=0)throw Error("订单项目".concat(t+1,"的数量必须大于0"))})}static validateCostInput(e,t,r){if("number"!=typeof e||e<0)throw Error("成型成本必须是非负数");if("number"!=typeof t||t<0)throw Error("热压成本必须是非负数");if("number"!=typeof r||r<0)throw Error("材料成本必须是非负数")}}var er=e=>{let{value:t=[],onChange:r}=e,{message:l}=c.Z.useApp(),[s,d]=(0,n.useState)(t),[u,m]=(0,n.useState)(!1),[p,f]=(0,n.useState)(!1),{productModels:S,productModelsLoading:P}=(0,X.rm)(),C=(e,t,r)=>{let a=et.calculateItemAmount(e,t,{taxRate:r/100,precision:2});return{totalPrice:a.itemSubtotal,taxAmount:a.itemTaxAmount}},k=(e,t)=>{let a=[...s],n=a[t];n.quantity=e;let{totalPrice:l,taxAmount:i}=C(n.productPrice,e,n.taxRate);n.totalPrice=l,n.taxAmount=i,d(a),null==r||r(a)},I=(e,t)=>{if(!e)return;let a=[...s];a[t].deliveryDate=e.format("YYYY-MM-DD"),d(a),null==r||r(a)},D=(e,t)=>{let a=[...s];a[t].remark=e,d(a),null==r||r(a)},N=e=>{let t=Array.isArray(e)?e:[e],a=[...s,...t.map(e=>({id:"item_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),productId:e.id,productCode:e.modelCode,productName:e.modelName,productPrice:e.productPrice,quantity:1,productWeight:e.productWeight,deliveryDate:L()().add(7,"day").format("YYYY-MM-DD"),taxRate:13,totalPrice:e.productPrice,taxAmount:.13*e.productPrice,remark:""}))];d(a),null==r||r(a),1===t.length?l.success("产品添加成功"):l.success("成功添加 ".concat(t.length," 个产品"))},O=e=>{let t=s.filter((t,r)=>r!==e);d(t),null==r||r(t),l.success("产品删除成功")},z={totalQuantity:s.reduce((e,t)=>e+t.quantity,0),totalAmount:s.reduce((e,t)=>e+t.totalPrice,0),totalTax:s.reduce((e,t)=>e+t.taxAmount,0),totalWeight:s.reduce((e,t)=>e+t.productWeight*t.quantity,0)/1e6};return(0,n.useEffect)(()=>{d(t)},[t]),(0,a.jsxs)("div",{children:[(0,a.jsxs)(w.Z,{title:"产品信息",extra:(0,a.jsxs)(g.Z,{children:[(0,a.jsx)(h.ZP,{icon:(0,a.jsx)(Q.Z,{}),onClick:()=>m(!0),loading:P,children:"选择产品"}),(0,a.jsx)(h.ZP,{type:"primary",icon:(0,a.jsx)(A.Z,{}),onClick:()=>{f(!0)},children:"新增产品"})]}),children:[s.length>0&&(0,a.jsxs)(v.Z,{gutter:16,className:"mb-4",children:[(0,a.jsx)(j.Z,{span:6,children:(0,a.jsx)(b.Z,{title:"产品种类",value:s.length,suffix:"种"})}),(0,a.jsx)(j.Z,{span:6,children:(0,a.jsx)(b.Z,{title:"总数量",value:z.totalQuantity,suffix:"个"})}),(0,a.jsx)(j.Z,{span:6,children:(0,a.jsx)(b.Z,{title:"总重量",value:(z.totalWeight||0).toFixed(3),suffix:"吨"})}),(0,a.jsx)(j.Z,{span:6,children:(0,a.jsx)(b.Z,{title:"总金额",value:(z.totalAmount||0).toFixed(2),prefix:"\xa5"})})]}),(0,a.jsx)(Z.Z,{columns:[{title:"产品编码",dataIndex:"productCode",key:"productCode",width:54,fixed:"left"},{title:"产品名称",dataIndex:"productName",key:"productName",width:84,ellipsis:!0},{title:"单价(元)",dataIndex:"productPrice",key:"productPrice",width:44,render:e=>"\xa5".concat(e?e.toFixed(3):"0.000")},{title:"数量",dataIndex:"quantity",key:"quantity",width:50,render:(e,t,r)=>(0,a.jsx)(y.Z,{value:e,min:1,precision:0,size:"small",style:{width:"100%"},onChange:e=>k(e||1,r)})},{title:"重量(g)",dataIndex:"productWeight",key:"productWeight",width:45,render:e=>"".concat(e?e.toFixed(2):"0.00","g")},{title:"交货日期",dataIndex:"deliveryDate",key:"deliveryDate",width:74,render:(e,t,r)=>(0,a.jsx)(o.default,{value:e?L()(e):null,size:"small",format:"YYYY-MM-DD",style:{width:"100%"},disabledDate:e=>e&&e<L()().startOf("day"),onChange:e=>I(e,r)})},{title:"税率(%)",dataIndex:"taxRate",key:"taxRate",width:50,render:e=>"".concat(e,"%")},{title:"总价(元)",dataIndex:"totalPrice",key:"totalPrice",width:58,render:e=>"\xa5".concat(e?e.toFixed(2):"0.00")},{title:"税额(元)",dataIndex:"taxAmount",key:"taxAmount",width:53,render:e=>"\xa5".concat(e?e.toFixed(2):"0.00")},{title:"备注",dataIndex:"remark",key:"remark",width:160,ellipsis:!0,render:(e,t,r)=>(0,a.jsx)(i.default,{value:e||"",size:"small",placeholder:"备注信息",maxLength:100,onChange:e=>D(e.target.value,r)})},{title:"操作",key:"action",width:46,fixed:"right",render:(e,t,r)=>(0,a.jsx)(x.Z,{title:"确定删除这个产品吗？",onConfirm:()=>O(r),okText:"确定",cancelText:"取消",children:(0,a.jsx)(h.ZP,{type:"link",size:"small",danger:!0,icon:(0,a.jsx)(M.Z,{}),title:"删除"})})}],dataSource:s,rowKey:"id",pagination:!1,scroll:{x:1400},size:"small",locale:{emptyText:"暂无产品，请点击上方按钮添加产品"}})]}),(0,a.jsx)(U,{open:u,onCancel:()=>m(!1),onSelect:N}),(0,a.jsx)(ee,{open:p,onCancel:()=>f(!1),onSuccess:e=>{l.success("产品创建成功，可以继续选择该产品"),N(e)}})]})},ea=r(59841);let{Option:en}=s.default;var el=e=>{let{open:t,onCancel:r,onSuccess:l}=e,{message:d}=c.Z.useApp(),[m]=u.Z.useForm(),[p,f]=(0,n.useState)(!1),[g,x]=(0,n.useState)(!1),[y,w]=(0,n.useState)([]),[b,Z]=(0,n.useState)([]),[P,C]=(0,n.useState)(!1),[k,M]=(0,n.useState)(!1),[I,D]=(0,n.useState)([]),N=async()=>{try{let e=await (0,T.Ro)(()=>R.dataAccessManager.customers.getAll(),"获取客户数据");e&&e.items&&w(e.items)}catch(e){console.error("加载客户数据失败:",e),d.error("加载客户数据失败")}},A=async()=>{try{let e=await (0,T.Ro)(()=>R.dataAccessManager.employees.getAll(),"获取员工数据");e&&e.items&&Z(e.items)}catch(e){console.error("加载员工数据失败:",e),d.error("加载员工数据失败")}},O=async()=>{C(!0);try{await Promise.all([N(),A()])}finally{C(!1)}},z=async()=>{M(!0);try{let e=_.s.generateSalesOrderId();m.setFieldsValue({orderNumber:e}),d.success("销售订单号生成成功")}catch(e){console.error("生成销售订单号失败:",e),d.error("生成销售订单号失败")}finally{M(!1)}},E=()=>Array.isArray(y)?y.filter(e=>"active"===e.status):[],q=async()=>{try{let e=await m.validateFields();x(!0);let t=I.map(e=>({price:e.productPrice,quantity:e.quantity})),r=et.calculateOrderAmount(t,{precision:2}),a=r.subtotal;r.taxAmount;let n=r.totalAmount,s=I.map(e=>e.deliveryDate).sort()[0]||"",i=ea.IZ.transformSalesOrder({orderNumber:e.orderNumber,customerId:e.customerId,customerName:e.customerName,customerContact:e.customerContact,orderDate:L()(e.orderDate).isValid()?L()(e.orderDate).format("YYYY-MM-DD"):e.orderDate,deliveryDate:s,promisedDeliveryDate:s,salesRepresentative:e.salesRepresentative,totalAmount:a,discountAmount:0,finalAmount:n,status:"pending",productionStatus:"not_started",paymentStatus:"unpaid",paymentTerms:"月结30天",items:I.map(t=>({id:t.id,orderNumber:e.orderNumber,productModelCode:t.productCode,productName:t.productName,productCode:t.productCode,quantity:t.quantity,unit:"个",unitPrice:t.productPrice,totalPrice:t.totalPrice,deliveryQuantity:0,remainingQuantity:t.quantity,deliveryDate:t.deliveryDate,remark:t.remark||""})),changes:[],mrpStatus:"not_started",remark:e.remark||""},{generateId:!0,generateTimestamps:!0,validateData:!0});if(!i.success){let e=ea.N5.handleInternal(Error(i.error),"订单数据转换","salesOrder");d.error(e.message);return}i.warnings&&i.warnings.length>0&&(console.warn("订单数据转换警告:",i.warnings),i.warnings.forEach(e=>{d.warning(e)}));let o=i.data,c=await (0,T.Ro)(()=>R.dataAccessManager.orders.create(o),"创建订单");c?(d.success("订单创建成功"),l(c),W()):d.error("创建订单失败")}catch(e){d.error("创建订单失败: ".concat(e instanceof Error?e.message:"未知错误"))}finally{x(!1)}},W=()=>{m.resetFields(),D([]),r()};return(0,n.useEffect)(()=>{if(t){f(!0),Promise.all([O(),z()]).finally(()=>{f(!1)});let e=L()();m.setFieldsValue({orderDate:e}),D([])}},[t]),(0,a.jsx)(S.Z,{title:"新增订单",open:t,onCancel:W,width:1200,footer:[(0,a.jsx)(h.ZP,{onClick:W,children:"取消"},"cancel"),(0,a.jsx)(h.ZP,{type:"primary",loading:g,onClick:q,children:"确认创建"},"submit")],destroyOnHidden:!0,children:(0,a.jsx)(V.Z,{spinning:p||P,tip:"加载数据中...",children:(0,a.jsxs)(u.Z,{form:m,layout:"vertical",requiredMark:!1,children:[(0,a.jsxs)(v.Z,{gutter:16,children:[(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"销售订单号",name:"orderNumber",rules:Y.createFormValidationRules("orderNumber",{required:!1,orderType:"sales",checkUniqueness:async e=>{if(!(await Y.validateOrderNumber(e,"sales")).isValid)return!1;let t=await R.dataAccessManager.orders.getByNumber(e);return"success"!==t.status||!t.data}}),children:(0,a.jsx)(i.default,{placeholder:"XSDD202507040001",addonAfter:(0,a.jsx)(h.ZP,{type:"text",icon:(0,a.jsx)(F.Z,{}),loading:k,onClick:z,size:"small",children:"重新生成"})})})}),(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"销售日期",name:"orderDate",rules:[{required:!0,message:"请选择销售日期"}],children:(0,a.jsx)(o.default,{style:{width:"100%"},format:"YYYY-MM-DD",placeholder:"请选择销售日期"})})})]}),(0,a.jsxs)(v.Z,{gutter:16,children:[(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"客户",name:"customerId",rules:[{required:!0,message:"请选择客户"}],children:(0,a.jsx)(s.default,{placeholder:"请选择客户",showSearch:!0,filterOption:(e,t)=>{let r=E().find(e=>e.id===(null==t?void 0:t.value));return!!r&&(r.customerName.toLowerCase().includes(e.toLowerCase())||r.customerCode.toLowerCase().includes(e.toLowerCase()))},onChange:e=>{let t=y.find(t=>t.id===e);t&&m.setFieldsValue({customerName:t.customerName,customerContact:t.contactPerson||t.contactPhone||""})},notFoundContent:"暂无数据",children:E().map(e=>(0,a.jsxs)(en,{value:e.id,children:[e.customerCode," - ",e.customerName]},e.id))})})}),(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{label:"所属业务员",name:"salesRepresentative",rules:[{required:!0,message:"请选择业务员"}],children:(0,a.jsx)(s.default,{placeholder:"请选择业务员",notFoundContent:"暂无数据",children:(Array.isArray(b)?b.filter(e=>"销售部"===e.department||"sales"===e.role):[]).map(e=>(0,a.jsxs)(en,{value:e.id,children:[e.employeeCode," - ",e.name]},e.id))})})})]}),(0,a.jsxs)(v.Z,{gutter:16,children:[(0,a.jsx)(j.Z,{xs:24,sm:24,md:7,lg:7,xl:7,children:(0,a.jsx)(u.Z.Item,{label:"客户名称",name:"customerName",children:(0,a.jsx)(i.default,{placeholder:"选择客户后自动填充",disabled:!0})})}),(0,a.jsx)(j.Z,{xs:24,sm:24,md:5,lg:5,xl:5,children:(0,a.jsx)(u.Z.Item,{label:"客户联系方式",name:"customerContact",children:(0,a.jsx)(i.default,{placeholder:"选择客户后自动填充",disabled:!0,style:{backgroundColor:"#f5f5f5"}})})}),(0,a.jsx)(j.Z,{xs:24,sm:24,md:12,lg:12,xl:12,children:(0,a.jsx)(u.Z.Item,{label:"备注",name:"remark",children:(0,a.jsx)(i.default,{placeholder:"请输入备注信息（可选）",showCount:!0,maxLength:500})})})]}),(0,a.jsx)(er,{value:I,onChange:D})]})})})};let es=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{salesOrderNumber:t,enableRealTimeSync:r=!0,cacheTimeout:a=3e5,autoRefresh:l=!1,refreshInterval:s=3e4}=e,[i,o]=(0,n.useState)([]),[d,c]=(0,n.useState)(!1),[u,m]=(0,n.useState)(null),[h,p]=(0,n.useState)(null),f=(0,n.useCallback)(async()=>{c(!0),m(null);try{let e=await R.dataAccessManager.productionOrders.getAll({filters:t?{salesOrderNumber:t}:void 0});if("success"===e.status&&e.data&&e.data.items){let r=e.data.items;t&&(r=e.data.items.filter(e=>e.salesOrderNumber===t)),o(r),p(new Date)}else m(e.message||"获取生产订单数据失败")}catch(e){m("系统错误，请稍后重试")}c(!1)},[t]),g=(0,n.useCallback)(async()=>{await f()},[f]);(0,n.useEffect)(()=>{f()},[f]),(0,n.useEffect)(()=>{if(!r)return;let e=null;return l&&(e=setInterval(()=>{f()},s)),()=>{e&&clearInterval(e)}},[r,l,s,f]),(0,n.useEffect)(()=>{if(!l)return;let e=setInterval(()=>{f()},s);return()=>clearInterval(e)},[l,s,f]);let x=(0,n.useMemo)(()=>{let e={total:i.length,planned:0,inProgress:0,completed:0,cancelled:0};return i.forEach(t=>{switch(t.status){case"planned":e.planned++;break;case"in_progress":e.inProgress++;break;case"completed":e.completed++;break;case"cancelled":e.cancelled++}}),e},[i]);return{productionOrders:i,loading:d,error:u,refresh:g,statistics:x,lastUpdated:h}},ei=e=>es({salesOrderNumber:e,enableRealTimeSync:!0,autoRefresh:!1});r(29109);var eo=r(62416),ed=r(71910),ec=r(40810);let eu=e=>{let t={autoLoad:!0,pollingInterval:0,enableCache:!0,...e},[r,a]=(0,n.useState)({orders:[],loading:!1,error:null,lastUpdateTime:null}),l=(0,n.useCallback)(async()=>{a(e=>({...e,loading:!0,error:null}));try{let e=await (0,T.Ro)(()=>R.dataAccessManager.orders.getAll(),"加载销售订单");if(e&&e.items)return a(t=>({...t,orders:e.items,loading:!1,lastUpdateTime:Date.now()})),console.log("✅ [useOrdersData] 加载销售订单成功: ".concat(e.items.length," 个订单")),e.items;throw Error("订单数据格式错误")}catch(t){let e=t instanceof Error?t.message:"加载订单失败";return a(t=>({...t,loading:!1,error:e})),console.error("❌ [useOrdersData] 加载订单失败:",t),[]}},[]),s=(0,n.useCallback)(async()=>{try{return t.enableCache&&(R.dataAccessManager.clearDataTypeCache("orders"),console.log("\uD83D\uDD04 [useOrdersData] 已清理订单缓存")),await l()}catch(e){return console.error("❌ [useOrdersData] 刷新订单失败:",e),[]}},[l,t.enableCache]),i=(0,n.useCallback)(async e=>{try{let t=await R.dataAccessManager.orders.getById(e);if("success"===t.status&&t.data)return t.data;return console.error("❌ [useOrdersData] 获取订单失败:",t.message),null}catch(e){return console.error("❌ [useOrdersData] 获取订单异常:",e),null}},[]),o=(0,n.useCallback)(async e=>{try{let t=await R.dataAccessManager.orders.getByNumber(e);if("success"===t.status&&t.data)return t.data;return console.error("❌ [useOrdersData] 获取订单失败:",t.message),null}catch(e){return console.error("❌ [useOrdersData] 获取订单异常:",e),null}},[]),d=(0,n.useCallback)(async e=>{try{let t=await (0,T.Ro)(()=>R.dataAccessManager.orders.create(e),"创建销售订单");if(t)return await s(),console.log("✅ [useOrdersData] 创建订单成功:",t.orderNumber),t;throw Error("创建订单失败")}catch(e){throw console.error("❌ [useOrdersData] 创建订单失败:",e),e}},[s]),c=(0,n.useCallback)(async(e,t)=>{try{let r=await (0,T.Ro)(()=>R.dataAccessManager.orders.update(e,t),"更新销售订单");if(r)return await s(),console.log("✅ [useOrdersData] 更新订单成功:",e),r;throw Error("更新订单失败")}catch(e){throw console.error("❌ [useOrdersData] 更新订单失败:",e),e}},[s]),u=(0,n.useCallback)(async e=>{try{if(await (0,T.Ro)(()=>R.dataAccessManager.orders.delete(e),"删除销售订单"))return await s(),console.log("✅ [useOrdersData] 删除订单成功:",e),!0;throw Error("删除订单失败")}catch(e){throw console.error("❌ [useOrdersData] 删除订单失败:",e),e}},[s]);return(0,n.useEffect)(()=>{t.autoLoad&&l()},[t.autoLoad,l]),(0,n.useEffect)(()=>{if(t.pollingInterval>0){let e=setInterval(()=>{l()},t.pollingInterval);return()=>clearInterval(e)}},[t.pollingInterval,l]),{...r,loadOrders:l,refreshOrders:s,getOrderById:i,getOrderByNumber:o,createOrder:d,updateOrder:c,deleteOrder:u,hasOrders:r.orders.length>0,isEmpty:0===r.orders.length&&!r.loading,isStale:!r.lastUpdateTime||Date.now()-r.lastUpdateTime>3e5}};var em=r(66326),eh=r(34863),ep=r(74424);let ef={pending:{color:"orange",text:"待审核"},confirmed:{color:"blue",text:"已确认"},completed:{color:"green",text:"已完成"},cancelled:{color:"red",text:"已取消"}},eg={not_started:{color:"default",text:"未开始"},in_progress:{color:"processing",text:"执行中"},completed:{color:"success",text:"已完成"},failed:{color:"error",text:"执行失败"}},ex={unpaid:{color:"red",text:"未付款"},partial:{color:"orange",text:"部分付款"},paid:{color:"green",text:"已付款"}},ey={not_started:{color:"default",text:"未开始"},in_progress:{color:"processing",text:"生产中"},completed:{color:"success",text:"已完成"},paused:{color:"warning",text:"暂停"}},ev={title:"订单详情",statusConfig:ef,getStatus:e=>e.status,width:1200,sections:[{title:"基本信息",columns:3,bordered:!0,size:"small",fields:[{label:"销售订单号",key:"orderNumber"},{label:"客户名称",key:"customerName"},{label:"联系人",key:"customerContact"},{label:"订单日期",key:"orderDate",render:e=>ep.Xe.date(e)},{label:"交货日期",key:"deliveryDate",render:e=>ep.Xe.date(e)},{label:"承诺交期",key:"promisedDeliveryDate",render:e=>ep.Xe.date(e)}]},{title:"状态信息",columns:3,bordered:!0,size:"small",fields:[{label:"订单状态",key:"status",render:e=>ep.Xe.status(e,ef)},{label:"生产状态",key:"productionStatus",render:e=>ep.Xe.status(e,ey)},{label:"MRP状态",key:"mrpStatus",render:e=>ep.Xe.status(e||"not_started",eg)},{label:"付款状态",key:"paymentStatus",render:e=>ep.Xe.status(e,ex)},{label:"付款条件",key:"paymentTerms"},{label:"销售代表",key:"salesRepresentative"}]},{title:"执行信息",columns:3,bordered:!0,size:"small",fields:[{label:"MRP执行时间",key:"mrpExecutedAt",render:e=>e?ep.Xe.datetime(e):ep.Xe.empty("-")},{label:"MRP执行人",key:"mrpExecutedBy",render:e=>e||ep.Xe.empty("-")},{label:"",key:"placeholder",render:()=>ep.Xe.empty(""),span:1}]},{title:"金额信息",columns:3,bordered:!0,size:"small",fields:[{label:"订单总额",key:"totalAmount",render:e=>ep.Xe.currency(e||0)},{label:"折扣金额",key:"discountAmount",render:e=>ep.Xe.currency(e||0)},{label:"最终金额",key:"finalAmount",render:e=>(0,a.jsxs)("span",{style:{fontSize:"16px",fontWeight:"bold",color:"#3f8600"},children:["\xa5",(e||0).toLocaleString()]})}]},{title:"时间信息",columns:2,bordered:!0,size:"small",fields:[{label:"创建时间",key:"createdAt",render:e=>ep.Xe.datetime(e)},{label:"更新时间",key:"updatedAt",render:e=>ep.Xe.datetime(e)}]},{title:"备注信息",columns:1,bordered:!0,size:"small",fields:[{label:"备注",key:"remark",span:3,render:e=>e||ep.Xe.empty("无")}]},{title:"订单明细",columns:1,bordered:!1,fields:[],customContent:e=>(0,a.jsx)(Z.Z,{dataSource:e.items,rowKey:"id",pagination:!1,size:"small",columns:[{title:"产品型号",dataIndex:"productModelCode",key:"productModelCode",width:120},{title:"产品名称",dataIndex:"productName",key:"productName",width:150},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,render:(e,t)=>"".concat((e||0).toLocaleString()," ").concat(t.unit||"")},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:100,render:e=>"\xa5".concat(e.toFixed(3))},{title:"小计",dataIndex:"totalPrice",key:"totalPrice",width:120,render:e=>(0,a.jsxs)("span",{style:{fontWeight:"bold"},children:["\xa5",(e||0).toLocaleString()]})},{title:"已交货",dataIndex:"deliveryQuantity",key:"deliveryQuantity",width:100,render:(e,t)=>"".concat((e||0).toLocaleString()," ").concat(t.unit||"")},{title:"剩余数量",dataIndex:"remainingQuantity",key:"remainingQuantity",width:100,render:(e,t)=>"".concat((e||0).toLocaleString()," ").concat(t.unit||"")},{title:"交货日期",dataIndex:"deliveryDate",key:"deliveryDate",width:120,render:(t,r)=>{let n=t||e.deliveryDate,l=!!t;return(0,a.jsxs)("span",{style:{color:l?"#1890ff":"#666",fontWeight:l?"bold":"normal"},children:[n,l&&(0,a.jsx)("span",{style:{fontSize:"12px",marginLeft:"4px"},children:"(项目级)"})]})}},{title:"生产进度",key:"progress",width:120,render:(e,t)=>{let r=(t.quantity-t.remainingQuantity)/t.quantity*100;return(0,a.jsx)(eh.Z,{percent:r,size:"small",status:100===r?"success":"active"})}},{title:"工位",dataIndex:"productionWorkstation",key:"productionWorkstation",width:80},{title:"批次号",dataIndex:"batchNumber",key:"batchNumber",width:120}]})}],actions:[{key:"mrp",text:e=>"completed"===e.mrpStatus?"MRP已完成":"in_progress"===e.mrpStatus?"MRP执行中":"failed"===e.mrpStatus?"重新启动MRP":"启动MRP",type:"primary",icon:(0,a.jsx)(E.Z,{}),onClick:e=>{console.log("启动MRP:",e.orderNumber)},hidden:e=>"confirmed"!==e.status,disabled:e=>"completed"===e.mrpStatus||"in_progress"===e.mrpStatus}]},{Option:ej}=s.default,{TextArea:ew}=i.default,{RangePicker:eb}=o.default,{Step:eZ}=d.default,eS=()=>{let{message:e,modal:t}=c.Z.useApp(),[d,q]=(0,n.useState)(!1),[V,F]=(0,n.useState)(!1),[_,W]=(0,n.useState)(!1),[B,Q]=(0,n.useState)(!1),[X,H]=(0,n.useState)(!1),[U,$]=(0,n.useState)(null),[J,K]=(0,n.useState)(""),[G]=u.Z.useForm(),{orders:ee,loading:et,error:er,refreshOrders:ea,loadOrders:en,hasOrders:es,isEmpty:eh}=eu({autoLoad:!0,enableCache:!0}),[ep,ef]=(0,n.useState)([]);(0,n.useEffect)(()=>{ef(ee)},[ee]),(0,n.useEffect)(()=>{er&&e.error(er)},[er,e]),(0,ed.el)("sales-orders-page",{onOrderCreated:()=>{console.log("\uD83D\uDCDD 检测到销售订单创建，自动刷新数据"),ea()},onOrderUpdated:()=>{console.log("\uD83D\uDCDD 检测到销售订单更新，自动刷新数据"),ea()},onOrderDeleted:()=>{console.log("\uD83D\uDCDD 检测到销售订单删除，自动刷新数据"),ea()}});let[eg,ex]=(0,n.useState)(""),[ey,eb]=(0,n.useState)(""),[eZ,eS]=(0,n.useState)(void 0),[eP,eC]=(0,n.useState)(void 0),ek=(0,eo.Yy)(e=>{ex(e)},300,[]),{metrics:eM,cacheStats:eI,isMonitoring:eD,clearCache:eN,getPerformanceAlerts:eA,formatMemorySize:eO,formatPercentage:ez,isHealthy:eE,needsOptimization:eR}=(0,ec.e)({interval:6e4,enabled:!0,showDetails:!1}),[eT,eq]=(0,n.useState)([]),[eL,eV]=(0,n.useState)([]),[eF,e_]=(0,n.useState)(!1),[eW,eB]=(0,n.useState)(null),[eY,eQ]=(0,n.useState)(!1),[eX,eH]=(0,n.useState)(0),[eU]=(0,n.useState)(["启动MRP","MRP分析","生成生产订单","完成"]),[e$]=(0,n.useState)({}),{productionOrders:eJ,loading:eK}=ei((null==U?void 0:U.orderNumber)||""),eG=e=>{let t={pending:{color:"red",text:"未审核"},confirmed:{color:"green",text:"已审核"},completed:{color:"gray",text:"完成"},cancelled:{color:"orange",text:"已取消"}}[e]||{color:"default",text:"未知"};return(0,a.jsx)(m.Z,{color:t.color,children:t.text})},e0=(e,t)=>{if("pending"===e||"cancelled"===e)return(0,a.jsx)("span",{style:{color:"#999"},children:"-"});let r={not_started:{color:"orange",text:"未开始"},pending:{color:"blue",text:"待生产"},in_progress:{color:"green",text:"生产中"},completed:{color:"cyan",text:"已完成"}}["confirmed"!==e||t?t:"not_started"]||{color:"orange",text:"未开始"};return(0,a.jsx)(m.Z,{color:r.color,children:r.text})},e1=e=>{let t={unpaid:{color:"red",text:"未付款"},partial:{color:"orange",text:"部分付款"},paid:{color:"green",text:"已付款"}}[e]||{color:"default",text:"未知"};return(0,a.jsx)(m.Z,{color:t.color,children:t.text})},e2=ep.filter(e=>{let t=!eg||e.orderNumber.toLowerCase().includes(eg.toLowerCase())||e.customerName.toLowerCase().includes(eg.toLowerCase())||e.customerContact.toLowerCase().includes(eg.toLowerCase()),r=!eZ||e.status===eZ,a=!eP||e.productionStatus===eP;return t&&r&&a}),e6={total:ep.length,pending:ep.filter(e=>"pending"===e.status).length,confirmed:ep.filter(e=>"confirmed"===e.status).length,completed:ep.filter(e=>"completed"===e.status).length,cancelled:ep.filter(e=>"cancelled"===e.status).length,totalAmount:ep.reduce((e,t)=>e+t.finalAmount,0),delayedOrders:ep.filter(e=>new Date(e.deliveryDate)<new Date&&"completed"!==e.status&&"cancelled"!==e.status).length};(0,l.useRouter)();let e4=e=>{$(e),W(!0)},e3=e=>{$(e),K(""),Q(!0),G.resetFields()},e8=(e,t)=>{var r;return e&&(null===(r=({quantity:{originalValue:"当前订单总数量",newValue:"请输入新的数量"},delivery_date:{originalValue:"当前交期日期",newValue:"请选择新的交期日期"},cancel:{originalValue:"当前订单状态",newValue:"取消原因"}})[e])||void 0===r?void 0:r[t])||("originalValue"===t?"请输入原始值":"请输入新值")},e5=(e,t)=>{let r=e8(e,t),n="originalValue"===t&&!!J;switch(e){case"quantity":return(0,a.jsx)(y.Z,{placeholder:r,min:0,style:{width:"100%"},readOnly:n});case"delivery_date":if("originalValue"===t)return(0,a.jsx)(i.default,{placeholder:r,readOnly:!0});return(0,a.jsx)(o.default,{placeholder:r,style:{width:"100%"}});default:return(0,a.jsx)(i.default,{placeholder:r,readOnly:n})}},e7=async t=>{null!==await (0,T.Ro)(()=>R.dataAccessManager.orders.delete(t),"删除订单")?(await ea(),e.success("订单删除成功")):e.error("删除订单失败")},e9=async t=>{await ea(),e.success("订单创建成功"),H(!1)},te=async t=>{try{if("confirmed"!==t.status){e.error("只有已审核的订单才能启动MRP");return}if("completed"===t.mrpStatus){e.warning("该订单的MRP已经执行完成");return}if("in_progress"===t.mrpStatus){e.warning("该订单的MRP正在执行中，请等待完成");return}q(!0),eH(1),eQ(!1),eB(null);try{await R.dataAccessManager.orders.update(t.id,{mrpStatus:"in_progress",updatedAt:new Date().toISOString()}),U&&U.id===t.id&&$({...U,mrpStatus:"in_progress",updatedAt:new Date().toISOString()}),await ea()}catch(e){console.error("更新订单MRP状态失败:",e)}let{mrpService:a}=await r.e(6368).then(r.bind(r,46368));e.info("正在启动MRP..."),await new Promise(e=>setTimeout(e,1e3)),eH(2),e.info("正在进行MRP分析..."),await new Promise(e=>setTimeout(e,1500)),eH(3),e.info("正在生成生产订单...");let n=await a.executeMRP({salesOrder:t,executedBy:"当前用户",executionDate:new Date().toISOString()});eH(4),await new Promise(e=>setTimeout(e,500)),q(!1),eB(n),eQ(!0);try{await R.dataAccessManager.orders.update(t.id,{mrpStatus:"completed",mrpExecutedAt:new Date().toISOString(),mrpExecutedBy:"当前用户",mrpResultId:n.id,updatedAt:new Date().toISOString()}),U&&U.id===t.id&&$({...U,mrpStatus:"completed",mrpExecutedAt:new Date().toISOString(),mrpExecutedBy:"当前用户",mrpResultId:n.id,updatedAt:new Date().toISOString()})}catch(e){console.error("更新订单MRP完成状态失败:",e)}await ea(),n.generatedProductionOrders&&n.generatedProductionOrders.length>0?e.success("MRP执行完成！生成了 ".concat(n.totalProductionOrders," 个生产订单，请前往生产管理模块查看")):e.success("MRP执行完成！未生成新的生产订单（可能库存充足）")}catch(r){q(!1),await (0,T.Ro)(()=>R.dataAccessManager.orders.update(t.id,{mrpStatus:"failed",updatedAt:new Date().toISOString()}),"更新订单MRP状态")&&(U&&U.id===t.id&&$({...U,mrpStatus:"failed",updatedAt:new Date().toISOString()}),await ea()),e.error("MRP执行失败: ".concat(r instanceof Error?r.message:"未知错误"))}};return(0,a.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,a.jsxs)(v.Z,{gutter:[16,16],children:[(0,a.jsx)(j.Z,{xs:24,sm:6,children:(0,a.jsx)(w.Z,{children:(0,a.jsx)(b.Z,{title:"订单总数",value:e6.total,suffix:"个",prefix:(0,a.jsx)(I.Z,{})})})}),(0,a.jsx)(j.Z,{xs:24,sm:6,children:(0,a.jsx)(w.Z,{children:(0,a.jsx)(b.Z,{title:"订单总额",value:e6.totalAmount,precision:0,prefix:"\xa5",valueStyle:{color:"#3f8600"}})})}),(0,a.jsx)(j.Z,{xs:24,sm:6,children:(0,a.jsx)(w.Z,{children:(0,a.jsx)(b.Z,{title:"生产中订单",value:ep.filter(e=>"in_progress"===e.productionStatus).length,suffix:"个",valueStyle:{color:"#1890ff"}})})}),(0,a.jsx)(j.Z,{xs:24,sm:6,children:(0,a.jsx)(w.Z,{children:(0,a.jsx)(b.Z,{title:"已取消订单",value:e6.cancelled,suffix:"个",valueStyle:{color:"#fa8c16"}})})})]}),eM&&(0,a.jsxs)(v.Z,{gutter:[16,16],children:[(0,a.jsx)(j.Z,{xs:24,sm:8,children:(0,a.jsx)(w.Z,{size:"small",children:(0,a.jsx)(b.Z,{title:"平均响应时间",value:"".concat(eM.averageResponseTime,"ms"),valueStyle:{color:eM.averageResponseTime>1e3?"#ff4d4f":eM.averageResponseTime>500?"#fa8c16":"#3f8600"}})})}),(0,a.jsx)(j.Z,{xs:24,sm:8,children:(0,a.jsx)(w.Z,{size:"small",children:(0,a.jsx)(b.Z,{title:"缓存命中率",value:ez((null==eI?void 0:eI.hitRate)||0),valueStyle:{color:.3>((null==eI?void 0:eI.hitRate)||0)?"#ff4d4f":.6>((null==eI?void 0:eI.hitRate)||0)?"#fa8c16":"#3f8600"}})})}),(0,a.jsx)(j.Z,{xs:24,sm:8,children:(0,a.jsx)(w.Z,{size:"small",children:(0,a.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{style:{fontSize:"14px",color:"#6b7280"},children:"监控状态"}),(0,a.jsx)("div",{style:{fontSize:"18px",fontWeight:"500"},children:(0,a.jsx)(f.Z,{status:eD?"processing":"default",text:eD?"运行中":"已停止"})})]}),!eE&&(0,a.jsx)(p.Z,{title:"系统性能需要优化",children:(0,a.jsx)(h.ZP,{size:"small",type:"text",danger:!0,onClick:()=>eN(),children:"⚠️ 优化"})})]})})})]}),(0,a.jsx)(w.Z,{size:"small",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,a.jsx)(i.default,{placeholder:"搜索订单号、客户名称或联系人",prefix:(0,a.jsx)(D.Z,{}),value:ey,onChange:e=>{let t=e.target.value;eb(t),ek(t)},className:"w-full sm:w-64"}),(0,a.jsxs)(s.default,{placeholder:"订单状态",value:eZ,onChange:eS,className:"w-full sm:w-32",allowClear:!0,children:[(0,a.jsx)(ej,{value:"pending",children:"未审核"}),(0,a.jsx)(ej,{value:"confirmed",children:"已审核"}),(0,a.jsx)(ej,{value:"completed",children:"完成"}),(0,a.jsx)(ej,{value:"cancelled",children:"已取消"})]}),(0,a.jsxs)(s.default,{placeholder:"生产状态",value:eP,onChange:eC,className:"w-full sm:w-32",allowClear:!0,children:[(0,a.jsx)(ej,{value:"not_started",children:"未开始"}),(0,a.jsx)(ej,{value:"pending",children:"待生产"}),(0,a.jsx)(ej,{value:"in_progress",children:"生产中"}),(0,a.jsx)(ej,{value:"completed",children:"已完成"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[!1,(0,a.jsx)(h.ZP,{icon:(0,a.jsx)(N.Z,{}),children:"导出"}),(0,a.jsx)(h.ZP,{type:"primary",icon:(0,a.jsx)(A.Z,{}),onClick:()=>{H(!0)},children:"新增订单"})]})]})}),(0,a.jsxs)(w.Z,{title:"订单列表",size:"small",children:[(0,a.jsxs)("div",{className:"mb-3 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0",children:[(0,a.jsxs)(g.Z,{wrap:!0,children:[(0,a.jsxs)(h.ZP,{type:"primary",icon:(0,a.jsx)(O.Z,{}),onClick:()=>{if(0===eT.length){e.warning("请先选择要审核的订单");return}let r=eL.filter(e=>"pending"===e.status);if(0===r.length){e.warning("所选订单中没有未审核的订单");return}t.confirm({title:"批量审核确认",content:"确定要审核 ".concat(r.length," 个订单吗？"),onOk:async()=>{e_(!0);try{let t=r.map(e=>(0,T.Ro)(()=>R.dataAccessManager.orders.update(e.id,{status:"confirmed",productionStatus:"not_started",updatedAt:new Date().toISOString()}),"审核订单 ".concat(e.orderNumber))),a=(await Promise.all(t)).filter(e=>null!==e).length;a>0?(await ea(),eq([]),eV([]),e.success("成功审核 ".concat(a," 个订单"))):e.error("批量审核失败，没有订单被成功审核")}catch(t){e.error("批量审核失败: ".concat(t instanceof Error?t.message:"未知错误")),console.error("批量审核异常:",t)}finally{e_(!1)}}})},disabled:0===eT.length||!eL.some(e=>"pending"===e.status),loading:eF,children:["审核 (",eL.filter(e=>"pending"===e.status).length,")"]}),(0,a.jsxs)(h.ZP,{icon:(0,a.jsx)(z.Z,{}),onClick:()=>{if(0===eT.length){e.warning("请先选择要反审核的订单");return}let r=eL.filter(e=>"confirmed"===e.status);if(0===r.length){e.warning("所选订单中没有已审核的订单");return}t.confirm({title:"批量反审核确认",content:"确定要反审核 ".concat(r.length," 个订单吗？"),onOk:async()=>{e_(!0);try{let t=r.map(e=>(0,T.Ro)(()=>R.dataAccessManager.orders.update(e.id,{status:"pending",productionStatus:"not_started",updatedAt:new Date().toISOString()}),"反审核订单 ".concat(e.orderNumber))),a=(await Promise.all(t)).filter(e=>null!==e).length;a>0?(await ea(),eq([]),eV([]),e.success("成功反审核 ".concat(a," 个订单"))):e.error("批量反审核失败，没有订单被成功反审核")}catch(t){e.error("批量反审核失败: ".concat(t instanceof Error?t.message:"未知错误")),console.error("批量反审核异常:",t)}finally{e_(!1)}}})},disabled:0===eT.length||!eL.some(e=>"confirmed"===e.status),loading:eF,children:["反审核 (",eL.filter(e=>"confirmed"===e.status).length,")"]})]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:eT.length>0?"已选择 ".concat(eT.length," 个订单"):"共 ".concat(e2.length," 个订单")})]}),(0,a.jsx)(Z.Z,{columns:[{title:"销售订单号",dataIndex:"orderNumber",key:"orderNumber",width:140,fixed:"left",render:(e,t)=>(0,a.jsx)(h.ZP,{type:"link",onClick:()=>e4(t),style:{padding:0,height:"auto",fontWeight:"bold"},children:e})},{title:"订单状态",dataIndex:"status",key:"status",width:100,render:e=>eG(e)},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:160,sorter:(e,t)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime(),defaultSortOrder:"descend",render:e=>e?L()(e).format("YYYY-MM-DD HH:mm:ss"):"-"},{title:"客户名称",dataIndex:"customerName",key:"customerName",width:180,ellipsis:!0},{title:"订单日期",dataIndex:"orderDate",key:"orderDate",width:120,sorter:(e,t)=>new Date(e.orderDate).getTime()-new Date(t.orderDate).getTime()},{title:"订单金额",dataIndex:"finalAmount",key:"finalAmount",width:150,sorter:(e,t)=>e.finalAmount-t.finalAmount,render:(e,t)=>(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{style:{fontWeight:"bold",color:"#1890ff"},children:["\xa5",(e||0).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})]}),(t.discountAmount||0)>0&&(0,a.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["折扣: \xa5",(t.discountAmount||0).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})]})]})},{title:"生产状态",dataIndex:"productionStatus",key:"productionStatus",width:120,render:(e,t)=>e0(t.status,e)},{title:"付款状态",dataIndex:"paymentStatus",key:"paymentStatus",width:100,render:e=>e1(e)},{title:"变更次数",key:"changeCount",width:100,render:(e,t)=>(0,a.jsx)("div",{children:t.changes.length>0?(0,a.jsx)(p.Z,{title:"点击查看变更历史",children:(0,a.jsx)(f.Z,{count:t.changes.length,size:"small",children:(0,a.jsxs)(h.ZP,{type:"link",size:"small",children:[t.changes.length,"次"]})})}):(0,a.jsx)("span",{style:{color:"#666"},children:"无"})})},{title:"操作",key:"action",width:180,fixed:"right",render:(e,t)=>(0,a.jsxs)(g.Z,{size:"small",children:[(0,a.jsx)(h.ZP,{type:"link",icon:(0,a.jsx)(k.Z,{}),onClick:()=>e3(t),children:"变更"}),(0,a.jsx)(x.Z,{title:"确定要删除这个订单吗？",onConfirm:()=>e7(t.id),okText:"确定",cancelText:"取消",children:(0,a.jsx)(h.ZP,{type:"link",danger:!0,icon:(0,a.jsx)(M.Z,{}),children:"删除"})})]})}],dataSource:e2,rowKey:"id",loading:et||eF,rowSelection:{selectedRowKeys:eT,onChange:(e,t)=>{eq(e),eV(t)},getCheckboxProps:e=>({disabled:!1})},pagination:{total:e2.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条"),pageSizeOptions:["10","20","50","100"],defaultPageSize:10},scroll:{x:1600}})]}),(0,a.jsxs)(S.Z,{title:"订单变更申请",open:B,onOk:()=>{G.validateFields().then(async t=>{if(!U)return;let r=Y.validateOrderChange(U,t.changeType,t.originalValue,t.newValue);if(!r.isValid){e.error("变更验证失败：".concat(r.errors[0]));return}r.warnings&&r.warnings.length>0&&r.warnings.forEach(t=>{e.warning(t)});let a=new Date().toISOString(),n={id:Date.now().toString(),orderNumber:U.orderNumber,...t,changeStatus:"pending",applicant:"当前用户",customerConfirmed:!1,productionStatus:U.productionStatus,createdAt:a},l=ep.find(e=>e.id===U.id);if(l){let t=[...l.changes||[],n];await (0,T.Ro)(()=>R.dataAccessManager.orders.update(U.id,{changes:t,updatedAt:a}),"提交变更申请")?(await ea(),Q(!1),G.resetFields(),e.success("订单变更申请提交成功")):e.error("提交变更申请失败")}})},onCancel:()=>{Q(!1),K(""),G.resetFields()},width:600,okText:"提交申请",cancelText:"取消",destroyOnHidden:!0,children:[(0,a.jsx)(P.Z,{message:"变更控制流程",description:"系统将根据生产状态自动判断变更可行性",type:"warning",showIcon:!0,style:{marginBottom:16}}),(0,a.jsxs)(u.Z,{form:G,layout:"vertical",children:[(0,a.jsx)(u.Z.Item,{name:"changeType",label:"变更类型",rules:[{required:!0,message:"请选择变更类型"}],children:(0,a.jsxs)(s.default,{placeholder:"请选择变更类型",onChange:e=>{if(!U)return;K(e);let t="";switch(e){case"quantity":var r;t=((null===(r=U.items)||void 0===r?void 0:r.reduce((e,t)=>e+t.quantity,0))||0).toString();break;case"delivery_date":try{t=new Date(U.deliveryDate).toISOString().split("T")[0]}catch(e){t=U.deliveryDate.split("T")[0]}break;case"cancel":t=({pending:"未审核",confirmed:"已审核",completed:"完成",cancelled:"已取消"})[U.status]||U.status;break;default:t=""}G.setFieldsValue({originalValue:t,newValue:""})},children:[(0,a.jsx)(ej,{value:"quantity",children:"数量变更"}),(0,a.jsx)(ej,{value:"delivery_date",children:"交期变更"}),(0,a.jsx)(ej,{value:"cancel",children:"订单取消"})]})}),J&&(0,a.jsx)(P.Z,{message:"".concat("quantity"===J?"数量变更":"delivery_date"===J?"交期变更":"订单取消","提示"),description:"quantity"===J?"系统已自动填入当前订单总数量，请在新值中输入变更后的数量。":"delivery_date"===J?"系统已自动填入当前交期，请选择新的交期日期。":"系统已自动填入当前订单状态，请在新值中说明取消原因。",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,a.jsxs)(v.Z,{gutter:16,children:[(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{name:"originalValue",label:"原始值",rules:[{required:!0,message:"请输入原始值"}],children:e5(J,"originalValue")})}),(0,a.jsx)(j.Z,{span:12,children:(0,a.jsx)(u.Z.Item,{name:"newValue",label:"新值",rules:[{required:!0,message:"请输入新值"}],children:e5(J,"newValue")})})]}),(0,a.jsx)(u.Z.Item,{name:"changeReason",label:"变更原因",rules:[{required:!0,message:"请输入变更原因"}],children:(0,a.jsx)(ew,{rows:3,placeholder:"请详细说明变更原因"})}),U&&(0,a.jsxs)("div",{style:{marginTop:16},children:[(0,a.jsx)("h4",{children:"当前订单状态"}),(0,a.jsxs)(C.Z,{size:"small",column:2,children:[(0,a.jsx)(C.Z.Item,{label:"销售订单号",children:U.orderNumber}),(0,a.jsx)(C.Z.Item,{label:"生产状态",children:e0(U.status,U.productionStatus)}),(0,a.jsx)(C.Z.Item,{label:"订单状态",children:eG(U.status)}),(0,a.jsxs)(C.Z.Item,{label:"变更次数",children:[U.changes.length,"次"]})]})]})]})]}),(0,a.jsx)(em.ZP,{open:_,order:U,onClose:()=>{W(!1),eQ(!1),eB(null),q(!1),eH(0)},config:{...ev,actions:[{key:"mrp",text:e=>d?"执行中...":"completed"===e.mrpStatus?"MRP已完成":"in_progress"===e.mrpStatus?"MRP执行中":"failed"===e.mrpStatus?"重新启动MRP":eY?"MRP已完成":"启动MRP",type:"primary",icon:(0,a.jsx)(E.Z,{}),onClick:e=>te(e),loading:d,hidden:e=>"confirmed"!==e.status,disabled:e=>d||"completed"===e.mrpStatus||"in_progress"===e.mrpStatus||eY,style:{marginRight:8}}]}}),(0,a.jsx)(el,{open:X,onCancel:()=>{H(!1)},onSuccess:e9})]})};function eP(){return(0,a.jsx)(c.Z,{children:(0,a.jsx)(eS,{})})}},57613:function(e,t,r){"use strict";r.d(t,{Tx:function(){return s},rm:function(){return l}});var a=r(94660),n=r(74810);let l=(0,a.Ue)()((0,n.mW)((0,n.tJ)(e=>({materials:[],selectedMaterial:null,materialsLoading:!1,productModels:[],selectedProductModel:null,productModelsLoading:!1,setMaterials:t=>e({materials:t}),setSelectedMaterial:t=>e({selectedMaterial:t}),setMaterialsLoading:t=>e({materialsLoading:t}),setProductModels:t=>{e({productModels:t})},setSelectedProductModel:t=>e({selectedProductModel:t}),setProductModelsLoading:t=>e({productModelsLoading:t}),clearAll:()=>e({materials:[],selectedMaterial:null,materialsLoading:!1,productModels:[],selectedProductModel:null,productModelsLoading:!1}),initializeFromStorage:()=>{}}),{name:"master-data-store",partialize:e=>({materials:e.materials,productModels:e.productModels}),version:1,migrate:(e,t)=>0===t?{materials:e.materials||[],productModels:e.productModels||[]}:e}),{name:"master-data-store-devtools"})),s={clearLocalStorage:()=>{localStorage.removeItem("master-data-storage")},exportData:()=>{let e=l.getState(),t={materials:e.materials,productModels:e.productModels,exportTime:new Date().toISOString(),version:"1.0"},r=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),a=URL.createObjectURL(r),n=document.createElement("a");n.href=a,n.download="master-data-".concat(new Date().toISOString().split("T")[0],".json"),document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(a)},importData:e=>new Promise((t,r)=>{let a=new FileReader;a.onload=e=>{try{var a;let r=JSON.parse(null===(a=e.target)||void 0===a?void 0:a.result),n=l.getState();r.materials&&Array.isArray(r.materials)&&n.setMaterials(r.materials),r.productModels&&Array.isArray(r.productModels)&&n.setProductModels(r.productModels),t()}catch(e){r(e)}},a.onerror=()=>r(Error("文件读取失败")),a.readAsText(e)}),getStorageInfo:()=>{var e;let t=l.getState(),r=localStorage.getItem("master-data-storage");return{materialsCount:t.materials.length,productModelsCount:t.productModels.length,storageSize:r?new Blob([r]).size:0,lastUpdated:r?null===(e=JSON.parse(r).state)||void 0===e?void 0:e.updatedAt:null}}}},24033:function(e,t,r){e.exports=r(15313)}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,9427,1157,4863,7416,8236,30,5166,9617,2897,5424,3656,1330,2779,1865,2971,4938,1744],function(){return e(e.s=14849)}),_N_E=e.O()}]);