"use strict";exports.id=1428,exports.ids=[1428],exports.modules={1428:(e,r,t)=>{t.d(r,{mrpService:()=>s});var o=t(37637),a=t(85287);class d{static getInstance(){return d.instance||(d.instance=new d),d.instance}async executeMRP(e){let{salesOrder:r,executedBy:t,executionDate:o}=e;console.log("\uD83D\uDE80 [MRPService] 开始执行MRP",{orderId:r.id,orderNumber:r.orderNumber,executedBy:t});try{let e=await this.checkInventory(r),a=await this.analyzeSharedMolds(r,e),d=`mrp-${Date.now()}`,s=await this.generateProductionOrdersWithSharedMold(r,e,a,d,t),u=e.reduce((e,r)=>e+100*r.shortageQuantity,0),c=s.filter(e=>e.isSharedMold).length,n=s.filter(e=>!e.isSharedMold).length,i={id:d,salesOrderId:r.id,salesOrderNumber:r.orderNumber,executionDate:o,status:s.length>0?"success":"partial_shortage",totalProductionOrders:s.length,sharedMoldOrders:c,traditionalOrders:n,inventoryCheckResults:e,generatedProductionOrders:s,totalShortageValue:u,executedBy:t,executedAt:new Date().toISOString()};return console.log("✅ [MRPService] MRP执行完成",{resultId:i.id,totalOrders:i.totalProductionOrders,sharedMoldOrders:i.sharedMoldOrders,traditionalOrders:i.traditionalOrders,status:i.status}),i}catch(e){return console.error("❌ [MRPService] MRP执行失败:",e),{id:`mrp-error-${Date.now()}`,salesOrderId:r.id,salesOrderNumber:r.orderNumber,executionDate:o,status:"failed",totalProductionOrders:0,sharedMoldOrders:0,traditionalOrders:0,inventoryCheckResults:[],generatedProductionOrders:[],totalShortageValue:0,executedBy:t,executedAt:new Date().toISOString()}}}async checkInventory(e){let r=[];for(let t of e.items)try{let e=await o.dataAccessManager.inventory.getByProductCode(t.productCode),a=0;"success"===e.status&&e.data&&(a=e.data.currentStock||0);let d=t.quantity,s=Math.max(0,d-a);r.push({productCode:t.productCode,productName:t.productName,requiredQuantity:d,availableQuantity:a,shortageQuantity:s,isSufficient:0===s})}catch(e){console.error(`❌ [MRPService] 库存检查失败 - 产品: ${t.productCode}`,e),r.push({productCode:t.productCode,productName:t.productName,requiredQuantity:t.quantity,availableQuantity:0,shortageQuantity:t.quantity,isSufficient:!1})}return r}async analyzeSharedMolds(e,r){console.log("\uD83D\uDD0D [MRPService] 开始分析共享模具情况");let t=[],a=new Map;for(let t of r)if(t.shortageQuantity>0)try{let r=await o.dataAccessManager.products.getByCode(t.productCode);if("success"===r.status&&r.data){let o=r.data.formingMold||"";if(o){let d={productCode:t.productCode,productName:t.productName,productModelCode:r.data.modelCode,requiredQuantity:t.requiredQuantity,deliveryDate:e.deliveryDate,sourceOrderId:e.id,sourceOrderNumber:e.orderNumber,customerName:e.customerName,urgencyLevel:"medium"};a.has(o)||a.set(o,[]),a.get(o).push(d)}}}catch(e){console.error(`❌ 获取产品 ${t.productCode} 主数据失败:`,e)}for(let[o,d]of a.entries())if(d.length>1){console.log(`🔧 发现共享模具: ${o}, 包含 ${d.length} 个产品`);let a=Math.max(...d.map(e=>e.requiredQuantity)),s=Math.max(...d.map(e=>{let t=r.find(r=>r.productCode===e.productCode);return t?.shortageQuantity||0})),u={moldNumber:o,deliveryDate:e.deliveryDate,products:d,isFirstOccurrence:!0,maxRequiredQuantity:a,maxShortageQuantity:s};t.push(u)}return console.log(`✅ [MRPService] 共享模具分析完成，发现 ${t.length} 个共享模具组`),t}async generateProductionOrdersWithSharedMold(e,r,t,o,a){let d=[],s=new Set;for(let u of t){let t=await this.generateSharedMoldOrder(u,r,e,o,a);t&&(d.push(t),u.products.forEach(e=>s.add(e.productCode)))}for(let t of r)if(t.shortageQuantity>0&&!s.has(t.productCode)){let r=await this.generateTraditionalOrder(t,e,o,a);r&&d.push(r)}return d}async generateTraditionalOrder(e,r,t,d){try{let s,u="",c=await o.dataAccessManager.products.getByCode(e.productCode);"success"===c.status&&c.data?(u=c.data.formingMold||"",console.log(`✅ 获取产品 ${e.productCode} 的成型模具编号: ${u}`)):console.warn(`⚠️ 未找到产品 ${e.productCode} 的主数据，模具编号将为空`);let n=await o.dataAccessManager.customers.getById(r.customerId);"success"===n.status&&n.data?(s=n.data.customerLevel,console.log(`✅ 获取客户 ${r.customerName} 的信用等级: ${s}`)):console.warn(`⚠️ 未找到客户 ${r.customerId} 的主数据，信用等级将为空`);let i=a.s.generateProductionOrderId(r.orderNumber),l={id:`po-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,orderNumber:i,productName:e.productName,productCode:e.productCode,formingMoldNumber:u,isSharedMold:!1,productItems:[],plannedQuantity:e.shortageQuantity,producedQuantity:0,startDate:new Date().toISOString().split("T")[0],endDate:r.deliveryDate,deliveryDate:r.deliveryDate,status:"in_plan",workstation:"默认工位",customerName:r.customerName,customerId:r.customerId,customerCreditLevel:s,prioritySource:"auto",salesOrderNumber:r.orderNumber,sourceOrderNumbers:[r.orderNumber],sourceOrderIds:[r.id],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},m=await o.dataAccessManager.productionOrders.createFromMRP({...l,mrpExecutionId:t,mrpExecutedBy:d,mrpExecutedAt:new Date().toISOString()});if("success"!==m.status)throw Error(`生产订单创建失败: ${m.message}`);return console.log(`✅ [MRPService] 传统生产订单已保存: ${l.orderNumber}, MRP执行ID: ${t}`),l}catch(r){return console.error(`❌ [MRPService] 生成传统订单失败 - 产品: ${e.productCode}`,r),null}}async generateSharedMoldOrder(e,r,t,d,s){try{let r;console.log(`🔧 [MRPService] 开始生成共享模具订单: ${e.moldNumber}`);let u=await o.dataAccessManager.customers.getById(t.customerId);"success"===u.status&&u.data&&(r=u.data.customerLevel);let c=a.s.generateProductionOrderId(t.orderNumber),n=e.products.map(e=>({id:`item-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,productCode:e.productCode,productName:e.productName,plannedQuantity:e.requiredQuantity,producedQuantity:0,requiredQuantity:e.requiredQuantity,sourceOrderItems:[t.id]})),i=e.maxShortageQuantity??e.maxRequiredQuantity,l=e.products[0],m={id:`po-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,orderNumber:c,productName:`${e.moldNumber} 共享模具生产`,productCode:l.productCode,formingMoldNumber:e.moldNumber,isSharedMold:!0,moldGroup:e.moldNumber,productItems:n,plannedQuantity:i,producedQuantity:0,startDate:new Date().toISOString().split("T")[0],endDate:e.deliveryDate,deliveryDate:e.deliveryDate,status:"in_plan",workstation:"默认工位",customerName:t.customerName,customerId:t.customerId,customerCreditLevel:r,prioritySource:"auto",salesOrderNumber:t.orderNumber,sourceOrderNumbers:[...new Set(e.products.map(e=>e.sourceOrderNumber))],sourceOrderIds:[...new Set(e.products.map(e=>e.sourceOrderId))],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},p=await o.dataAccessManager.productionOrders.createFromMRP({...m,mrpExecutionId:d,mrpExecutedBy:s,mrpExecutedAt:new Date().toISOString()});if("success"!==p.status)throw Error(`共享模具生产订单创建失败: ${p.message}`);return console.log(`✅ [MRPService] 共享模具生产订单已保存: ${m.orderNumber}, 生产数量: ${i}, MRP执行ID: ${d}`),m}catch(r){return console.error(`❌ [MRPService] 生成共享模具订单失败 - 模具: ${e.moldNumber}`,r),null}}}let s=d.getInstance()}};