(()=>{var e={};e.id=8381,e.ids=[8381],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},5889:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>g,tree:()=>c});var n=r(50482),a=r(69108),i=r(62563),l=r.n(i),o=r(68300),s={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(s[e]=()=>o[e]);r.d(t,s);let c=["",{children:["admin",{children:["integration-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,33686)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\admin\\integration-test\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\admin\\integration-test\\page.tsx"],u="/admin/integration-test/page",m={require:r,loadChunk:()=>Promise.resolve()},g=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/integration-test/page",pathname:"/admin/integration-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},31308:(e,t,r)=>{Promise.resolve().then(r.bind(r,76876))},1426:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M304 280h56c4.4 0 8-3.6 8-8 0-28.3 5.9-53.2 17.1-73.5 10.6-19.4 26-34.8 45.4-45.4C450.9 142 475.7 136 504 136h16c28.3 0 53.2 5.9 73.5 17.1 19.4 10.6 34.8 26 45.4 45.4C650 218.9 656 243.7 656 272c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-40-8.8-76.7-25.9-108.1a184.31 184.31 0 00-74-74C596.7 72.8 560 64 520 64h-16c-40 0-76.7 8.8-108.1 25.9a184.31 184.31 0 00-74 74C304.8 195.3 296 232 296 272c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M940 512H792V412c76.8 0 139-62.2 139-139 0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8a63 63 0 01-63 63H232a63 63 0 01-63-63c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 76.8 62.2 139 139 139v100H84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h148v96c0 6.5.2 13 .7 19.3C164.1 728.6 116 796.7 116 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-44.2 23.9-82.9 59.6-103.7a273 273 0 0022.7 49c24.3 41.5 59 76.2 100.5 100.5S460.5 960 512 960s99.8-13.9 141.3-38.2a281.38 281.38 0 00123.2-149.5A120 120 0 01836 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-79.3-48.1-147.4-116.7-176.7.4-6.4.7-12.8.7-19.3v-96h148c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM716 680c0 36.8-9.7 72-27.8 102.9-17.7 30.3-43 55.6-73.3 73.3C584 874.3 548.8 884 512 884s-72-9.7-102.9-27.8c-30.3-17.7-55.6-43-73.3-73.3A202.75 202.75 0 01308 680V412h408v268z"}}]},name:"bug",theme:"outlined"};var l=r(49809);let o=a.forwardRef(function(e,t){return a.createElement(l.Z,(0,n.Z)({},e,{ref:t,icon:i}))})},3745:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var l=r(49809);let o=a.forwardRef(function(e,t){return a.createElement(l.Z,(0,n.Z)({},e,{ref:t,icon:i}))})},70414:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"};var l=r(49809);let o=a.forwardRef(function(e,t){return a.createElement(l.Z,(0,n.Z)({},e,{ref:t,icon:i}))})},15595:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"};var l=r(49809);let o=a.forwardRef(function(e,t){return a.createElement(l.Z,(0,n.Z)({},e,{ref:t,icon:i}))})},98507:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"};var l=r(49809);let o=a.forwardRef(function(e,t){return a.createElement(l.Z,(0,n.Z)({},e,{ref:t,icon:i}))})},44670:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var n=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"};var l=r(49809);let o=a.forwardRef(function(e,t){return a.createElement(l.Z,(0,n.Z)({},e,{ref:t,icon:i}))})},27976:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=r(73371).Z},13113:(e,t,r)=>{"use strict";r.d(t,{Z:()=>b});var n=r(3729),a=r(34132),i=r.n(a),l=r(84893),o=r(54527),s=r(92959),c=r(22989),d=r(13165),u=r(96373);let m=e=>{let{componentCls:t}=e;return{[t]:{"&-horizontal":{[`&${t}`]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},g=e=>{let{componentCls:t,sizePaddingEdgeHorizontal:r,colorSplit:n,lineWidth:a,textPaddingInline:i,orientationMargin:l,verticalMarginInline:o}=e;return{[t]:Object.assign(Object.assign({},(0,c.Wf)(e)),{borderBlockStart:`${(0,s.bf)(a)} solid ${n}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:o,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,s.bf)(a)} solid ${n}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,s.bf)(e.marginLG)} 0`},[`&-horizontal${t}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,s.bf)(e.dividerHorizontalWithTextGutterMargin)} 0`,color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${n}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,s.bf)(a)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${t}-with-text-start`]:{"&::before":{width:`calc(${l} * 100%)`},"&::after":{width:`calc(100% - ${l} * 100%)`}},[`&-horizontal${t}-with-text-end`]:{"&::before":{width:`calc(100% - ${l} * 100%)`},"&::after":{width:`calc(${l} * 100%)`}},[`${t}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:i},"&-dashed":{background:"none",borderColor:n,borderStyle:"dashed",borderWidth:`${(0,s.bf)(a)} 0 0`},[`&-horizontal${t}-with-text${t}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${t}-dashed`]:{borderInlineStartWidth:a,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:n,borderStyle:"dotted",borderWidth:`${(0,s.bf)(a)} 0 0`},[`&-horizontal${t}-with-text${t}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${t}-dotted`]:{borderInlineStartWidth:a,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${t}-with-text`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},[`&-horizontal${t}-with-text-start${t}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${t}-inner-text`]:{paddingInlineStart:r}},[`&-horizontal${t}-with-text-end${t}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${t}-inner-text`]:{paddingInlineEnd:r}}})}},p=(0,d.I$)("Divider",e=>{let t=(0,u.IX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[g(t),m(t)]},e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}});var h=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let f={small:"sm",middle:"md"},b=e=>{let{getPrefixCls:t,direction:r,className:a,style:s}=(0,l.dj)("divider"),{prefixCls:c,type:d="horizontal",orientation:u="center",orientationMargin:m,className:g,rootClassName:b,children:$,dashed:x,variant:y="solid",plain:v,style:S,size:j}=e,w=h(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),O=t("divider",c),[C,k,Z]=p(O),I=f[(0,o.Z)(j)],z=!!$,E=n.useMemo(()=>"left"===u?"rtl"===r?"end":"start":"right"===u?"rtl"===r?"start":"end":u,[r,u]),P="start"===E&&null!=m,B="end"===E&&null!=m,N=i()(O,a,k,Z,`${O}-${d}`,{[`${O}-with-text`]:z,[`${O}-with-text-${E}`]:z,[`${O}-dashed`]:!!x,[`${O}-${y}`]:"solid"!==y,[`${O}-plain`]:!!v,[`${O}-rtl`]:"rtl"===r,[`${O}-no-default-orientation-margin-start`]:P,[`${O}-no-default-orientation-margin-end`]:B,[`${O}-${I}`]:!!I},g,b),M=n.useMemo(()=>"number"==typeof m?m:/^\d+$/.test(m)?Number(m):m,[m]);return C(n.createElement("div",Object.assign({className:N,style:Object.assign(Object.assign({},s),S)},w,{role:"separator"}),$&&"vertical"!==d&&n.createElement("span",{className:`${O}-inner-text`,style:{marginInlineStart:P?M:void 0,marginInlineEnd:B?M:void 0}},$)))}},34953:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(3729).createContext)({})},73371:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var n=r(3729),a=r(34132),i=r.n(a),l=r(84893),o=r(34953),s=r(19249),c=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};function d(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}let u=["xs","sm","md","lg","xl","xxl"],m=n.forwardRef((e,t)=>{let{getPrefixCls:r,direction:a}=n.useContext(l.E_),{gutter:m,wrap:g}=n.useContext(o.Z),{prefixCls:p,span:h,order:f,offset:b,push:$,pull:x,className:y,children:v,flex:S,style:j}=e,w=c(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),O=r("col",p),[C,k,Z]=(0,s.cG)(O),I={},z={};u.forEach(t=>{let r={},n=e[t];"number"==typeof n?r.span=n:"object"==typeof n&&(r=n||{}),delete w[t],z=Object.assign(Object.assign({},z),{[`${O}-${t}-${r.span}`]:void 0!==r.span,[`${O}-${t}-order-${r.order}`]:r.order||0===r.order,[`${O}-${t}-offset-${r.offset}`]:r.offset||0===r.offset,[`${O}-${t}-push-${r.push}`]:r.push||0===r.push,[`${O}-${t}-pull-${r.pull}`]:r.pull||0===r.pull,[`${O}-rtl`]:"rtl"===a}),r.flex&&(z[`${O}-${t}-flex`]=!0,I[`--${O}-${t}-flex`]=d(r.flex))});let E=i()(O,{[`${O}-${h}`]:void 0!==h,[`${O}-order-${f}`]:f,[`${O}-offset-${b}`]:b,[`${O}-push-${$}`]:$,[`${O}-pull-${x}`]:x},y,z,k,Z),P={};if(m&&m[0]>0){let e=m[0]/2;P.paddingLeft=e,P.paddingRight=e}return S&&(P.flex=d(S),!1!==g||P.minWidth||(P.minWidth=0)),C(n.createElement("div",Object.assign({},w,{style:Object.assign(Object.assign(Object.assign({},P),j),I),className:E,ref:t}),v))})},66074:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var n=r(3729),a=r(34132),i=r.n(a),l=r(91782),o=r(84893),s=r(91735),c=r(34953),d=r(19249),u=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};function m(e,t){let[r,a]=n.useState("string"==typeof e?e:""),i=()=>{if("string"==typeof e&&a(e),"object"==typeof e)for(let r=0;r<l.c4.length;r++){let n=l.c4[r];if(!t||!t[n])continue;let i=e[n];if(void 0!==i){a(i);return}}};return n.useEffect(()=>{i()},[JSON.stringify(e),t]),r}let g=n.forwardRef((e,t)=>{let{prefixCls:r,justify:a,align:g,className:p,style:h,children:f,gutter:b=0,wrap:$}=e,x=u(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:y,direction:v}=n.useContext(o.E_),S=(0,s.Z)(!0,null),j=m(g,S),w=m(a,S),O=y("row",r),[C,k,Z]=(0,d.VM)(O),I=function(e,t){let r=[void 0,void 0],n=Array.isArray(e)?e:[e,void 0],a=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return n.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let n=0;n<l.c4.length;n++){let i=l.c4[n];if(a[i]&&void 0!==e[i]){r[t]=e[i];break}}else r[t]=e}),r}(b,S),z=i()(O,{[`${O}-no-wrap`]:!1===$,[`${O}-${w}`]:w,[`${O}-${j}`]:j,[`${O}-rtl`]:"rtl"===v},p,k,Z),E={},P=null!=I[0]&&I[0]>0?-(I[0]/2):void 0;P&&(E.marginLeft=P,E.marginRight=P);let[B,N]=I;E.rowGap=N;let M=n.useMemo(()=>({gutter:[B,N],wrap:$}),[B,N,$]);return C(n.createElement(c.Z.Provider,{value:M},n.createElement("div",Object.assign({},x,{className:z,style:Object.assign(Object.assign({},E),h),ref:t}),f)))})},63724:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=r(66074).Z},90377:(e,t,r)=>{"use strict";r.d(t,{Z:()=>z});var n=r(3729),a=r(34132),i=r.n(a),l=r(24773),o=r(22624),s=r(46164),c=r(29545),d=r(30605),u=r(84893),m=r(92959),g=r(55002),p=r(22989),h=r(96373),f=r(13165);let b=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:n,componentCls:a,calc:i}=e,l=i(n).sub(r).equal(),o=i(t).sub(r).equal();return{[a]:Object.assign(Object.assign({},(0,p.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,m.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:o,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},$=e=>{let{lineWidth:t,fontSizeIcon:r,calc:n}=e,a=e.fontSizeSM;return(0,h.IX)(e,{tagFontSize:a,tagLineHeight:(0,m.bf)(n(e.lineHeightSM).mul(a).equal()),tagIconSize:n(r).sub(n(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},x=e=>({defaultBg:new g.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),y=(0,f.I$)("Tag",e=>b($(e)),x);var v=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let S=n.forwardRef((e,t)=>{let{prefixCls:r,style:a,className:l,checked:o,onChange:s,onClick:c}=e,d=v(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:m,tag:g}=n.useContext(u.E_),p=m("tag",r),[h,f,b]=y(p),$=i()(p,`${p}-checkable`,{[`${p}-checkable-checked`]:o},null==g?void 0:g.className,l,f,b);return h(n.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},a),null==g?void 0:g.style),className:$,onClick:e=>{null==s||s(!o),null==c||c(e)}})))});var j=r(78701);let w=e=>(0,j.Z)(e,(t,{textColor:r,lightBorderColor:n,lightColor:a,darkColor:i})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:a,borderColor:n,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),O=(0,f.bk)(["Tag","preset"],e=>w($(e)),x),C=(e,t,r)=>{let n=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${n}Bg`],borderColor:e[`color${n}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},k=(0,f.bk)(["Tag","status"],e=>{let t=$(e);return[C(t,"success","Success"),C(t,"processing","Info"),C(t,"error","Error"),C(t,"warning","Warning")]},x);var Z=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let I=n.forwardRef((e,t)=>{let{prefixCls:r,className:a,rootClassName:m,style:g,children:p,icon:h,color:f,onClose:b,bordered:$=!0,visible:x}=e,v=Z(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:S,direction:j,tag:w}=n.useContext(u.E_),[C,I]=n.useState(!0),z=(0,l.Z)(v,["closeIcon","closable"]);n.useEffect(()=>{void 0!==x&&I(x)},[x]);let E=(0,o.o2)(f),P=(0,o.yT)(f),B=E||P,N=Object.assign(Object.assign({backgroundColor:f&&!B?f:void 0},null==w?void 0:w.style),g),M=S("tag",r),[A,T,D]=y(M),H=i()(M,null==w?void 0:w.className,{[`${M}-${f}`]:B,[`${M}-has-color`]:f&&!B,[`${M}-hidden`]:!C,[`${M}-rtl`]:"rtl"===j,[`${M}-borderless`]:!$},a,m,T,D),q=e=>{e.stopPropagation(),null==b||b(e),e.defaultPrevented||I(!1)},[,W]=(0,s.Z)((0,s.w)(e),(0,s.w)(w),{closable:!1,closeIconRender:e=>{let t=n.createElement("span",{className:`${M}-close-icon`,onClick:q},e);return(0,c.wm)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),q(t)},className:i()(null==e?void 0:e.className,`${M}-close-icon`)}))}}),R="function"==typeof v.onClick||p&&"a"===p.type,_=h||null,L=_?n.createElement(n.Fragment,null,_,p&&n.createElement("span",null,p)):p,X=n.createElement("span",Object.assign({},z,{ref:t,className:H,style:N}),L,W,E&&n.createElement(O,{key:"preset",prefixCls:M}),P&&n.createElement(k,{key:"status",prefixCls:M}));return A(R?n.createElement(d.Z,{component:"Tag"},X):X)});I.CheckableTag=S;let z=I},18161:(e,t,r)=>{"use strict";r.d(t,{Z:()=>j});var n=r(3729),a=r(34132),i=r.n(a),l=r(84893),o=r(13878),s=r(92959),c=r(22989),d=r(13165),u=r(96373);let m=e=>{let{componentCls:t,calc:r}=e;return{[t]:Object.assign(Object.assign({},(0,c.Wf)(e)),{margin:0,padding:0,listStyle:"none",[`${t}-item`]:{position:"relative",margin:0,paddingBottom:e.itemPaddingBottom,fontSize:e.fontSize,listStyle:"none","&-tail":{position:"absolute",insetBlockStart:e.itemHeadSize,insetInlineStart:r(r(e.itemHeadSize).sub(e.tailWidth)).div(2).equal(),height:`calc(100% - ${(0,s.bf)(e.itemHeadSize)})`,borderInlineStart:`${(0,s.bf)(e.tailWidth)} ${e.lineType} ${e.tailColor}`},"&-pending":{[`${t}-item-head`]:{fontSize:e.fontSizeSM,backgroundColor:"transparent"},[`${t}-item-tail`]:{display:"none"}},"&-head":{position:"absolute",width:e.itemHeadSize,height:e.itemHeadSize,backgroundColor:e.dotBg,border:`${(0,s.bf)(e.dotBorderWidth)} ${e.lineType} transparent`,borderRadius:"50%","&-blue":{color:e.colorPrimary,borderColor:e.colorPrimary},"&-red":{color:e.colorError,borderColor:e.colorError},"&-green":{color:e.colorSuccess,borderColor:e.colorSuccess},"&-gray":{color:e.colorTextDisabled,borderColor:e.colorTextDisabled}},"&-head-custom":{position:"absolute",insetBlockStart:r(e.itemHeadSize).div(2).equal(),insetInlineStart:r(e.itemHeadSize).div(2).equal(),width:"auto",height:"auto",marginBlockStart:0,paddingBlock:e.customHeadPaddingVertical,lineHeight:1,textAlign:"center",border:0,borderRadius:0,transform:"translate(-50%, -50%)"},"&-content":{position:"relative",insetBlockStart:r(r(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.lineWidth).equal(),marginInlineStart:r(e.margin).add(e.itemHeadSize).equal(),marginInlineEnd:0,marginBlockStart:0,marginBlockEnd:0,wordBreak:"break-word"},"&-last":{[`> ${t}-item-tail`]:{display:"none"},[`> ${t}-item-content`]:{minHeight:r(e.controlHeightLG).mul(1.2).equal()}}},[`&${t}-alternate,
        &${t}-right,
        &${t}-label`]:{[`${t}-item`]:{"&-tail, &-head, &-head-custom":{insetInlineStart:"50%"},"&-head":{marginInlineStart:r(e.marginXXS).mul(-1).equal(),"&-custom":{marginInlineStart:r(e.tailWidth).div(2).equal()}},"&-left":{[`${t}-item-content`]:{insetInlineStart:`calc(50% - ${(0,s.bf)(e.marginXXS)})`,width:`calc(50% - ${(0,s.bf)(e.marginSM)})`,textAlign:"start"}},"&-right":{[`${t}-item-content`]:{width:`calc(50% - ${(0,s.bf)(e.marginSM)})`,margin:0,textAlign:"end"}}}},[`&${t}-right`]:{[`${t}-item-right`]:{[`${t}-item-tail,
            ${t}-item-head,
            ${t}-item-head-custom`]:{insetInlineStart:`calc(100% - ${(0,s.bf)(r(r(e.itemHeadSize).add(e.tailWidth)).div(2).equal())})`},[`${t}-item-content`]:{width:`calc(100% - ${(0,s.bf)(r(e.itemHeadSize).add(e.marginXS).equal())})`}}},[`&${t}-pending
        ${t}-item-last
        ${t}-item-tail`]:{display:"block",height:`calc(100% - ${(0,s.bf)(e.margin)})`,borderInlineStart:`${(0,s.bf)(e.tailWidth)} dotted ${e.tailColor}`},[`&${t}-reverse
        ${t}-item-last
        ${t}-item-tail`]:{display:"none"},[`&${t}-reverse ${t}-item-pending`]:{[`${t}-item-tail`]:{insetBlockStart:e.margin,display:"block",height:`calc(100% - ${(0,s.bf)(e.margin)})`,borderInlineStart:`${(0,s.bf)(e.tailWidth)} dotted ${e.tailColor}`},[`${t}-item-content`]:{minHeight:r(e.controlHeightLG).mul(1.2).equal()}},[`&${t}-label`]:{[`${t}-item-label`]:{position:"absolute",insetBlockStart:r(r(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.tailWidth).equal(),width:`calc(50% - ${(0,s.bf)(e.marginSM)})`,textAlign:"end"},[`${t}-item-right`]:{[`${t}-item-label`]:{insetInlineStart:`calc(50% + ${(0,s.bf)(e.marginSM)})`,width:`calc(50% - ${(0,s.bf)(e.marginSM)})`,textAlign:"start"}}},"&-rtl":{direction:"rtl",[`${t}-item-head-custom`]:{transform:"translate(50%, -50%)"}}})}},g=(0,d.I$)("Timeline",e=>[m((0,u.IX)(e,{itemHeadSize:10,customHeadPaddingVertical:e.paddingXXS,paddingInlineEnd:2}))],e=>({tailColor:e.colorSplit,tailWidth:e.lineWidthBold,dotBorderWidth:e.wireframe?e.lineWidthBold:3*e.lineWidth,dotBg:e.colorBgContainer,itemPaddingBottom:1.25*e.padding}));var p=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let h=e=>{var{prefixCls:t,className:r,color:a="blue",dot:o,pending:s=!1,position:c,label:d,children:u}=e,m=p(e,["prefixCls","className","color","dot","pending","position","label","children"]);let{getPrefixCls:g}=n.useContext(l.E_),h=g("timeline",t),f=i()(`${h}-item`,{[`${h}-item-pending`]:s},r),b=/blue|red|green|gray/.test(a||"")?void 0:a,$=i()(`${h}-item-head`,{[`${h}-item-head-custom`]:!!o,[`${h}-item-head-${a}`]:!b});return n.createElement("li",Object.assign({},m,{className:f}),d&&n.createElement("div",{className:`${h}-item-label`},d),n.createElement("div",{className:`${h}-item-tail`}),n.createElement("div",{className:$,style:{borderColor:b,color:b}},o),n.createElement("div",{className:`${h}-item-content`},u))};var f=r(72375),b=r(31529),$=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let x=e=>{var{prefixCls:t,className:r,pending:a=!1,children:l,items:o,rootClassName:s,reverse:c=!1,direction:d,hashId:u,pendingDot:m,mode:g=""}=e,p=$(e,["prefixCls","className","pending","children","items","rootClassName","reverse","direction","hashId","pendingDot","mode"]);let x=(e,r)=>"alternate"===g?"right"===e?`${t}-item-right`:"left"===e?`${t}-item-left`:r%2==0?`${t}-item-left`:`${t}-item-right`:"left"===g?`${t}-item-left`:"right"===g||"right"===e?`${t}-item-right`:"",y=(0,f.Z)(o||[]);a&&y.push({pending:!!a,dot:m||n.createElement(b.Z,null),children:"boolean"==typeof a?null:a}),c&&y.reverse();let v=y.length,S=`${t}-item-last`,j=y.filter(e=>!!e).map((e,t)=>{var r;let l=t===v-2?S:"",o=t===v-1?S:"",{className:s}=e,d=$(e,["className"]);return n.createElement(h,Object.assign({},d,{className:i()([s,!c&&a?l:o,x(null!==(r=null==e?void 0:e.position)&&void 0!==r?r:"",t)]),key:(null==e?void 0:e.key)||t}))}),w=y.some(e=>!!(null==e?void 0:e.label)),O=i()(t,{[`${t}-pending`]:!!a,[`${t}-reverse`]:!!c,[`${t}-${g}`]:!!g&&!w,[`${t}-label`]:w,[`${t}-rtl`]:"rtl"===d},r,s,u);return n.createElement("ol",Object.assign({},p,{className:O}),j)};var y=r(89299),v=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(r[n[a]]=e[n[a]]);return r};let S=e=>{let{getPrefixCls:t,direction:r,timeline:a}=n.useContext(l.E_),{prefixCls:s,children:c,items:d,className:u,style:m}=e,p=v(e,["prefixCls","children","items","className","style"]),h=t("timeline",s),f=(0,o.Z)(h),[b,$,S]=g(h,f),j=function(e,t){return e&&Array.isArray(e)?e:(0,y.Z)(t).map(e=>{var t,r;return Object.assign({children:null!==(r=null===(t=null==e?void 0:e.props)||void 0===t?void 0:t.children)&&void 0!==r?r:""},e.props)})}(d,c);return b(n.createElement(x,Object.assign({},p,{className:i()(null==a?void 0:a.className,u,S,f),style:Object.assign(Object.assign({},null==a?void 0:a.style),m),prefixCls:h,direction:r,items:j,hashId:$})))};S.Item=h;let j=S},76876:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>Z});var n=r(95344),a=r(3729),i=r(30977),l=r(32979),o=r(16728),s=r(83984),c=r(63724),d=r(27976),u=r(10707),m=r(11157),g=r(18161),p=r(90377),h=r(67383),f=r(87049),b=r(13113),$=r(14223),x=r(3745),y=r(70414),v=r(15595),S=r(1426),j=r(98507),w=r(44670),O=r(37637);let{Step:C}=i.default,k=()=>{let{message:e}=l.Z.useApp(),[t,r]=(0,a.useState)(!1),[k,Z]=(0,a.useState)(0),[I,z]=(0,a.useState)([]),[E,P]=(0,a.useState)(null),[B,N]=(0,a.useState)([]),[M,A]=(0,a.useState)([]),[T,D]=(0,a.useState)({totalOrders:0,totalTasks:0,assignedTasks:0,unassignedTasks:0,averageUtilization:0,conflictCount:0}),H=async()=>{r(!0),Z(0),z([]),P(null);let t=[];try{Z(1);let e=Date.now(),r={id:`test_${Date.now()}`,orderNumber:`SO${Date.now().toString().slice(-6)}`,customerId:"TEST001",customerName:"测试客户",customerContact:"测试联系人",orderDate:new Date().toISOString().split("T")[0],deliveryDate:new Date(Date.now()+6048e5).toISOString().split("T")[0],promisedDeliveryDate:new Date(Date.now()+6048e5).toISOString().split("T")[0],status:"pending",productionStatus:"not_started",paymentStatus:"unpaid",paymentTerms:"货到付款",salesRepresentative:"测试销售",totalAmount:17500,discountAmount:0,finalAmount:17500,mrpStatus:"not_started",mrpExecutedAt:void 0,mrpExecutedBy:void 0,mrpResultId:void 0,items:[{id:"item1",orderNumber:`SO${Date.now().toString().slice(-6)}`,productName:"测试产品A",productCode:"P00001",quantity:1e3,unit:"个",unitPrice:10,totalPrice:1e4,deliveryQuantity:0,remainingQuantity:1e3,moldCode:"M001",productionWorkstation:"",batchNumber:"",remark:""},{id:"item2",orderNumber:`SO${Date.now().toString().slice(-6)}`,productName:"测试产品B",productCode:"P00002",quantity:500,unit:"个",unitPrice:15,totalPrice:7500,deliveryQuantity:0,remainingQuantity:500,moldCode:"M002",productionWorkstation:"",batchNumber:"",remark:""}],changes:[],remark:"集成测试订单",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},n=await O.dataAccessManager.orders.create({orderNumber:r.orderNumber,customerId:r.customerId,customerName:r.customerName,customerContact:r.customerContact,orderDate:r.orderDate,deliveryDate:r.deliveryDate,salesRepresentative:r.salesRepresentative,items:r.items,totalAmount:r.totalAmount,finalAmount:r.finalAmount,status:r.status,paymentTerms:r.paymentTerms,paymentStatus:r.paymentStatus,remark:r.remark});if("success"===n.status&&n.data){P(n.data);let r=await O.dataAccessManager.orders.getAll();"success"===r.status&&r.data&&N(r.data.items),t.push({step:"创建测试订单",status:"success",message:`成功创建订单 ${n.data.orderNumber}`,data:n.data,duration:Date.now()-e})}else throw Error(n.message||"创建订单失败");Z(2);let a=Date.now(),i=await O.dataAccessManager.orders.update(E.id,{status:"confirmed",updatedAt:new Date().toISOString()});if("success"===i.status){let e=await O.dataAccessManager.orders.getAll();if("success"===e.status&&e.data){N(e.data.items);let r=e.data.items.find(e=>e.id===E.id);if(r?.status==="confirmed")t.push({step:"审核订单",status:"success",message:`订单 ${E.orderNumber} 审核成功`,data:r,duration:Date.now()-a});else throw Error("订单状态未正确更新")}}else throw Error(i.message||"更新订单状态失败");Z(3);let l=Date.now();try{await new Promise(e=>setTimeout(e,1e3)),t.push({step:"MRP执行",status:"success",message:"MRP功能已移除，跳过此步骤",data:null,duration:Date.now()-l}),Z(4);let e=Date.now(),r={success:!1,message:"订单排产集成服务已删除",transferredCount:0,errors:["服务已删除"]};if(r.success){t.push({step:"传输到自动排单",status:"success",message:`成功传输 ${r.transferredCount} 个待排产订单`,data:r,duration:Date.now()-e}),Z(5);let n=Date.now();t.push({step:"智能排单执行",status:"warning",message:`智能排单功能已删除`,data:{success:!1,message:"智能排产功能已删除",scheduledTasks:[],totalOrders:0},duration:Date.now()-n})}else t.push({step:"传输到自动排单",status:"error",message:`传输失败: ${r.errors.join(", ")}`,duration:Date.now()-e})}catch(e){t.push({step:"MRP执行",status:"error",message:`MRP执行失败: ${e instanceof Error?e.message:"未知错误"}`,duration:Date.now()-l})}}catch(e){t.push({step:"测试异常",status:"error",message:`测试过程中发生异常: ${e instanceof Error?e.message:"未知错误"}`,duration:0})}z(t),Z(t.length),r(!1);let n=t.filter(e=>"success"===e.status).length,a=t.filter(e=>"error"===e.status).length;0===a?e.success(`集成测试完成！${n} 个步骤全部成功`):e.error(`集成测试完成，${a} 个步骤失败，${n} 个步骤成功`),D({totalOrders:{totalOrders:0,completedOrders:0,pendingOrders:0,lastUpdateTime:new Date().toISOString()}.totalOrders,totalTasks:0,assignedTasks:0,unassignedTasks:0,averageUtilization:0,conflictCount:0})},q=e=>{switch(e){case"success":return n.jsx(x.Z,{style:{color:"#52c41a"}});case"error":return n.jsx(y.Z,{style:{color:"#ff4d4f"}});case"warning":return n.jsx(v.Z,{style:{color:"#faad14"}});case"running":return n.jsx(o.Z,{size:"small"});default:return null}};return n.jsx("div",{className:"integration-test-page",style:{padding:"24px"},children:(0,n.jsxs)(s.Z,{children:[(0,n.jsxs)(c.Z,{justify:"space-between",align:"middle",style:{marginBottom:"24px"},children:[(0,n.jsxs)(d.Z,{children:[(0,n.jsxs)("h2",{style:{margin:0},children:[n.jsx(S.Z,{style:{marginRight:"8px"}}),"订单管理与自动排单集成测试"]}),n.jsx("p",{style:{margin:"8px 0 0 0",color:"#666"},children:"验证从订单创建到自动排单的完整业务流程"})]}),n.jsx(d.Z,{children:(0,n.jsxs)(u.Z,{children:[n.jsx(m.ZP,{type:"primary",icon:n.jsx(j.Z,{}),loading:t,onClick:H,children:"开始测试"}),n.jsx(m.ZP,{icon:n.jsx(w.Z,{}),onClick:()=>{z([]),Z(0),P(null)},children:"重置"})]})})]}),n.jsx(s.Z,{title:"测试步骤",size:"small",style:{marginBottom:"24px"},children:(0,n.jsxs)(i.default,{current:k,status:t?"process":"finish",children:[n.jsx(C,{title:"创建订单",description:"创建测试销售订单"}),n.jsx(C,{title:"审核订单",description:"将订单状态变更为已审核"}),n.jsx(C,{title:"触发MRP",description:"自动执行物料需求计划"}),n.jsx(C,{title:"传输订单",description:"将待排产订单传输到自动排单模块"}),n.jsx(C,{title:"智能排单",description:"执行智能排单算法"})]})}),I.length>0&&n.jsx(s.Z,{title:"测试结果",size:"small",style:{marginBottom:"24px"},children:n.jsx(g.Z,{children:I.map((e,t)=>(0,n.jsxs)(g.Z.Item,{dot:q(e.status),color:"success"===e.status?"green":"error"===e.status?"red":"orange",children:[(0,n.jsxs)("div",{children:[n.jsx("strong",{children:e.step}),n.jsx(p.Z,{color:"success"===e.status?"green":"error"===e.status?"red":"orange",style:{marginLeft:"8px"},children:e.status}),e.duration&&(0,n.jsxs)(p.Z,{color:"blue",style:{marginLeft:"4px"},children:[e.duration,"ms"]})]}),n.jsx("div",{style:{marginTop:"4px",color:"#666"},children:e.message})]},t))})}),(0,n.jsxs)(c.Z,{gutter:16,children:[n.jsx(d.Z,{span:12,children:n.jsx(s.Z,{title:"订单管理状态",size:"small",children:E?(0,n.jsxs)(h.Z,{column:1,size:"small",children:[n.jsx(h.Z.Item,{label:"订单号",children:E.orderNumber}),n.jsx(h.Z.Item,{label:"状态",children:n.jsx(p.Z,{color:"confirmed"===E.status?"green":"orange",children:"confirmed"===E.status?"已审核":"未审核"})}),n.jsx(h.Z.Item,{label:"产品数量",children:E.items.length}),(0,n.jsxs)(h.Z.Item,{label:"订单金额",children:["\xa5",E.totalAmount]})]}):n.jsx("div",{style:{textAlign:"center",color:"#999",padding:"20px"},children:"暂无测试订单"})})}),n.jsx(d.Z,{span:12,children:n.jsx(s.Z,{title:"自动排单状态",size:"small",children:(0,n.jsxs)(h.Z,{column:1,size:"small",children:[n.jsx(h.Z.Item,{label:"待排产订单",children:T.totalOrders}),n.jsx(h.Z.Item,{label:"总任务数",children:T.totalTasks}),n.jsx(h.Z.Item,{label:"已分配任务",children:T.assignedTasks}),n.jsx(h.Z.Item,{label:"未分配任务",children:T.unassignedTasks}),n.jsx(h.Z.Item,{label:"平均利用率",children:n.jsx(f.Z,{percent:Math.round(100*T.averageUtilization),size:"small",style:{width:"100px"}})})]})})})]}),n.jsx(b.Z,{}),n.jsx($.Z,{type:"info",message:"测试说明",description:(0,n.jsxs)("div",{children:[n.jsx("p",{children:"此测试将验证以下业务流程的完整性："}),(0,n.jsxs)("ul",{children:[n.jsx("li",{children:"1. 在订单管理模块创建销售订单"}),n.jsx("li",{children:'2. 审核订单，状态从"未审核"变更为"已审核"'}),n.jsx("li",{children:"3. 系统自动触发MRP（物料需求计划）执行"}),n.jsx("li",{children:"4. MRP检查库存，生成待排产订单"}),n.jsx("li",{children:"5. 待排产订单自动传输到自动排单模块"}),n.jsx("li",{children:"6. 执行智能排单算法，生成排产计划"})]}),(0,n.jsxs)("p",{children:[n.jsx("strong",{children:"预期结果："}),"整个流程应该无缝衔接，数据完整传输，无错误发生。"]})]}),showIcon:!0})]})})};function Z(){return n.jsx(l.Z,{children:n.jsx(k,{})})}},33686:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let n=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\admin\integration-test\page.tsx`),{__esModule:a,$$typeof:i}=n,l=n.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1638,1880,8811,3984,8699,7049,7383,934,6274,996],()=>r(5889));module.exports=n})();