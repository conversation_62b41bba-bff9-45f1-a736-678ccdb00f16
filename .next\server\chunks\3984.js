"use strict";exports.id=3984,exports.ids=[3984],exports.modules={58535:(e,t,n)=>{n.d(t,{Z:()=>l});var a=n(65651),o=n(3729);let r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"};var i=n(49809);let l=o.forwardRef(function(e,t){return o.createElement(i.Z,(0,a.Z)({},e,{ref:t,icon:r}))})},83984:(e,t,n)=>{n.d(t,{Z:()=>P});var a=n(3729),o=n(34132),r=n.n(o),i=n(24773),l=n(84893),c=n(54527),d=n(56989),s=n(53869),u=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let b=e=>{var{prefixCls:t,className:n,hoverable:o=!0}=e,i=u(e,["prefixCls","className","hoverable"]);let{getPrefixCls:c}=a.useContext(l.E_),d=c("card",t),s=r()(`${d}-grid`,n,{[`${d}-grid-hoverable`]:o});return a.createElement("div",Object.assign({},i,{className:s}))};var f=n(92959),v=n(22989),p=n(13165),m=n(96373);let g=e=>{let{antCls:t,componentCls:n,headerHeight:a,headerPadding:o,tabsMarginBottom:r}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:a,marginBottom:-1,padding:`0 ${(0,f.bf)(o)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${(0,f.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${(0,f.bf)(e.borderRadiusLG)} ${(0,f.bf)(e.borderRadiusLG)} 0 0`},(0,v.dF)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},v.vS),{[`
          > ${n}-typography,
          > ${n}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:r,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${(0,f.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},h=e=>{let{cardPaddingBase:t,colorBorderSecondary:n,cardShadow:a,lineWidth:o}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`
      ${(0,f.bf)(o)} 0 0 0 ${n},
      0 ${(0,f.bf)(o)} 0 0 ${n},
      ${(0,f.bf)(o)} ${(0,f.bf)(o)} 0 0 ${n},
      ${(0,f.bf)(o)} 0 0 0 ${n} inset,
      0 ${(0,f.bf)(o)} 0 0 ${n} inset;
    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:a}}},$=e=>{let{componentCls:t,iconCls:n,actionsLiMargin:a,cardActionsIconSize:o,colorBorderSecondary:r,actionsBg:i}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:i,borderTop:`${(0,f.bf)(e.lineWidth)} ${e.lineType} ${r}`,display:"flex",borderRadius:`0 0 ${(0,f.bf)(e.borderRadiusLG)} ${(0,f.bf)(e.borderRadiusLG)}`},(0,v.dF)()),{"& > li":{margin:a,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${n}`]:{display:"inline-block",width:"100%",color:e.colorIcon,lineHeight:(0,f.bf)(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${n}`]:{fontSize:o,lineHeight:(0,f.bf)(e.calc(o).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${(0,f.bf)(e.lineWidth)} ${e.lineType} ${r}`}}})},y=e=>Object.assign(Object.assign({margin:`${(0,f.bf)(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},(0,v.dF)()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},v.vS),"&-description":{color:e.colorTextDescription}}),k=e=>{let{componentCls:t,colorFillAlter:n,headerPadding:a,bodyPadding:o}=e;return{[`${t}-head`]:{padding:`0 ${(0,f.bf)(a)}`,background:n,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${(0,f.bf)(e.padding)} ${(0,f.bf)(o)}`}}},x=e=>{let{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},S=e=>{let{componentCls:t,cardShadow:n,cardHeadPadding:a,colorBorderSecondary:o,boxShadowTertiary:r,bodyPadding:i,extraColor:l}=e;return{[t]:Object.assign(Object.assign({},(0,v.Wf)(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:r},[`${t}-head`]:g(e),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:Object.assign({padding:i,borderRadius:`0 0 ${(0,f.bf)(e.borderRadiusLG)} ${(0,f.bf)(e.borderRadiusLG)}`},(0,v.dF)()),[`${t}-grid`]:h(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${(0,f.bf)(e.borderRadiusLG)} ${(0,f.bf)(e.borderRadiusLG)} 0 0`}},[`${t}-actions`]:$(e),[`${t}-meta`]:y(e)}),[`${t}-bordered`]:{border:`${(0,f.bf)(e.lineWidth)} ${e.lineType} ${o}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:n}},[`${t}-contain-grid`]:{borderRadius:`${(0,f.bf)(e.borderRadiusLG)} ${(0,f.bf)(e.borderRadiusLG)} 0 0 `,[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${t}-contain-tabs`]:{[`> div${t}-head`]:{minHeight:0,[`${t}-head-title, ${t}-extra`]:{paddingTop:a}}},[`${t}-type-inner`]:k(e),[`${t}-loading`]:x(e),[`${t}-rtl`]:{direction:"rtl"}}},w=e=>{let{componentCls:t,bodyPaddingSM:n,headerPaddingSM:a,headerHeightSM:o,headerFontSizeSM:r}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:o,padding:`0 ${(0,f.bf)(a)}`,fontSize:r,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:n}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},E=(0,p.I$)("Card",e=>{let t=(0,m.IX)(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize});return[S(t),w(t)]},e=>{var t,n;return{headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+2*e.padding,headerHeightSM:e.fontSize*e.lineHeight+2*e.paddingXS,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:null!==(t=e.bodyPadding)&&void 0!==t?t:e.paddingLG,headerPadding:null!==(n=e.headerPadding)&&void 0!==n?n:e.paddingLG}});var _=n(69109),C=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let O=e=>{let{actionClasses:t,actions:n=[],actionStyle:o}=e;return a.createElement("ul",{className:t,style:o},n.map((e,t)=>{let o=`action-${t}`;return a.createElement("li",{style:{width:`${100/n.length}%`},key:o},a.createElement("span",null,e))}))},Z=a.forwardRef((e,t)=>{let n;let{prefixCls:o,className:u,rootClassName:f,style:v,extra:p,headStyle:m={},bodyStyle:g={},title:h,loading:$,bordered:y,variant:k,size:x,type:S,cover:w,actions:Z,tabList:R,children:P,activeTabKey:T,defaultActiveTabKey:I,tabBarExtraContent:j,hoverable:L,tabProps:z={},classNames:M,styles:N}=e,B=C(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:D,direction:G,card:H}=a.useContext(l.E_),[W]=(0,_.Z)("card",k,y),A=e=>{var t;return r()(null===(t=null==H?void 0:H.classNames)||void 0===t?void 0:t[e],null==M?void 0:M[e])},X=e=>{var t;return Object.assign(Object.assign({},null===(t=null==H?void 0:H.styles)||void 0===t?void 0:t[e]),null==N?void 0:N[e])},q=a.useMemo(()=>{let e=!1;return a.Children.forEach(P,t=>{(null==t?void 0:t.type)===b&&(e=!0)}),e},[P]),K=D("card",o),[F,V,Y]=E(K),U=a.createElement(d.Z,{loading:!0,active:!0,paragraph:{rows:4},title:!1},P),Q=void 0!==T,J=Object.assign(Object.assign({},z),{[Q?"activeKey":"defaultActiveKey"]:Q?T:I,tabBarExtraContent:j}),ee=(0,c.Z)(x),et=ee&&"default"!==ee?ee:"large",en=R?a.createElement(s.default,Object.assign({size:et},J,{className:`${K}-head-tabs`,onChange:t=>{var n;null===(n=e.onTabChange)||void 0===n||n.call(e,t)},items:R.map(e=>{var{tab:t}=e;return Object.assign({label:t},C(e,["tab"]))})})):null;if(h||p||en){let e=r()(`${K}-head`,A("header")),t=r()(`${K}-head-title`,A("title")),o=r()(`${K}-extra`,A("extra")),i=Object.assign(Object.assign({},m),X("header"));n=a.createElement("div",{className:e,style:i},a.createElement("div",{className:`${K}-head-wrapper`},h&&a.createElement("div",{className:t,style:X("title")},h),p&&a.createElement("div",{className:o,style:X("extra")},p)),en)}let ea=r()(`${K}-cover`,A("cover")),eo=w?a.createElement("div",{className:ea,style:X("cover")},w):null,er=r()(`${K}-body`,A("body")),ei=Object.assign(Object.assign({},g),X("body")),el=a.createElement("div",{className:er,style:ei},$?U:P),ec=r()(`${K}-actions`,A("actions")),ed=(null==Z?void 0:Z.length)?a.createElement(O,{actionClasses:ec,actionStyle:X("actions"),actions:Z}):null,es=(0,i.Z)(B,["onTabChange"]),eu=r()(K,null==H?void 0:H.className,{[`${K}-loading`]:$,[`${K}-bordered`]:"borderless"!==W,[`${K}-hoverable`]:L,[`${K}-contain-grid`]:q,[`${K}-contain-tabs`]:null==R?void 0:R.length,[`${K}-${ee}`]:ee,[`${K}-type-${S}`]:!!S,[`${K}-rtl`]:"rtl"===G},u,f,V,Y),eb=Object.assign(Object.assign({},null==H?void 0:H.style),v);return F(a.createElement("div",Object.assign({ref:t},es,{className:eu,style:eb}),n,eo,el,ed))});var R=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};Z.Grid=b,Z.Meta=e=>{let{prefixCls:t,className:n,avatar:o,title:i,description:c}=e,d=R(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:s}=a.useContext(l.E_),u=s("card",t),b=r()(`${u}-meta`,n),f=o?a.createElement("div",{className:`${u}-meta-avatar`},o):null,v=i?a.createElement("div",{className:`${u}-meta-title`},i):null,p=c?a.createElement("div",{className:`${u}-meta-description`},c):null,m=v||p?a.createElement("div",{className:`${u}-meta-detail`},v,p):null;return a.createElement("div",Object.assign({},d,{className:b}),f,m)};let P=Z},69109:(e,t,n)=>{n.d(t,{Z:()=>i});var a=n(3729),o=n(30308),r=n(84893);let i=(e,t,n)=>{var i,l;let c;let{variant:d,[e]:s}=a.useContext(r.E_),u=a.useContext(o.pg),b=null==s?void 0:s.variant;c=void 0!==t?t:!1===n?"borderless":null!==(l=null!==(i=null!=u?u:b)&&void 0!==i?i:d)&&void 0!==l?l:"outlined";let f=r.tr.includes(c);return[c,f]}},53869:(e,t,n)=>{n.d(t,{default:()=>ew});var a=n(3729),o=n.n(a),r=n(32066),i=n(71998),l=n(58535),c=n(34132),d=n.n(c),s=n(65651),u=n(22363),b=n(65830),f=n(93727),v=n(82841),p=n(12403),m=n(80595),g=n(12472);let h=(0,a.createContext)(null);var $=n(72375),y=n(70242),k=n(67827),x=n(67862),S=n(42534);let w=function(e){var t=e.activeTabOffset,n=e.horizontal,r=e.rtl,i=e.indicator,l=void 0===i?{}:i,c=l.size,d=l.align,s=void 0===d?"center":d,u=(0,a.useState)(),b=(0,f.Z)(u,2),v=b[0],p=b[1],m=(0,a.useRef)(),g=o().useCallback(function(e){return"function"==typeof c?c(e):"number"==typeof c?c:e},[c]);function h(){S.Z.cancel(m.current)}return(0,a.useEffect)(function(){var e={};if(t){if(n){e.width=g(t.width);var a=r?"right":"left";"start"===s&&(e[a]=t[a]),"center"===s&&(e[a]=t[a]+t.width/2,e.transform=r?"translateX(50%)":"translateX(-50%)"),"end"===s&&(e[a]=t[a]+t.width,e.transform="translateX(-100%)")}else e.height=g(t.height),"start"===s&&(e.top=t.top),"center"===s&&(e.top=t.top+t.height/2,e.transform="translateY(-50%)"),"end"===s&&(e.top=t.top+t.height,e.transform="translateY(-100%)")}return h(),m.current=(0,S.Z)(function(){v&&e&&Object.keys(e).every(function(t){var n=e[t],a=v[t];return"number"==typeof n&&"number"==typeof a?Math.round(n)===Math.round(a):n===a})||p(e)}),h},[JSON.stringify(t),n,r,s,g]),{style:v}};var E={width:0,height:0,left:0,top:0};function _(e,t){var n=a.useRef(e),o=a.useState({}),r=(0,f.Z)(o,2)[1];return[n.current,function(e){var a="function"==typeof e?e(n.current):e;a!==n.current&&t(a,n.current),n.current=a,r({})}]}var C=n(17981);function O(e){var t=(0,a.useState)(0),n=(0,f.Z)(t,2),o=n[0],r=n[1],i=(0,a.useRef)(0),l=(0,a.useRef)();return l.current=e,(0,C.o)(function(){var e;null===(e=l.current)||void 0===e||e.call(l)},[o]),function(){i.current===o&&(i.current+=1,r(i.current))}}var Z={width:0,height:0,left:0,top:0,right:0};function R(e){var t;return e instanceof Map?(t={},e.forEach(function(e,n){t[n]=e})):t=e,JSON.stringify(t)}function P(e){return String(e).replace(/"/g,"TABS_DQ")}function T(e,t,n,a){return!!n&&!a&&!1!==e&&(void 0!==e||!1!==t&&null!==t)}var I=a.forwardRef(function(e,t){var n=e.prefixCls,o=e.editable,r=e.locale,i=e.style;return o&&!1!==o.showAdd?a.createElement("button",{ref:t,type:"button",className:"".concat(n,"-nav-add"),style:i,"aria-label":(null==r?void 0:r.addAriaLabel)||"Add tab",onClick:function(e){o.onEdit("add",{event:e})}},o.addIcon||"+"):null}),j=a.forwardRef(function(e,t){var n,o=e.position,r=e.prefixCls,i=e.extra;if(!i)return null;var l={};return"object"!==(0,v.Z)(i)||a.isValidElement(i)?l.right=i:l=i,"right"===o&&(n=l.right),"left"===o&&(n=l.left),n?a.createElement("div",{className:"".concat(r,"-extra-content"),ref:t},n):null}),L=n(42952),z=n(85826),M=n(21029),N=a.forwardRef(function(e,t){var n=e.prefixCls,o=e.id,r=e.tabs,i=e.locale,l=e.mobile,c=e.more,b=void 0===c?{}:c,v=e.style,p=e.className,m=e.editable,g=e.tabBarGutter,h=e.rtl,$=e.removeAriaLabel,y=e.onTabClick,k=e.getPopupContainer,x=e.popupClassName,S=(0,a.useState)(!1),w=(0,f.Z)(S,2),E=w[0],_=w[1],C=(0,a.useState)(null),O=(0,f.Z)(C,2),Z=O[0],R=O[1],P=b.icon,j="".concat(o,"-more-popup"),N="".concat(n,"-dropdown"),B=null!==Z?"".concat(j,"-").concat(Z):null,D=null==i?void 0:i.dropdownAriaLabel,G=a.createElement(z.ZP,{onClick:function(e){y(e.key,e.domEvent),_(!1)},prefixCls:"".concat(N,"-menu"),id:j,tabIndex:-1,role:"listbox","aria-activedescendant":B,selectedKeys:[Z],"aria-label":void 0!==D?D:"expanded dropdown"},r.map(function(e){var t=e.closable,n=e.disabled,r=e.closeIcon,i=e.key,l=e.label,c=T(t,r,m,n);return a.createElement(z.sN,{key:i,id:"".concat(j,"-").concat(i),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(i),disabled:n},a.createElement("span",null,l),c&&a.createElement("button",{type:"button","aria-label":$||"remove",tabIndex:0,className:"".concat(N,"-menu-item-remove"),onClick:function(e){e.stopPropagation(),e.preventDefault(),e.stopPropagation(),m.onEdit("remove",{key:i,event:e})}},r||m.removeIcon||"\xd7"))}));function H(e){for(var t=r.filter(function(e){return!e.disabled}),n=t.findIndex(function(e){return e.key===Z})||0,a=t.length,o=0;o<a;o+=1){var i=t[n=(n+e+a)%a];if(!i.disabled){R(i.key);return}}}(0,a.useEffect)(function(){var e=document.getElementById(B);e&&e.scrollIntoView&&e.scrollIntoView(!1)},[Z]),(0,a.useEffect)(function(){E||R(null)},[E]);var W=(0,u.Z)({},h?"marginRight":"marginLeft",g);r.length||(W.visibility="hidden",W.order=1);var A=d()((0,u.Z)({},"".concat(N,"-rtl"),h)),X=l?null:a.createElement(L.Z,(0,s.Z)({prefixCls:N,overlay:G,visible:!!r.length&&E,onVisibleChange:_,overlayClassName:d()(A,x),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:k},b),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:W,"aria-haspopup":"listbox","aria-controls":j,id:"".concat(o,"-more"),"aria-expanded":E,onKeyDown:function(e){var t=e.which;if(!E){[M.Z.DOWN,M.Z.SPACE,M.Z.ENTER].includes(t)&&(_(!0),e.preventDefault());return}switch(t){case M.Z.UP:H(-1),e.preventDefault();break;case M.Z.DOWN:H(1),e.preventDefault();break;case M.Z.ESC:_(!1);break;case M.Z.SPACE:case M.Z.ENTER:null!==Z&&y(Z,e)}}},void 0===P?"More":P));return a.createElement("div",{className:d()("".concat(n,"-nav-operations"),p),style:v,ref:t},X,a.createElement(I,{prefixCls:n,locale:i,editable:m}))});let B=a.memo(N,function(e,t){return t.tabMoving}),D=function(e){var t=e.prefixCls,n=e.id,o=e.active,r=e.focus,i=e.tab,l=i.key,c=i.label,s=i.disabled,b=i.closeIcon,f=i.icon,v=e.closable,p=e.renderWrapper,m=e.removeAriaLabel,g=e.editable,h=e.onClick,$=e.onFocus,y=e.onBlur,k=e.onKeyDown,x=e.onMouseDown,S=e.onMouseUp,w=e.style,E=e.tabCount,_=e.currentPosition,C="".concat(t,"-tab"),O=T(v,b,g,s);function Z(e){s||h(e)}var R=a.useMemo(function(){return f&&"string"==typeof c?a.createElement("span",null,c):c},[c,f]),I=a.useRef(null);a.useEffect(function(){r&&I.current&&I.current.focus()},[r]);var j=a.createElement("div",{key:l,"data-node-key":P(l),className:d()(C,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(C,"-with-remove"),O),"".concat(C,"-active"),o),"".concat(C,"-disabled"),s),"".concat(C,"-focus"),r)),style:w,onClick:Z},a.createElement("div",{ref:I,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(l),className:"".concat(C,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(l),"aria-disabled":s,tabIndex:s?null:o?0:-1,onClick:function(e){e.stopPropagation(),Z(e)},onKeyDown:k,onMouseDown:x,onMouseUp:S,onFocus:$,onBlur:y},r&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(_," of ").concat(E)),f&&a.createElement("span",{className:"".concat(C,"-icon")},f),c&&R),O&&a.createElement("button",{type:"button",role:"tab","aria-label":m||"remove",tabIndex:o?0:-1,className:"".concat(C,"-remove"),onClick:function(e){e.stopPropagation(),e.preventDefault(),e.stopPropagation(),g.onEdit("remove",{key:l,event:e})}},b||g.removeIcon||"\xd7"));return p?p(j):j};var G=function(e,t){var n=e.offsetWidth,a=e.offsetHeight,o=e.offsetTop,r=e.offsetLeft,i=e.getBoundingClientRect(),l=i.width,c=i.height,d=i.left,s=i.top;return 1>Math.abs(l-n)?[l,c,d-t.left,s-t.top]:[n,a,r,o]},H=function(e){var t=e.current||{},n=t.offsetWidth,a=void 0===n?0:n,o=t.offsetHeight;if(e.current){var r=e.current.getBoundingClientRect(),i=r.width,l=r.height;if(1>Math.abs(i-a))return[i,l]}return[a,void 0===o?0:o]},W=function(e,t){return e[t?0:1]},A=a.forwardRef(function(e,t){var n,o,r,i,l,c,v,p,m,g,S,C,L,z,M,N,A,X,q,K,F,V,Y,U,Q,J,ee,et,en,ea,eo,er,ei,el,ec,ed,es,eu,eb,ef=e.className,ev=e.style,ep=e.id,em=e.animated,eg=e.activeKey,eh=e.rtl,e$=e.extra,ey=e.editable,ek=e.locale,ex=e.tabPosition,eS=e.tabBarGutter,ew=e.children,eE=e.onTabClick,e_=e.onTabScroll,eC=e.indicator,eO=a.useContext(h),eZ=eO.prefixCls,eR=eO.tabs,eP=(0,a.useRef)(null),eT=(0,a.useRef)(null),eI=(0,a.useRef)(null),ej=(0,a.useRef)(null),eL=(0,a.useRef)(null),ez=(0,a.useRef)(null),eM=(0,a.useRef)(null),eN="top"===ex||"bottom"===ex,eB=_(0,function(e,t){eN&&e_&&e_({direction:e>t?"left":"right"})}),eD=(0,f.Z)(eB,2),eG=eD[0],eH=eD[1],eW=_(0,function(e,t){!eN&&e_&&e_({direction:e>t?"top":"bottom"})}),eA=(0,f.Z)(eW,2),eX=eA[0],eq=eA[1],eK=(0,a.useState)([0,0]),eF=(0,f.Z)(eK,2),eV=eF[0],eY=eF[1],eU=(0,a.useState)([0,0]),eQ=(0,f.Z)(eU,2),eJ=eQ[0],e0=eQ[1],e1=(0,a.useState)([0,0]),e2=(0,f.Z)(e1,2),e8=e2[0],e9=e2[1],e3=(0,a.useState)([0,0]),e5=(0,f.Z)(e3,2),e6=e5[0],e4=e5[1],e7=(n=new Map,o=(0,a.useRef)([]),r=(0,a.useState)({}),i=(0,f.Z)(r,2)[1],l=(0,a.useRef)("function"==typeof n?n():n),c=O(function(){var e=l.current;o.current.forEach(function(t){e=t(e)}),o.current=[],l.current=e,i({})}),[l.current,function(e){o.current.push(e),c()}]),te=(0,f.Z)(e7,2),tt=te[0],tn=te[1],ta=(v=eJ[0],(0,a.useMemo)(function(){for(var e=new Map,t=tt.get(null===(o=eR[0])||void 0===o?void 0:o.key)||E,n=t.left+t.width,a=0;a<eR.length;a+=1){var o,r,i=eR[a].key,l=tt.get(i);l||(l=tt.get(null===(r=eR[a-1])||void 0===r?void 0:r.key)||E);var c=e.get(i)||(0,b.Z)({},l);c.right=n-c.left-c.width,e.set(i,c)}return e},[eR.map(function(e){return e.key}).join("_"),tt,v])),to=W(eV,eN),tr=W(eJ,eN),ti=W(e8,eN),tl=W(e6,eN),tc=Math.floor(to)<Math.floor(tr+ti),td=tc?to-tl:to-ti,ts="".concat(eZ,"-nav-operations-hidden"),tu=0,tb=0;function tf(e){return e<tu?tu:e>tb?tb:e}eN&&eh?(tu=0,tb=Math.max(0,tr-td)):(tu=Math.min(0,td-tr),tb=0);var tv=(0,a.useRef)(null),tp=(0,a.useState)(),tm=(0,f.Z)(tp,2),tg=tm[0],th=tm[1];function t$(){th(Date.now())}function ty(){tv.current&&clearTimeout(tv.current)}p=function(e,t){function n(e,t){e(function(e){return tf(e+t)})}return!!tc&&(eN?n(eH,e):n(eq,t),ty(),t$(),!0)},m=(0,a.useState)(),S=(g=(0,f.Z)(m,2))[0],C=g[1],L=(0,a.useState)(0),M=(z=(0,f.Z)(L,2))[0],N=z[1],A=(0,a.useState)(0),q=(X=(0,f.Z)(A,2))[0],K=X[1],F=(0,a.useState)(),Y=(V=(0,f.Z)(F,2))[0],U=V[1],Q=(0,a.useRef)(),J=(0,a.useRef)(),(ee=(0,a.useRef)(null)).current={onTouchStart:function(e){var t=e.touches[0];C({x:t.screenX,y:t.screenY}),window.clearInterval(Q.current)},onTouchMove:function(e){if(S){var t=e.touches[0],n=t.screenX,a=t.screenY;C({x:n,y:a});var o=n-S.x,r=a-S.y;p(o,r);var i=Date.now();N(i),K(i-M),U({x:o,y:r})}},onTouchEnd:function(){if(S&&(C(null),U(null),Y)){var e=Y.x/q,t=Y.y/q;if(!(.1>Math.max(Math.abs(e),Math.abs(t)))){var n=e,a=t;Q.current=window.setInterval(function(){if(.01>Math.abs(n)&&.01>Math.abs(a)){window.clearInterval(Q.current);return}n*=.9046104802746175,a*=.9046104802746175,p(20*n,20*a)},20)}}},onWheel:function(e){var t=e.deltaX,n=e.deltaY,a=0,o=Math.abs(t),r=Math.abs(n);o===r?a="x"===J.current?t:n:o>r?(a=t,J.current="x"):(a=n,J.current="y"),p(-a,-a)&&e.preventDefault()}},a.useEffect(function(){function e(e){ee.current.onTouchMove(e)}function t(e){ee.current.onTouchEnd(e)}return document.addEventListener("touchmove",e,{passive:!1}),document.addEventListener("touchend",t,{passive:!0}),ej.current.addEventListener("touchstart",function(e){ee.current.onTouchStart(e)},{passive:!0}),ej.current.addEventListener("wheel",function(e){ee.current.onWheel(e)},{passive:!1}),function(){document.removeEventListener("touchmove",e),document.removeEventListener("touchend",t)}},[]),(0,a.useEffect)(function(){return ty(),tg&&(tv.current=setTimeout(function(){th(0)},100)),ty},[tg]);var tk=(et=eN?eG:eX,ei=(en=(0,b.Z)((0,b.Z)({},e),{},{tabs:eR})).tabs,el=en.tabPosition,ec=en.rtl,["top","bottom"].includes(el)?(ea="width",eo=ec?"right":"left",er=Math.abs(et)):(ea="height",eo="top",er=-et),(0,a.useMemo)(function(){if(!ei.length)return[0,0];for(var e=ei.length,t=e,n=0;n<e;n+=1){var a=ta.get(ei[n].key)||Z;if(Math.floor(a[eo]+a[ea])>Math.floor(er+td)){t=n-1;break}}for(var o=0,r=e-1;r>=0;r-=1)if((ta.get(ei[r].key)||Z)[eo]<er){o=r+1;break}return o>=t?[0,0]:[o,t]},[ta,td,tr,ti,tl,er,el,ei.map(function(e){return e.key}).join("_"),ec])),tx=(0,f.Z)(tk,2),tS=tx[0],tw=tx[1],tE=(0,k.Z)(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:eg,t=ta.get(e)||{width:0,height:0,left:0,right:0,top:0};if(eN){var n=eG;eh?t.right<eG?n=t.right:t.right+t.width>eG+td&&(n=t.right+t.width-td):t.left<-eG?n=-t.left:t.left+t.width>-eG+td&&(n=-(t.left+t.width-td)),eq(0),eH(tf(n))}else{var a=eX;t.top<-eX?a=-t.top:t.top+t.height>-eX+td&&(a=-(t.top+t.height-td)),eH(0),eq(tf(a))}}),t_=(0,a.useState)(),tC=(0,f.Z)(t_,2),tO=tC[0],tZ=tC[1],tR=(0,a.useState)(!1),tP=(0,f.Z)(tR,2),tT=tP[0],tI=tP[1],tj=eR.filter(function(e){return!e.disabled}).map(function(e){return e.key}),tL=function(e){var t=tj.indexOf(tO||eg),n=tj.length;tZ(tj[(t+e+n)%n])},tz=function(e){var t=e.code,n=eh&&eN,a=tj[0],o=tj[tj.length-1];switch(t){case"ArrowLeft":eN&&tL(n?1:-1);break;case"ArrowRight":eN&&tL(n?-1:1);break;case"ArrowUp":e.preventDefault(),eN||tL(-1);break;case"ArrowDown":e.preventDefault(),eN||tL(1);break;case"Home":e.preventDefault(),tZ(a);break;case"End":e.preventDefault(),tZ(o);break;case"Enter":case"Space":e.preventDefault(),eE(null!=tO?tO:eg,e);break;case"Backspace":case"Delete":var r=tj.indexOf(tO),i=eR.find(function(e){return e.key===tO});T(null==i?void 0:i.closable,null==i?void 0:i.closeIcon,ey,null==i?void 0:i.disabled)&&(e.preventDefault(),e.stopPropagation(),ey.onEdit("remove",{key:tO,event:e}),r===tj.length-1?tL(-1):tL(1))}},tM={};eN?tM[eh?"marginRight":"marginLeft"]=eS:tM.marginTop=eS;var tN=eR.map(function(e,t){var n=e.key;return a.createElement(D,{id:ep,prefixCls:eZ,key:n,tab:e,style:0===t?void 0:tM,closable:e.closable,editable:ey,active:n===eg,focus:n===tO,renderWrapper:ew,removeAriaLabel:null==ek?void 0:ek.removeAriaLabel,tabCount:tj.length,currentPosition:t+1,onClick:function(e){eE(n,e)},onKeyDown:tz,onFocus:function(){tT||tZ(n),tE(n),t$(),ej.current&&(eh||(ej.current.scrollLeft=0),ej.current.scrollTop=0)},onBlur:function(){tZ(void 0)},onMouseDown:function(){tI(!0)},onMouseUp:function(){tI(!1)}})}),tB=function(){return tn(function(){var e,t=new Map,n=null===(e=eL.current)||void 0===e?void 0:e.getBoundingClientRect();return eR.forEach(function(e){var a,o=e.key,r=null===(a=eL.current)||void 0===a?void 0:a.querySelector('[data-node-key="'.concat(P(o),'"]'));if(r){var i=G(r,n),l=(0,f.Z)(i,4),c=l[0],d=l[1],s=l[2],u=l[3];t.set(o,{width:c,height:d,left:s,top:u})}}),t})};(0,a.useEffect)(function(){tB()},[eR.map(function(e){return e.key}).join("_")]);var tD=O(function(){var e=H(eP),t=H(eT),n=H(eI);eY([e[0]-t[0]-n[0],e[1]-t[1]-n[1]]);var a=H(eM);e9(a),e4(H(ez));var o=H(eL);e0([o[0]-a[0],o[1]-a[1]]),tB()}),tG=eR.slice(0,tS),tH=eR.slice(tw+1),tW=[].concat((0,$.Z)(tG),(0,$.Z)(tH)),tA=ta.get(eg),tX=w({activeTabOffset:tA,horizontal:eN,indicator:eC,rtl:eh}).style;(0,a.useEffect)(function(){tE()},[eg,tu,tb,R(tA),R(ta),eN]),(0,a.useEffect)(function(){tD()},[eh]);var tq=!!tW.length,tK="".concat(eZ,"-nav-wrap");return eN?eh?(es=eG>0,ed=eG!==tb):(ed=eG<0,es=eG!==tu):(eu=eX<0,eb=eX!==tu),a.createElement(y.Z,{onResize:tD},a.createElement("div",{ref:(0,x.x1)(t,eP),role:"tablist","aria-orientation":eN?"horizontal":"vertical",className:d()("".concat(eZ,"-nav"),ef),style:ev,onKeyDown:function(){t$()}},a.createElement(j,{ref:eT,position:"left",extra:e$,prefixCls:eZ}),a.createElement(y.Z,{onResize:tD},a.createElement("div",{className:d()(tK,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(tK,"-ping-left"),ed),"".concat(tK,"-ping-right"),es),"".concat(tK,"-ping-top"),eu),"".concat(tK,"-ping-bottom"),eb)),ref:ej},a.createElement(y.Z,{onResize:tD},a.createElement("div",{ref:eL,className:"".concat(eZ,"-nav-list"),style:{transform:"translate(".concat(eG,"px, ").concat(eX,"px)"),transition:tg?"none":void 0}},tN,a.createElement(I,{ref:eM,prefixCls:eZ,locale:ek,editable:ey,style:(0,b.Z)((0,b.Z)({},0===tN.length?void 0:tM),{},{visibility:tq?"hidden":null})}),a.createElement("div",{className:d()("".concat(eZ,"-ink-bar"),(0,u.Z)({},"".concat(eZ,"-ink-bar-animated"),em.inkBar)),style:tX}))))),a.createElement(B,(0,s.Z)({},e,{removeAriaLabel:null==ek?void 0:ek.removeAriaLabel,ref:ez,prefixCls:eZ,tabs:tW,className:!tq&&ts,tabMoving:!!tg})),a.createElement(j,{ref:eI,position:"right",extra:e$,prefixCls:eZ})))}),X=a.forwardRef(function(e,t){var n=e.prefixCls,o=e.className,r=e.style,i=e.id,l=e.active,c=e.tabKey,s=e.children;return a.createElement("div",{id:i&&"".concat(i,"-panel-").concat(c),role:"tabpanel",tabIndex:l?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(c),"aria-hidden":!l,style:r,className:d()(n,l&&"".concat(n,"-active"),o),ref:t},s)}),q=["renderTabBar"],K=["label","key"];let F=function(e){var t=e.renderTabBar,n=(0,p.Z)(e,q),o=a.useContext(h).tabs;return t?t((0,b.Z)((0,b.Z)({},n),{},{panes:o.map(function(e){var t=e.label,n=e.key,o=(0,p.Z)(e,K);return a.createElement(X,(0,s.Z)({tab:t,key:n,tabKey:n},o))})}),A):a.createElement(A,n)};var V=n(27335),Y=["key","forceRender","style","className","destroyInactiveTabPane"];let U=function(e){var t=e.id,n=e.activeKey,o=e.animated,r=e.tabPosition,i=e.destroyInactiveTabPane,l=a.useContext(h),c=l.prefixCls,f=l.tabs,v=o.tabPane,m="".concat(c,"-tabpane");return a.createElement("div",{className:d()("".concat(c,"-content-holder"))},a.createElement("div",{className:d()("".concat(c,"-content"),"".concat(c,"-content-").concat(r),(0,u.Z)({},"".concat(c,"-content-animated"),v))},f.map(function(e){var r=e.key,l=e.forceRender,c=e.style,u=e.className,f=e.destroyInactiveTabPane,g=(0,p.Z)(e,Y),h=r===n;return a.createElement(V.ZP,(0,s.Z)({key:r,visible:h,forceRender:l,removeOnLeave:!!(i||f),leavedClassName:"".concat(m,"-hidden")},o.tabPaneMotion),function(e,n){var o=e.style,i=e.className;return a.createElement(X,(0,s.Z)({},g,{prefixCls:m,id:t,tabKey:r,animated:v,active:h,style:(0,b.Z)((0,b.Z)({},c),o),className:d()(u,i),ref:n}))})})))};n(41255);var Q=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],J=0,ee=a.forwardRef(function(e,t){var n=e.id,o=e.prefixCls,r=void 0===o?"rc-tabs":o,i=e.className,l=e.items,c=e.direction,$=e.activeKey,y=e.defaultActiveKey,k=e.editable,x=e.animated,S=e.tabPosition,w=void 0===S?"top":S,E=e.tabBarGutter,_=e.tabBarStyle,C=e.tabBarExtraContent,O=e.locale,Z=e.more,R=e.destroyInactiveTabPane,P=e.renderTabBar,T=e.onChange,I=e.onTabClick,j=e.onTabScroll,L=e.getPopupContainer,z=e.popupClassName,M=e.indicator,N=(0,p.Z)(e,Q),B=a.useMemo(function(){return(l||[]).filter(function(e){return e&&"object"===(0,v.Z)(e)&&"key"in e})},[l]),D="rtl"===c,G=function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(e=!1===t?{inkBar:!1,tabPane:!1}:!0===t?{inkBar:!0,tabPane:!1}:(0,b.Z)({inkBar:!0},"object"===(0,v.Z)(t)?t:{})).tabPaneMotion&&void 0===e.tabPane&&(e.tabPane=!0),!e.tabPaneMotion&&e.tabPane&&(e.tabPane=!1),e}(x),H=(0,a.useState)(!1),W=(0,f.Z)(H,2),A=W[0],X=W[1];(0,a.useEffect)(function(){X((0,g.Z)())},[]);var q=(0,m.Z)(function(){var e;return null===(e=B[0])||void 0===e?void 0:e.key},{value:$,defaultValue:y}),K=(0,f.Z)(q,2),V=K[0],Y=K[1],ee=(0,a.useState)(function(){return B.findIndex(function(e){return e.key===V})}),et=(0,f.Z)(ee,2),en=et[0],ea=et[1];(0,a.useEffect)(function(){var e,t=B.findIndex(function(e){return e.key===V});-1===t&&(t=Math.max(0,Math.min(en,B.length-1)),Y(null===(e=B[t])||void 0===e?void 0:e.key)),ea(t)},[B.map(function(e){return e.key}).join("_"),V,en]);var eo=(0,m.Z)(null,{value:n}),er=(0,f.Z)(eo,2),ei=er[0],el=er[1];(0,a.useEffect)(function(){n||(el("rc-tabs-".concat(J)),J+=1)},[]);var ec={id:ei,activeKey:V,animated:G,tabPosition:w,rtl:D,mobile:A},ed=(0,b.Z)((0,b.Z)({},ec),{},{editable:k,locale:O,more:Z,tabBarGutter:E,onTabClick:function(e,t){null==I||I(e,t);var n=e!==V;Y(e),n&&(null==T||T(e))},onTabScroll:j,extra:C,style:_,panes:null,getPopupContainer:L,popupClassName:z,indicator:M});return a.createElement(h.Provider,{value:{tabs:B,prefixCls:r}},a.createElement("div",(0,s.Z)({ref:t,id:n,className:d()(r,"".concat(r,"-").concat(w),(0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(r,"-mobile"),A),"".concat(r,"-editable"),k),"".concat(r,"-rtl"),D),i)},N),a.createElement(F,(0,s.Z)({},ed,{renderTabBar:P})),a.createElement(U,(0,s.Z)({destroyInactiveTabPane:R},ec,{animated:G}))))}),et=n(84893),en=n(13878),ea=n(54527),eo=n(95295);let er={motionAppear:!1,motionEnter:!0,motionLeave:!0};var ei=n(89299),el=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n},ec=n(92959),ed=n(22989),es=n(13165),eu=n(96373),eb=n(19532);let ef=e=>{let{componentCls:t,motionDurationSlow:n}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${n}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${n}`}}}}},[(0,eb.oN)(e,"slide-up"),(0,eb.oN)(e,"slide-down")]]},ev=e=>{let{componentCls:t,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:r,itemSelectedColor:i}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:n,background:a,border:`${(0,ec.bf)(e.lineWidth)} ${e.lineType} ${r}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:i,background:e.colorBgContainer},[`${t}-tab-focus:has(${t}-tab-btn:focus-visible)`]:(0,ed.oN)(e,-3),[`& ${t}-tab${t}-tab-focus ${t}-tab-btn:focus-visible`]:{outline:"none"},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:(0,ec.bf)(o)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${(0,ec.bf)(e.borderRadiusLG)} ${(0,ec.bf)(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${(0,ec.bf)(e.borderRadiusLG)} ${(0,ec.bf)(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:(0,ec.bf)(o)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,ec.bf)(e.borderRadiusLG)} 0 0 ${(0,ec.bf)(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,ec.bf)(e.borderRadiusLG)} ${(0,ec.bf)(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},ep=e=>{let{componentCls:t,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},(0,ed.Wf)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${(0,ec.bf)(a)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},ed.vS),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${(0,ec.bf)(e.paddingXXS)} ${(0,ec.bf)(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},em=e=>{let{componentCls:t,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:r,verticalItemMargin:i,calc:l}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${(0,ec.bf)(e.lineWidth)} ${e.lineType} ${a}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:l(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:r,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:i},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:(0,ec.bf)(l(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${(0,ec.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:l(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${(0,ec.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},eg=e=>{let{componentCls:t,cardPaddingSM:n,cardPaddingLG:a,cardHeightSM:o,cardHeightLG:r,horizontalItemPaddingSM:i,horizontalItemPaddingLG:l}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:i,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:l,fontSize:e.titleFontSizeLG,lineHeight:e.lineHeightLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n},[`${t}-nav-add`]:{minWidth:o,minHeight:o}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${(0,ec.bf)(e.borderRadius)} ${(0,ec.bf)(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${(0,ec.bf)(e.borderRadius)} ${(0,ec.bf)(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,ec.bf)(e.borderRadius)} ${(0,ec.bf)(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,ec.bf)(e.borderRadius)} 0 0 ${(0,ec.bf)(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:a},[`${t}-nav-add`]:{minWidth:r,minHeight:r}}}}}},eh=e=>{let{componentCls:t,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:r,horizontalItemPadding:i,itemSelectedColor:l,itemColor:c}=e,d=`${t}-tab`;return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:i,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:c,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${d}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},(0,ed.Qy)(e)),"&:hover":{color:a},[`&${d}-active ${d}-btn`]:{color:l,textShadow:e.tabsActiveTextShadow},[`&${d}-focus ${d}-btn:focus-visible`]:(0,ed.oN)(e),[`&${d}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${d}-disabled ${d}-btn, &${d}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${d}-remove ${o}`]:{margin:0},[`${o}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${d} + ${d}`]:{margin:{_skip_check_:!0,value:r}}}},e$=e=>{let{componentCls:t,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:r}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:n},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,ec.bf)(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:(0,ec.bf)(e.marginXS)},marginLeft:{_skip_check_:!0,value:(0,ec.bf)(r(e.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},ey=e=>{let{componentCls:t,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:r,itemActiveColor:i,colorBorderSecondary:l}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,ed.Wf)(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:n,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:a,minHeight:a,marginLeft:{_skip_check_:!0,value:o},background:"transparent",border:`${(0,ec.bf)(e.lineWidth)} ${e.lineType} ${l}`,borderRadius:`${(0,ec.bf)(e.borderRadiusLG)} ${(0,ec.bf)(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:r},"&:active, &:focus:not(:focus-visible)":{color:i}},(0,ed.Qy)(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),eh(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},(0,ed.Qy)(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}},ek=(0,es.I$)("Tabs",e=>{let t=(0,eu.IX)(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${(0,ec.bf)(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${(0,ec.bf)(e.horizontalItemGutter)}`});return[eg(t),e$(t),em(t),ep(t),ev(t),ey(t),ef(t)]},e=>{let{cardHeight:t,cardHeightSM:n,cardHeightLG:a,controlHeight:o,controlHeightLG:r}=e,i=t||r,l=n||o,c=a||r+8;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:i,cardHeightSM:l,cardHeightLG:c,cardPadding:`${(i-e.fontHeight)/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${(l-e.fontHeight)/2-e.lineWidth}px ${e.paddingXS}px`,cardPaddingLG:`${(c-e.fontHeightLG)/2-e.lineWidth}px ${e.padding}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}});var ex=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n};let eS=e=>{var t,n,o,c,s,u,b,f,v,p,m;let g;let{type:h,className:$,rootClassName:y,size:k,onEdit:x,hideAdd:S,centered:w,addIcon:E,removeIcon:_,moreIcon:C,more:O,popupClassName:Z,children:R,items:P,animated:T,style:I,indicatorSize:j,indicator:L,destroyInactiveTabPane:z,destroyOnHidden:M}=e,N=ex(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:B}=N,{direction:D,tabs:G,getPrefixCls:H,getPopupContainer:W}=a.useContext(et.E_),A=H("tabs",B),X=(0,en.Z)(A),[q,K,F]=ek(A,X);"editable-card"===h&&(g={onEdit:(e,{key:t,event:n})=>{null==x||x("add"===e?n:t,e)},removeIcon:null!==(t=null!=_?_:null==G?void 0:G.removeIcon)&&void 0!==t?t:a.createElement(r.Z,null),addIcon:(null!=E?E:null==G?void 0:G.addIcon)||a.createElement(l.Z,null),showAdd:!0!==S});let V=H(),Y=(0,ea.Z)(k),U=function(e,t){return e?e.map(e=>{var t;let n=null!==(t=e.destroyOnHidden)&&void 0!==t?t:e.destroyInactiveTabPane;return Object.assign(Object.assign({},e),{destroyInactiveTabPane:n})}):(0,ei.Z)(t).map(e=>{if(a.isValidElement(e)){let{key:t,props:n}=e,a=n||{},{tab:o}=a,r=el(a,["tab"]);return Object.assign(Object.assign({key:String(t)},r),{label:o})}return null}).filter(e=>e)}(P,R),Q=function(e,t={inkBar:!0,tabPane:!1}){let n;return(n=!1===t?{inkBar:!1,tabPane:!1}:!0===t?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof t?t:{})).tabPane&&(n.tabPaneMotion=Object.assign(Object.assign({},er),{motionName:(0,eo.m)(e,"switch")})),n}(A,T),J=Object.assign(Object.assign({},null==G?void 0:G.style),I),ec={align:null!==(n=null==L?void 0:L.align)&&void 0!==n?n:null===(o=null==G?void 0:G.indicator)||void 0===o?void 0:o.align,size:null!==(b=null!==(s=null!==(c=null==L?void 0:L.size)&&void 0!==c?c:j)&&void 0!==s?s:null===(u=null==G?void 0:G.indicator)||void 0===u?void 0:u.size)&&void 0!==b?b:null==G?void 0:G.indicatorSize};return q(a.createElement(ee,Object.assign({direction:D,getPopupContainer:W},N,{items:U,className:d()({[`${A}-${Y}`]:Y,[`${A}-card`]:["card","editable-card"].includes(h),[`${A}-editable-card`]:"editable-card"===h,[`${A}-centered`]:w},null==G?void 0:G.className,$,y,K,F,X),popupClassName:d()(Z,K,F,X),style:J,editable:g,more:Object.assign({icon:null!==(m=null!==(p=null!==(v=null===(f=null==G?void 0:G.more)||void 0===f?void 0:f.icon)&&void 0!==v?v:null==G?void 0:G.moreIcon)&&void 0!==p?p:C)&&void 0!==m?m:a.createElement(i.Z,null),transitionName:`${V}-slide-up`},O),prefixCls:A,animated:Q,indicator:ec,destroyInactiveTabPane:null!=M?M:z})))};eS.TabPane=()=>null;let ew=eS}};