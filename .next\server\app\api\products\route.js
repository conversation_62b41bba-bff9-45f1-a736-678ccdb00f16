"use strict";(()=>{var e={};e.id=9684,e.ids=[9684],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},82361:e=>{e.exports=require("events")},14538:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>_,originalPathname:()=>P,patchFetch:()=>b,requestAsyncStorage:()=>f,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>y});var o={};r.r(o),r.d(o,{DELETE:()=>h,GET:()=>l,POST:()=>d,PUT:()=>c});var i=r(95419),s=r(69108),a=r(99678),n=r(78070),u=r(86274);async function l(e){try{let{searchParams:t}=new URL(e.url),r="true"===t.get("active_only"),o=t.get("product_code");if(o){let e=await u.dataAccessManager.products.getByCode(o);if("success"!==e.status||!e.data)return n.Z.json({error:"产品不存在"},{status:404});let t=e.data;return n.Z.json({product_code:t.modelCode,product_name:t.modelName,pieces_per_mold:t.piecesPerMold,forming_mold:t.formingMold,forming_mold_quantity:t.formingMoldQuantity,hot_press_mold:t.hotPressMold,hot_press_mold_quantity:t.hotPressMoldQuantity,forming_piece_price:t.formingPiecePrice,hot_press_piece_price:t.hotPressPiecePrice,product_price:t.productPrice,product_weight:t.productWeight,box_specification:t.boxSpecification,packing_quantity:t.packingQuantity,mold_id:t.moldId,is_active:"active"===t.status,created_at:t.createdAt,updated_at:t.updatedAt})}let i=r?await u.dataAccessManager.products.getActive():await u.dataAccessManager.products.getAll();if("success"!==i.status||!i.data)return n.Z.json({error:"获取产品数据失败"},{status:500});let s=(Array.isArray(i.data)?i.data:i.data.items).map(e=>({product_id:e.id,product_code:e.modelCode,product_name:e.modelName,pieces_per_mold:e.piecesPerMold||1,forming_mold:e.formingMold,forming_mold_quantity:e.formingMoldQuantity,hot_press_mold:e.hotPressMold,hot_press_mold_quantity:e.hotPressMoldQuantity,forming_piece_price:e.formingPiecePrice,hot_press_piece_price:e.hotPressPiecePrice,product_price:e.productPrice,product_weight:e.productWeight,box_specification:e.boxSpecification,packing_quantity:e.packingQuantity,mold_id:e.moldId,is_active:"active"===e.status,created_at:e.createdAt,updated_at:e.updatedAt}));return n.Z.json(s)}catch(e){return n.Z.json({error:"获取产品数据失败"},{status:500})}}async function d(e){try{let{product_name:t,pieces_per_mold:r,forming_mold:o,forming_mold_quantity:i,hot_press_mold:s,hot_press_mold_quantity:a,forming_piece_price:l,hot_press_piece_price:d,product_price:c,product_weight:h,box_specification:p,packing_quantity:f}=await e.json();if(!t)return n.Z.json({error:"产品名称不能为空"},{status:400});if(!r||r<=0)return n.Z.json({error:"单模出数必须大于0"},{status:400});if(!i||i<=0)return n.Z.json({error:"成型模具单模数量必须大于0"},{status:400});let m=await u.dataAccessManager.products.create({modelName:t,formingMold:o||"",formingMoldQuantity:i||1,hotPressMold:s||"",hotPressMoldQuantity:a||1,formingPiecePrice:l||0,hotPressPiecePrice:d||0,productPrice:c||0,productWeight:h||0,boxSpecification:p||"",packingQuantity:f||100,piecesPerMold:r,moldId:o||"",status:"active"});if("success"!==m.status||!m.data)return n.Z.json({error:m.message||"创建产品失败"},{status:500});let g=m.data;return n.Z.json({success:!0,message:"产品创建成功",product:{product_id:g.id,product_code:g.modelCode,product_name:g.modelName,pieces_per_mold:g.piecesPerMold,forming_mold_quantity:g.formingMoldQuantity}})}catch(e){return n.Z.json({error:"创建产品失败"},{status:500})}}async function c(e){try{let{product_code:t,...r}=await e.json();if(!t)return n.Z.json({error:"产品编码不能为空"},{status:400});let o=await u.dataAccessManager.products.getByCode(t);if("success"!==o.status||!o.data)return n.Z.json({error:"产品不存在"},{status:404});let i=o.data;if(void 0!==r.pieces_per_mold&&r.pieces_per_mold<=0)return n.Z.json({error:"单模出数必须大于0"},{status:400});if(void 0!==r.forming_mold_quantity&&r.forming_mold_quantity<=0)return n.Z.json({error:"成型模具单模数量必须大于0"},{status:400});let s={};void 0!==r.product_name&&(s.modelName=r.product_name),void 0!==r.pieces_per_mold&&(s.piecesPerMold=r.pieces_per_mold),void 0!==r.forming_mold&&(s.formingMold=r.forming_mold),void 0!==r.forming_mold_quantity&&(s.formingMoldQuantity=r.forming_mold_quantity),void 0!==r.hot_press_mold&&(s.hotPressMold=r.hot_press_mold),void 0!==r.hot_press_mold_quantity&&(s.hotPressMoldQuantity=r.hot_press_mold_quantity),void 0!==r.forming_piece_price&&(s.formingPiecePrice=r.forming_piece_price),void 0!==r.hot_press_piece_price&&(s.hotPressPiecePrice=r.hot_press_piece_price),void 0!==r.product_price&&(s.productPrice=r.product_price),void 0!==r.product_weight&&(s.productWeight=r.product_weight),void 0!==r.box_specification&&(s.boxSpecification=r.box_specification),void 0!==r.packing_quantity&&(s.packingQuantity=r.packing_quantity),void 0!==r.forming_mold&&(s.moldId=r.forming_mold),void 0!==r.is_active&&(s.status=r.is_active?"active":"inactive");let a=await u.dataAccessManager.products.update(i.id,s);if("success"!==a.status||!a.data)return n.Z.json({error:a.message||"更新产品失败"},{status:500});let l=a.data;return n.Z.json({success:!0,message:"产品更新成功",product:{product_code:l.modelCode,product_name:l.modelName,pieces_per_mold:l.piecesPerMold,forming_mold_quantity:l.formingMoldQuantity}})}catch(e){return n.Z.json({error:"更新产品失败"},{status:500})}}async function h(e){try{let{searchParams:t}=new URL(e.url),r=t.get("product_code");if(!r)return n.Z.json({error:"产品编码不能为空"},{status:400});let o=await u.dataAccessManager.products.getByCode(r);if("success"!==o.status||!o.data)return n.Z.json({error:"产品不存在"},{status:404});let i=o.data,s=await u.dataAccessManager.products.delete(i.id);if("success"!==s.status)return n.Z.json({error:s.message||"删除产品失败"},{status:500});return n.Z.json({success:!0,message:"产品删除成功"})}catch(e){return n.Z.json({error:"删除产品失败"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\api\\products\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:f,staticGenerationAsyncStorage:m,serverHooks:g,headerHooks:_,staticGenerationBailout:y}=p,P="/api/products/route";function b(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}},97347:e=>{var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,s={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean);return`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}; ${r.join("; ")}`}function n(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[o,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(o,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function u(e){var t,r;if(!e)return;let[[o,i],...s]=n(e),{domain:a,expires:u,httponly:c,maxage:h,path:p,samesite:f,secure:m,priority:g}=Object.fromEntries(s.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:o,value:decodeURIComponent(i),domain:a,...u&&{expires:new Date(u)},...c&&{httpOnly:!0},..."string"==typeof h&&{maxAge:Number(h)},path:p,...f&&{sameSite:l.includes(t=(t=f).toLowerCase())?t:void 0},...m&&{secure:!0},...g&&{priority:d.includes(r=(r=g).toLowerCase())?r:void 0}})}((e,r)=>{for(var o in r)t(e,o,{get:r[o],enumerable:!0})})(s,{RequestCookies:()=>c,ResponseCookies:()=>h,parseCookie:()=>n,parseSetCookie:()=>u,stringifyCookie:()=>a}),e.exports=((e,s,a,n)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let a of o(s))i.call(e,a)||void 0===a||t(e,a,{get:()=>s[a],enumerable:!(n=r(s,a))||n.enumerable});return e})(t({},"__esModule",{value:!0}),s);var l=["strict","lax","none"],d=["low","medium","high"],c=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of n(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===o).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,o=this._parsed;return o.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(o).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},h=class{constructor(e){var t,r,o;this._parsed=new Map,this._headers=e;let i=null!=(o=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?o:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,o,i,s,a=[],n=0;function u(){for(;n<e.length&&/\s/.test(e.charAt(n));)n+=1;return n<e.length}for(;n<e.length;){for(t=n,s=!1;u();)if(","===(r=e.charAt(n))){for(o=n,n+=1,u(),i=n;n<e.length&&"="!==(r=e.charAt(n))&&";"!==r&&","!==r;)n+=1;n<e.length&&"="===e.charAt(n)?(s=!0,n=i,a.push(e.substring(t,o)),t=n):n=o+1}else n+=1;(!s||n>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=u(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let o="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===o)}has(e){return this._parsed.has(e)}set(...e){let[t,r,o]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...o})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r,o]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:o,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},95419:(e,t,r)=>{e.exports=r(30517)},78070:(e,t,r)=>{Object.defineProperty(t,"Z",{enumerable:!0,get:function(){return o.NextResponse}});let o=r(70457)},10514:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextURL",{enumerable:!0,get:function(){return d}});let o=r(737),i=r(65418),s=r(40283),a=r(23588),n=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function u(e,t){return new URL(String(e).replace(n,"localhost"),t&&String(t).replace(n,"localhost"))}let l=Symbol("NextURLInternal");class d{constructor(e,t,r){let o,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(o=t,i=r||{}):i=r||t||{},this[l]={url:u(e,o??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,i,n;let u=(0,a.getNextPathnameInfo)(this[l].url.pathname,{nextConfig:this[l].options.nextConfig,parseData:!0,i18nProvider:this[l].options.i18nProvider}),d=(0,s.getHostname)(this[l].url,this[l].options.headers);this[l].domainLocale=this[l].options.i18nProvider?this[l].options.i18nProvider.detectDomainLocale(d):(0,o.detectDomainLocale)(null==(t=this[l].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,d);let c=(null==(r=this[l].domainLocale)?void 0:r.defaultLocale)||(null==(n=this[l].options.nextConfig)?void 0:null==(i=n.i18n)?void 0:i.defaultLocale);this[l].url.pathname=u.pathname,this[l].defaultLocale=c,this[l].basePath=u.basePath??"",this[l].buildId=u.buildId,this[l].locale=u.locale??c,this[l].trailingSlash=u.trailingSlash}formatPathname(){return(0,i.formatNextPathnameInfo)({basePath:this[l].basePath,buildId:this[l].buildId,defaultLocale:this[l].options.forceLocale?void 0:this[l].defaultLocale,locale:this[l].locale,pathname:this[l].url.pathname,trailingSlash:this[l].trailingSlash})}formatSearch(){return this[l].url.search}get buildId(){return this[l].buildId}set buildId(e){this[l].buildId=e}get locale(){return this[l].locale??""}set locale(e){var t,r;if(!this[l].locale||!(null==(r=this[l].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[l].locale=e}get defaultLocale(){return this[l].defaultLocale}get domainLocale(){return this[l].domainLocale}get searchParams(){return this[l].url.searchParams}get host(){return this[l].url.host}set host(e){this[l].url.host=e}get hostname(){return this[l].url.hostname}set hostname(e){this[l].url.hostname=e}get port(){return this[l].url.port}set port(e){this[l].url.port=e}get protocol(){return this[l].url.protocol}set protocol(e){this[l].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[l].url=u(e),this.analyze()}get origin(){return this[l].url.origin}get pathname(){return this[l].url.pathname}set pathname(e){this[l].url.pathname=e}get hash(){return this[l].url.hash}set hash(e){this[l].url.hash=e}get search(){return this[l].url.search}set search(e){this[l].url.search=e}get password(){return this[l].url.password}set password(e){this[l].url.password=e}get username(){return this[l].url.username}set username(e){this[l].url.username=e}get basePath(){return this[l].basePath}set basePath(e){this[l].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new d(String(this),this[l].options)}}},63608:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return o.RequestCookies},ResponseCookies:function(){return o.ResponseCookies}});let o=r(97347)},70457:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NextResponse",{enumerable:!0,get:function(){return l}});let o=r(10514),i=r(68670),s=r(63608),a=Symbol("internal response"),n=new Set([301,302,303,307,308]);function u(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[o,i]of e.request.headers)t.set("x-middleware-request-"+o,i),r.push(o);t.set("x-middleware-override-headers",r.join(","))}}class l extends Response{constructor(e,t={}){super(e,t),this[a]={cookies:new s.ResponseCookies(this.headers),url:t.url?new o.NextURL(t.url,{headers:(0,i.toNodeOutgoingHttpHeaders)(this.headers),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[a].cookies}static json(e,t){let r=Response.json(e,t);return new l(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!n.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let o="object"==typeof t?t:{},s=new Headers(null==o?void 0:o.headers);return s.set("Location",(0,i.validateURL)(e)),new l(null,{...o,headers:s,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,i.validateURL)(e)),u(t,r),new l(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),u(e,t),new l(null,{...e,headers:t})}}},68670:(e,t)=>{function r(e){let t=new Headers;for(let[r,o]of Object.entries(e))for(let e of Array.isArray(o)?o:[o])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function o(e){var t,r,o,i,s,a=[],n=0;function u(){for(;n<e.length&&/\s/.test(e.charAt(n));)n+=1;return n<e.length}for(;n<e.length;){for(t=n,s=!1;u();)if(","===(r=e.charAt(n))){for(o=n,n+=1,u(),i=n;n<e.length&&"="!==(r=e.charAt(n))&&";"!==r&&","!==r;)n+=1;n<e.length&&"="===e.charAt(n)?(s=!0,n=i,a.push(e.substring(t,o)),t=n):n=o+1}else n+=1;(!s||n>=e.length)&&a.push(e.substring(t,e.length))}return a}function i(e){let t={},r=[];if(e)for(let[i,s]of e.entries())"set-cookie"===i.toLowerCase()?(r.push(...o(s)),t[i]=1===r.length?r[0]:r):t[i]=s;return t}function s(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fromNodeOutgoingHttpHeaders:function(){return r},splitCookiesString:function(){return o},toNodeOutgoingHttpHeaders:function(){return i},validateURL:function(){return s}})},40283:(e,t)=>{function r(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getHostname",{enumerable:!0,get:function(){return r}})},737:(e,t)=>{function r(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var o,i;if(t===(null==(o=s.domain)?void 0:o.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(i=s.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return s}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}})},73935:(e,t)=>{function r(e,t){let r;let o=e.split("/");return(t||[]).some(t=>!!o[1]&&o[1].toLowerCase()===t.toLowerCase()&&(r=t,o.splice(1,1),e=o.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return r}})},28030:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return s}});let o=r(23495),i=r(67211);function s(e,t,r,s){if(!t||t===r)return e;let a=e.toLowerCase();return!s&&((0,i.pathHasPrefix)(a,"/api")||(0,i.pathHasPrefix)(a,"/"+t.toLowerCase()))?e:(0,o.addPathPrefix)(e,"/"+t)}},23495:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let o=r(81955);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:s}=(0,o.parsePath)(e);return""+t+r+i+s}},2348:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return i}});let o=r(81955);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:s}=(0,o.parsePath)(e);return""+r+t+i+s}},65418:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return n}});let o=r(5545),i=r(23495),s=r(2348),a=r(28030);function n(e){let t=(0,a.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,o.removeTrailingSlash)(t)),e.buildId&&(t=(0,s.addPathSuffix)((0,i.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,i.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,s.addPathSuffix)(t,"/"):(0,o.removeTrailingSlash)(t)}},23588:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return a}});let o=r(73935),i=r(37188),s=r(67211);function a(e,t){var r,a;let{basePath:n,i18n:u,trailingSlash:l}=null!=(r=t.nextConfig)?r:{},d={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):l};n&&(0,s.pathHasPrefix)(d.pathname,n)&&(d.pathname=(0,i.removePathPrefix)(d.pathname,n),d.basePath=n);let c=d.pathname;if(d.pathname.startsWith("/_next/data/")&&d.pathname.endsWith(".json")){let e=d.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];d.buildId=r,c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(d.pathname=c)}if(u){let e=t.i18nProvider?t.i18nProvider.analyze(d.pathname):(0,o.normalizeLocalePath)(d.pathname,u.locales);d.locale=e.detectedLocale,d.pathname=null!=(a=e.pathname)?a:d.pathname,!e.detectedLocale&&d.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):(0,o.normalizeLocalePath)(c,u.locales)).detectedLocale&&(d.locale=e.detectedLocale)}return d}},81955:(e,t)=>{function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),o=r>-1&&(t<0||r<t);return o||t>-1?{pathname:e.substring(0,o?r:t),query:o?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},67211:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let o=r(81955);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,o.parsePath)(e);return r===t||r.startsWith(t+"/")}},37188:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return i}});let o=r(67211);function i(e,t){if(!(0,o.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},5545:(e,t)=>{function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,6274],()=>r(14538));module.exports=o})();