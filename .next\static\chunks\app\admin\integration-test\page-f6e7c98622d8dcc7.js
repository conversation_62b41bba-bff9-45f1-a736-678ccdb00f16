(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8381],{68885:function(e,t,s){Promise.resolve().then(s.bind(s,77561))},77561:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return A}});var a=s(57437),r=s(2265),n=s(11330),i=s(57416),o=s(63424),l=s(89511),c=s(38302),d=s(28683),u=s(65270),m=s(94734),p=s(44753),h=s(6053),g=s(99617),x=s(34863),j=s(28116),f=s(59189),w=s(87304),Z=s(80782),y=s(43043),D=s(83539),S=s(57084),b=s(82765),k=s(32779);let{Step:v}=n.default,I=()=>{let{message:e}=i.Z.useApp(),[t,s]=(0,r.useState)(!1),[I,A]=(0,r.useState)(0),[T,O]=(0,r.useState)([]),[N,P]=(0,r.useState)(null),[C,M]=(0,r.useState)([]),[R,z]=(0,r.useState)([]),[E,_]=(0,r.useState)({totalOrders:0,totalTasks:0,assignedTasks:0,unassignedTasks:0,averageUtilization:0,conflictCount:0}),B=async()=>{s(!0),A(0),O([]),P(null);let t=[];try{A(1);let e=Date.now(),s={id:"test_".concat(Date.now()),orderNumber:"SO".concat(Date.now().toString().slice(-6)),customerId:"TEST001",customerName:"测试客户",customerContact:"测试联系人",orderDate:new Date().toISOString().split("T")[0],deliveryDate:new Date(Date.now()+6048e5).toISOString().split("T")[0],promisedDeliveryDate:new Date(Date.now()+6048e5).toISOString().split("T")[0],status:"pending",productionStatus:"not_started",paymentStatus:"unpaid",paymentTerms:"货到付款",salesRepresentative:"测试销售",totalAmount:17500,discountAmount:0,finalAmount:17500,mrpStatus:"not_started",mrpExecutedAt:void 0,mrpExecutedBy:void 0,mrpResultId:void 0,items:[{id:"item1",orderNumber:"SO".concat(Date.now().toString().slice(-6)),productName:"测试产品A",productCode:"P00001",quantity:1e3,unit:"个",unitPrice:10,totalPrice:1e4,deliveryQuantity:0,remainingQuantity:1e3,moldCode:"M001",productionWorkstation:"",batchNumber:"",remark:""},{id:"item2",orderNumber:"SO".concat(Date.now().toString().slice(-6)),productName:"测试产品B",productCode:"P00002",quantity:500,unit:"个",unitPrice:15,totalPrice:7500,deliveryQuantity:0,remainingQuantity:500,moldCode:"M002",productionWorkstation:"",batchNumber:"",remark:""}],changes:[],remark:"集成测试订单",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},a=await k.dataAccessManager.orders.create({orderNumber:s.orderNumber,customerId:s.customerId,customerName:s.customerName,customerContact:s.customerContact,orderDate:s.orderDate,deliveryDate:s.deliveryDate,salesRepresentative:s.salesRepresentative,items:s.items,totalAmount:s.totalAmount,finalAmount:s.finalAmount,status:s.status,paymentTerms:s.paymentTerms,paymentStatus:s.paymentStatus,remark:s.remark});if("success"===a.status&&a.data){P(a.data);let s=await k.dataAccessManager.orders.getAll();"success"===s.status&&s.data&&M(s.data.items),t.push({step:"创建测试订单",status:"success",message:"成功创建订单 ".concat(a.data.orderNumber),data:a.data,duration:Date.now()-e})}else throw Error(a.message||"创建订单失败");A(2);let r=Date.now(),n=await k.dataAccessManager.orders.update(N.id,{status:"confirmed",updatedAt:new Date().toISOString()});if("success"===n.status){let e=await k.dataAccessManager.orders.getAll();if("success"===e.status&&e.data){M(e.data.items);let s=e.data.items.find(e=>e.id===N.id);if((null==s?void 0:s.status)==="confirmed")t.push({step:"审核订单",status:"success",message:"订单 ".concat(N.orderNumber," 审核成功"),data:s,duration:Date.now()-r});else throw Error("订单状态未正确更新")}}else throw Error(n.message||"更新订单状态失败");A(3);let i=Date.now();try{await new Promise(e=>setTimeout(e,1e3)),t.push({step:"MRP执行",status:"success",message:"MRP功能已移除，跳过此步骤",data:null,duration:Date.now()-i}),A(4);let e=Date.now(),s={success:!1,message:"订单排产集成服务已删除",transferredCount:0,errors:["服务已删除"]};if(s.success){t.push({step:"传输到自动排单",status:"success",message:"成功传输 ".concat(s.transferredCount," 个待排产订单"),data:s,duration:Date.now()-e}),A(5);let a=Date.now();t.push({step:"智能排单执行",status:"warning",message:"智能排单功能已删除",data:{success:!1,message:"智能排产功能已删除",scheduledTasks:[],totalOrders:0},duration:Date.now()-a})}else t.push({step:"传输到自动排单",status:"error",message:"传输失败: ".concat(s.errors.join(", ")),duration:Date.now()-e})}catch(e){t.push({step:"MRP执行",status:"error",message:"MRP执行失败: ".concat(e instanceof Error?e.message:"未知错误"),duration:Date.now()-i})}}catch(e){t.push({step:"测试异常",status:"error",message:"测试过程中发生异常: ".concat(e instanceof Error?e.message:"未知错误"),duration:0})}O(t),A(t.length),s(!1);let a=t.filter(e=>"success"===e.status).length,r=t.filter(e=>"error"===e.status).length;0===r?e.success("集成测试完成！".concat(a," 个步骤全部成功")):e.error("集成测试完成，".concat(r," 个步骤失败，").concat(a," 个步骤成功")),_({totalOrders:{totalOrders:0,completedOrders:0,pendingOrders:0,lastUpdateTime:new Date().toISOString()}.totalOrders,totalTasks:0,assignedTasks:0,unassignedTasks:0,averageUtilization:0,conflictCount:0})},Q=e=>{switch(e){case"success":return(0,a.jsx)(w.Z,{style:{color:"#52c41a"}});case"error":return(0,a.jsx)(Z.Z,{style:{color:"#ff4d4f"}});case"warning":return(0,a.jsx)(y.Z,{style:{color:"#faad14"}});case"running":return(0,a.jsx)(o.Z,{size:"small"});default:return null}};return(0,a.jsx)("div",{className:"integration-test-page",style:{padding:"24px"},children:(0,a.jsxs)(l.Z,{children:[(0,a.jsxs)(c.Z,{justify:"space-between",align:"middle",style:{marginBottom:"24px"},children:[(0,a.jsxs)(d.Z,{children:[(0,a.jsxs)("h2",{style:{margin:0},children:[(0,a.jsx)(D.Z,{style:{marginRight:"8px"}}),"订单管理与自动排单集成测试"]}),(0,a.jsx)("p",{style:{margin:"8px 0 0 0",color:"#666"},children:"验证从订单创建到自动排单的完整业务流程"})]}),(0,a.jsx)(d.Z,{children:(0,a.jsxs)(u.Z,{children:[(0,a.jsx)(m.ZP,{type:"primary",icon:(0,a.jsx)(S.Z,{}),loading:t,onClick:B,children:"开始测试"}),(0,a.jsx)(m.ZP,{icon:(0,a.jsx)(b.Z,{}),onClick:()=>{O([]),A(0),P(null)},children:"重置"})]})})]}),(0,a.jsx)(l.Z,{title:"测试步骤",size:"small",style:{marginBottom:"24px"},children:(0,a.jsxs)(n.default,{current:I,status:t?"process":"finish",children:[(0,a.jsx)(v,{title:"创建订单",description:"创建测试销售订单"}),(0,a.jsx)(v,{title:"审核订单",description:"将订单状态变更为已审核"}),(0,a.jsx)(v,{title:"触发MRP",description:"自动执行物料需求计划"}),(0,a.jsx)(v,{title:"传输订单",description:"将待排产订单传输到自动排单模块"}),(0,a.jsx)(v,{title:"智能排单",description:"执行智能排单算法"})]})}),T.length>0&&(0,a.jsx)(l.Z,{title:"测试结果",size:"small",style:{marginBottom:"24px"},children:(0,a.jsx)(p.Z,{children:T.map((e,t)=>(0,a.jsxs)(p.Z.Item,{dot:Q(e.status),color:"success"===e.status?"green":"error"===e.status?"red":"orange",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{children:e.step}),(0,a.jsx)(h.Z,{color:"success"===e.status?"green":"error"===e.status?"red":"orange",style:{marginLeft:"8px"},children:e.status}),e.duration&&(0,a.jsxs)(h.Z,{color:"blue",style:{marginLeft:"4px"},children:[e.duration,"ms"]})]}),(0,a.jsx)("div",{style:{marginTop:"4px",color:"#666"},children:e.message})]},t))})}),(0,a.jsxs)(c.Z,{gutter:16,children:[(0,a.jsx)(d.Z,{span:12,children:(0,a.jsx)(l.Z,{title:"订单管理状态",size:"small",children:N?(0,a.jsxs)(g.Z,{column:1,size:"small",children:[(0,a.jsx)(g.Z.Item,{label:"订单号",children:N.orderNumber}),(0,a.jsx)(g.Z.Item,{label:"状态",children:(0,a.jsx)(h.Z,{color:"confirmed"===N.status?"green":"orange",children:"confirmed"===N.status?"已审核":"未审核"})}),(0,a.jsx)(g.Z.Item,{label:"产品数量",children:N.items.length}),(0,a.jsxs)(g.Z.Item,{label:"订单金额",children:["\xa5",N.totalAmount]})]}):(0,a.jsx)("div",{style:{textAlign:"center",color:"#999",padding:"20px"},children:"暂无测试订单"})})}),(0,a.jsx)(d.Z,{span:12,children:(0,a.jsx)(l.Z,{title:"自动排单状态",size:"small",children:(0,a.jsxs)(g.Z,{column:1,size:"small",children:[(0,a.jsx)(g.Z.Item,{label:"待排产订单",children:E.totalOrders}),(0,a.jsx)(g.Z.Item,{label:"总任务数",children:E.totalTasks}),(0,a.jsx)(g.Z.Item,{label:"已分配任务",children:E.assignedTasks}),(0,a.jsx)(g.Z.Item,{label:"未分配任务",children:E.unassignedTasks}),(0,a.jsx)(g.Z.Item,{label:"平均利用率",children:(0,a.jsx)(x.Z,{percent:Math.round(100*E.averageUtilization),size:"small",style:{width:"100px"}})})]})})})]}),(0,a.jsx)(j.Z,{}),(0,a.jsx)(f.Z,{type:"info",message:"测试说明",description:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{children:"此测试将验证以下业务流程的完整性："}),(0,a.jsxs)("ul",{children:[(0,a.jsx)("li",{children:"1. 在订单管理模块创建销售订单"}),(0,a.jsx)("li",{children:'2. 审核订单，状态从"未审核"变更为"已审核"'}),(0,a.jsx)("li",{children:"3. 系统自动触发MRP（物料需求计划）执行"}),(0,a.jsx)("li",{children:"4. MRP检查库存，生成待排产订单"}),(0,a.jsx)("li",{children:"5. 待排产订单自动传输到自动排单模块"}),(0,a.jsx)("li",{children:"6. 执行智能排单算法，生成排产计划"})]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"预期结果："}),"整个流程应该无缝衔接，数据完整传输，无错误发生。"]})]}),showIcon:!0})]})})};function A(){return(0,a.jsx)(i.Z,{children:(0,a.jsx)(I,{})})}}},function(e){e.O(0,[9444,8454,6254,7661,9511,5117,6245,2217,4863,7416,9617,2897,5424,1330,5470,741,2779,2971,4938,1744],function(){return e(e.s=68885)}),_N_E=e.O()}]);