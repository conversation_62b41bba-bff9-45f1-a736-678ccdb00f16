(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6851],{87304:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(13428),a=n(2265),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},s=n(46614),i=a.forwardRef(function(e,t){return a.createElement(s.Z,(0,r.Z)({},e,{ref:t,icon:l}))})},65362:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(13428),a=n(2265),l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"},s=n(46614),i=a.forwardRef(function(e,t){return a.createElement(s.Z,(0,r.Z)({},e,{ref:t,icon:l}))})},51769:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(13428),a=n(2265),l={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"},s=n(46614),i=a.forwardRef(function(e,t){return a.createElement(s.Z,(0,r.Z)({},e,{ref:t,icon:l}))})},70865:function(e,t,n){Promise.resolve().then(n.bind(n,25917))},55175:function(e,t,n){"use strict";var r=n(16141),a=n(2265),l=n(40955),s=n(57499),i=n(13292),o=n(38140),c=n(70002),d=n(82432),u=n(83350);let m=null,h=e=>e(),f=[],x={};function p(){let{getContainer:e,duration:t,rtl:n,maxCount:r,top:a}=x,l=(null==e?void 0:e())||document.body;return{getContainer:()=>l,duration:t,rtl:n,maxCount:r,top:a}}let v=a.forwardRef((e,t)=>{let{messageConfig:n,sync:r}=e,{getPrefixCls:i}=(0,a.useContext)(s.E_),o=x.prefixCls||i("message"),c=(0,a.useContext)(l.J),[u,m]=(0,d.K)(Object.assign(Object.assign(Object.assign({},n),{prefixCls:o}),c.message));return a.useImperativeHandle(t,()=>{let e=Object.assign({},u);return Object.keys(e).forEach(t=>{e[t]=function(){for(var e=arguments.length,n=Array(e),a=0;a<e;a++)n[a]=arguments[a];return r(),u[t].apply(u,n)}}),{instance:e,sync:r}}),m}),g=a.forwardRef((e,t)=>{let[n,r]=a.useState(p),l=()=>{r(p)};a.useEffect(l,[]);let s=(0,i.w6)(),o=s.getRootPrefixCls(),c=s.getIconPrefixCls(),d=s.getTheme(),u=a.createElement(v,{ref:t,sync:l,messageConfig:n});return a.createElement(i.ZP,{prefixCls:o,iconPrefixCls:c,theme:d},s.holderRender?s.holderRender(u):u)});function y(){if(!m){let e=document.createDocumentFragment(),t={fragment:e};m=t,h(()=>{(0,o.q)()(a.createElement(g,{ref:e=>{let{instance:n,sync:r}=e||{};Promise.resolve().then(()=>{!t.instance&&n&&(t.instance=n,t.sync=r,y())})}}),e)});return}m.instance&&(f.forEach(e=>{let{type:t,skipped:n}=e;if(!n)switch(t){case"open":h(()=>{let t=m.instance.open(Object.assign(Object.assign({},x),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":h(()=>{null==m||m.instance.destroy(e.key)});break;default:h(()=>{var n;let a=(n=m.instance)[t].apply(n,(0,r.Z)(e.args));null==a||a.then(e.resolve),e.setCloseFn(a)})}}),f=[])}let j={open:function(e){let t=(0,u.J)(t=>{let n;let r={type:"open",config:e,resolve:t,setCloseFn:e=>{n=e}};return f.push(r),()=>{n?h(()=>{n()}):r.skipped=!0}});return y(),t},destroy:e=>{f.push({type:"destroy",key:e}),y()},config:function(e){x=Object.assign(Object.assign({},x),e),h(()=>{var e;null===(e=null==m?void 0:m.sync)||void 0===e||e.call(m)})},useMessage:d.Z,_InternalPanelDoNotUseOrYouWillBeFired:c.ZP};["success","info","warning","error","loading"].forEach(e=>{j[e]=function(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];return function(e,t){(0,i.w6)();let n=(0,u.J)(n=>{let r;let a={type:e,args:t,resolve:n,setCloseFn:e=>{r=e}};return f.push(a),()=>{r?h(()=>{r()}):a.skipped=!0}});return y(),n}(e,n)}}),t.ZP=j},25917:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return F}});var r=n(57437),a=n(2265),l=n(27296),s=n(39992),i=n(50030),o=n(89198),c=n(6053),d=n(65270),u=n(94734),m=n(92503),h=n(55175),f=n(38302),x=n(28683),p=n(89511),v=n(86155),g=n(50574),y=n(47628),j=n(75123),Z=n(68826),w=n(87304),b=n(13428),k={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M894 462c30.9 0 43.8-39.7 18.7-58L530.8 126.2a31.81 31.81 0 00-37.6 0L111.3 404c-25.1 18.2-12.2 58 18.8 58H192v374h-72c-4.4 0-8 3.6-8 8v52c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-52c0-4.4-3.6-8-8-8h-72V462h62zM512 196.7l271.1 197.2H240.9L512 196.7zM264 462h117v374H264V462zm189 0h117v374H453V462zm307 374H642V462h118v374z"}}]},name:"bank",theme:"outlined"},A=n(46614),N=a.forwardRef(function(e,t){return a.createElement(A.Z,(0,b.Z)({},e,{ref:t,icon:k}))}),D=n(75216),I=n(65362),C=n(34021),S=n(75393),L=n(51769),P=n(74898),T=n(12102),E=n(74548),O=n.n(E);let{Option:M}=l.default,{TextArea:V}=s.default,{RangePicker:z}=i.default;var F=()=>{let[e,t]=(0,a.useState)([]),[n,b]=(0,a.useState)([]),[k,A]=(0,a.useState)(!1),[E,z]=(0,a.useState)(!1),[F,R]=(0,a.useState)(!1),[_,H]=(0,a.useState)(!1),[Y,q]=(0,a.useState)(null),[B,J]=(0,a.useState)(null),[U,W]=(0,a.useState)(null),[K]=o.Z.useForm(),[Q,X]=(0,a.useState)(""),[G,$]=(0,a.useState)(void 0),[ee,et]=(0,a.useState)(void 0),[en,er]=(0,a.useState)("payments");(0,a.useEffect)(()=>{t([{id:"1",paymentNumber:"PAY-2024-001",customerId:"1",customerName:"上海包装材料有限公司",paymentDate:"2024-01-26",paymentAmount:6e4,paymentMethod:"bank_transfer",bankAccount:"工商银行***1234",referenceNumber:"TXN20240126001",relatedOrders:["SO-2024-001"],relatedInvoices:["INV-2024-001"],status:"confirmed",remark:"部分付款，剩余款项月底结算",createdAt:"2024-01-26T10:00:00",updatedAt:"2024-01-26T10:00:00"},{id:"2",paymentNumber:"PAY-2024-002",customerId:"2",customerName:"北京绿色包装科技公司",paymentDate:"2024-01-28",paymentAmount:82490,paymentMethod:"acceptance",referenceNumber:"ACC20240128001",relatedOrders:["SO-2024-002"],relatedInvoices:["INV-2024-002"],status:"pending",remark:"承兑汇票，6个月到期",createdAt:"2024-01-28T14:00:00",updatedAt:"2024-01-28T14:00:00"}]),b([{id:"1",customerId:"1",customerName:"上海包装材料有限公司",orderNumber:"SO-2024-001",invoiceNumber:"INV-2024-001",invoiceDate:"2024-01-25",dueDate:"2024-02-25",originalAmount:120062.5,paidAmount:6e4,remainingAmount:60062.5,overdueDays:0,status:"normal",riskLevel:"low",followUpActions:[{id:"1",receivableId:"1",actionType:"call",actionDate:"2024-01-26",actionResult:"客户确认收到发票，承诺月底付清余款",nextActionDate:"2024-02-20",operator:"财务专员",remark:"客户信誉良好"}],createdAt:"2024-01-25T10:00:00",updatedAt:"2024-01-26T10:00:00"},{id:"2",customerId:"3",customerName:"广州环保餐具厂",orderNumber:"SO-2024-003",invoiceNumber:"INV-2024-003",invoiceDate:"2024-01-20",dueDate:"2024-01-30",originalAmount:45e3,paidAmount:0,remainingAmount:45e3,overdueDays:8,status:"overdue",riskLevel:"high",followUpActions:[{id:"2",receivableId:"2",actionType:"call",actionDate:"2024-01-31",actionResult:"客户电话无人接听",nextActionDate:"2024-02-02",operator:"财务专员"},{id:"3",receivableId:"2",actionType:"email",actionDate:"2024-02-01",actionResult:"发送催款邮件",nextActionDate:"2024-02-05",operator:"财务专员"}],createdAt:"2024-01-20T10:00:00",updatedAt:"2024-02-01T10:00:00"}])},[]);let ea=e=>{let t={pending:{color:"orange",text:"待确认",icon:(0,r.jsx)(Z.Z,{})},confirmed:{color:"green",text:"已确认",icon:(0,r.jsx)(w.Z,{})},reconciled:{color:"cyan",text:"已对账",icon:(0,r.jsx)(N,{})}}[e]||{color:"default",text:"未知",icon:null};return(0,r.jsx)(c.Z,{color:t.color,icon:t.icon,children:t.text})},el=e=>{let t={cash:{color:"green",text:"现金"},bank_transfer:{color:"blue",text:"银行转账"},check:{color:"orange",text:"支票"},acceptance:{color:"purple",text:"承兑汇票"}}[e]||{color:"default",text:"其他"};return(0,r.jsx)(c.Z,{color:t.color,children:t.text})},es=e=>{let t={normal:{color:"green",text:"正常"},overdue:{color:"red",text:"逾期"},bad_debt:{color:"volcano",text:"坏账"}}[e]||{color:"default",text:"未知"};return(0,r.jsx)(c.Z,{color:t.color,children:t.text})},ei=e=>{let t={low:{color:"green",text:"低风险"},medium:{color:"orange",text:"中风险"},high:{color:"red",text:"高风险"}}[e]||{color:"default",text:"未知"};return(0,r.jsx)(c.Z,{color:t.color,children:t.text})},eo=[{title:"收款单号",dataIndex:"paymentNumber",key:"paymentNumber",width:140,fixed:"left"},{title:"客户名称",dataIndex:"customerName",key:"customerName",width:180,ellipsis:!0},{title:"收款日期",dataIndex:"paymentDate",key:"paymentDate",width:120,sorter:(e,t)=>new Date(e.paymentDate).getTime()-new Date(t.paymentDate).getTime()},{title:"收款金额",dataIndex:"paymentAmount",key:"paymentAmount",width:120,render:e=>(0,r.jsxs)("span",{style:{fontWeight:"bold",color:"#3f8600"},children:["\xa5",e.toLocaleString()]}),sorter:(e,t)=>e.paymentAmount-t.paymentAmount},{title:"付款方式",dataIndex:"paymentMethod",key:"paymentMethod",width:120,render:e=>el(e)},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>ea(e)},{title:"关联订单",dataIndex:"relatedOrders",key:"relatedOrders",width:120,render:e=>(0,r.jsx)("div",{children:e.map(e=>(0,r.jsx)(c.Z,{children:e},e))})},{title:"操作",key:"action",width:200,fixed:"right",render:(e,t)=>(0,r.jsxs)(d.Z,{size:"small",children:[(0,r.jsx)(u.ZP,{type:"link",icon:(0,r.jsx)(D.Z,{}),onClick:()=>ef(t),children:"详情"}),(0,r.jsx)(u.ZP,{type:"link",icon:(0,r.jsx)(I.Z,{}),onClick:()=>eh(t),children:"编辑"}),(0,r.jsx)(m.Z,{title:"确定要删除这条收款记录吗？",onConfirm:()=>ep(t.id),okText:"确定",cancelText:"取消",children:(0,r.jsx)(u.ZP,{type:"link",danger:!0,icon:(0,r.jsx)(C.Z,{}),children:"删除"})})]})}],ec=e.filter(e=>{let t=!Q||e.paymentNumber.toLowerCase().includes(Q.toLowerCase())||e.customerName.toLowerCase().includes(Q.toLowerCase()),n=!G||e.status===G,r=!ee||e.paymentMethod===ee;return t&&n&&r}),ed=n.filter(e=>!Q||e.customerName.toLowerCase().includes(Q.toLowerCase())||e.orderNumber.toLowerCase().includes(Q.toLowerCase())||e.invoiceNumber.toLowerCase().includes(Q.toLowerCase())),eu={total:e.length,totalAmount:e.reduce((e,t)=>e+t.paymentAmount,0),pending:e.filter(e=>"pending"===e.status).length,confirmed:e.filter(e=>"confirmed"===e.status).length},em={total:n.length,totalAmount:n.reduce((e,t)=>e+t.remainingAmount,0),overdue:n.filter(e=>"overdue"===e.status).length,highRisk:n.filter(e=>"high"===e.riskLevel).length},eh=e=>{q(e),z(!0),K.setFieldsValue({...e,paymentDate:O()(e.paymentDate)})},ef=e=>{J(e),R(!0)},ex=e=>{W(e),H(!0)},ep=n=>{t(e.filter(e=>e.id!==n)),h.ZP.success("收款记录删除成功")};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"page-header",children:[(0,r.jsx)("h1",{className:"page-title",children:"收款管理"}),(0,r.jsx)("p",{className:"page-description",children:"收款记录管理、应收账款监控、账款跟踪时间轴"})]}),(0,r.jsxs)(f.Z,{gutter:[16,16],children:[(0,r.jsx)(x.Z,{xs:24,sm:6,children:(0,r.jsx)(p.Z,{children:(0,r.jsx)(v.Z,{title:"收款总额",value:eu.totalAmount,precision:0,prefix:"\xa5",valueStyle:{color:"#3f8600"}})})}),(0,r.jsx)(x.Z,{xs:24,sm:6,children:(0,r.jsx)(p.Z,{children:(0,r.jsx)(v.Z,{title:"应收余额",value:em.totalAmount,precision:0,prefix:"\xa5",valueStyle:{color:"#cf1322"}})})}),(0,r.jsx)(x.Z,{xs:24,sm:6,children:(0,r.jsx)(p.Z,{children:(0,r.jsx)(v.Z,{title:"逾期账款",value:em.overdue,suffix:"笔",valueStyle:{color:"#faad14"}})})}),(0,r.jsx)(x.Z,{xs:24,sm:6,children:(0,r.jsx)(p.Z,{children:(0,r.jsx)(v.Z,{title:"高风险客户",value:em.highRisk,suffix:"个",valueStyle:{color:"#ff4d4f"}})})})]}),(0,r.jsxs)(p.Z,{children:[(0,r.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,r.jsx)(u.ZP,{type:"payments"===en?"primary":"default",onClick:()=>er("payments"),children:"收款记录"}),(0,r.jsx)(u.ZP,{type:"receivables"===en?"primary":"default",onClick:()=>er("receivables"),children:"应收账款"})]}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,r.jsx)(s.default,{placeholder:"payments"===en?"搜索收款单号或客户名称":"搜索客户名称、订单号或发票号",prefix:(0,r.jsx)(S.Z,{}),value:Q,onChange:e=>X(e.target.value),className:"w-full sm:w-64"}),"payments"===en&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(T.Z,{placeholder:"收款状态",value:G,onChange:$,className:"w-full sm:w-32",allowClear:!0,children:[(0,r.jsx)(M,{value:"pending",children:"待确认"}),(0,r.jsx)(M,{value:"confirmed",children:"已确认"}),(0,r.jsx)(M,{value:"reconciled",children:"已对账"})]}),(0,r.jsxs)(T.Z,{placeholder:"付款方式",value:ee,onChange:et,className:"w-full sm:w-32",allowClear:!0,children:[(0,r.jsx)(M,{value:"cash",children:"现金"}),(0,r.jsx)(M,{value:"bank_transfer",children:"银行转账"}),(0,r.jsx)(M,{value:"check",children:"支票"}),(0,r.jsx)(M,{value:"acceptance",children:"承兑汇票"})]})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(u.ZP,{icon:(0,r.jsx)(L.Z,{}),children:"导出"}),"payments"===en&&(0,r.jsx)(u.ZP,{type:"primary",icon:(0,r.jsx)(P.Z,{}),onClick:()=>{q(null),z(!0),K.resetFields()},children:"新建收款"})]})]})]}),(0,r.jsx)(p.Z,{title:"payments"===en?"收款记录":"应收账款",children:(0,r.jsx)(g.Z,{columns:"payments"===en?eo:[{title:"客户名称",dataIndex:"customerName",key:"customerName",width:180,ellipsis:!0,fixed:"left"},{title:"销售订单号",dataIndex:"orderNumber",key:"orderNumber",width:120},{title:"发票号",dataIndex:"invoiceNumber",key:"invoiceNumber",width:120},{title:"到期日期",dataIndex:"dueDate",key:"dueDate",width:120,render:(e,t)=>(0,r.jsx)("span",{style:{color:t.overdueDays>0?"#ff4d4f":"inherit"},children:e}),sorter:(e,t)=>new Date(e.dueDate).getTime()-new Date(t.dueDate).getTime()},{title:"应收金额",dataIndex:"originalAmount",key:"originalAmount",width:120,render:e=>"\xa5".concat(e.toLocaleString())},{title:"已收金额",dataIndex:"paidAmount",key:"paidAmount",width:120,render:e=>"\xa5".concat(e.toLocaleString())},{title:"剩余金额",dataIndex:"remainingAmount",key:"remainingAmount",width:120,render:e=>(0,r.jsxs)("span",{style:{fontWeight:"bold",color:e>0?"#cf1322":"#3f8600"},children:["\xa5",e.toLocaleString()]})},{title:"逾期天数",dataIndex:"overdueDays",key:"overdueDays",width:100,render:e=>(0,r.jsx)("span",{style:{color:e>0?"#ff4d4f":"#52c41a"},children:e>0?"".concat(e,"天"):"正常"}),sorter:(e,t)=>e.overdueDays-t.overdueDays},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>es(e)},{title:"风险等级",dataIndex:"riskLevel",key:"riskLevel",width:100,render:e=>ei(e)},{title:"操作",key:"action",width:150,fixed:"right",render:(e,t)=>(0,r.jsx)(d.Z,{size:"small",children:(0,r.jsx)(u.ZP,{type:"link",icon:(0,r.jsx)(D.Z,{}),onClick:()=>ex(t),children:"跟踪"})})}],dataSource:"payments"===en?ec:ed,rowKey:"id",loading:k,pagination:{total:"payments"===en?ec.length:ed.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条")},scroll:{x:1400}})}),(0,r.jsx)(y.Z,{title:Y?"编辑收款记录":"新建收款记录",open:E,onOk:()=>{K.validateFields().then(n=>{let r=new Date().toISOString(),a={...n,paymentDate:n.paymentDate.format("YYYY-MM-DD")};if(Y)t(e.map(e=>e.id===Y.id?{...e,...a,updatedAt:r}:e)),h.ZP.success("收款记录更新成功");else{let n={id:Date.now().toString(),paymentNumber:"PAY-".concat(new Date().getFullYear(),"-").concat(String(e.length+1).padStart(3,"0")),...a,createdAt:r,updatedAt:r};t([...e,n]),h.ZP.success("收款记录创建成功")}z(!1),K.resetFields()})},onCancel:()=>{z(!1),K.resetFields()},width:800,okText:"确认",cancelText:"取消",children:(0,r.jsxs)(o.Z,{form:K,layout:"vertical",initialValues:{paymentMethod:"bank_transfer",status:"pending"},children:[(0,r.jsxs)(f.Z,{gutter:16,children:[(0,r.jsx)(x.Z,{span:12,children:(0,r.jsx)(o.Z.Item,{name:"customerId",label:"客户ID",rules:[{required:!0,message:"请输入客户ID"}],children:(0,r.jsx)(s.default,{placeholder:"请输入客户ID"})})}),(0,r.jsx)(x.Z,{span:12,children:(0,r.jsx)(o.Z.Item,{name:"customerName",label:"客户名称",rules:[{required:!0,message:"请输入客户名称"}],children:(0,r.jsx)(s.default,{placeholder:"请输入客户名称"})})})]}),(0,r.jsxs)(f.Z,{gutter:16,children:[(0,r.jsx)(x.Z,{span:12,children:(0,r.jsx)(o.Z.Item,{name:"paymentDate",label:"收款日期",rules:[{required:!0,message:"请选择收款日期"}],children:(0,r.jsx)(i.default,{style:{width:"100%"}})})}),(0,r.jsx)(x.Z,{span:12,children:(0,r.jsx)(o.Z.Item,{name:"paymentAmount",label:"收款金额",rules:[{required:!0,message:"请输入收款金额"}],children:(0,r.jsx)(j.Z,{placeholder:"请输入收款金额",min:0,style:{width:"100%"},formatter:e=>"\xa5 ".concat(e).replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>e.replace(/¥\s?|(,*)/g,"")})})})]}),(0,r.jsxs)(f.Z,{gutter:16,children:[(0,r.jsx)(x.Z,{span:12,children:(0,r.jsx)(o.Z.Item,{name:"paymentMethod",label:"付款方式",rules:[{required:!0,message:"请选择付款方式"}],children:(0,r.jsxs)(l.default,{children:[(0,r.jsx)(M,{value:"cash",children:"现金"}),(0,r.jsx)(M,{value:"bank_transfer",children:"银行转账"}),(0,r.jsx)(M,{value:"check",children:"支票"}),(0,r.jsx)(M,{value:"acceptance",children:"承兑汇票"})]})})}),(0,r.jsx)(x.Z,{span:12,children:(0,r.jsx)(o.Z.Item,{name:"bankAccount",label:"收款银行账户",children:(0,r.jsx)(s.default,{placeholder:"请输入收款银行账户"})})})]}),(0,r.jsx)(o.Z.Item,{name:"referenceNumber",label:"参考号/流水号",children:(0,r.jsx)(s.default,{placeholder:"请输入转账流水号或参考号"})}),(0,r.jsx)(o.Z.Item,{name:"remark",label:"备注",children:(0,r.jsx)(V,{rows:3,placeholder:"请输入备注信息"})})]})})]})}},12102:function(e,t,n){"use strict";var r=n(57437),a=n(2265),l=n(27296);t.Z=e=>{let{value:t,options:n,children:s,onChange:i,...o}=e,c=(0,a.useRef)(null),d=(0,a.useRef)(void 0),u=a.useMemo(()=>{let e=new Set;return n&&Array.isArray(n)&&n.forEach(t=>e.add(t.value)),s&&a.Children.forEach(s,t=>{var n;(null==t?void 0:null===(n=t.props)||void 0===n?void 0:n.value)!==void 0&&e.add(t.props.value)}),e},[n,s]),m=a.useMemo(()=>{if(null!=t&&""!==t&&("string"!=typeof t||""!==t.trim())){if(u.has(t))return d.current=t,t;console.warn("FormSelect: 值不在可用选项中",{value:t,availableValues:Array.from(u),placeholder:o.placeholder})}},[t,u,o.placeholder]);(0,a.useEffect)(()=>{""===t&&i&&setTimeout(()=>{i(void 0,void 0)},0)},[t,i]),(0,a.useEffect)(()=>{if(c.current){let e=c.current.nativeElement||c.current;if(e){let t=e.querySelector('input[type="hidden"]');t&&""===t.value&&i&&i(void 0,void 0)}}},[m,i]);let h=""===m?void 0:m;return(0,r.jsx)(l.default,{ref:c,...o,value:h,onChange:(e,t)=>{let n=""===e?void 0:e;console.log("FormSelect onChange:",{placeholder:o.placeholder,originalValue:e,safeValue:n,option:t,isEmptyString:""===e,isUndefined:void 0===e,isValidValue:u.has(e),availableValues:Array.from(u)}),i&&i(n,t)},options:n,children:s})}}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,1157,8236,30,2971,4938,1744],function(){return e(e.s=70865)}),_N_E=e.O()}]);