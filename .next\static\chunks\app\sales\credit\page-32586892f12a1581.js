(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3026],{76562:function(e,s,l){Promise.resolve().then(l.bind(l,3418))},3418:function(e,s,l){"use strict";l.r(s);var r=l(57437),t=l(2265),i=l(27296),a=l(39992),c=l(89198),o=l(6053),n=l(34863),d=l(65270),x=l(94734),h=l(92503),u=l(55175),m=l(38302),j=l(28683),Z=l(89511),f=l(86155),p=l(50574),v=l(47628),S=l(59189),g=l(50030),w=l(75123),k=l(99617),y=l(87304),I=l(43043),C=l(23455),L=l(6063),D=l(36230),b=l(40856),P=l(75216),A=l(65362),N=l(34021),R=l(75393),T=l(51769),B=l(74898),q=l(74548),z=l.n(q);let{Option:H}=i.default,{TextArea:E}=a.default;s.default=()=>{let[e,s]=(0,t.useState)([]),[l,q]=(0,t.useState)([]),[F,_]=(0,t.useState)(!1),[M,O]=(0,t.useState)(!1),[V,Y]=(0,t.useState)(!1),[W,J]=(0,t.useState)(null),[K,Q]=(0,t.useState)(null),[U]=c.Z.useForm(),[G,X]=(0,t.useState)(""),[$,ee]=(0,t.useState)(""),[es,el]=(0,t.useState)("");(0,t.useEffect)(()=>{q([{id:"1",customerCode:"CUS-001",customerName:"上海包装材料有限公司",customerLevel:"A",customerCategory:"important",creditLimit:5e5,usedCredit:32e4,annualSalesAmount:12e5,contactPerson:"张经理",contactPhone:"13800138001",address:"上海市浦东新区",paymentTerms:"月结30天",discountRate:.05,preferredProducts:["CP-202"],salesRepresentative:"李销售",status:"active",createdAt:"2023-01-15",updatedAt:"2024-01-15"}]),s([{id:"1",customerId:"1",assessmentDate:"2024-01-15",paymentHistoryScore:45,orderScaleScore:28,productPriceScore:18,totalScore:91,creditLevel:"A",riskLevel:"low",assessor:"风控专员",remark:"优质客户，付款及时，订单规模大",createdAt:"2024-01-15T10:00:00"},{id:"2",customerId:"2",assessmentDate:"2024-01-18",paymentHistoryScore:35,orderScaleScore:20,productPriceScore:15,totalScore:70,creditLevel:"B",riskLevel:"medium",assessor:"风控专员",remark:"一般客户，偶有延期付款",createdAt:"2024-01-18T14:00:00"},{id:"3",customerId:"3",assessmentDate:"2024-01-20",paymentHistoryScore:25,orderScaleScore:15,productPriceScore:10,totalScore:50,creditLevel:"C",riskLevel:"high",assessor:"风控专员",remark:"高风险客户，多次逾期付款",createdAt:"2024-01-20T16:00:00"}])},[]);let er=e=>{let s={low:{color:"green",text:"低风险",icon:(0,r.jsx)(y.Z,{})},medium:{color:"orange",text:"中风险",icon:(0,r.jsx)(I.Z,{})},high:{color:"red",text:"高风险",icon:(0,r.jsx)(C.Z,{})}}[e]||{color:"default",text:"未知",icon:null};return(0,r.jsx)(o.Z,{color:s.color,icon:s.icon,children:s.text})},et=e=>{let s={A:{color:"gold",text:"A级",icon:(0,r.jsx)(L.Z,{})},B:{color:"blue",text:"B级",icon:(0,r.jsx)(D.Z,{})},C:{color:"green",text:"C级",icon:(0,r.jsx)(b.Z,{})},D:{color:"orange",text:"D级",icon:(0,r.jsx)(b.Z,{})},E:{color:"red",text:"E级",icon:(0,r.jsx)(b.Z,{})}}[e]||{color:"default",text:"未知",icon:null};return(0,r.jsx)(o.Z,{color:s.color,icon:s.icon,children:s.text})},ei=(e,s)=>{let l=e/s*100;return l>=80?"#52c41a":l>=60?"#faad14":"#ff4d4f"},ea=e=>{let s=l.find(s=>s.id===e);if(!s)return null;let r=0;switch(s.customerLevel){case"A":r=45;break;case"B":r=35;break;case"C":r=25}let t=Math.min(30,(s.annualSalesAmount||0)/1e5*3),i="A"===s.customerLevel?18:"B"===s.customerLevel?15:10,a=r+t+i,c="E",o="high";return a>=90?(c="A",o="low"):a>=75?(c="B",o="low"):a>=60?(c="C",o="medium"):a>=40?(c="D",o="medium"):(c="E",o="high"),{paymentHistoryScore:r,orderScaleScore:t,productPriceScore:i,totalScore:a,creditLevel:c,riskLevel:o}},ec=[{title:"客户ID",dataIndex:"customerId",key:"customerId",width:100,fixed:"left"},{title:"评估日期",dataIndex:"assessmentDate",key:"assessmentDate",width:120,sorter:(e,s)=>new Date(e.assessmentDate).getTime()-new Date(s.assessmentDate).getTime()},{title:"信用等级",dataIndex:"creditLevel",key:"creditLevel",width:100,render:e=>et(e)},{title:"风险等级",dataIndex:"riskLevel",key:"riskLevel",width:100,render:e=>er(e)},{title:"总评分",dataIndex:"totalScore",key:"totalScore",width:120,render:e=>(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{style:{fontWeight:"bold",color:ei(e,100)},children:[e,"分"]}),(0,r.jsx)(n.Z,{percent:e,size:"small",strokeColor:ei(e,100),showInfo:!1})]}),sorter:(e,s)=>e.totalScore-s.totalScore},{title:"评分详情",key:"scoreDetails",width:200,render:(e,s)=>(0,r.jsxs)("div",{style:{fontSize:"12px"},children:[(0,r.jsxs)("div",{children:["历史付款: ",s.paymentHistoryScore,"/50"]}),(0,r.jsxs)("div",{children:["订单规模: ",s.orderScaleScore,"/30"]}),(0,r.jsxs)("div",{children:["产品单价: ",s.productPriceScore,"/20"]})]})},{title:"评估人",dataIndex:"assessor",key:"assessor",width:100},{title:"操作",key:"action",width:200,fixed:"right",render:(e,s)=>(0,r.jsxs)(d.Z,{size:"small",children:[(0,r.jsx)(x.ZP,{type:"link",icon:(0,r.jsx)(P.Z,{}),onClick:()=>ex(s),children:"详情"}),(0,r.jsx)(x.ZP,{type:"link",icon:(0,r.jsx)(A.Z,{}),onClick:()=>ed(s),children:"编辑"}),(0,r.jsx)(h.Z,{title:"确定要删除这个评估记录吗？",onConfirm:()=>eh(s.id),okText:"确定",cancelText:"取消",children:(0,r.jsx)(x.ZP,{type:"link",danger:!0,icon:(0,r.jsx)(N.Z,{}),children:"删除"})})]})}],eo=e.filter(e=>{let s=!G||e.customerId.toLowerCase().includes(G.toLowerCase())||e.assessor.toLowerCase().includes(G.toLowerCase()),l=!$||e.riskLevel===$,r=!es||e.creditLevel===es;return s&&l&&r}),en={total:e.length,lowRisk:e.filter(e=>"low"===e.riskLevel).length,mediumRisk:e.filter(e=>"medium"===e.riskLevel).length,highRisk:e.filter(e=>"high"===e.riskLevel).length,averageScore:e.length>0?Math.round(e.reduce((e,s)=>e+s.totalScore,0)/e.length):0},ed=e=>{J(e),O(!0),U.setFieldsValue({...e,assessmentDate:z()(e.assessmentDate)})},ex=e=>{Q(e),Y(!0)},eh=l=>{s(e.filter(e=>e.id!==l)),u.ZP.success("评估记录删除成功")};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"page-header",children:[(0,r.jsx)("h1",{className:"page-title",children:"信用风控管理"}),(0,r.jsx)("p",{className:"page-description",children:"三维度评估模型，智能风险识别与管控"})]}),(0,r.jsxs)(m.Z,{gutter:[16,16],children:[(0,r.jsx)(j.Z,{xs:24,sm:6,children:(0,r.jsx)(Z.Z,{children:(0,r.jsx)(f.Z,{title:"评估总数",value:en.total,suffix:"个",prefix:(0,r.jsx)(D.Z,{})})})}),(0,r.jsx)(j.Z,{xs:24,sm:6,children:(0,r.jsx)(Z.Z,{children:(0,r.jsx)(f.Z,{title:"平均评分",value:en.averageScore,suffix:"分",valueStyle:{color:ei(en.averageScore,100)}})})}),(0,r.jsx)(j.Z,{xs:24,sm:6,children:(0,r.jsx)(Z.Z,{children:(0,r.jsx)(f.Z,{title:"高风险客户",value:en.highRisk,suffix:"个",valueStyle:{color:"#cf1322"}})})}),(0,r.jsx)(j.Z,{xs:24,sm:6,children:(0,r.jsx)(Z.Z,{children:(0,r.jsx)(f.Z,{title:"低风险客户",value:en.lowRisk,suffix:"个",valueStyle:{color:"#3f8600"}})})})]}),(0,r.jsxs)(m.Z,{gutter:[16,16],children:[(0,r.jsx)(j.Z,{xs:24,sm:8,children:(0,r.jsxs)(Z.Z,{children:[(0,r.jsx)(f.Z,{title:"低风险",value:en.lowRisk,suffix:"个",valueStyle:{color:"#52c41a"}}),(0,r.jsx)(n.Z,{percent:en.total>0?en.lowRisk/en.total*100:0,strokeColor:"#52c41a",showInfo:!1})]})}),(0,r.jsx)(j.Z,{xs:24,sm:8,children:(0,r.jsxs)(Z.Z,{children:[(0,r.jsx)(f.Z,{title:"中风险",value:en.mediumRisk,suffix:"个",valueStyle:{color:"#faad14"}}),(0,r.jsx)(n.Z,{percent:en.total>0?en.mediumRisk/en.total*100:0,strokeColor:"#faad14",showInfo:!1})]})}),(0,r.jsx)(j.Z,{xs:24,sm:8,children:(0,r.jsxs)(Z.Z,{children:[(0,r.jsx)(f.Z,{title:"高风险",value:en.highRisk,suffix:"个",valueStyle:{color:"#ff4d4f"}}),(0,r.jsx)(n.Z,{percent:en.total>0?en.highRisk/en.total*100:0,strokeColor:"#ff4d4f",showInfo:!1})]})})]}),(0,r.jsx)(Z.Z,{children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,r.jsx)(a.default,{placeholder:"搜索客户ID或评估人",prefix:(0,r.jsx)(R.Z,{}),value:G,onChange:e=>X(e.target.value),className:"w-full sm:w-64"}),(0,r.jsxs)(i.default,{placeholder:"风险等级",value:$,onChange:ee,className:"w-full sm:w-32",allowClear:!0,children:[(0,r.jsx)(H,{value:"low",children:"低风险"}),(0,r.jsx)(H,{value:"medium",children:"中风险"}),(0,r.jsx)(H,{value:"high",children:"高风险"})]}),(0,r.jsxs)(i.default,{placeholder:"信用等级",value:es,onChange:el,className:"w-full sm:w-32",allowClear:!0,children:[(0,r.jsx)(H,{value:"A",children:"A级"}),(0,r.jsx)(H,{value:"B",children:"B级"}),(0,r.jsx)(H,{value:"C",children:"C级"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(x.ZP,{icon:(0,r.jsx)(T.Z,{}),children:"导出"}),(0,r.jsx)(x.ZP,{type:"primary",icon:(0,r.jsx)(B.Z,{}),onClick:()=>{J(null),O(!0),U.resetFields()},children:"新建评估"})]})]})}),(0,r.jsx)(Z.Z,{title:"信用评估列表",children:(0,r.jsx)(p.Z,{columns:ec,dataSource:eo,rowKey:"id",loading:F,pagination:{total:eo.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>"第 ".concat(s[0],"-").concat(s[1]," 条/共 ").concat(e," 条")},scroll:{x:1200}})}),(0,r.jsxs)(v.Z,{title:W?"编辑信用评估":"新建信用评估",open:M,onOk:()=>{U.validateFields().then(l=>{let r=new Date().toISOString(),t={...l,assessmentDate:l.assessmentDate.format("YYYY-MM-DD")};W?(s(e.map(e=>e.id===W.id?{...e,...t}:e)),u.ZP.success("评估记录更新成功")):(s([...e,{id:Date.now().toString(),...t,createdAt:r}]),u.ZP.success("评估记录创建成功")),O(!1),U.resetFields()})},onCancel:()=>{O(!1),U.resetFields()},width:800,okText:"确认",cancelText:"取消",children:[(0,r.jsx)(S.Z,{message:"三维度评估模型",description:"历史付款(50分) + 订单规模(30分) + 产品单价(20分) = 总评分(100分)",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,r.jsxs)(c.Z,{form:U,layout:"vertical",initialValues:{riskLevel:"medium"},children:[(0,r.jsxs)(m.Z,{gutter:16,children:[(0,r.jsx)(j.Z,{span:12,children:(0,r.jsx)(c.Z.Item,{name:"customerId",label:"客户ID",rules:[{required:!0,message:"请输入客户ID"}],children:(0,r.jsx)(i.default,{placeholder:"请选择客户",children:l.map(e=>(0,r.jsxs)(H,{value:e.id,children:[e.customerCode," - ",e.customerName]},e.id))})})}),(0,r.jsx)(j.Z,{span:12,children:(0,r.jsx)(c.Z.Item,{name:"assessmentDate",label:"评估日期",rules:[{required:!0,message:"请选择评估日期"}],children:(0,r.jsx)(g.default,{style:{width:"100%"}})})})]}),(0,r.jsx)("div",{style:{textAlign:"center",marginBottom:16},children:(0,r.jsx)(x.ZP,{type:"dashed",onClick:()=>{let e=U.getFieldValue("customerId");if(!e){u.ZP.warning("请先选择客户");return}let s=ea(e);s?(U.setFieldsValue(s),u.ZP.success("自动计算完成")):u.ZP.error("未找到客户信息")},children:"智能计算评分"})}),(0,r.jsxs)(m.Z,{gutter:16,children:[(0,r.jsx)(j.Z,{span:8,children:(0,r.jsx)(c.Z.Item,{name:"paymentHistoryScore",label:"历史付款评分 (0-50分)",rules:[{required:!0,message:"请输入历史付款评分"}],children:(0,r.jsx)(w.Z,{min:0,max:50,style:{width:"100%"},placeholder:"0-50分"})})}),(0,r.jsx)(j.Z,{span:8,children:(0,r.jsx)(c.Z.Item,{name:"orderScaleScore",label:"订单规模评分 (0-30分)",rules:[{required:!0,message:"请输入订单规模评分"}],children:(0,r.jsx)(w.Z,{min:0,max:30,style:{width:"100%"},placeholder:"0-30分"})})}),(0,r.jsx)(j.Z,{span:8,children:(0,r.jsx)(c.Z.Item,{name:"productPriceScore",label:"产品单价评分 (0-20分)",rules:[{required:!0,message:"请输入产品单价评分"}],children:(0,r.jsx)(w.Z,{min:0,max:20,style:{width:"100%"},placeholder:"0-20分"})})})]}),(0,r.jsxs)(m.Z,{gutter:16,children:[(0,r.jsx)(j.Z,{span:8,children:(0,r.jsx)(c.Z.Item,{name:"totalScore",label:"总评分",rules:[{required:!0,message:"请输入总评分"}],children:(0,r.jsx)(w.Z,{min:0,max:100,style:{width:"100%"},placeholder:"0-100分"})})}),(0,r.jsx)(j.Z,{span:8,children:(0,r.jsx)(c.Z.Item,{name:"creditLevel",label:"信用等级",rules:[{required:!0,message:"请选择信用等级"}],children:(0,r.jsxs)(i.default,{children:[(0,r.jsx)(H,{value:"A",children:"A级 (80分以上)"}),(0,r.jsx)(H,{value:"B",children:"B级 (60-79分)"}),(0,r.jsx)(H,{value:"C",children:"C级 (60分以下)"})]})})}),(0,r.jsx)(j.Z,{span:8,children:(0,r.jsx)(c.Z.Item,{name:"riskLevel",label:"风险等级",rules:[{required:!0,message:"请选择风险等级"}],children:(0,r.jsxs)(i.default,{children:[(0,r.jsx)(H,{value:"low",children:"低风险"}),(0,r.jsx)(H,{value:"medium",children:"中风险"}),(0,r.jsx)(H,{value:"high",children:"高风险"})]})})})]}),(0,r.jsx)(c.Z.Item,{name:"assessor",label:"评估人",rules:[{required:!0,message:"请输入评估人"}],children:(0,r.jsx)(a.default,{placeholder:"请输入评估人"})}),(0,r.jsx)(c.Z.Item,{name:"remark",label:"评估备注",children:(0,r.jsx)(E,{rows:3,placeholder:"请输入评估备注"})})]})]}),(0,r.jsx)(v.Z,{title:"信用评估详情",open:V,onCancel:()=>Y(!1),footer:[(0,r.jsx)(x.ZP,{onClick:()=>Y(!1),children:"关闭"},"close")],width:700,children:K&&K.customerId&&K.assessmentDate&&(0,r.jsxs)("div",{children:[(0,r.jsxs)(k.Z,{column:2,bordered:!0,children:[(0,r.jsx)(k.Z.Item,{label:"客户ID",children:K.customerId}),(0,r.jsx)(k.Z.Item,{label:"评估日期",children:K.assessmentDate}),(0,r.jsx)(k.Z.Item,{label:"信用等级",children:et(K.creditLevel)}),(0,r.jsx)(k.Z.Item,{label:"风险等级",children:er(K.riskLevel)}),(0,r.jsx)(k.Z.Item,{label:"总评分",children:(0,r.jsxs)("span",{style:{fontSize:"18px",fontWeight:"bold",color:ei(K.totalScore,100)},children:[K.totalScore,"分"]})}),(0,r.jsx)(k.Z.Item,{label:"评估人",children:K.assessor}),(0,r.jsx)(k.Z.Item,{label:"创建时间",span:2,children:new Date(K.createdAt).toLocaleString()}),(0,r.jsx)(k.Z.Item,{label:"备注",span:2,children:K.remark||"无"})]}),(0,r.jsxs)("div",{style:{marginTop:24},children:[(0,r.jsx)("h4",{children:"评分详情"}),(0,r.jsxs)(m.Z,{gutter:16,children:[(0,r.jsx)(j.Z,{span:8,children:(0,r.jsxs)(Z.Z,{size:"small",children:[(0,r.jsx)(f.Z,{title:"历史付款",value:K.paymentHistoryScore,suffix:"/50",valueStyle:{color:ei(K.paymentHistoryScore,50)}}),(0,r.jsx)(n.Z,{percent:K.paymentHistoryScore/50*100,strokeColor:ei(K.paymentHistoryScore,50),showInfo:!1})]})}),(0,r.jsx)(j.Z,{span:8,children:(0,r.jsxs)(Z.Z,{size:"small",children:[(0,r.jsx)(f.Z,{title:"订单规模",value:K.orderScaleScore,suffix:"/30",valueStyle:{color:ei(K.orderScaleScore,30)}}),(0,r.jsx)(n.Z,{percent:K.orderScaleScore/30*100,strokeColor:ei(K.orderScaleScore,30),showInfo:!1})]})}),(0,r.jsx)(j.Z,{span:8,children:(0,r.jsxs)(Z.Z,{size:"small",children:[(0,r.jsx)(f.Z,{title:"产品单价",value:K.productPriceScore,suffix:"/20",valueStyle:{color:ei(K.productPriceScore,20)}}),(0,r.jsx)(n.Z,{percent:K.productPriceScore/20*100,strokeColor:ei(K.productPriceScore,20),showInfo:!1})]})})]})]}),(0,r.jsxs)("div",{style:{marginTop:24},children:[(0,r.jsx)("h4",{children:"风险管控建议"}),(0,r.jsx)(S.Z,{message:"low"===K.riskLevel?"低风险客户":"medium"===K.riskLevel?"中风险客户":"高风险客户",description:"low"===K.riskLevel?"建议：正常交易，可适当提高信用额度":"medium"===K.riskLevel?"建议：加强跟踪，控制信用额度，缩短付款周期":"建议：严格控制信用额度，要求预付款或担保，降低排单优先级",type:"low"===K.riskLevel?"success":"medium"===K.riskLevel?"warning":"error",showIcon:!0})]})]})})]})}}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,1157,4863,8236,30,9617,7743,2971,4938,1744],function(){return e(e.s=76562)}),_N_E=e.O()}]);