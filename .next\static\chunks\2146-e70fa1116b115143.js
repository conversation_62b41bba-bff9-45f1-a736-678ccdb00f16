"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2146],{4e4:function(e,t,a){a.d(t,{Z:function(){return l}});var n=a(13428),r=a(2265),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 385.6a446.7 446.7 0 00-96-142.4 446.7 446.7 0 00-142.4-96C631.1 123.8 572.5 112 512 112s-119.1 11.8-174.4 35.2a446.7 446.7 0 00-142.4 96 446.7 446.7 0 00-96 142.4C75.8 440.9 64 499.5 64 560c0 132.7 58.3 257.7 159.9 343.1l1.7 1.4c5.8 4.8 13.1 7.5 20.6 7.5h531.7c7.5 0 14.8-2.7 20.6-7.5l1.7-1.4C901.7 817.7 960 692.7 960 560c0-60.5-11.9-119.1-35.2-174.4zM761.4 836H262.6A371.12 371.12 0 01140 560c0-99.4 38.7-192.8 109-263 70.3-70.3 163.7-109 263-109 99.4 0 192.8 38.7 263 109 70.3 70.3 109 163.7 109 263 0 105.6-44.5 205.5-122.6 276zM623.5 421.5a8.03 8.03 0 00-11.3 0L527.7 506c-18.7-5-39.4-.2-54.1 14.5a55.95 55.95 0 000 79.2 55.95 55.95 0 0079.2 0 55.87 55.87 0 0014.5-54.1l84.5-84.5c3.1-3.1 3.1-8.2 0-11.3l-28.3-28.3zM490 320h44c4.4 0 8-3.6 8-8v-80c0-4.4-3.6-8-8-8h-44c-4.4 0-8 3.6-8 8v80c0 4.4 3.6 8 8 8zm260 218v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8h-80c-4.4 0-8 3.6-8 8zm12.7-197.2l-31.1-31.1a8.03 8.03 0 00-11.3 0l-56.6 56.6a8.03 8.03 0 000 11.3l31.1 31.1c3.1 3.1 8.2 3.1 11.3 0l56.6-56.6c3.1-3.1 3.1-8.2 0-11.3zm-458.6-31.1a8.03 8.03 0 00-11.3 0l-31.1 31.1a8.03 8.03 0 000 11.3l56.6 56.6c3.1 3.1 8.2 3.1 11.3 0l31.1-31.1c3.1-3.1 3.1-8.2 0-11.3l-56.6-56.6zM262 530h-80c-4.4 0-8 3.6-8 8v44c0 4.4 3.6 8 8 8h80c4.4 0 8-3.6 8-8v-44c0-4.4-3.6-8-8-8z"}}]},name:"dashboard",theme:"outlined"},o=a(46614),l=r.forwardRef(function(e,t){return r.createElement(o.Z,(0,n.Z)({},e,{ref:t,icon:c}))})},84326:function(e,t,a){a.d(t,{Z:function(){return l}});var n=a(13428),r=a(2265),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M824.2 699.9a301.55 301.55 0 00-86.4-60.4C783.1 602.8 812 546.8 812 484c0-110.8-92.4-201.7-203.2-200-109.1 1.7-197 90.6-197 200 0 62.8 29 118.8 74.2 155.5a300.95 300.95 0 00-86.4 60.4C345 754.6 314 826.8 312 903.8a8 8 0 008 8.2h56c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5A226.62 226.62 0 01612 684c60.9 0 118.2 23.7 161.3 66.8C814.5 792 838 846.3 840 904.3c.1 4.3 3.7 7.7 8 7.7h56a8 8 0 008-8.2c-2-77-33-149.2-87.8-203.9zM612 612c-34.2 0-66.4-13.3-90.5-37.5a126.86 126.86 0 01-37.5-91.8c.3-32.8 13.4-64.5 36.3-88 24-24.6 56.1-38.3 90.4-38.7 33.9-.3 66.8 12.9 91 36.6 24.8 24.3 38.4 56.8 38.4 91.4 0 34.2-13.3 66.3-37.5 90.5A127.3 127.3 0 01612 612zM361.5 510.4c-.9-8.7-1.4-17.5-1.4-26.4 0-15.9 1.5-31.4 4.3-46.5.7-3.6-1.2-7.3-4.5-8.8-13.6-6.1-26.1-14.5-36.9-25.1a127.54 127.54 0 01-38.7-95.4c.9-32.1 13.8-62.6 36.3-85.6 24.7-25.3 57.9-39.1 93.2-38.7 31.9.3 62.7 12.6 86 34.4 7.9 7.4 14.7 15.6 20.4 24.4 2 3.1 5.9 4.4 9.3 3.2 17.6-6.1 36.2-10.4 55.3-12.4 5.6-.6 8.8-6.6 6.3-11.6-32.5-64.3-98.9-108.7-175.7-109.9-110.9-1.7-203.3 89.2-203.3 199.9 0 62.8 28.9 118.8 74.2 155.5-31.8 14.7-61.1 35-86.5 60.4-54.8 54.7-85.8 126.9-87.8 204a8 8 0 008 8.2h56.1c4.3 0 7.9-3.4 8-7.7 1.9-58 25.4-112.3 66.7-153.5 29.4-29.4 65.4-49.8 104.7-59.7 3.9-1 6.5-4.7 6-8.7z"}}]},name:"team",theme:"outlined"},o=a(46614),l=r.forwardRef(function(e,t){return r.createElement(o.Z,(0,n.Z)({},e,{ref:t,icon:c}))})},6371:function(e,t,a){a.d(t,{Z:function(){return l}});var n=a(13428),r=a(2265),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"}}]},name:"user",theme:"outlined"},o=a(46614),l=r.forwardRef(function(e,t){return r.createElement(o.Z,(0,n.Z)({},e,{ref:t,icon:c}))})},37397:function(e,t,a){a.d(t,{Z:function(){return w}});var n=a(2265),r=a(42744),c=a.n(r),o=a(11288),l=a(17146),s=a(43313),i=a(57499),u=a(92935),d=a(10693),f=a(65471);let p=n.createContext({});var g=a(58489),v=a(11303),m=a(78387),h=a(12711);let b=e=>{let{antCls:t,componentCls:a,iconCls:n,avatarBg:r,avatarColor:c,containerSize:o,containerSizeLG:l,containerSizeSM:s,textFontSize:i,textFontSizeLG:u,textFontSizeSM:d,borderRadius:f,borderRadiusLG:p,borderRadiusSM:m,lineWidth:h,lineType:b}=e,y=(e,t,r)=>({width:e,height:e,borderRadius:"50%",["&".concat(a,"-square")]:{borderRadius:r},["&".concat(a,"-icon")]:{fontSize:t,["> ".concat(n)]:{margin:0}}});return{[a]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,v.Wf)(e)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:c,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:r,border:"".concat((0,g.bf)(h)," ").concat(b," transparent"),"&-image":{background:"transparent"},["".concat(t,"-image-img")]:{display:"block"}}),y(o,i,f)),{"&-lg":Object.assign({},y(l,u,p)),"&-sm":Object.assign({},y(s,d,m)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},y=e=>{let{componentCls:t,groupBorderColor:a,groupOverlapping:n,groupSpace:r}=e;return{["".concat(t,"-group")]:{display:"inline-flex",[t]:{borderColor:a},"> *:not(:first-child)":{marginInlineStart:n}},["".concat(t,"-group-popover")]:{["".concat(t," + ").concat(t)]:{marginInlineStart:r}}}};var z=(0,m.I$)("Avatar",e=>{let{colorTextLightSolid:t,colorTextPlaceholder:a}=e,n=(0,h.IX)(e,{avatarBg:a,avatarColor:t});return[b(n),y(n)]},e=>{let{controlHeight:t,controlHeightLG:a,controlHeightSM:n,fontSize:r,fontSizeLG:c,fontSizeXL:o,fontSizeHeading3:l,marginXS:s,marginXXS:i,colorBorderBg:u}=e;return{containerSize:t,containerSizeLG:a,containerSizeSM:n,textFontSize:Math.round((c+o)/2),textFontSizeLG:l,textFontSizeSM:r,groupSpace:i,groupOverlapping:-s,groupBorderColor:u}}),O=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(a[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a};let E=n.forwardRef((e,t)=>{let a;let{prefixCls:r,shape:g,size:v,src:m,srcSet:h,icon:b,className:y,rootClassName:E,style:S,alt:j,draggable:x,children:C,crossOrigin:w,gap:Z=4,onError:M}=e,k=O(e,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","style","alt","draggable","children","crossOrigin","gap","onError"]),[N,R]=n.useState(1),[A,I]=n.useState(!1),[_,B]=n.useState(!0),F=n.useRef(null),P=n.useRef(null),W=(0,l.sQ)(t,F),{getPrefixCls:G,avatar:L}=n.useContext(i.E_),T=n.useContext(p),H=()=>{if(!P.current||!F.current)return;let e=P.current.offsetWidth,t=F.current.offsetWidth;0!==e&&0!==t&&2*Z<t&&R(t-2*Z<e?(t-2*Z)/e:1)};n.useEffect(()=>{I(!0)},[]),n.useEffect(()=>{B(!0),R(1)},[m]),n.useEffect(H,[Z]);let q=(0,d.Z)(e=>{var t,a;return null!==(a=null!==(t=null!=v?v:null==T?void 0:T.size)&&void 0!==t?t:e)&&void 0!==a?a:"default"}),Q=Object.keys("object"==typeof q&&q||{}).some(e=>["xs","sm","md","lg","xl","xxl"].includes(e)),V=(0,f.Z)(Q),X=n.useMemo(()=>{if("object"!=typeof q)return{};let e=q[s.c4.find(e=>V[e])];return e?{width:e,height:e,fontSize:e&&(b||C)?e/2:18}:{}},[V,q]),$=G("avatar",r),D=(0,u.Z)($),[J,K,U]=z($,D),Y=c()({["".concat($,"-lg")]:"large"===q,["".concat($,"-sm")]:"small"===q}),ee=n.isValidElement(m),et=g||(null==T?void 0:T.shape)||"circle",ea=c()($,Y,null==L?void 0:L.className,"".concat($,"-").concat(et),{["".concat($,"-image")]:ee||m&&_,["".concat($,"-icon")]:!!b},U,D,y,E,K),en="number"==typeof q?{width:q,height:q,fontSize:b?q/2:18}:{};if("string"==typeof m&&_)a=n.createElement("img",{src:m,draggable:x,srcSet:h,onError:()=>{!1!==(null==M?void 0:M())&&B(!1)},alt:j,crossOrigin:w});else if(ee)a=m;else if(b)a=b;else if(A||1!==N){let e="scale(".concat(N,")");a=n.createElement(o.Z,{onResize:H},n.createElement("span",{className:"".concat($,"-string"),ref:P,style:Object.assign({},{msTransform:e,WebkitTransform:e,transform:e})},C))}else a=n.createElement("span",{className:"".concat($,"-string"),style:{opacity:0},ref:P},C);return J(n.createElement("span",Object.assign({},k,{style:Object.assign(Object.assign(Object.assign(Object.assign({},en),X),null==L?void 0:L.style),S),className:ea,ref:W}),a))});var S=a(79173),j=a(65823),x=a(97676);let C=e=>{let{size:t,shape:a}=n.useContext(p),r=n.useMemo(()=>({size:e.size||t,shape:e.shape||a}),[e.size,e.shape,t,a]);return n.createElement(p.Provider,{value:r},e.children)};E.Group=e=>{var t,a,r,o;let{getPrefixCls:l,direction:s}=n.useContext(i.E_),{prefixCls:d,className:f,rootClassName:p,style:g,maxCount:v,maxStyle:m,size:h,shape:b,maxPopoverPlacement:y,maxPopoverTrigger:O,children:w,max:Z}=e,M=l("avatar",d),k="".concat(M,"-group"),N=(0,u.Z)(M),[R,A,I]=z(M,N),_=c()(k,{["".concat(k,"-rtl")]:"rtl"===s},I,N,f,p,A),B=(0,S.Z)(w).map((e,t)=>(0,j.Tm)(e,{key:"avatar-key-".concat(t)})),F=(null==Z?void 0:Z.count)||v,P=B.length;if(F&&F<P){let e=B.slice(0,F),l=B.slice(F,P),s=(null==Z?void 0:Z.style)||m,i=(null===(t=null==Z?void 0:Z.popover)||void 0===t?void 0:t.trigger)||O||"hover",u=(null===(a=null==Z?void 0:Z.popover)||void 0===a?void 0:a.placement)||y||"top",d=Object.assign(Object.assign({content:l},null==Z?void 0:Z.popover),{classNames:{root:c()("".concat(k,"-popover"),null===(o=null===(r=null==Z?void 0:Z.popover)||void 0===r?void 0:r.classNames)||void 0===o?void 0:o.root)},placement:u,trigger:i});return e.push(n.createElement(x.Z,Object.assign({key:"avatar-popover-key",destroyOnHidden:!0},d),n.createElement(E,{style:s},"+".concat(P-F)))),R(n.createElement(C,{shape:b,size:h},n.createElement("div",{className:_,style:g},e)))}return R(n.createElement(C,{shape:b,size:h},n.createElement("div",{className:_,style:g},B)))};var w=E}}]);