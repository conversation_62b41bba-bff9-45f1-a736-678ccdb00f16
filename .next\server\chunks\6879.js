"use strict";exports.id=6879,exports.ids=[6879],exports.modules={16879:(e,t,n)=>{n.d(t,{Z:()=>B});var a,r=n(3729),o=n.n(r),i=n(34132),l=n.n(i),s=n(65651),u=n(22363),c=n(65830),d=n(72375),f=n(93727),p=n(12403),m=n(93903),v=n(4912),g=n(56095),x=n(80595),h=n(82841),b=n(70242),w=n(17981),y=n(42534),S=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing","word-break","white-space"],z={},C=["prefixCls","defaultValue","value","autoSize","onResize","className","style","disabled","onChange","onInternalAutoSize"],Z=r.forwardRef(function(e,t){var n=e.prefixCls,o=e.defaultValue,i=e.value,d=e.autoSize,m=e.onResize,v=e.className,g=e.style,Z=e.disabled,E=e.onChange,$=(e.onInternalAutoSize,(0,p.Z)(e,C)),A=(0,x.Z)(o,{value:i,postState:function(e){return null!=e?e:""}}),I=(0,f.Z)(A,2),R=I[0],N=I[1],O=r.useRef();r.useImperativeHandle(t,function(){return{textArea:O.current}});var j=r.useMemo(function(){return d&&"object"===(0,h.Z)(d)?[d.minRows,d.maxRows]:[]},[d]),H=(0,f.Z)(j,2),F=H[0],P=H[1],T=!!d,V=function(){try{if(document.activeElement===O.current){var e=O.current,t=e.selectionStart,n=e.selectionEnd,a=e.scrollTop;O.current.setSelectionRange(t,n),O.current.scrollTop=a}}catch(e){}},D=r.useState(2),M=(0,f.Z)(D,2),k=M[0],L=M[1],W=r.useState(),B=(0,f.Z)(W,2),q=B[0],K=B[1],X=function(){L(0)};(0,w.Z)(function(){T&&X()},[i,F,P,T]),(0,w.Z)(function(){if(0===k)L(1);else if(1===k){var e=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;a||((a=document.createElement("textarea")).setAttribute("tab-index","-1"),a.setAttribute("aria-hidden","true"),a.setAttribute("name","hiddenTextarea"),document.body.appendChild(a)),e.getAttribute("wrap")?a.setAttribute("wrap",e.getAttribute("wrap")):a.removeAttribute("wrap");var i=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(t&&z[n])return z[n];var a=window.getComputedStyle(e),r=a.getPropertyValue("box-sizing")||a.getPropertyValue("-moz-box-sizing")||a.getPropertyValue("-webkit-box-sizing"),o=parseFloat(a.getPropertyValue("padding-bottom"))+parseFloat(a.getPropertyValue("padding-top")),i=parseFloat(a.getPropertyValue("border-bottom-width"))+parseFloat(a.getPropertyValue("border-top-width")),l={sizingStyle:S.map(function(e){return"".concat(e,":").concat(a.getPropertyValue(e))}).join(";"),paddingSize:o,borderSize:i,boxSizing:r};return t&&n&&(z[n]=l),l}(e,n),l=i.paddingSize,s=i.borderSize,u=i.boxSizing,c=i.sizingStyle;a.setAttribute("style","".concat(c,";").concat("\n  min-height:0 !important;\n  max-height:none !important;\n  height:0 !important;\n  visibility:hidden !important;\n  overflow:hidden !important;\n  position:absolute !important;\n  z-index:-1000 !important;\n  top:0 !important;\n  right:0 !important;\n  pointer-events: none !important;\n")),a.value=e.value||e.placeholder||"";var d=void 0,f=void 0,p=a.scrollHeight;if("border-box"===u?p+=s:"content-box"===u&&(p-=l),null!==r||null!==o){a.value=" ";var m=a.scrollHeight-l;null!==r&&(d=m*r,"border-box"===u&&(d=d+l+s),p=Math.max(d,p)),null!==o&&(f=m*o,"border-box"===u&&(f=f+l+s),t=p>f?"":"hidden",p=Math.min(f,p))}var v={height:p,overflowY:t,resize:"none"};return d&&(v.minHeight=d),f&&(v.maxHeight=f),v}(O.current,!1,F,P);L(2),K(e)}else V()},[k]);var J=r.useRef(),Y=function(){y.Z.cancel(J.current)};r.useEffect(function(){return Y},[]);var Q=(0,c.Z)((0,c.Z)({},g),T?q:null);return(0===k||1===k)&&(Q.overflowY="hidden",Q.overflowX="hidden"),r.createElement(b.Z,{onResize:function(e){2===k&&(null==m||m(e),d&&(Y(),J.current=(0,y.Z)(function(){X()})))},disabled:!(d||m)},r.createElement("textarea",(0,s.Z)({},$,{ref:O,style:Q,className:l()(n,v,(0,u.Z)({},"".concat(n,"-disabled"),Z)),disabled:Z,value:R,onChange:function(e){N(e.target.value),null==E||E(e)}})))}),E=["defaultValue","value","onFocus","onBlur","onChange","allowClear","maxLength","onCompositionStart","onCompositionEnd","suffix","prefixCls","showCount","count","className","style","disabled","hidden","classNames","styles","onResize","onClear","onPressEnter","readOnly","autoSize","onKeyDown"],$=o().forwardRef(function(e,t){var n,a,i=e.defaultValue,h=e.value,b=e.onFocus,w=e.onBlur,y=e.onChange,S=e.allowClear,z=e.maxLength,C=e.onCompositionStart,$=e.onCompositionEnd,A=e.suffix,I=e.prefixCls,R=void 0===I?"rc-textarea":I,N=e.showCount,O=e.count,j=e.className,H=e.style,F=e.disabled,P=e.hidden,T=e.classNames,V=e.styles,D=e.onResize,M=e.onClear,k=e.onPressEnter,L=e.readOnly,W=e.autoSize,B=e.onKeyDown,q=(0,p.Z)(e,E),K=(0,x.Z)(i,{value:h,defaultValue:i}),X=(0,f.Z)(K,2),J=X[0],Y=X[1],Q=null==J?"":String(J),G=o().useState(!1),U=(0,f.Z)(G,2),_=U[0],ee=U[1],et=o().useRef(!1),en=o().useState(null),ea=(0,f.Z)(en,2),er=ea[0],eo=ea[1],ei=(0,r.useRef)(null),el=(0,r.useRef)(null),es=function(){var e;return null===(e=el.current)||void 0===e?void 0:e.textArea},eu=function(){es().focus()};(0,r.useImperativeHandle)(t,function(){var e;return{resizableTextArea:el.current,focus:eu,blur:function(){es().blur()},nativeElement:(null===(e=ei.current)||void 0===e?void 0:e.nativeElement)||es()}}),(0,r.useEffect)(function(){ee(function(e){return!F&&e})},[F]);var ec=o().useState(null),ed=(0,f.Z)(ec,2),ef=ed[0],ep=ed[1];o().useEffect(function(){if(ef){var e;(e=es()).setSelectionRange.apply(e,(0,d.Z)(ef))}},[ef]);var em=(0,v.Z)(O,N),ev=null!==(n=em.max)&&void 0!==n?n:z,eg=Number(ev)>0,ex=em.strategy(Q),eh=!!ev&&ex>ev,eb=function(e,t){var n=t;!et.current&&em.exceedFormatter&&em.max&&em.strategy(t)>em.max&&(n=em.exceedFormatter(t,{max:em.max}),t!==n&&ep([es().selectionStart||0,es().selectionEnd||0])),Y(n),(0,g.rJ)(e.currentTarget,e,y,n)},ew=A;em.show&&(a=em.showFormatter?em.showFormatter({value:Q,count:ex,maxLength:ev}):"".concat(ex).concat(eg?" / ".concat(ev):""),ew=o().createElement(o().Fragment,null,ew,o().createElement("span",{className:l()("".concat(R,"-data-count"),null==T?void 0:T.count),style:null==V?void 0:V.count},a)));var ey=!W&&!N&&!S;return o().createElement(m.Q,{ref:ei,value:Q,allowClear:S,handleReset:function(e){Y(""),eu(),(0,g.rJ)(es(),e,y)},suffix:ew,prefixCls:R,classNames:(0,c.Z)((0,c.Z)({},T),{},{affixWrapper:l()(null==T?void 0:T.affixWrapper,(0,u.Z)((0,u.Z)({},"".concat(R,"-show-count"),N),"".concat(R,"-textarea-allow-clear"),S))}),disabled:F,focused:_,className:l()(j,eh&&"".concat(R,"-out-of-range")),style:(0,c.Z)((0,c.Z)({},H),er&&!ey?{height:"auto"}:{}),dataAttrs:{affixWrapper:{"data-count":"string"==typeof a?a:void 0}},hidden:P,readOnly:L,onClear:M},o().createElement(Z,(0,s.Z)({},q,{autoSize:W,maxLength:z,onKeyDown:function(e){"Enter"===e.key&&k&&k(e),null==B||B(e)},onChange:function(e){eb(e,e.target.value)},onFocus:function(e){ee(!0),null==b||b(e)},onBlur:function(e){ee(!1),null==w||w(e)},onCompositionStart:function(e){et.current=!0,null==C||C(e)},onCompositionEnd:function(e){et.current=!1,eb(e,e.currentTarget.value),null==$||$(e)},className:l()(null==T?void 0:T.textarea),style:(0,c.Z)((0,c.Z)({},null==V?void 0:V.textarea),{},{resize:null==H?void 0:H.resize}),disabled:F,prefixCls:R,onResize:function(e){var t;null==D||D(e),null!==(t=es())&&void 0!==t&&t.style.height&&eo(!0)},ref:el,readOnly:L})))}),A=n(60258),I=n(85969),R=n(84893),N=n(30681),O=n(13878),j=n(54527),H=n(30308),F=n(69109),P=n(71264),T=n(25654),V=n(13165),D=n(96373),M=n(67031);let k=e=>{let{componentCls:t,paddingLG:n}=e,a=`${t}-textarea`;return{[`textarea${t}`]:{maxWidth:"100%",height:"auto",minHeight:e.controlHeight,lineHeight:e.lineHeight,verticalAlign:"bottom",transition:`all ${e.motionDurationSlow}`,resize:"vertical",[`&${t}-mouse-active`]:{transition:`all ${e.motionDurationSlow}, height 0s, width 0s`}},[`${t}-textarea-affix-wrapper-resize-dirty`]:{width:"auto"},[a]:{position:"relative","&-show-count":{[`${t}-data-count`]:{position:"absolute",bottom:e.calc(e.fontSize).mul(e.lineHeight).mul(-1).equal(),insetInlineEnd:0,color:e.colorTextDescription,whiteSpace:"nowrap",pointerEvents:"none"}},[`
        &-allow-clear > ${t},
        &-affix-wrapper${a}-has-feedback ${t}
      `]:{paddingInlineEnd:n},[`&-affix-wrapper${t}-affix-wrapper`]:{padding:0,[`> textarea${t}`]:{fontSize:"inherit",border:"none",outline:"none",background:"transparent",minHeight:e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),"&:focus":{boxShadow:"none !important"}},[`${t}-suffix`]:{margin:0,"> *:not(:last-child)":{marginInline:0},[`${t}-clear-icon`]:{position:"absolute",insetInlineEnd:e.paddingInline,insetBlockStart:e.paddingXS},[`${a}-suffix`]:{position:"absolute",top:0,insetInlineEnd:e.paddingInline,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto",pointerEvents:"none"}}},[`&-affix-wrapper${t}-affix-wrapper-rtl`]:{[`${t}-suffix`]:{[`${t}-data-count`]:{direction:"ltr",insetInlineStart:0}}},[`&-affix-wrapper${t}-affix-wrapper-sm`]:{[`${t}-suffix`]:{[`${t}-clear-icon`]:{insetInlineEnd:e.paddingInlineSM}}}}}},L=(0,V.I$)(["Input","TextArea"],e=>[k((0,D.IX)(e,(0,M.e)(e)))],M.T,{resetFont:!1});var W=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)0>t.indexOf(a[r])&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]]);return n};let B=(0,r.forwardRef)((e,t)=>{var n;let{prefixCls:a,bordered:o=!0,size:i,disabled:s,status:u,allowClear:c,classNames:d,rootClassName:f,className:p,style:m,styles:v,variant:x,showCount:h,onMouseDown:b,onResize:w}=e,y=W(e,["prefixCls","bordered","size","disabled","status","allowClear","classNames","rootClassName","className","style","styles","variant","showCount","onMouseDown","onResize"]),{getPrefixCls:S,direction:z,allowClear:C,autoComplete:Z,className:E,style:V,classNames:D,styles:M}=(0,R.dj)("textArea"),k=r.useContext(N.Z),{status:B,hasFeedback:q,feedbackIcon:K}=r.useContext(H.aM),X=(0,I.F)(B,u),J=r.useRef(null);r.useImperativeHandle(t,()=>{var e;return{resizableTextArea:null===(e=J.current)||void 0===e?void 0:e.resizableTextArea,focus:e=>{var t,n;(0,g.nH)(null===(n=null===(t=J.current)||void 0===t?void 0:t.resizableTextArea)||void 0===n?void 0:n.textArea,e)},blur:()=>{var e;return null===(e=J.current)||void 0===e?void 0:e.blur()}}});let Y=S("input",a),Q=(0,O.Z)(Y),[G,U,_]=(0,T.TI)(Y,f),[ee]=L(Y,Q),{compactSize:et,compactItemClassnames:en}=(0,P.ri)(Y,z),ea=(0,j.Z)(e=>{var t;return null!==(t=null!=i?i:et)&&void 0!==t?t:e}),[er,eo]=(0,F.Z)("textArea",x,o),ei=(0,A.Z)(null!=c?c:C),[el,es]=r.useState(!1),[eu,ec]=r.useState(!1);return G(ee(r.createElement($,Object.assign({autoComplete:Z},y,{style:Object.assign(Object.assign({},V),m),styles:Object.assign(Object.assign({},M),v),disabled:null!=s?s:k,allowClear:ei,className:l()(_,Q,p,f,en,E,eu&&`${Y}-textarea-affix-wrapper-resize-dirty`),classNames:Object.assign(Object.assign(Object.assign({},d),D),{textarea:l()({[`${Y}-sm`]:"small"===ea,[`${Y}-lg`]:"large"===ea},U,null==d?void 0:d.textarea,D.textarea,el&&`${Y}-mouse-active`),variant:l()({[`${Y}-${er}`]:eo},(0,I.Z)(Y,X)),affixWrapper:l()(`${Y}-textarea-affix-wrapper`,{[`${Y}-affix-wrapper-rtl`]:"rtl"===z,[`${Y}-affix-wrapper-sm`]:"small"===ea,[`${Y}-affix-wrapper-lg`]:"large"===ea,[`${Y}-textarea-show-count`]:h||(null===(n=e.count)||void 0===n?void 0:n.show)},U)}),prefixCls:Y,suffix:q&&r.createElement("span",{className:`${Y}-textarea-suffix`},K),showCount:h,ref:J,onResize:e=>{var t,n;if(null==w||w(e),el&&"function"==typeof getComputedStyle){let e=null===(n=null===(t=J.current)||void 0===t?void 0:t.nativeElement)||void 0===n?void 0:n.querySelector("textarea");e&&"both"===getComputedStyle(e).resize&&ec(!0)}},onMouseDown:e=>{es(!0),null==b||b(e);let t=()=>{es(!1),document.removeEventListener("mouseup",t)};document.addEventListener("mouseup",t)}}))))})}};