(()=>{var e={};e.id=4544,e.ids=[4544],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},2754:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var l=r(50482),a=r(69108),i=r(62563),n=r.n(i),s=r(68300),o={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>s[e]);r.d(t,o);let d=["",{children:["sales",{children:["delivery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49834)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\delivery\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,75497)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\delivery\\page.tsx"],u="/sales/delivery/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new l.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/sales/delivery/page",pathname:"/sales/delivery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},38365:(e,t,r)=>{Promise.resolve().then(r.bind(r,94929))},32646:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var l=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M251.2 387H320v68.8c0 1.8 1.8 3.2 4 3.2h48c2.2 0 4-1.4 4-3.3V387h68.8c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H376v-68.8c0-1.8-1.8-3.2-4-3.2h-48c-2.2 0-4 1.4-4 3.2V331h-68.8c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm328 0h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 265h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 104h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm-195.7-81l61.2-74.9c4.3-5.2.7-13.1-5.9-13.1H388c-2.3 0-4.5 1-5.9 2.9l-34 41.6-34-41.6a7.85 7.85 0 00-5.9-2.9h-50.9c-6.6 0-10.2 7.9-5.9 13.1l61.2 74.9-62.7 76.8c-4.4 5.2-.8 13.1 5.8 13.1h50.8c2.3 0 4.5-1 5.9-2.9l35.5-43.5 35.5 43.5c1.5 1.8 3.7 2.9 5.9 2.9h50.8c6.6 0 10.2-7.9 5.9-13.1L383.5 675zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-36 732H180V180h664v664z"}}]},name:"calculator",theme:"outlined"};var n=r(49809);let s=a.forwardRef(function(e,t){return a.createElement(n.Z,(0,l.Z)({},e,{ref:t,icon:i}))})},3745:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var l=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var n=r(49809);let s=a.forwardRef(function(e,t){return a.createElement(n.Z,(0,l.Z)({},e,{ref:t,icon:i}))})},35329:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var l=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"};var n=r(49809);let s=a.forwardRef(function(e,t){return a.createElement(n.Z,(0,l.Z)({},e,{ref:t,icon:i}))})},54649:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var l=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var n=r(49809);let s=a.forwardRef(function(e,t){return a.createElement(n.Z,(0,l.Z)({},e,{ref:t,icon:i}))})},89645:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var l=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var n=r(49809);let s=a.forwardRef(function(e,t){return a.createElement(n.Z,(0,l.Z)({},e,{ref:t,icon:i}))})},20316:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var l=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M136 384h56c4.4 0 8-3.6 8-8V200h176c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H196c-37.6 0-68 30.4-68 68v180c0 4.4 3.6 8 8 8zm512-184h176v176c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V196c0-37.6-30.4-68-68-68H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zM376 824H200V648c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v180c0 37.6 30.4 68 68 68h180c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm512-184h-56c-4.4 0-8 3.6-8 8v176H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h180c37.6 0 68-30.4 68-68V648c0-4.4-3.6-8-8-8zm16-164H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"scan",theme:"outlined"};var n=r(49809);let s=a.forwardRef(function(e,t){return a.createElement(n.Z,(0,l.Z)({},e,{ref:t,icon:i}))})},37372:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var l=r(65651),a=r(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var n=r(49809);let s=a.forwardRef(function(e,t){return a.createElement(n.Z,(0,l.Z)({},e,{ref:t,icon:i}))})},16407:(e,t,r)=>{"use strict";r.d(t,{ZP:()=>Z});var l=r(72375),a=r(3729),i=r.n(a),n=r(13743),s=r(84893),o=r(90263),d=r(73101),c=r(727),u=r(11779),m=r(47190);let h=null,p=e=>e(),x=[],g={};function f(){let{getContainer:e,duration:t,rtl:r,maxCount:l,top:a}=g,i=(null==e?void 0:e())||document.body;return{getContainer:()=>i,duration:t,rtl:r,maxCount:l,top:a}}let v=i().forwardRef((e,t)=>{let{messageConfig:r,sync:l}=e,{getPrefixCls:o}=(0,a.useContext)(s.E_),d=g.prefixCls||o("message"),c=(0,a.useContext)(n.J),[m,h]=(0,u.K)(Object.assign(Object.assign(Object.assign({},r),{prefixCls:d}),c.message));return i().useImperativeHandle(t,()=>{let e=Object.assign({},m);return Object.keys(e).forEach(t=>{e[t]=(...e)=>(l(),m[t].apply(m,e))}),{instance:e,sync:l}}),h}),y=i().forwardRef((e,t)=>{let[r,l]=i().useState(f),a=()=>{l(f)};i().useEffect(a,[]);let n=(0,o.w6)(),s=n.getRootPrefixCls(),d=n.getIconPrefixCls(),c=n.getTheme(),u=i().createElement(v,{ref:t,sync:a,messageConfig:r});return i().createElement(o.ZP,{prefixCls:s,iconPrefixCls:d,theme:c},n.holderRender?n.holderRender(u):u)});function j(){if(!h){let e=document.createDocumentFragment(),t={fragment:e};h=t,p(()=>{(0,d.q)()(i().createElement(y,{ref:e=>{let{instance:r,sync:l}=e||{};Promise.resolve().then(()=>{!t.instance&&r&&(t.instance=r,t.sync=l,j())})}}),e)});return}h.instance&&(x.forEach(e=>{let{type:t,skipped:r}=e;if(!r)switch(t){case"open":p(()=>{let t=h.instance.open(Object.assign(Object.assign({},g),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":p(()=>{null==h||h.instance.destroy(e.key)});break;default:p(()=>{var r;let a=(r=h.instance)[t].apply(r,(0,l.Z)(e.args));null==a||a.then(e.resolve),e.setCloseFn(a)})}}),x=[])}let b={open:function(e){let t=(0,m.J)(t=>{let r;let l={type:"open",config:e,resolve:t,setCloseFn:e=>{r=e}};return x.push(l),()=>{r?p(()=>{r()}):l.skipped=!0}});return j(),t},destroy:e=>{x.push({type:"destroy",key:e}),j()},config:function(e){g=Object.assign(Object.assign({},g),e),p(()=>{var e;null===(e=null==h?void 0:h.sync)||void 0===e||e.call(h)})},useMessage:u.Z,_InternalPanelDoNotUseOrYouWillBeFired:c.ZP};["success","info","warning","error","loading"].forEach(e=>{b[e]=(...t)=>(function(e,t){(0,o.w6)();let r=(0,m.J)(r=>{let l;let a={type:e,args:t,resolve:r,setCloseFn:e=>{l=e}};return x.push(a),()=>{l?p(()=>{l()}):a.skipped=!0}});return j(),r})(e,t)});let Z=b},18161:(e,t,r)=>{"use strict";r.d(t,{Z:()=>w});var l=r(3729),a=r(34132),i=r.n(a),n=r(84893),s=r(13878),o=r(92959),d=r(22989),c=r(13165),u=r(96373);let m=e=>{let{componentCls:t,calc:r}=e;return{[t]:Object.assign(Object.assign({},(0,d.Wf)(e)),{margin:0,padding:0,listStyle:"none",[`${t}-item`]:{position:"relative",margin:0,paddingBottom:e.itemPaddingBottom,fontSize:e.fontSize,listStyle:"none","&-tail":{position:"absolute",insetBlockStart:e.itemHeadSize,insetInlineStart:r(r(e.itemHeadSize).sub(e.tailWidth)).div(2).equal(),height:`calc(100% - ${(0,o.bf)(e.itemHeadSize)})`,borderInlineStart:`${(0,o.bf)(e.tailWidth)} ${e.lineType} ${e.tailColor}`},"&-pending":{[`${t}-item-head`]:{fontSize:e.fontSizeSM,backgroundColor:"transparent"},[`${t}-item-tail`]:{display:"none"}},"&-head":{position:"absolute",width:e.itemHeadSize,height:e.itemHeadSize,backgroundColor:e.dotBg,border:`${(0,o.bf)(e.dotBorderWidth)} ${e.lineType} transparent`,borderRadius:"50%","&-blue":{color:e.colorPrimary,borderColor:e.colorPrimary},"&-red":{color:e.colorError,borderColor:e.colorError},"&-green":{color:e.colorSuccess,borderColor:e.colorSuccess},"&-gray":{color:e.colorTextDisabled,borderColor:e.colorTextDisabled}},"&-head-custom":{position:"absolute",insetBlockStart:r(e.itemHeadSize).div(2).equal(),insetInlineStart:r(e.itemHeadSize).div(2).equal(),width:"auto",height:"auto",marginBlockStart:0,paddingBlock:e.customHeadPaddingVertical,lineHeight:1,textAlign:"center",border:0,borderRadius:0,transform:"translate(-50%, -50%)"},"&-content":{position:"relative",insetBlockStart:r(r(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.lineWidth).equal(),marginInlineStart:r(e.margin).add(e.itemHeadSize).equal(),marginInlineEnd:0,marginBlockStart:0,marginBlockEnd:0,wordBreak:"break-word"},"&-last":{[`> ${t}-item-tail`]:{display:"none"},[`> ${t}-item-content`]:{minHeight:r(e.controlHeightLG).mul(1.2).equal()}}},[`&${t}-alternate,
        &${t}-right,
        &${t}-label`]:{[`${t}-item`]:{"&-tail, &-head, &-head-custom":{insetInlineStart:"50%"},"&-head":{marginInlineStart:r(e.marginXXS).mul(-1).equal(),"&-custom":{marginInlineStart:r(e.tailWidth).div(2).equal()}},"&-left":{[`${t}-item-content`]:{insetInlineStart:`calc(50% - ${(0,o.bf)(e.marginXXS)})`,width:`calc(50% - ${(0,o.bf)(e.marginSM)})`,textAlign:"start"}},"&-right":{[`${t}-item-content`]:{width:`calc(50% - ${(0,o.bf)(e.marginSM)})`,margin:0,textAlign:"end"}}}},[`&${t}-right`]:{[`${t}-item-right`]:{[`${t}-item-tail,
            ${t}-item-head,
            ${t}-item-head-custom`]:{insetInlineStart:`calc(100% - ${(0,o.bf)(r(r(e.itemHeadSize).add(e.tailWidth)).div(2).equal())})`},[`${t}-item-content`]:{width:`calc(100% - ${(0,o.bf)(r(e.itemHeadSize).add(e.marginXS).equal())})`}}},[`&${t}-pending
        ${t}-item-last
        ${t}-item-tail`]:{display:"block",height:`calc(100% - ${(0,o.bf)(e.margin)})`,borderInlineStart:`${(0,o.bf)(e.tailWidth)} dotted ${e.tailColor}`},[`&${t}-reverse
        ${t}-item-last
        ${t}-item-tail`]:{display:"none"},[`&${t}-reverse ${t}-item-pending`]:{[`${t}-item-tail`]:{insetBlockStart:e.margin,display:"block",height:`calc(100% - ${(0,o.bf)(e.margin)})`,borderInlineStart:`${(0,o.bf)(e.tailWidth)} dotted ${e.tailColor}`},[`${t}-item-content`]:{minHeight:r(e.controlHeightLG).mul(1.2).equal()}},[`&${t}-label`]:{[`${t}-item-label`]:{position:"absolute",insetBlockStart:r(r(e.fontSize).mul(e.lineHeight).sub(e.fontSize)).mul(-1).add(e.tailWidth).equal(),width:`calc(50% - ${(0,o.bf)(e.marginSM)})`,textAlign:"end"},[`${t}-item-right`]:{[`${t}-item-label`]:{insetInlineStart:`calc(50% + ${(0,o.bf)(e.marginSM)})`,width:`calc(50% - ${(0,o.bf)(e.marginSM)})`,textAlign:"start"}}},"&-rtl":{direction:"rtl",[`${t}-item-head-custom`]:{transform:"translate(50%, -50%)"}}})}},h=(0,c.I$)("Timeline",e=>[m((0,u.IX)(e,{itemHeadSize:10,customHeadPaddingVertical:e.paddingXXS,paddingInlineEnd:2}))],e=>({tailColor:e.colorSplit,tailWidth:e.lineWidthBold,dotBorderWidth:e.wireframe?e.lineWidthBold:3*e.lineWidth,dotBg:e.colorBgContainer,itemPaddingBottom:1.25*e.padding}));var p=function(e,t){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(r[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(r[l[a]]=e[l[a]]);return r};let x=e=>{var{prefixCls:t,className:r,color:a="blue",dot:s,pending:o=!1,position:d,label:c,children:u}=e,m=p(e,["prefixCls","className","color","dot","pending","position","label","children"]);let{getPrefixCls:h}=l.useContext(n.E_),x=h("timeline",t),g=i()(`${x}-item`,{[`${x}-item-pending`]:o},r),f=/blue|red|green|gray/.test(a||"")?void 0:a,v=i()(`${x}-item-head`,{[`${x}-item-head-custom`]:!!s,[`${x}-item-head-${a}`]:!f});return l.createElement("li",Object.assign({},m,{className:g}),c&&l.createElement("div",{className:`${x}-item-label`},c),l.createElement("div",{className:`${x}-item-tail`}),l.createElement("div",{className:v,style:{borderColor:f,color:f}},s),l.createElement("div",{className:`${x}-item-content`},u))};var g=r(72375),f=r(31529),v=function(e,t){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(r[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(r[l[a]]=e[l[a]]);return r};let y=e=>{var{prefixCls:t,className:r,pending:a=!1,children:n,items:s,rootClassName:o,reverse:d=!1,direction:c,hashId:u,pendingDot:m,mode:h=""}=e,p=v(e,["prefixCls","className","pending","children","items","rootClassName","reverse","direction","hashId","pendingDot","mode"]);let y=(e,r)=>"alternate"===h?"right"===e?`${t}-item-right`:"left"===e?`${t}-item-left`:r%2==0?`${t}-item-left`:`${t}-item-right`:"left"===h?`${t}-item-left`:"right"===h||"right"===e?`${t}-item-right`:"",j=(0,g.Z)(s||[]);a&&j.push({pending:!!a,dot:m||l.createElement(f.Z,null),children:"boolean"==typeof a?null:a}),d&&j.reverse();let b=j.length,Z=`${t}-item-last`,w=j.filter(e=>!!e).map((e,t)=>{var r;let n=t===b-2?Z:"",s=t===b-1?Z:"",{className:o}=e,c=v(e,["className"]);return l.createElement(x,Object.assign({},c,{className:i()([o,!d&&a?n:s,y(null!==(r=null==e?void 0:e.position)&&void 0!==r?r:"",t)]),key:(null==e?void 0:e.key)||t}))}),S=j.some(e=>!!(null==e?void 0:e.label)),k=i()(t,{[`${t}-pending`]:!!a,[`${t}-reverse`]:!!d,[`${t}-${h}`]:!!h&&!S,[`${t}-label`]:S,[`${t}-rtl`]:"rtl"===c},r,o,u);return l.createElement("ol",Object.assign({},p,{className:k}),w)};var j=r(89299),b=function(e,t){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(r[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(r[l[a]]=e[l[a]]);return r};let Z=e=>{let{getPrefixCls:t,direction:r,timeline:a}=l.useContext(n.E_),{prefixCls:o,children:d,items:c,className:u,style:m}=e,p=b(e,["prefixCls","children","items","className","style"]),x=t("timeline",o),g=(0,s.Z)(x),[f,v,Z]=h(x,g),w=function(e,t){return e&&Array.isArray(e)?e:(0,j.Z)(t).map(e=>{var t,r;return Object.assign({children:null!==(r=null===(t=null==e?void 0:e.props)||void 0===t?void 0:t.children)&&void 0!==r?r:""},e.props)})}(c,d);return f(l.createElement(y,Object.assign({},p,{className:i()(null==a?void 0:a.className,u,Z,g),style:Object.assign(Object.assign({},null==a?void 0:a.style),m),prefixCls:x,direction:r,items:w,hashId:v})))};Z.Item=x;let w=Z},94929:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>X});var l=r(95344),a=r(3729),i=r(97854),n=r(284),s=r(30977),o=r(7618),d=r(90377),c=r(39470),u=r(87049),m=r(10707),h=r(11157),p=r(52788),x=r(16407),g=r(63724),f=r(27976),v=r(83984),y=r(43896),j=r(36527),b=r(16408),Z=r(14223),w=r(6025),S=r(67383),k=r(18161),I=r(35329),$=r(2778),C=r(65651);let N={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M608 192a32 32 0 0132 32v160h174.81a32 32 0 0126.68 14.33l113.19 170.84a32 32 0 015.32 17.68V672a32 32 0 01-32 32h-96c0 70.7-57.3 128-128 128s-128-57.3-128-128H384c0 70.7-57.3 128-128 128s-128-57.3-128-128H96a32 32 0 01-32-32V224a32 32 0 0132-32zM256 640a64 64 0 000 128h1.06A64 64 0 00256 640m448 0a64 64 0 000 128h1.06A64 64 0 00704 640M576 256H128v384h17.12c22.13-38.26 63.5-64 110.88-64 47.38 0 88.75 25.74 110.88 64H576zm221.63 192H640v145.12A127.43 127.43 0 01704 576c47.38 0 88.75 25.74 110.88 64H896v-43.52zM500 448a12 12 0 0112 12v40a12 12 0 01-12 12H332a12 12 0 01-12-12v-40a12 12 0 0112-12zM308 320a12 12 0 0112 12v40a12 12 0 01-12 12H204a12 12 0 01-12-12v-40a12 12 0 0112-12z"}}]},name:"truck",theme:"outlined"};var P=r(49809),z=a.forwardRef(function(e,t){return a.createElement(P.Z,(0,C.Z)({},e,{ref:t,icon:N}))}),O=r(3745),q=r(37372),H=r(46116),E=r(54649),D=r(20316),L=r(33537),T=r(70469),M=r(89645),A=r(32646),B=r(58535),V=r(23894),R=r(48869),W=r.n(R);let{Option:_}=i.default,{TextArea:F}=n.default,{Step:Q}=s.default,X=()=>{let[e,t]=(0,a.useState)([]),[r,C]=(0,a.useState)(!1),[N,P]=(0,a.useState)(!1),[R,X]=(0,a.useState)(!1),[U,G]=(0,a.useState)(!1),[Y,J]=(0,a.useState)(null),[K,ee]=(0,a.useState)(null),[et]=o.Z.useForm(),[er,el]=(0,a.useState)(""),[ea,ei]=(0,a.useState)(void 0);(0,a.useEffect)(()=>{t([{id:"1",deliveryNumber:"DN-2024-001",orderNumber:"SO-2024-001",customerId:"1",customerName:"上海包装材料有限公司",deliveryDate:"2024-01-25",deliveryAddress:"上海市浦东新区张江高科技园区",contactPerson:"张经理",contactPhone:"13800138001",driverName:"李师傅",vehicleNumber:"沪A12345",vehicleType:"厢式货车",totalWeight:2.5,totalVolume:15.6,loadingRate:92,status:"shipped",items:[{id:"1",deliveryNumber:"DN-2024-001",orderItemId:"1",productModelCode:"CP-202",productName:"圆形餐盘202mm",plannedQuantity:3e3,actualQuantity:3e3,unit:"个",batchNumber:"*********-001",warehouseLocation:"A01-01-01",palletNumber:"PLT-001",remainingQuantity:0}],warehouseTasks:[{id:"1",deliveryNumber:"DN-2024-001",taskType:"pick",productModelCode:"CP-202",quantity:3e3,fromLocation:"A01-01-01",recommendedLocation:"A01-01-01",priority:"high",status:"completed",operator:"仓管员A",completedAt:"2024-01-25T08:30:00"}],remark:"优先发货，客户急需",createdAt:"2024-01-24T16:00:00",updatedAt:"2024-01-25T10:00:00"},{id:"2",deliveryNumber:"DN-2024-002",orderNumber:"SO-2024-002",customerId:"2",customerName:"北京绿色包装科技公司",deliveryDate:"2024-01-28",deliveryAddress:"北京市朝阳区CBD商务区",contactPerson:"王总",contactPhone:"13900139002",driverName:"张师傅",vehicleNumber:"京B67890",vehicleType:"平板货车",totalWeight:4.2,totalVolume:28.5,loadingRate:85,status:"loading",items:[{id:"2",deliveryNumber:"DN-2024-002",orderItemId:"2",productModelCode:"CP-201",productName:"方形餐盒180mm",plannedQuantity:2e4,actualQuantity:19800,unit:"个",batchNumber:"*********-001",warehouseLocation:"B02-02-03",palletNumber:"PLT-002",remainingQuantity:200,remark:"有200个余量需退回"}],warehouseTasks:[{id:"2",deliveryNumber:"DN-2024-002",taskType:"pick",productModelCode:"CP-201",quantity:2e4,fromLocation:"B02-02-03",recommendedLocation:"B02-02-03",priority:"medium",status:"in_progress",operator:"仓管员B"}],createdAt:"2024-01-26T09:00:00",updatedAt:"2024-01-28T14:00:00"}])},[]);let en=e=>{let t={pending:{color:"orange",text:"待发货",icon:l.jsx(I.Z,{})},loading:{color:"blue",text:"装货中",icon:l.jsx($.Z,{})},shipped:{color:"green",text:"已发货",icon:l.jsx(z,{})},delivered:{color:"cyan",text:"已送达",icon:l.jsx(O.Z,{})},returned:{color:"red",text:"已退回",icon:l.jsx(q.Z,{})}}[e]||{color:"default",text:"未知",icon:null};return l.jsx(d.Z,{color:t.color,icon:t.icon,children:t.text})},es=e=>{let t={pending:{color:"default",text:"待处理"},in_progress:{color:"processing",text:"进行中"},completed:{color:"success",text:"已完成"}}[e]||{color:"default",text:"未知"};return l.jsx(c.Z,{status:t.color,text:t.text})},eo=e=>{let t={high:{color:"red",text:"高"},medium:{color:"orange",text:"中"},low:{color:"green",text:"低"}}[e]||{color:"default",text:"未知"};return l.jsx(d.Z,{color:t.color,children:t.text})},ed=(e,t,r)=>r>=90?{type:"success",message:"装载率优秀，配载合理"}:r>=80?{type:"warning",message:"装载率良好，可考虑合并其他订单"}:{type:"error",message:"装载率偏低，建议优化配载方案"},ec=e.filter(e=>{let t=!er||e.deliveryNumber.toLowerCase().includes(er.toLowerCase())||e.orderNumber.toLowerCase().includes(er.toLowerCase())||e.customerName.toLowerCase().includes(er.toLowerCase()),r=!ea||e.status===ea;return t&&r}),eu={total:e.length,pending:e.filter(e=>"pending"===e.status).length,loading:e.filter(e=>"loading"===e.status).length,shipped:e.filter(e=>"shipped"===e.status).length,averageLoadingRate:e.length>0?Math.round(e.reduce((e,t)=>e+t.loadingRate,0)/e.length):0},em=e=>{J(e),P(!0),et.setFieldsValue({...e,deliveryDate:W()(e.deliveryDate)})},eh=e=>{ee(e),X(!0)},ep=e=>{ee(e),G(!0)},ex=r=>{t(e.filter(e=>e.id!==r)),x.ZP.success("送货单删除成功")};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"page-header",children:[l.jsx("h1",{className:"page-title",children:"发货执行管理"}),l.jsx("p",{className:"page-description",children:"智能配载、仓库任务、出库扫码、余量管理"})]}),(0,l.jsxs)(g.Z,{gutter:[16,16],children:[l.jsx(f.Z,{xs:24,sm:6,children:l.jsx(v.Z,{children:l.jsx(y.Z,{title:"送货单总数",value:eu.total,suffix:"个",prefix:l.jsx(z,{})})})}),l.jsx(f.Z,{xs:24,sm:6,children:l.jsx(v.Z,{children:l.jsx(y.Z,{title:"待发货",value:eu.pending,suffix:"个",valueStyle:{color:"#faad14"}})})}),l.jsx(f.Z,{xs:24,sm:6,children:l.jsx(v.Z,{children:l.jsx(y.Z,{title:"装货中",value:eu.loading,suffix:"个",valueStyle:{color:"#1890ff"}})})}),l.jsx(f.Z,{xs:24,sm:6,children:l.jsx(v.Z,{children:l.jsx(y.Z,{title:"平均装载率",value:eu.averageLoadingRate,suffix:"%",valueStyle:{color:eu.averageLoadingRate>=85?"#3f8600":"#cf1322"}})})})]}),l.jsx(v.Z,{children:(0,l.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[l.jsx(n.default,{placeholder:"搜索送货单号、订单号或客户名称",prefix:l.jsx(T.Z,{}),value:er,onChange:e=>el(e.target.value),className:"w-full sm:w-64"}),(0,l.jsxs)(V.Z,{placeholder:"发货状态",value:ea,onChange:ei,className:"w-full sm:w-32",allowClear:!0,children:[l.jsx(_,{value:"pending",children:"待发货"}),l.jsx(_,{value:"loading",children:"装货中"}),l.jsx(_,{value:"shipped",children:"已发货"}),l.jsx(_,{value:"delivered",children:"已送达"}),l.jsx(_,{value:"returned",children:"已退回"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[l.jsx(h.ZP,{icon:l.jsx(M.Z,{}),children:"导出"}),l.jsx(h.ZP,{icon:l.jsx(A.Z,{}),children:"配载优化"}),l.jsx(h.ZP,{type:"primary",icon:l.jsx(B.Z,{}),onClick:()=>{J(null),P(!0),et.resetFields()},children:"新建送货单"})]})]})}),l.jsx(v.Z,{title:"送货单列表",children:l.jsx(j.Z,{columns:[{title:"送货单号",dataIndex:"deliveryNumber",key:"deliveryNumber",width:140,fixed:"left"},{title:"关联订单",dataIndex:"orderNumber",key:"orderNumber",width:140},{title:"客户名称",dataIndex:"customerName",key:"customerName",width:180,ellipsis:!0},{title:"发货日期",dataIndex:"deliveryDate",key:"deliveryDate",width:120,sorter:(e,t)=>new Date(e.deliveryDate).getTime()-new Date(t.deliveryDate).getTime()},{title:"车辆信息",key:"vehicleInfo",width:150,render:(e,t)=>(0,l.jsxs)("div",{children:[l.jsx("div",{children:t.vehicleNumber}),(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:[t.driverName," | ",t.vehicleType]})]})},{title:"装载信息",key:"loadingInfo",width:150,render:(e,t)=>{let r=ed(t.totalWeight,t.totalVolume,t.loadingRate);return(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{children:[t.totalWeight,"吨 | ",t.totalVolume,"m\xb3"]}),l.jsx("div",{children:l.jsx(u.Z,{percent:t.loadingRate,size:"small",status:"success"===r.type?"success":"warning"===r.type?"normal":"exception"})})]})}},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>en(e)},{title:"仓库任务",key:"taskStatus",width:120,render:(e,t)=>{let r=t.warehouseTasks.filter(e=>"completed"===e.status).length,a=t.warehouseTasks.length;return(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{children:[r,"/",a]}),l.jsx(u.Z,{percent:a>0?r/a*100:0,size:"small",showInfo:!1})]})}},{title:"操作",key:"action",width:250,fixed:"right",render:(e,t)=>(0,l.jsxs)(m.Z,{size:"small",children:[l.jsx(h.ZP,{type:"link",icon:l.jsx(H.Z,{}),onClick:()=>eh(t),children:"详情"}),l.jsx(h.ZP,{type:"link",icon:l.jsx(E.Z,{}),onClick:()=>em(t),children:"编辑"}),l.jsx(h.ZP,{type:"link",icon:l.jsx(D.Z,{}),onClick:()=>ep(t),children:"任务"}),l.jsx(p.Z,{title:"确定要删除这个送货单吗？",onConfirm:()=>ex(t.id),okText:"确定",cancelText:"取消",children:l.jsx(h.ZP,{type:"link",danger:!0,icon:l.jsx(L.Z,{}),children:"删除"})})]})}],dataSource:ec,rowKey:"id",loading:r,pagination:{total:ec.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`,pageSizeOptions:["10","20","50","100"]},scroll:{x:1400}})}),(0,l.jsxs)(b.Z,{title:Y?"编辑送货单":"新建送货单",open:N,onOk:()=>{et.validateFields().then(r=>{let l=new Date().toISOString(),a={...r,deliveryDate:r.deliveryDate.format("YYYY-MM-DD")};if(Y)t(e.map(e=>e.id===Y.id?{...e,...a,updatedAt:l}:e)),x.ZP.success("送货单更新成功");else{let r={id:Date.now().toString(),deliveryNumber:`DN-${new Date().getFullYear()}-${String(e.length+1).padStart(3,"0")}`,...a,items:[],warehouseTasks:[],createdAt:l,updatedAt:l};t([...e,r]),x.ZP.success("送货单创建成功")}P(!1),et.resetFields()})},onCancel:()=>{P(!1),et.resetFields()},width:800,okText:"确认",cancelText:"取消",children:[l.jsx(Z.Z,{message:"智能发货流程",description:"系统将自动推送仓库任务、推荐库位、计算装载率并生成配载建议",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,l.jsxs)(o.Z,{form:et,layout:"vertical",initialValues:{status:"pending"},children:[(0,l.jsxs)(g.Z,{gutter:16,children:[l.jsx(f.Z,{span:12,children:l.jsx(o.Z.Item,{name:"orderNumber",label:"关联订单号",rules:[{required:!0,message:"请输入关联订单号"}],children:l.jsx(n.default,{placeholder:"请输入关联订单号"})})}),l.jsx(f.Z,{span:12,children:l.jsx(o.Z.Item,{name:"deliveryDate",label:"发货日期",rules:[{required:!0,message:"请选择发货日期"}],children:l.jsx(n.default,{placeholder:"请选择发货日期"})})})]}),(0,l.jsxs)(g.Z,{gutter:16,children:[l.jsx(f.Z,{span:8,children:l.jsx(o.Z.Item,{name:"customerId",label:"客户ID",rules:[{required:!0,message:"请输入客户ID"}],children:l.jsx(n.default,{placeholder:"请输入客户ID"})})}),l.jsx(f.Z,{span:8,children:l.jsx(o.Z.Item,{name:"customerName",label:"客户名称",rules:[{required:!0,message:"请输入客户名称"}],children:l.jsx(n.default,{placeholder:"请输入客户名称"})})}),l.jsx(f.Z,{span:8,children:l.jsx(o.Z.Item,{name:"contactPerson",label:"联系人",rules:[{required:!0,message:"请输入联系人"}],children:l.jsx(n.default,{placeholder:"请输入联系人"})})})]}),l.jsx(o.Z.Item,{name:"deliveryAddress",label:"送货地址",rules:[{required:!0,message:"请输入送货地址"}],children:l.jsx(n.default,{placeholder:"请输入详细送货地址"})}),(0,l.jsxs)(g.Z,{gutter:16,children:[l.jsx(f.Z,{span:8,children:l.jsx(o.Z.Item,{name:"contactPhone",label:"联系电话",rules:[{required:!0,message:"请输入联系电话"}],children:l.jsx(n.default,{placeholder:"请输入联系电话"})})}),l.jsx(f.Z,{span:8,children:l.jsx(o.Z.Item,{name:"driverName",label:"司机姓名",rules:[{required:!0,message:"请输入司机姓名"}],children:l.jsx(n.default,{placeholder:"请输入司机姓名"})})}),l.jsx(f.Z,{span:8,children:l.jsx(o.Z.Item,{name:"vehicleNumber",label:"车牌号",rules:[{required:!0,message:"请输入车牌号"}],children:l.jsx(n.default,{placeholder:"请输入车牌号"})})})]}),(0,l.jsxs)(g.Z,{gutter:16,children:[l.jsx(f.Z,{span:8,children:l.jsx(o.Z.Item,{name:"vehicleType",label:"车型",rules:[{required:!0,message:"请选择车型"}],children:(0,l.jsxs)(i.default,{placeholder:"请选择车型",children:[l.jsx(_,{value:"厢式货车",children:"厢式货车"}),l.jsx(_,{value:"平板货车",children:"平板货车"}),l.jsx(_,{value:"集装箱车",children:"集装箱车"}),l.jsx(_,{value:"冷藏车",children:"冷藏车"})]})})}),l.jsx(f.Z,{span:8,children:l.jsx(o.Z.Item,{name:"totalWeight",label:"总重量(吨)",rules:[{required:!0,message:"请输入总重量"}],children:l.jsx(w.Z,{min:0,step:.1,style:{width:"100%"},placeholder:"请输入总重量"})})}),l.jsx(f.Z,{span:8,children:l.jsx(o.Z.Item,{name:"totalVolume",label:"总体积(m\xb3)",rules:[{required:!0,message:"请输入总体积"}],children:l.jsx(w.Z,{min:0,step:.1,style:{width:"100%"},placeholder:"请输入总体积"})})})]}),l.jsx(o.Z.Item,{name:"remark",label:"备注",children:l.jsx(F,{rows:3,placeholder:"请输入备注信息"})})]})]}),l.jsx(b.Z,{title:"送货单详情",open:R,onCancel:()=>X(!1),footer:[l.jsx(h.ZP,{onClick:()=>X(!1),children:"关闭"},"close")],width:1e3,children:K&&K.deliveryNumber&&K.orderNumber&&(0,l.jsxs)("div",{children:[(0,l.jsxs)(S.Z,{column:3,bordered:!0,children:[l.jsx(S.Z.Item,{label:"送货单号",children:K.deliveryNumber}),l.jsx(S.Z.Item,{label:"关联订单",children:K.orderNumber}),l.jsx(S.Z.Item,{label:"客户名称",children:K.customerName}),l.jsx(S.Z.Item,{label:"发货日期",children:K.deliveryDate}),l.jsx(S.Z.Item,{label:"联系人",children:K.contactPerson}),l.jsx(S.Z.Item,{label:"联系电话",children:K.contactPhone}),l.jsx(S.Z.Item,{label:"司机姓名",children:K.driverName}),l.jsx(S.Z.Item,{label:"车牌号",children:K.vehicleNumber}),l.jsx(S.Z.Item,{label:"车型",children:K.vehicleType}),(0,l.jsxs)(S.Z.Item,{label:"总重量",children:[K.totalWeight,"吨"]}),(0,l.jsxs)(S.Z.Item,{label:"总体积",children:[K.totalVolume,"m\xb3"]}),l.jsx(S.Z.Item,{label:"装载率",children:(0,l.jsxs)("span",{style:{color:K.loadingRate>=85?"#3f8600":"#cf1322",fontWeight:"bold"},children:[K.loadingRate,"%"]})}),l.jsx(S.Z.Item,{label:"状态",children:en(K.status)}),l.jsx(S.Z.Item,{label:"创建时间",children:new Date(K.createdAt).toLocaleString()}),l.jsx(S.Z.Item,{label:"更新时间",children:new Date(K.updatedAt).toLocaleString()}),l.jsx(S.Z.Item,{label:"送货地址",span:3,children:K.deliveryAddress}),l.jsx(S.Z.Item,{label:"备注",span:3,children:K.remark||"无"})]}),(0,l.jsxs)("div",{style:{marginTop:16},children:[l.jsx("h4",{children:"配载分析"}),(()=>{let e=ed(K.totalWeight,K.totalVolume,K.loadingRate);return l.jsx(Z.Z,{message:`装载率: ${K.loadingRate}%`,description:e.message,type:e.type,showIcon:!0})})()]}),(0,l.jsxs)("div",{style:{marginTop:16},children:[l.jsx("h4",{children:"发货明细"}),l.jsx(j.Z,{dataSource:K.items,rowKey:"id",pagination:!1,size:"small",columns:[{title:"产品型号",dataIndex:"productModelCode",key:"productModelCode",width:120},{title:"产品名称",dataIndex:"productName",key:"productName",width:150},{title:"计划数量",dataIndex:"plannedQuantity",key:"plannedQuantity",width:100,render:(e,t)=>`${e.toLocaleString()} ${t.unit}`},{title:"实装数量",dataIndex:"actualQuantity",key:"actualQuantity",width:100,render:(e,t)=>`${e.toLocaleString()} ${t.unit}`},{title:"余量",dataIndex:"remainingQuantity",key:"remainingQuantity",width:100,render:(e,t)=>(0,l.jsxs)("span",{style:{color:e>0?"#faad14":"#52c41a"},children:[e.toLocaleString()," ",t.unit]})},{title:"批次号",dataIndex:"batchNumber",key:"batchNumber",width:120},{title:"库位",dataIndex:"warehouseLocation",key:"warehouseLocation",width:100},{title:"托盘号",dataIndex:"palletNumber",key:"palletNumber",width:100}]})]})]})}),l.jsx(b.Z,{title:"仓库任务管理",open:U,onCancel:()=>G(!1),footer:[l.jsx(h.ZP,{onClick:()=>G(!1),children:"关闭"},"close")],width:900,children:K&&(0,l.jsxs)("div",{children:[l.jsx(Z.Z,{message:"任务执行流程",description:"拣货 → 包装 → 装车 → 发货，系统自动推荐最优库位和执行路径",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,l.jsxs)(s.default,{size:"small",style:{marginBottom:24},children:[l.jsx(Q,{title:"拣货",status:"finish",icon:l.jsx($.Z,{}),description:"从库位拣取商品"}),l.jsx(Q,{title:"包装",status:"process",icon:l.jsx(O.Z,{}),description:"商品包装和标识"}),l.jsx(Q,{title:"装车",status:"wait",icon:l.jsx(z,{}),description:"装载到运输车辆"}),l.jsx(Q,{title:"发货",status:"wait",icon:l.jsx(D.Z,{}),description:"扫码确认发货"})]}),l.jsx(j.Z,{dataSource:K.warehouseTasks,rowKey:"id",pagination:!1,size:"small",columns:[{title:"任务类型",dataIndex:"taskType",key:"taskType",width:100,render:e=>({pick:"拣货",pack:"包装",load:"装车",return:"退货"})[e]||e},{title:"产品型号",dataIndex:"productModelCode",key:"productModelCode",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,render:e=>e.toLocaleString()},{title:"源库位",dataIndex:"fromLocation",key:"fromLocation",width:100},{title:"推荐库位",dataIndex:"recommendedLocation",key:"recommendedLocation",width:100,render:e=>l.jsx(d.Z,{color:"blue",children:e})},{title:"优先级",dataIndex:"priority",key:"priority",width:80,render:e=>eo(e)},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>es(e)},{title:"操作员",dataIndex:"operator",key:"operator",width:100},{title:"完成时间",dataIndex:"completedAt",key:"completedAt",width:150,render:e=>e?new Date(e).toLocaleString():"-"}]}),(0,l.jsxs)("div",{style:{marginTop:16},children:[l.jsx("h4",{children:"任务执行时间线"}),l.jsx(k.Z,{children:K.warehouseTasks.map(e=>l.jsx(k.Z.Item,{color:"completed"===e.status?"green":"in_progress"===e.status?"blue":"gray",children:(0,l.jsxs)("div",{children:[l.jsx("div",{style:{fontWeight:"bold"},children:"pick"===e.taskType?"拣货任务":"pack"===e.taskType?"包装任务":"load"===e.taskType?"装车任务":"退货任务"}),(0,l.jsxs)("div",{children:["产品: ",e.productModelCode," | 数量: ",e.quantity.toLocaleString()]}),(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["库位: ",e.fromLocation," → ",e.recommendedLocation]}),(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["操作员: ",e.operator||"待分配"," | 状态: ","completed"===e.status?"已完成":"in_progress"===e.status?"进行中":"待处理"]}),e.completedAt&&(0,l.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["完成时间: ",new Date(e.completedAt).toLocaleString()]})]})},e.id))})]})]})})]})}},23894:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var l=r(95344),a=r(3729),i=r.n(a),n=r(97854);let s=({value:e,options:t,children:r,onChange:s,...o})=>{let d=(0,a.useRef)(null),c=(0,a.useRef)(void 0),u=i().useMemo(()=>{let e=new Set;return t&&Array.isArray(t)&&t.forEach(t=>e.add(t.value)),r&&i().Children.forEach(r,t=>{t?.props?.value!==void 0&&e.add(t.props.value)}),e},[t,r]),m=i().useMemo(()=>{if(null!=e&&""!==e&&("string"!=typeof e||""!==e.trim())){if(u.has(e))return c.current=e,e;console.warn("FormSelect: 值不在可用选项中",{value:e,availableValues:Array.from(u),placeholder:o.placeholder})}},[e,u,o.placeholder]);(0,a.useEffect)(()=>{""===e&&s&&setTimeout(()=>{s(void 0,void 0)},0)},[e,s]),(0,a.useEffect)(()=>{if(d.current){let e=d.current.nativeElement||d.current;if(e){let t=e.querySelector('input[type="hidden"]');t&&""===t.value&&s&&s(void 0,void 0)}}},[m,s]);let h=""===m?void 0:m;return l.jsx(n.default,{ref:d,...o,value:h,onChange:(e,t)=>{let r=""===e?void 0:e;console.log("FormSelect onChange:",{placeholder:o.placeholder,originalValue:e,safeValue:r,option:t,isEmptyString:""===e,isUndefined:void 0===e,isValidValue:u.has(e),availableValues:Array.from(u)}),s&&s(r,t)},options:t,children:r})}},49834:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let l=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\sales\delivery\page.tsx`),{__esModule:a,$$typeof:i}=l,n=l.default},75497:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var l=r(25036),a=r(38834);function i({children:e}){return l.jsx(a.Z,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),l=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,2345,7049,4441,7383,934,6274,996,6133],()=>r(2754));module.exports=l})();