(()=>{var e={};e.id=6851,e.ids=[6851],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},17281:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(50482),n=r(69108),s=r(62563),l=r.n(s),i=r(68300),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let c=["",{children:["sales",{children:["payments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,10571)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\payments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,75497)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\payments\\page.tsx"],u="/sales/payments/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/sales/payments/page",pathname:"/sales/payments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},96449:(e,t,r)=>{Promise.resolve().then(r.bind(r,12652))},3745:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(65651),n=r(3729);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var l=r(49809);let i=n.forwardRef(function(e,t){return n.createElement(l.Z,(0,a.Z)({},e,{ref:t,icon:s}))})},54649:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(65651),n=r(3729);let s={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var l=r(49809);let i=n.forwardRef(function(e,t){return n.createElement(l.Z,(0,a.Z)({},e,{ref:t,icon:s}))})},89645:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(65651),n=r(3729);let s={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var l=r(49809);let i=n.forwardRef(function(e,t){return n.createElement(l.Z,(0,a.Z)({},e,{ref:t,icon:s}))})},16407:(e,t,r)=>{"use strict";r.d(t,{ZP:()=>w});var a=r(72375),n=r(3729),s=r.n(n),l=r(13743),i=r(84893),o=r(90263),c=r(73101),d=r(727),u=r(11779),m=r(47190);let p=null,h=e=>e(),x=[],f={};function g(){let{getContainer:e,duration:t,rtl:r,maxCount:a,top:n}=f,s=(null==e?void 0:e())||document.body;return{getContainer:()=>s,duration:t,rtl:r,maxCount:a,top:n}}let y=s().forwardRef((e,t)=>{let{messageConfig:r,sync:a}=e,{getPrefixCls:o}=(0,n.useContext)(i.E_),c=f.prefixCls||o("message"),d=(0,n.useContext)(l.J),[m,p]=(0,u.K)(Object.assign(Object.assign(Object.assign({},r),{prefixCls:c}),d.message));return s().useImperativeHandle(t,()=>{let e=Object.assign({},m);return Object.keys(e).forEach(t=>{e[t]=(...e)=>(a(),m[t].apply(m,e))}),{instance:e,sync:a}}),p}),v=s().forwardRef((e,t)=>{let[r,a]=s().useState(g),n=()=>{a(g)};s().useEffect(n,[]);let l=(0,o.w6)(),i=l.getRootPrefixCls(),c=l.getIconPrefixCls(),d=l.getTheme(),u=s().createElement(y,{ref:t,sync:n,messageConfig:r});return s().createElement(o.ZP,{prefixCls:i,iconPrefixCls:c,theme:d},l.holderRender?l.holderRender(u):u)});function j(){if(!p){let e=document.createDocumentFragment(),t={fragment:e};p=t,h(()=>{(0,c.q)()(s().createElement(v,{ref:e=>{let{instance:r,sync:a}=e||{};Promise.resolve().then(()=>{!t.instance&&r&&(t.instance=r,t.sync=a,j())})}}),e)});return}p.instance&&(x.forEach(e=>{let{type:t,skipped:r}=e;if(!r)switch(t){case"open":h(()=>{let t=p.instance.open(Object.assign(Object.assign({},f),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":h(()=>{null==p||p.instance.destroy(e.key)});break;default:h(()=>{var r;let n=(r=p.instance)[t].apply(r,(0,a.Z)(e.args));null==n||n.then(e.resolve),e.setCloseFn(n)})}}),x=[])}let Z={open:function(e){let t=(0,m.J)(t=>{let r;let a={type:"open",config:e,resolve:t,setCloseFn:e=>{r=e}};return x.push(a),()=>{r?h(()=>{r()}):a.skipped=!0}});return j(),t},destroy:e=>{x.push({type:"destroy",key:e}),j()},config:function(e){f=Object.assign(Object.assign({},f),e),h(()=>{var e;null===(e=null==p?void 0:p.sync)||void 0===e||e.call(p)})},useMessage:u.Z,_InternalPanelDoNotUseOrYouWillBeFired:d.ZP};["success","info","warning","error","loading"].forEach(e=>{Z[e]=(...t)=>(function(e,t){(0,o.w6)();let r=(0,m.J)(r=>{let a;let n={type:e,args:t,resolve:r,setCloseFn:e=>{a=e}};return x.push(n),()=>{a?h(()=>{a()}):n.skipped=!0}});return j(),r})(e,t)});let w=Z},12652:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>V});var a=r(95344),n=r(3729),s=r(97854),l=r(284),i=r(97557),o=r(7618),c=r(90377),d=r(10707),u=r(11157),m=r(52788),p=r(16407),h=r(63724),x=r(27976),f=r(83984),g=r(43896),y=r(36527),v=r(16408),j=r(6025),Z=r(35329),w=r(3745),b=r(65651);let k={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M894 462c30.9 0 43.8-39.7 18.7-58L530.8 126.2a31.81 31.81 0 00-37.6 0L111.3 404c-25.1 18.2-12.2 58 18.8 58H192v374h-72c-4.4 0-8 3.6-8 8v52c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-52c0-4.4-3.6-8-8-8h-72V462h62zM512 196.7l271.1 197.2H240.9L512 196.7zM264 462h117v374H264V462zm189 0h117v374H453V462zm307 374H642V462h118v374z"}}]},name:"bank",theme:"outlined"};var A=r(49809),D=n.forwardRef(function(e,t){return n.createElement(A.Z,(0,b.Z)({},e,{ref:t,icon:k}))}),C=r(46116),N=r(54649),I=r(33537),S=r(70469),P=r(89645),L=r(58535),M=r(23894),_=r(48869),q=r.n(_);let{Option:T}=s.default,{TextArea:E}=l.default,{RangePicker:O}=i.default,V=()=>{let[e,t]=(0,n.useState)([]),[r,b]=(0,n.useState)([]),[k,A]=(0,n.useState)(!1),[_,O]=(0,n.useState)(!1),[V,z]=(0,n.useState)(!1),[R,F]=(0,n.useState)(!1),[$,H]=(0,n.useState)(null),[U,Y]=(0,n.useState)(null),[B,G]=(0,n.useState)(null),[J]=o.Z.useForm(),[W,K]=(0,n.useState)(""),[X,Q]=(0,n.useState)(void 0),[ee,et]=(0,n.useState)(void 0),[er,ea]=(0,n.useState)("payments");(0,n.useEffect)(()=>{t([{id:"1",paymentNumber:"PAY-2024-001",customerId:"1",customerName:"上海包装材料有限公司",paymentDate:"2024-01-26",paymentAmount:6e4,paymentMethod:"bank_transfer",bankAccount:"工商银行***1234",referenceNumber:"TXN20240126001",relatedOrders:["SO-2024-001"],relatedInvoices:["INV-2024-001"],status:"confirmed",remark:"部分付款，剩余款项月底结算",createdAt:"2024-01-26T10:00:00",updatedAt:"2024-01-26T10:00:00"},{id:"2",paymentNumber:"PAY-2024-002",customerId:"2",customerName:"北京绿色包装科技公司",paymentDate:"2024-01-28",paymentAmount:82490,paymentMethod:"acceptance",referenceNumber:"ACC20240128001",relatedOrders:["SO-2024-002"],relatedInvoices:["INV-2024-002"],status:"pending",remark:"承兑汇票，6个月到期",createdAt:"2024-01-28T14:00:00",updatedAt:"2024-01-28T14:00:00"}]),b([{id:"1",customerId:"1",customerName:"上海包装材料有限公司",orderNumber:"SO-2024-001",invoiceNumber:"INV-2024-001",invoiceDate:"2024-01-25",dueDate:"2024-02-25",originalAmount:120062.5,paidAmount:6e4,remainingAmount:60062.5,overdueDays:0,status:"normal",riskLevel:"low",followUpActions:[{id:"1",receivableId:"1",actionType:"call",actionDate:"2024-01-26",actionResult:"客户确认收到发票，承诺月底付清余款",nextActionDate:"2024-02-20",operator:"财务专员",remark:"客户信誉良好"}],createdAt:"2024-01-25T10:00:00",updatedAt:"2024-01-26T10:00:00"},{id:"2",customerId:"3",customerName:"广州环保餐具厂",orderNumber:"SO-2024-003",invoiceNumber:"INV-2024-003",invoiceDate:"2024-01-20",dueDate:"2024-01-30",originalAmount:45e3,paidAmount:0,remainingAmount:45e3,overdueDays:8,status:"overdue",riskLevel:"high",followUpActions:[{id:"2",receivableId:"2",actionType:"call",actionDate:"2024-01-31",actionResult:"客户电话无人接听",nextActionDate:"2024-02-02",operator:"财务专员"},{id:"3",receivableId:"2",actionType:"email",actionDate:"2024-02-01",actionResult:"发送催款邮件",nextActionDate:"2024-02-05",operator:"财务专员"}],createdAt:"2024-01-20T10:00:00",updatedAt:"2024-02-01T10:00:00"}])},[]);let en=e=>{let t={pending:{color:"orange",text:"待确认",icon:a.jsx(Z.Z,{})},confirmed:{color:"green",text:"已确认",icon:a.jsx(w.Z,{})},reconciled:{color:"cyan",text:"已对账",icon:a.jsx(D,{})}}[e]||{color:"default",text:"未知",icon:null};return a.jsx(c.Z,{color:t.color,icon:t.icon,children:t.text})},es=e=>{let t={cash:{color:"green",text:"现金"},bank_transfer:{color:"blue",text:"银行转账"},check:{color:"orange",text:"支票"},acceptance:{color:"purple",text:"承兑汇票"}}[e]||{color:"default",text:"其他"};return a.jsx(c.Z,{color:t.color,children:t.text})},el=e=>{let t={normal:{color:"green",text:"正常"},overdue:{color:"red",text:"逾期"},bad_debt:{color:"volcano",text:"坏账"}}[e]||{color:"default",text:"未知"};return a.jsx(c.Z,{color:t.color,children:t.text})},ei=e=>{let t={low:{color:"green",text:"低风险"},medium:{color:"orange",text:"中风险"},high:{color:"red",text:"高风险"}}[e]||{color:"default",text:"未知"};return a.jsx(c.Z,{color:t.color,children:t.text})},eo=[{title:"收款单号",dataIndex:"paymentNumber",key:"paymentNumber",width:140,fixed:"left"},{title:"客户名称",dataIndex:"customerName",key:"customerName",width:180,ellipsis:!0},{title:"收款日期",dataIndex:"paymentDate",key:"paymentDate",width:120,sorter:(e,t)=>new Date(e.paymentDate).getTime()-new Date(t.paymentDate).getTime()},{title:"收款金额",dataIndex:"paymentAmount",key:"paymentAmount",width:120,render:e=>(0,a.jsxs)("span",{style:{fontWeight:"bold",color:"#3f8600"},children:["\xa5",e.toLocaleString()]}),sorter:(e,t)=>e.paymentAmount-t.paymentAmount},{title:"付款方式",dataIndex:"paymentMethod",key:"paymentMethod",width:120,render:e=>es(e)},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>en(e)},{title:"关联订单",dataIndex:"relatedOrders",key:"relatedOrders",width:120,render:e=>a.jsx("div",{children:e.map(e=>a.jsx(c.Z,{children:e},e))})},{title:"操作",key:"action",width:200,fixed:"right",render:(e,t)=>(0,a.jsxs)(d.Z,{size:"small",children:[a.jsx(u.ZP,{type:"link",icon:a.jsx(C.Z,{}),onClick:()=>eh(t),children:"详情"}),a.jsx(u.ZP,{type:"link",icon:a.jsx(N.Z,{}),onClick:()=>ep(t),children:"编辑"}),a.jsx(m.Z,{title:"确定要删除这条收款记录吗？",onConfirm:()=>ef(t.id),okText:"确定",cancelText:"取消",children:a.jsx(u.ZP,{type:"link",danger:!0,icon:a.jsx(I.Z,{}),children:"删除"})})]})}],ec=e.filter(e=>{let t=!W||e.paymentNumber.toLowerCase().includes(W.toLowerCase())||e.customerName.toLowerCase().includes(W.toLowerCase()),r=!X||e.status===X,a=!ee||e.paymentMethod===ee;return t&&r&&a}),ed=r.filter(e=>!W||e.customerName.toLowerCase().includes(W.toLowerCase())||e.orderNumber.toLowerCase().includes(W.toLowerCase())||e.invoiceNumber.toLowerCase().includes(W.toLowerCase())),eu={total:e.length,totalAmount:e.reduce((e,t)=>e+t.paymentAmount,0),pending:e.filter(e=>"pending"===e.status).length,confirmed:e.filter(e=>"confirmed"===e.status).length},em={total:r.length,totalAmount:r.reduce((e,t)=>e+t.remainingAmount,0),overdue:r.filter(e=>"overdue"===e.status).length,highRisk:r.filter(e=>"high"===e.riskLevel).length},ep=e=>{H(e),O(!0),J.setFieldsValue({...e,paymentDate:q()(e.paymentDate)})},eh=e=>{Y(e),z(!0)},ex=e=>{G(e),F(!0)},ef=r=>{t(e.filter(e=>e.id!==r)),p.ZP.success("收款记录删除成功")};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"page-header",children:[a.jsx("h1",{className:"page-title",children:"收款管理"}),a.jsx("p",{className:"page-description",children:"收款记录管理、应收账款监控、账款跟踪时间轴"})]}),(0,a.jsxs)(h.Z,{gutter:[16,16],children:[a.jsx(x.Z,{xs:24,sm:6,children:a.jsx(f.Z,{children:a.jsx(g.Z,{title:"收款总额",value:eu.totalAmount,precision:0,prefix:"\xa5",valueStyle:{color:"#3f8600"}})})}),a.jsx(x.Z,{xs:24,sm:6,children:a.jsx(f.Z,{children:a.jsx(g.Z,{title:"应收余额",value:em.totalAmount,precision:0,prefix:"\xa5",valueStyle:{color:"#cf1322"}})})}),a.jsx(x.Z,{xs:24,sm:6,children:a.jsx(f.Z,{children:a.jsx(g.Z,{title:"逾期账款",value:em.overdue,suffix:"笔",valueStyle:{color:"#faad14"}})})}),a.jsx(x.Z,{xs:24,sm:6,children:a.jsx(f.Z,{children:a.jsx(g.Z,{title:"高风险客户",value:em.highRisk,suffix:"个",valueStyle:{color:"#ff4d4f"}})})})]}),(0,a.jsxs)(f.Z,{children:[(0,a.jsxs)("div",{className:"flex space-x-4 mb-4",children:[a.jsx(u.ZP,{type:"payments"===er?"primary":"default",onClick:()=>ea("payments"),children:"收款记录"}),a.jsx(u.ZP,{type:"receivables"===er?"primary":"default",onClick:()=>ea("receivables"),children:"应收账款"})]}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[a.jsx(l.default,{placeholder:"payments"===er?"搜索收款单号或客户名称":"搜索客户名称、订单号或发票号",prefix:a.jsx(S.Z,{}),value:W,onChange:e=>K(e.target.value),className:"w-full sm:w-64"}),"payments"===er&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(M.Z,{placeholder:"收款状态",value:X,onChange:Q,className:"w-full sm:w-32",allowClear:!0,children:[a.jsx(T,{value:"pending",children:"待确认"}),a.jsx(T,{value:"confirmed",children:"已确认"}),a.jsx(T,{value:"reconciled",children:"已对账"})]}),(0,a.jsxs)(M.Z,{placeholder:"付款方式",value:ee,onChange:et,className:"w-full sm:w-32",allowClear:!0,children:[a.jsx(T,{value:"cash",children:"现金"}),a.jsx(T,{value:"bank_transfer",children:"银行转账"}),a.jsx(T,{value:"check",children:"支票"}),a.jsx(T,{value:"acceptance",children:"承兑汇票"})]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(u.ZP,{icon:a.jsx(P.Z,{}),children:"导出"}),"payments"===er&&a.jsx(u.ZP,{type:"primary",icon:a.jsx(L.Z,{}),onClick:()=>{H(null),O(!0),J.resetFields()},children:"新建收款"})]})]})]}),a.jsx(f.Z,{title:"payments"===er?"收款记录":"应收账款",children:a.jsx(y.Z,{columns:"payments"===er?eo:[{title:"客户名称",dataIndex:"customerName",key:"customerName",width:180,ellipsis:!0,fixed:"left"},{title:"销售订单号",dataIndex:"orderNumber",key:"orderNumber",width:120},{title:"发票号",dataIndex:"invoiceNumber",key:"invoiceNumber",width:120},{title:"到期日期",dataIndex:"dueDate",key:"dueDate",width:120,render:(e,t)=>a.jsx("span",{style:{color:t.overdueDays>0?"#ff4d4f":"inherit"},children:e}),sorter:(e,t)=>new Date(e.dueDate).getTime()-new Date(t.dueDate).getTime()},{title:"应收金额",dataIndex:"originalAmount",key:"originalAmount",width:120,render:e=>`\xa5${e.toLocaleString()}`},{title:"已收金额",dataIndex:"paidAmount",key:"paidAmount",width:120,render:e=>`\xa5${e.toLocaleString()}`},{title:"剩余金额",dataIndex:"remainingAmount",key:"remainingAmount",width:120,render:e=>(0,a.jsxs)("span",{style:{fontWeight:"bold",color:e>0?"#cf1322":"#3f8600"},children:["\xa5",e.toLocaleString()]})},{title:"逾期天数",dataIndex:"overdueDays",key:"overdueDays",width:100,render:e=>a.jsx("span",{style:{color:e>0?"#ff4d4f":"#52c41a"},children:e>0?`${e}天`:"正常"}),sorter:(e,t)=>e.overdueDays-t.overdueDays},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>el(e)},{title:"风险等级",dataIndex:"riskLevel",key:"riskLevel",width:100,render:e=>ei(e)},{title:"操作",key:"action",width:150,fixed:"right",render:(e,t)=>a.jsx(d.Z,{size:"small",children:a.jsx(u.ZP,{type:"link",icon:a.jsx(C.Z,{}),onClick:()=>ex(t),children:"跟踪"})})}],dataSource:"payments"===er?ec:ed,rowKey:"id",loading:k,pagination:{total:"payments"===er?ec.length:ed.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`},scroll:{x:1400}})}),a.jsx(v.Z,{title:$?"编辑收款记录":"新建收款记录",open:_,onOk:()=>{J.validateFields().then(r=>{let a=new Date().toISOString(),n={...r,paymentDate:r.paymentDate.format("YYYY-MM-DD")};if($)t(e.map(e=>e.id===$.id?{...e,...n,updatedAt:a}:e)),p.ZP.success("收款记录更新成功");else{let r={id:Date.now().toString(),paymentNumber:`PAY-${new Date().getFullYear()}-${String(e.length+1).padStart(3,"0")}`,...n,createdAt:a,updatedAt:a};t([...e,r]),p.ZP.success("收款记录创建成功")}O(!1),J.resetFields()})},onCancel:()=>{O(!1),J.resetFields()},width:800,okText:"确认",cancelText:"取消",children:(0,a.jsxs)(o.Z,{form:J,layout:"vertical",initialValues:{paymentMethod:"bank_transfer",status:"pending"},children:[(0,a.jsxs)(h.Z,{gutter:16,children:[a.jsx(x.Z,{span:12,children:a.jsx(o.Z.Item,{name:"customerId",label:"客户ID",rules:[{required:!0,message:"请输入客户ID"}],children:a.jsx(l.default,{placeholder:"请输入客户ID"})})}),a.jsx(x.Z,{span:12,children:a.jsx(o.Z.Item,{name:"customerName",label:"客户名称",rules:[{required:!0,message:"请输入客户名称"}],children:a.jsx(l.default,{placeholder:"请输入客户名称"})})})]}),(0,a.jsxs)(h.Z,{gutter:16,children:[a.jsx(x.Z,{span:12,children:a.jsx(o.Z.Item,{name:"paymentDate",label:"收款日期",rules:[{required:!0,message:"请选择收款日期"}],children:a.jsx(i.default,{style:{width:"100%"}})})}),a.jsx(x.Z,{span:12,children:a.jsx(o.Z.Item,{name:"paymentAmount",label:"收款金额",rules:[{required:!0,message:"请输入收款金额"}],children:a.jsx(j.Z,{placeholder:"请输入收款金额",min:0,style:{width:"100%"},formatter:e=>`\xa5 ${e}`.replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>e.replace(/¥\s?|(,*)/g,"")})})})]}),(0,a.jsxs)(h.Z,{gutter:16,children:[a.jsx(x.Z,{span:12,children:a.jsx(o.Z.Item,{name:"paymentMethod",label:"付款方式",rules:[{required:!0,message:"请选择付款方式"}],children:(0,a.jsxs)(s.default,{children:[a.jsx(T,{value:"cash",children:"现金"}),a.jsx(T,{value:"bank_transfer",children:"银行转账"}),a.jsx(T,{value:"check",children:"支票"}),a.jsx(T,{value:"acceptance",children:"承兑汇票"})]})})}),a.jsx(x.Z,{span:12,children:a.jsx(o.Z.Item,{name:"bankAccount",label:"收款银行账户",children:a.jsx(l.default,{placeholder:"请输入收款银行账户"})})})]}),a.jsx(o.Z.Item,{name:"referenceNumber",label:"参考号/流水号",children:a.jsx(l.default,{placeholder:"请输入转账流水号或参考号"})}),a.jsx(o.Z.Item,{name:"remark",label:"备注",children:a.jsx(E,{rows:3,placeholder:"请输入备注信息"})})]})})]})}},23894:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(95344),n=r(3729),s=r.n(n),l=r(97854);let i=({value:e,options:t,children:r,onChange:i,...o})=>{let c=(0,n.useRef)(null),d=(0,n.useRef)(void 0),u=s().useMemo(()=>{let e=new Set;return t&&Array.isArray(t)&&t.forEach(t=>e.add(t.value)),r&&s().Children.forEach(r,t=>{t?.props?.value!==void 0&&e.add(t.props.value)}),e},[t,r]),m=s().useMemo(()=>{if(null!=e&&""!==e&&("string"!=typeof e||""!==e.trim())){if(u.has(e))return d.current=e,e;console.warn("FormSelect: 值不在可用选项中",{value:e,availableValues:Array.from(u),placeholder:o.placeholder})}},[e,u,o.placeholder]);(0,n.useEffect)(()=>{""===e&&i&&setTimeout(()=>{i(void 0,void 0)},0)},[e,i]),(0,n.useEffect)(()=>{if(c.current){let e=c.current.nativeElement||c.current;if(e){let t=e.querySelector('input[type="hidden"]');t&&""===t.value&&i&&i(void 0,void 0)}}},[m,i]);let p=""===m?void 0:m;return a.jsx(l.default,{ref:c,...o,value:p,onChange:(e,t)=>{let r=""===e?void 0:e;console.log("FormSelect onChange:",{placeholder:o.placeholder,originalValue:e,safeValue:r,option:t,isEmptyString:""===e,isUndefined:void 0===e,isValidValue:u.has(e),availableValues:Array.from(u)}),i&&i(r,t)},options:t,children:r})}},75497:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(25036),n=r(38834);function s({children:e}){return a.jsx(n.Z,{children:e})}},10571:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>s,__esModule:()=>n,default:()=>l});let a=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\sales\payments\page.tsx`),{__esModule:n,$$typeof:s}=a,l=a.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,2345,4441,7557,6274,996,6133],()=>r(17281));module.exports=a})();