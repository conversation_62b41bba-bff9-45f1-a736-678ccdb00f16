(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7809],{10799:function(e,l,t){Promise.resolve().then(t.bind(t,7636))},7636:function(e,l,t){"use strict";t.r(l),t.d(l,{default:function(){return H}});var s=t(57437),a=t(2265),r=t(27296),n=t(39992),i=t(2012),d=t(57416),c=t(89198),o=t(6053),m=t(65270),x=t(94734),h=t(92503),u=t(38302),p=t(28683),j=t(89511),Z=t(86155),f=t(50574),v=t(47628),g=t(47030),y=t(99617),w=t(75216),N=t(65362),C=t(34021),I=t(6371),b=t(84326),D=t(75393),S=t(40960),R=t(33477),k=t(51769),E=t(74898),_=t(32779),P=t(50538),O=t(55175),A=t(94341);let{Option:T}=r.default,{TextArea:F}=n.default;var M=function(e){let{visible:l,onClose:t,departments:i,employees:d,onDepartmentUpdate:Z,onEmployeeUpdate:f}=e,[g,y]=(0,a.useState)(!1),[w,I]=(0,a.useState)(null),[D]=c.Z.useForm(),S=()=>{let e=i.map(e=>e.departmentCode).filter(e=>/^D\d{3,4}$/.test(e));if(0===e.length)return"D001";let l=Math.max(...e.map(e=>{let l=e.match(/^D(\d{3,4})$/);return l?parseInt(l[1],10):0}))+1;return l>9999?(O.ZP.warning("编码已达到最大值D9999，请手动输入编码"),""):l<=999?"D".concat(l.toString().padStart(3,"0")):"D".concat(l.toString())},R=(e,l)=>!i.some(t=>t.departmentCode===e&&t.id!==l),k=e=>{I(e),y(!0),D.setFieldsValue(e)},_=e=>{if(i.some(l=>l.parentDepartmentId===e)){O.ZP.error("该部门下还有子部门，无法删除");return}if(d.some(l=>{let t=i.find(l=>l.id===e);return t&&l.department===t.departmentName})){O.ZP.error("该部门下还有员工，无法删除");return}Z(i.filter(l=>l.id!==e)),O.ZP.success("部门删除成功")},P=async()=>{try{let l=await D.validateFields(),t=new Date().toISOString().split("T")[0];if(w){let e=i.map(e=>e.id===w.id?{...e,...l,updatedAt:t,employeeCount:d.filter(e=>e.department===l.departmentName).length}:e);if(w.departmentName!==l.departmentName){let e=d.map(e=>e.department===w.departmentName?{...e,department:l.departmentName,updatedAt:t}:e);f(e)}Z(e),O.ZP.success("部门信息更新成功")}else{var e;let s={id:Date.now().toString(),...l,level:l.parentDepartmentId?((null===(e=i.find(e=>e.id===l.parentDepartmentId))||void 0===e?void 0:e.level)||0)+1:1,employeeCount:0,status:"active",createdAt:t,updatedAt:t};Z([...i,s]),O.ZP.success("部门创建成功")}y(!1),D.resetFields()}catch(e){}},M=(e=>{let l=new Map;e.forEach(e=>l.set(e.id,e));let t=e.filter(e=>!e.parentDepartmentId),a=l=>{let t=e.filter(e=>e.parentDepartmentId===l.id).map(a);return{key:l.id,title:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(b.Z,{className:"mr-2"}),l.departmentName,(0,s.jsxs)(o.Z,{color:"blue",className:"ml-2",children:[l.employeeCount,"人"]})]}),(0,s.jsxs)(m.Z,{size:"small",children:[(0,s.jsx)(x.ZP,{type:"link",size:"small",icon:(0,s.jsx)(N.Z,{}),onClick:e=>{e.stopPropagation(),k(l)},children:"编辑"}),(0,s.jsx)(h.Z,{title:"确定要删除这个部门吗？",onConfirm:e=>{null==e||e.stopPropagation(),_(l.id)},okText:"确定",cancelText:"取消",children:(0,s.jsx)(x.ZP,{type:"link",size:"small",danger:!0,icon:(0,s.jsx)(C.Z,{}),onClick:e=>e.stopPropagation(),children:"删除"})})]})]}),children:t.length>0?t:void 0}};return t.map(a)})(i);return(0,s.jsxs)(v.Z,{title:"部门管理",open:l,onCancel:t,footer:[(0,s.jsx)(x.ZP,{onClick:t,children:"关闭"},"close")],width:1200,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"组织架构"}),(0,s.jsx)(x.ZP,{type:"primary",icon:(0,s.jsx)(E.Z,{}),onClick:()=>{I(null),y(!0),D.resetFields();let e=S();e&&D.setFieldsValue({departmentCode:e})},children:"新建部门"})]}),(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(j.Z,{title:"部门树形结构",size:"small",children:M.length>0?(0,s.jsx)(A.Z,{treeData:M,defaultExpandAll:!0,showLine:!0,showIcon:!0}):(0,s.jsx)("div",{className:"text-center text-gray-500 py-8",children:"暂无部门数据"})})}),(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(j.Z,{title:"部门统计",size:"small",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"部门总数："}),(0,s.jsxs)("span",{className:"font-medium",children:[i.length,"个"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"员工总数："}),(0,s.jsxs)("span",{className:"font-medium",children:[d.length,"人"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"平均部门规模："}),(0,s.jsxs)("span",{className:"font-medium",children:[i.length>0?Math.round(d.length/i.length):0,"人"]})]})]})})})]})]}),(0,s.jsx)(v.Z,{title:w?"编辑部门":"新建部门",open:g,onOk:P,onCancel:()=>{y(!1),D.resetFields()},okText:"确认",cancelText:"取消",children:(0,s.jsxs)(c.Z,{form:D,layout:"vertical",initialValues:{status:"active"},children:[(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"departmentCode",label:"部门编码",rules:[{required:!0,message:"请输入部门编码"},{pattern:/^D\d{3,4}$/,message:"格式：DXXX或DXXXX（如：D001）"},{validator:(e,l)=>l?R(l,null==w?void 0:w.id)?Promise.resolve():Promise.reject(Error("部门编码已存在，请使用其他编码")):Promise.resolve()}],children:(0,s.jsx)(n.default,{placeholder:"如：D001（自动生成）"})})}),(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"departmentName",label:"部门名称",rules:[{required:!0,message:"请输入部门名称"}],children:(0,s.jsx)(n.default,{placeholder:"请输入部门名称"})})})]}),(0,s.jsx)(c.Z.Item,{name:"parentDepartmentId",label:"上级部门",children:(0,s.jsx)(r.default,{placeholder:"请选择上级部门（可选）",allowClear:!0,children:i.filter(e=>e.id!==(null==w?void 0:w.id)).map(e=>(0,s.jsx)(T,{value:e.id,children:e.departmentName},e.id))})}),(0,s.jsx)(c.Z.Item,{name:"departmentHead",label:"部门负责人",children:(0,s.jsx)(r.default,{placeholder:"请选择部门负责人（可选）",allowClear:!0,children:d.map(e=>(0,s.jsxs)(T,{value:e.id,children:[e.name," (",e.employeeCode,")"]},e.id))})}),(0,s.jsx)(c.Z.Item,{name:"description",label:"部门描述",children:(0,s.jsx)(F,{rows:3,placeholder:"请输入部门描述"})})]})})]})};let{Option:U}=r.default,{TextArea:X}=n.default,{TabPane:z}=i.default;function q(){var e;let{message:l}=d.Z.useApp(),[t,O]=(0,a.useState)([]),[A,T]=(0,a.useState)([]),[F,z]=(0,a.useState)(["admin","manager","employee","sales","viewer"]),[q,H]=(0,a.useState)(!1),[L,V]=(0,a.useState)(null),[$,B]=(0,a.useState)(!1),[K,G]=(0,a.useState)(!1),[J,Q]=(0,a.useState)(!1),[W,Y]=(0,a.useState)(!1),[ee,el]=(0,a.useState)(null),[et,es]=(0,a.useState)("basic"),[ea]=c.Z.useForm(),[er,en]=(0,a.useState)(""),[ei,ed]=(0,a.useState)(void 0),[ec,eo]=(0,a.useState)(void 0),[em,ex]=(0,a.useState)(void 0),eh=async()=>{H(!0);try{let e=await (0,P.Ro)(()=>_.dataAccessManager.employees.getAll(),"获取员工数据");e&&e.items&&O(e.items)}catch(e){console.error("加载员工数据失败:",e),l.error("加载员工数据失败")}finally{H(!1)}},eu=async()=>{try{let e=await (0,P.Ro)(()=>_.dataAccessManager.employees.getAll(),"获取员工数据以提取部门信息");if(e&&e.items){let l=[...new Set(e.items.map(e=>e.department).filter(Boolean))].map((l,t)=>({id:"dept-".concat(t+1),departmentName:l,departmentCode:"DEPT".concat(String(t+1).padStart(3,"0")),description:"".concat(l,"部门"),level:1,employeeCount:e.items.filter(e=>e.department===l).length,status:"active",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}));T(l)}}catch(e){console.error("加载部门数据失败:",e),l.error("加载部门数据失败")}},ep=()=>{let e=t.reduce((e,l)=>{let t=parseInt(l.employeeCode.replace("EMP",""),10);return t>e?t:e},0);return"EMP".concat((e+1).toString().padStart(4,"0"))};(0,a.useEffect)(()=>{eh(),eu()},[]);let ej=(e,l)=>!t.some(t=>t.employeeCode===e&&t.id!==l);(0,a.useEffect)(()=>{console.log("员工数据统计:",{employees:t.length,departments:A.length,roles:F.length})},[t.length,A.length,F.length]);let eZ={active:{color:"green",text:"在职"},inactive:{color:"orange",text:"停职"},resigned:{color:"red",text:"离职"}},ef={admin:{color:"red",text:"管理员"},manager:{color:"blue",text:"经理"},employee:{color:"green",text:"员工"},sales:{color:"orange",text:"销售员"},viewer:{color:"gray",text:"访客"}},ev=t.filter(e=>{let l=!er||e.name.toLowerCase().includes(er.toLowerCase())||e.employeeCode.toLowerCase().includes(er.toLowerCase()),t=!ei||e.department===ei,s=!ec||e.role===ec,a=!em||e.status===em;return l&&t&&s&&a}),eg=async()=>{try{let e=await _.dataAccessManager.employees.getAll();"success"===e.status&&e.data&&O(e.data.items||[])}catch(e){console.error("刷新员工数据失败:",e)}};(0,a.useEffect)(()=>{eg()},[]);let ey={total:t.length,active:t.filter(e=>"active"===e.status).length,managers:t.filter(e=>e.isDepartmentHead).length,departments:A.length,avgAge:0,newHires:t.filter(e=>{let l=new Date(e.hireDate),t=new Date;return t.setMonth(t.getMonth()-3),l>t}).length},ew=e=>{el(e),B(!0),es("basic"),ea.setFieldsValue(e)},eN=e=>{V(e),G(!0)},eC=async e=>{try{H(!0),await (0,P.Ro)(()=>_.dataAccessManager.employees.delete(e),"删除员工")&&(await eh(),l.success("员工删除成功"))}catch(e){console.error("删除员工失败:",e),l.error("删除员工失败，请稍后重试")}finally{H(!1)}},eI=async()=>{try{let e=await ea.validateFields();if(H(!0),ee){if(!await (0,P.Ro)(()=>_.dataAccessManager.employees.update(ee.id,e),"更新员工信息"))return;await eh(),l.success("员工信息更新成功")}else{let t={...e,permissions:e.permissions||[],subordinates:[]};if(!await (0,P.Ro)(()=>_.dataAccessManager.employees.create(t),"创建员工"))return;await eh(),l.success("员工创建成功")}B(!1),es("basic"),ea.resetFields()}catch(e){l.error("操作失败，请稍后重试"),console.error("员工操作失败:",e)}finally{H(!1)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"page-header",children:[(0,s.jsx)("h1",{className:"page-title",children:"员工信息"}),(0,s.jsx)("p",{className:"page-description",children:"基础数据 - 员工信息 - 管理员工基本信息、组织架构和权限设置"})]}),(0,s.jsxs)(u.Z,{gutter:[16,16],children:[(0,s.jsx)(p.Z,{xs:24,sm:6,children:(0,s.jsx)(j.Z,{children:(0,s.jsx)(Z.Z,{title:"员工总数",value:ey.total,suffix:"人",prefix:(0,s.jsx)(I.Z,{})})})}),(0,s.jsx)(p.Z,{xs:24,sm:6,children:(0,s.jsx)(j.Z,{children:(0,s.jsx)(Z.Z,{title:"在职员工",value:ey.active,suffix:"人",valueStyle:{color:"#52c41a"}})})}),(0,s.jsx)(p.Z,{xs:24,sm:6,children:(0,s.jsx)(j.Z,{children:(0,s.jsx)(Z.Z,{title:"部门经理",value:ey.managers,suffix:"人",valueStyle:{color:"#1890ff"},prefix:(0,s.jsx)(b.Z,{})})})}),(0,s.jsx)(p.Z,{xs:24,sm:6,children:(0,s.jsx)(j.Z,{children:(0,s.jsx)(Z.Z,{title:"部门数量",value:ey.departments,suffix:"个",valueStyle:{color:"#722ed1"}})})})]}),(0,s.jsx)(j.Z,{children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,s.jsx)(n.default,{placeholder:"搜索员工姓名或编码",prefix:(0,s.jsx)(D.Z,{}),value:er,onChange:e=>en(e.target.value),className:"w-full sm:w-64"}),(0,s.jsx)(r.default,{placeholder:"部门",value:ei,onChange:ed,className:"w-full sm:w-32",allowClear:!0,children:A.map(e=>(0,s.jsx)(U,{value:e.departmentName,children:e.departmentName},e.id))}),(0,s.jsxs)(r.default,{placeholder:"角色",value:ec,onChange:eo,className:"w-full sm:w-32",allowClear:!0,children:[(0,s.jsx)(U,{value:"admin",children:"管理员"}),(0,s.jsx)(U,{value:"manager",children:"经理"}),(0,s.jsx)(U,{value:"employee",children:"员工"}),(0,s.jsx)(U,{value:"sales",children:"销售员"}),(0,s.jsx)(U,{value:"viewer",children:"访客"})]}),(0,s.jsxs)(r.default,{placeholder:"状态",value:em,onChange:ex,className:"w-full sm:w-32",allowClear:!0,children:[(0,s.jsx)(U,{value:"active",children:"在职"}),(0,s.jsx)(U,{value:"inactive",children:"停职"}),(0,s.jsx)(U,{value:"resigned",children:"离职"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(x.ZP,{icon:(0,s.jsx)(S.Z,{}),onClick:()=>{Y(!0)},children:"部门管理"}),(0,s.jsx)(x.ZP,{icon:(0,s.jsx)(b.Z,{}),onClick:()=>{Q(!0)},children:"组织架构"}),(0,s.jsx)(x.ZP,{icon:(0,s.jsx)(R.Z,{}),onClick:()=>{l.info("导入功能开发中...")},children:"导入"}),(0,s.jsx)(x.ZP,{icon:(0,s.jsx)(k.Z,{}),onClick:()=>{l.info("导出功能开发中...")},children:"导出"}),(0,s.jsx)(x.ZP,{type:"primary",icon:(0,s.jsx)(E.Z,{}),onClick:()=>{el(null),B(!0),es("basic"),ea.resetFields();let e=ep();e&&ea.setFieldsValue({employeeCode:e})},children:"新建员工"})]})]})}),(0,s.jsx)(j.Z,{title:"员工列表",children:(0,s.jsx)(f.Z,{columns:[{title:"员工编码",dataIndex:"employeeCode",key:"employeeCode",width:100,fixed:"left"},{title:"姓名",dataIndex:"name",key:"name",width:100,fixed:"left"},{title:"部门",dataIndex:"department",key:"department",width:120},{title:"职位",dataIndex:"position",key:"position",width:120},{title:"联系电话",dataIndex:"phone",key:"phone",width:130},{title:"角色",dataIndex:"role",key:"role",width:100,render:e=>{let l=ef[e]||{color:"default",text:e};return(0,s.jsx)(o.Z,{color:l.color,children:l.text})}},{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>(0,s.jsx)(o.Z,{color:eZ[e].color,children:eZ[e].text})},{title:"入职日期",dataIndex:"hireDate",key:"hireDate",width:110},{title:"操作",key:"action",width:200,render:(e,l)=>(0,s.jsxs)(m.Z,{size:"small",children:[(0,s.jsx)(x.ZP,{type:"link",icon:(0,s.jsx)(w.Z,{}),onClick:()=>eN(l),children:"查看"}),(0,s.jsx)(x.ZP,{type:"link",icon:(0,s.jsx)(N.Z,{}),onClick:()=>ew(l),children:"编辑"}),(0,s.jsx)(h.Z,{title:"确定要删除这个员工吗？",onConfirm:()=>eC(l.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(x.ZP,{type:"link",danger:!0,icon:(0,s.jsx)(C.Z,{}),children:"删除"})})]})}],dataSource:ev,rowKey:"id",loading:q,pagination:{total:ev.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,l)=>"第 ".concat(l[0],"-").concat(l[1]," 条/共 ").concat(e," 条")},scroll:{x:1400}})}),(0,s.jsx)(v.Z,{title:ee?"编辑员工":"新建员工",open:$,onOk:eI,onCancel:()=>{B(!1),es("basic"),ea.resetFields()},width:900,okText:"确认",cancelText:"取消",children:(0,s.jsx)(c.Z,{form:ea,layout:"vertical",initialValues:{status:"active",role:"employee",permissions:[]},children:(0,s.jsx)(i.default,{activeKey:et,onChange:es,items:[{key:"basic",label:"基础信息",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"employeeCode",label:"员工编码",rules:[{required:!0,message:"请输入员工编码"},{pattern:/^A\d{3,4}$/,message:"格式：AXXX或AXXXX（如：A001）"},{validator:(e,l)=>l?ej(l,null==ee?void 0:ee.id)?Promise.resolve():Promise.reject(Error("员工编码已存在，请使用其他编码")):Promise.resolve()}],children:(0,s.jsx)(n.default,{placeholder:"如：A001（自动生成）"})})}),(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"name",label:"姓名",rules:[{required:!0,message:"请输入姓名"}],children:(0,s.jsx)(n.default,{placeholder:"请输入姓名"})})})]}),(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"position",label:"职位",rules:[{required:!0,message:"请输入职位"}],children:(0,s.jsx)(n.default,{placeholder:"请输入职位"})})}),(0,s.jsx)(p.Z,{span:12})]}),(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"phone",label:"联系电话",rules:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式"}],children:(0,s.jsx)(n.default,{placeholder:"请输入联系电话"})})}),(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"email",label:"邮箱",rules:[{type:"email",message:"请输入正确的邮箱格式"}],children:(0,s.jsx)(n.default,{placeholder:"请输入邮箱"})})})]}),(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"hireDate",label:"入职日期",rules:[{required:!0,message:"请选择入职日期"}],children:(0,s.jsx)(n.default,{type:"date"})})}),(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"status",label:"状态",rules:[{required:!0,message:"请选择状态"}],children:(0,s.jsxs)(r.default,{placeholder:"请选择状态",children:[(0,s.jsx)(U,{value:"active",children:"在职"}),(0,s.jsx)(U,{value:"inactive",children:"停职"}),(0,s.jsx)(U,{value:"resigned",children:"离职"})]})})})]}),(0,s.jsx)(c.Z.Item,{name:"remark",label:"备注",children:(0,s.jsx)(X,{rows:3,placeholder:"请输入备注信息"})})]})},{key:"organization",label:"组织关系",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(u.Z,{gutter:16,children:[(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"department",label:"所属部门",rules:[{required:!0,message:"请选择所属部门"}],children:(0,s.jsx)(r.default,{placeholder:"请选择所属部门",children:A.map(e=>(0,s.jsx)(U,{value:e.departmentName,children:e.departmentName},e.id))})})}),(0,s.jsx)(p.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{name:"directSupervisor",label:"直接上级",children:(0,s.jsx)(r.default,{placeholder:"请选择直接上级",allowClear:!0,children:t.filter(e=>e.id!==(null==ee?void 0:ee.id)).map(e=>(0,s.jsxs)(U,{value:e.id,children:[e.name," (",e.employeeCode,")"]},e.id))})})})]}),(0,s.jsx)(c.Z.Item,{name:"isDepartmentHead",valuePropName:"checked",children:(0,s.jsx)(g.Z,{children:"是否部门负责人"})}),(0,s.jsx)("div",{className:"text-gray-500 text-center py-8",children:(0,s.jsx)("p",{children:"组织架构图和下属管理功能开发中..."})})]})},{key:"permissions",label:"权限设置",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(c.Z.Item,{name:"role",label:"用户角色",rules:[{required:!0,message:"请选择用户角色"}],children:(0,s.jsxs)(r.default,{placeholder:"请选择用户角色",children:[(0,s.jsx)(U,{value:"admin",children:"系统管理员"}),(0,s.jsx)(U,{value:"manager",children:"部门经理"}),(0,s.jsx)(U,{value:"employee",children:"普通员工"}),(0,s.jsx)(U,{value:"sales",children:"销售员"}),(0,s.jsx)(U,{value:"viewer",children:"只读用户"})]})}),(0,s.jsx)(c.Z.Item,{name:"permissions",label:"功能权限",children:(0,s.jsx)(g.Z.Group,{children:(0,s.jsxs)(u.Z,{children:[(0,s.jsx)(p.Z,{span:8,children:(0,s.jsx)(g.Z,{value:"sales_read",children:"销售查看"})}),(0,s.jsx)(p.Z,{span:8,children:(0,s.jsx)(g.Z,{value:"sales_write",children:"销售编辑"})}),(0,s.jsx)(p.Z,{span:8,children:(0,s.jsx)(g.Z,{value:"customer_read",children:"客户查看"})}),(0,s.jsx)(p.Z,{span:8,children:(0,s.jsx)(g.Z,{value:"customer_write",children:"客户编辑"})}),(0,s.jsx)(p.Z,{span:8,children:(0,s.jsx)(g.Z,{value:"production_read",children:"生产查看"})}),(0,s.jsx)(p.Z,{span:8,children:(0,s.jsx)(g.Z,{value:"production_write",children:"生产编辑"})}),(0,s.jsx)(p.Z,{span:8,children:(0,s.jsx)(g.Z,{value:"finance_read",children:"财务查看"})}),(0,s.jsx)(p.Z,{span:8,children:(0,s.jsx)(g.Z,{value:"finance_write",children:"财务编辑"})}),(0,s.jsx)(p.Z,{span:8,children:(0,s.jsx)(g.Z,{value:"report_read",children:"报表查看"})})]})})}),(0,s.jsx)("div",{className:"text-gray-500 text-center py-8",children:(0,s.jsx)("p",{children:"详细权限配置和角色管理功能开发中..."})})]})}]})})}),(0,s.jsx)(v.Z,{title:"员工详情",open:K,onCancel:()=>G(!1),footer:[(0,s.jsx)(x.ZP,{onClick:()=>G(!1),children:"关闭"},"close")],width:800,children:L&&L.employeeCode&&L.name&&(0,s.jsxs)(y.Z,{bordered:!0,column:2,children:[(0,s.jsx)(y.Z.Item,{label:"员工编码",children:L.employeeCode}),(0,s.jsx)(y.Z.Item,{label:"姓名",children:L.name}),(0,s.jsx)(y.Z.Item,{label:"部门",children:L.department}),(0,s.jsx)(y.Z.Item,{label:"职位",children:L.position}),(0,s.jsx)(y.Z.Item,{label:"联系电话",children:L.phone||"-"}),(0,s.jsx)(y.Z.Item,{label:"邮箱",children:L.email||"-"}),(0,s.jsx)(y.Z.Item,{label:"角色",children:(()=>{let e=ef[L.role]||{color:"default",text:L.role};return(0,s.jsx)(o.Z,{color:e.color,children:e.text})})()}),(0,s.jsx)(y.Z.Item,{label:"状态",children:(0,s.jsx)(o.Z,{color:eZ[L.status].color,children:eZ[L.status].text})}),(0,s.jsx)(y.Z.Item,{label:"是否部门负责人",children:L.isDepartmentHead?"是":"否"}),(0,s.jsx)(y.Z.Item,{label:"入职日期",children:L.hireDate}),(0,s.jsx)(y.Z.Item,{label:"离职日期",children:L.resignDate||"-"}),(0,s.jsx)(y.Z.Item,{label:"直接上级",children:L.directSupervisor&&(null===(e=t.find(e=>e.id===L.directSupervisor))||void 0===e?void 0:e.name)||"-"}),(0,s.jsxs)(y.Z.Item,{label:"权限数量",span:2,children:[L.permissions.length,"个"]}),(0,s.jsx)(y.Z.Item,{label:"备注",span:2,children:L.remark||"-"})]})}),(0,s.jsx)(v.Z,{title:"组织架构",open:J,onCancel:()=>Q(!1),footer:[(0,s.jsx)(x.ZP,{onClick:()=>Q(!1),children:"关闭"},"close")],width:1e3,children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(u.Z,{gutter:16,children:A.map(e=>(0,s.jsx)(p.Z,{span:8,className:"mb-4",children:(0,s.jsx)(j.Z,{title:e.departmentName,size:"small",extra:(0,s.jsxs)(o.Z,{color:"blue",children:[e.employeeCount,"人"]}),children:(0,s.jsx)("div",{className:"space-y-2",children:t.filter(l=>l.department===e.departmentName).map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(I.Z,{className:"mr-1"}),e.name,e.isDepartmentHead&&(0,s.jsx)(o.Z,{color:"red",className:"ml-1",children:"负责人"})]}),(()=>{let l=ef[e.role]||{color:"default",text:e.role};return(0,s.jsx)(o.Z,{color:l.color,children:l.text})})()]},e.id))})})},e.id))}),(0,s.jsx)("div",{className:"text-gray-500 text-center py-8",children:(0,s.jsx)("p",{children:"树形组织架构图和层级管理功能开发中..."})})]})}),(0,s.jsx)(M,{visible:W,onClose:()=>Y(!1),departments:A,employees:t,onDepartmentUpdate:e=>{T(e)},onEmployeeUpdate:e=>{O(e)}})]})}function H(){return(0,s.jsx)(d.Z,{children:(0,s.jsx)(q,{})})}},50538:function(e,l,t){"use strict";t.d(l,{Ro:function(){return s}});let s=async(e,l)=>{try{let t=await e();if("success"===t.status)return t.data||null;return t.code,t.message,t.message,l&&r(String(t.code||"UNKNOWN_ERROR")),null}catch(e){return e instanceof Error?e.message:String(e),null}},a={ERR_NOT_FOUND:"资源不存在",ERR_UNAUTHORIZED:"未授权访问",ERR_FORBIDDEN:"禁止访问",ERR_INTERNAL_ERROR:"内部服务器错误",ERR_PRODUCT_NOT_FOUND:"产品不存在",ERR_PRODUCT_CODE_EXISTS:"产品编码已存在",ERR_CUSTOMER_NOT_FOUND:"客户不存在",ERR_INVENTORY_INSUFFICIENT:"库存不足",ERR_ORDER_NOT_FOUND:"订单不存在",ERR_ORDER_NUMBER_EXISTS:"订单号已存在",DUPLICATE_ORDER_NUMBER:"订单号重复"},r=e=>a[e]||"操作失败"}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,7416,8236,9617,2897,8326,2779,2971,4938,1744],function(){return e(e.s=10799)}),_N_E=e.O()}]);