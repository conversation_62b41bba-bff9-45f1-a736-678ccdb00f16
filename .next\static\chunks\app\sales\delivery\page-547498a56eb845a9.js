(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4544],{16478:function(e,t,l){Promise.resolve().then(l.bind(l,55093))},55093:function(e,t,l){"use strict";l.r(t);var r=l(57437),s=l(2265),a=l(27296),i=l(39992),d=l(11330),n=l(89198),o=l(6053),c=l(9427),u=l(34863),h=l(65270),m=l(94734),x=l(92503),p=l(55175),j=l(38302),y=l(28683),g=l(89511),v=l(86155),Z=l(50574),f=l(47628),w=l(59189),b=l(75123),I=l(99617),k=l(44753),N=l(68826),S=l(78740),C=l(64182),T=l(87304),D=l(23455),L=l(75216),P=l(65362),A=l(66887),q=l(34021),V=l(75393),M=l(51769),R=l(72973),z=l(74898),Q=l(12102),E=l(74548),_=l.n(E);let{Option:B}=a.default,{TextArea:F}=i.default,{Step:W}=d.default;t.default=()=>{let[e,t]=(0,s.useState)([]),[l,E]=(0,s.useState)(!1),[O,Y]=(0,s.useState)(!1),[K,J]=(0,s.useState)(!1),[U,G]=(0,s.useState)(!1),[H,X]=(0,s.useState)(null),[$,ee]=(0,s.useState)(null),[et]=n.Z.useForm(),[el,er]=(0,s.useState)(""),[es,ea]=(0,s.useState)(void 0);(0,s.useEffect)(()=>{t([{id:"1",deliveryNumber:"DN-2024-001",orderNumber:"SO-2024-001",customerId:"1",customerName:"上海包装材料有限公司",deliveryDate:"2024-01-25",deliveryAddress:"上海市浦东新区张江高科技园区",contactPerson:"张经理",contactPhone:"13800138001",driverName:"李师傅",vehicleNumber:"沪A12345",vehicleType:"厢式货车",totalWeight:2.5,totalVolume:15.6,loadingRate:92,status:"shipped",items:[{id:"1",deliveryNumber:"DN-2024-001",orderItemId:"1",productModelCode:"CP-202",productName:"圆形餐盘202mm",plannedQuantity:3e3,actualQuantity:3e3,unit:"个",batchNumber:"*********-001",warehouseLocation:"A01-01-01",palletNumber:"PLT-001",remainingQuantity:0}],warehouseTasks:[{id:"1",deliveryNumber:"DN-2024-001",taskType:"pick",productModelCode:"CP-202",quantity:3e3,fromLocation:"A01-01-01",recommendedLocation:"A01-01-01",priority:"high",status:"completed",operator:"仓管员A",completedAt:"2024-01-25T08:30:00"}],remark:"优先发货，客户急需",createdAt:"2024-01-24T16:00:00",updatedAt:"2024-01-25T10:00:00"},{id:"2",deliveryNumber:"DN-2024-002",orderNumber:"SO-2024-002",customerId:"2",customerName:"北京绿色包装科技公司",deliveryDate:"2024-01-28",deliveryAddress:"北京市朝阳区CBD商务区",contactPerson:"王总",contactPhone:"13900139002",driverName:"张师傅",vehicleNumber:"京B67890",vehicleType:"平板货车",totalWeight:4.2,totalVolume:28.5,loadingRate:85,status:"loading",items:[{id:"2",deliveryNumber:"DN-2024-002",orderItemId:"2",productModelCode:"CP-201",productName:"方形餐盒180mm",plannedQuantity:2e4,actualQuantity:19800,unit:"个",batchNumber:"*********-001",warehouseLocation:"B02-02-03",palletNumber:"PLT-002",remainingQuantity:200,remark:"有200个余量需退回"}],warehouseTasks:[{id:"2",deliveryNumber:"DN-2024-002",taskType:"pick",productModelCode:"CP-201",quantity:2e4,fromLocation:"B02-02-03",recommendedLocation:"B02-02-03",priority:"medium",status:"in_progress",operator:"仓管员B"}],createdAt:"2024-01-26T09:00:00",updatedAt:"2024-01-28T14:00:00"}])},[]);let ei=e=>{let t={pending:{color:"orange",text:"待发货",icon:(0,r.jsx)(N.Z,{})},loading:{color:"blue",text:"装货中",icon:(0,r.jsx)(S.Z,{})},shipped:{color:"green",text:"已发货",icon:(0,r.jsx)(C.Z,{})},delivered:{color:"cyan",text:"已送达",icon:(0,r.jsx)(T.Z,{})},returned:{color:"red",text:"已退回",icon:(0,r.jsx)(D.Z,{})}}[e]||{color:"default",text:"未知",icon:null};return(0,r.jsx)(o.Z,{color:t.color,icon:t.icon,children:t.text})},ed=e=>{let t={pending:{color:"default",text:"待处理"},in_progress:{color:"processing",text:"进行中"},completed:{color:"success",text:"已完成"}}[e]||{color:"default",text:"未知"};return(0,r.jsx)(c.Z,{status:t.color,text:t.text})},en=e=>{let t={high:{color:"red",text:"高"},medium:{color:"orange",text:"中"},low:{color:"green",text:"低"}}[e]||{color:"default",text:"未知"};return(0,r.jsx)(o.Z,{color:t.color,children:t.text})},eo=(e,t,l)=>l>=90?{type:"success",message:"装载率优秀，配载合理"}:l>=80?{type:"warning",message:"装载率良好，可考虑合并其他订单"}:{type:"error",message:"装载率偏低，建议优化配载方案"},ec=e.filter(e=>{let t=!el||e.deliveryNumber.toLowerCase().includes(el.toLowerCase())||e.orderNumber.toLowerCase().includes(el.toLowerCase())||e.customerName.toLowerCase().includes(el.toLowerCase()),l=!es||e.status===es;return t&&l}),eu={total:e.length,pending:e.filter(e=>"pending"===e.status).length,loading:e.filter(e=>"loading"===e.status).length,shipped:e.filter(e=>"shipped"===e.status).length,averageLoadingRate:e.length>0?Math.round(e.reduce((e,t)=>e+t.loadingRate,0)/e.length):0},eh=e=>{X(e),Y(!0),et.setFieldsValue({...e,deliveryDate:_()(e.deliveryDate)})},em=e=>{ee(e),J(!0)},ex=e=>{ee(e),G(!0)},ep=l=>{t(e.filter(e=>e.id!==l)),p.ZP.success("送货单删除成功")};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"page-header",children:[(0,r.jsx)("h1",{className:"page-title",children:"发货执行管理"}),(0,r.jsx)("p",{className:"page-description",children:"智能配载、仓库任务、出库扫码、余量管理"})]}),(0,r.jsxs)(j.Z,{gutter:[16,16],children:[(0,r.jsx)(y.Z,{xs:24,sm:6,children:(0,r.jsx)(g.Z,{children:(0,r.jsx)(v.Z,{title:"送货单总数",value:eu.total,suffix:"个",prefix:(0,r.jsx)(C.Z,{})})})}),(0,r.jsx)(y.Z,{xs:24,sm:6,children:(0,r.jsx)(g.Z,{children:(0,r.jsx)(v.Z,{title:"待发货",value:eu.pending,suffix:"个",valueStyle:{color:"#faad14"}})})}),(0,r.jsx)(y.Z,{xs:24,sm:6,children:(0,r.jsx)(g.Z,{children:(0,r.jsx)(v.Z,{title:"装货中",value:eu.loading,suffix:"个",valueStyle:{color:"#1890ff"}})})}),(0,r.jsx)(y.Z,{xs:24,sm:6,children:(0,r.jsx)(g.Z,{children:(0,r.jsx)(v.Z,{title:"平均装载率",value:eu.averageLoadingRate,suffix:"%",valueStyle:{color:eu.averageLoadingRate>=85?"#3f8600":"#cf1322"}})})})]}),(0,r.jsx)(g.Z,{children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,r.jsx)(i.default,{placeholder:"搜索送货单号、订单号或客户名称",prefix:(0,r.jsx)(V.Z,{}),value:el,onChange:e=>er(e.target.value),className:"w-full sm:w-64"}),(0,r.jsxs)(Q.Z,{placeholder:"发货状态",value:es,onChange:ea,className:"w-full sm:w-32",allowClear:!0,children:[(0,r.jsx)(B,{value:"pending",children:"待发货"}),(0,r.jsx)(B,{value:"loading",children:"装货中"}),(0,r.jsx)(B,{value:"shipped",children:"已发货"}),(0,r.jsx)(B,{value:"delivered",children:"已送达"}),(0,r.jsx)(B,{value:"returned",children:"已退回"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(m.ZP,{icon:(0,r.jsx)(M.Z,{}),children:"导出"}),(0,r.jsx)(m.ZP,{icon:(0,r.jsx)(R.Z,{}),children:"配载优化"}),(0,r.jsx)(m.ZP,{type:"primary",icon:(0,r.jsx)(z.Z,{}),onClick:()=>{X(null),Y(!0),et.resetFields()},children:"新建送货单"})]})]})}),(0,r.jsx)(g.Z,{title:"送货单列表",children:(0,r.jsx)(Z.Z,{columns:[{title:"送货单号",dataIndex:"deliveryNumber",key:"deliveryNumber",width:140,fixed:"left"},{title:"关联订单",dataIndex:"orderNumber",key:"orderNumber",width:140},{title:"客户名称",dataIndex:"customerName",key:"customerName",width:180,ellipsis:!0},{title:"发货日期",dataIndex:"deliveryDate",key:"deliveryDate",width:120,sorter:(e,t)=>new Date(e.deliveryDate).getTime()-new Date(t.deliveryDate).getTime()},{title:"车辆信息",key:"vehicleInfo",width:150,render:(e,t)=>(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{children:t.vehicleNumber}),(0,r.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:[t.driverName," | ",t.vehicleType]})]})},{title:"装载信息",key:"loadingInfo",width:150,render:(e,t)=>{let l=eo(t.totalWeight,t.totalVolume,t.loadingRate);return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{children:[t.totalWeight,"吨 | ",t.totalVolume,"m\xb3"]}),(0,r.jsx)("div",{children:(0,r.jsx)(u.Z,{percent:t.loadingRate,size:"small",status:"success"===l.type?"success":"warning"===l.type?"normal":"exception"})})]})}},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>ei(e)},{title:"仓库任务",key:"taskStatus",width:120,render:(e,t)=>{let l=t.warehouseTasks.filter(e=>"completed"===e.status).length,s=t.warehouseTasks.length;return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{children:[l,"/",s]}),(0,r.jsx)(u.Z,{percent:s>0?l/s*100:0,size:"small",showInfo:!1})]})}},{title:"操作",key:"action",width:250,fixed:"right",render:(e,t)=>(0,r.jsxs)(h.Z,{size:"small",children:[(0,r.jsx)(m.ZP,{type:"link",icon:(0,r.jsx)(L.Z,{}),onClick:()=>em(t),children:"详情"}),(0,r.jsx)(m.ZP,{type:"link",icon:(0,r.jsx)(P.Z,{}),onClick:()=>eh(t),children:"编辑"}),(0,r.jsx)(m.ZP,{type:"link",icon:(0,r.jsx)(A.Z,{}),onClick:()=>ex(t),children:"任务"}),(0,r.jsx)(x.Z,{title:"确定要删除这个送货单吗？",onConfirm:()=>ep(t.id),okText:"确定",cancelText:"取消",children:(0,r.jsx)(m.ZP,{type:"link",danger:!0,icon:(0,r.jsx)(q.Z,{}),children:"删除"})})]})}],dataSource:ec,rowKey:"id",loading:l,pagination:{total:ec.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条"),pageSizeOptions:["10","20","50","100"]},scroll:{x:1400}})}),(0,r.jsxs)(f.Z,{title:H?"编辑送货单":"新建送货单",open:O,onOk:()=>{et.validateFields().then(l=>{let r=new Date().toISOString(),s={...l,deliveryDate:l.deliveryDate.format("YYYY-MM-DD")};if(H)t(e.map(e=>e.id===H.id?{...e,...s,updatedAt:r}:e)),p.ZP.success("送货单更新成功");else{let l={id:Date.now().toString(),deliveryNumber:"DN-".concat(new Date().getFullYear(),"-").concat(String(e.length+1).padStart(3,"0")),...s,items:[],warehouseTasks:[],createdAt:r,updatedAt:r};t([...e,l]),p.ZP.success("送货单创建成功")}Y(!1),et.resetFields()})},onCancel:()=>{Y(!1),et.resetFields()},width:800,okText:"确认",cancelText:"取消",children:[(0,r.jsx)(w.Z,{message:"智能发货流程",description:"系统将自动推送仓库任务、推荐库位、计算装载率并生成配载建议",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,r.jsxs)(n.Z,{form:et,layout:"vertical",initialValues:{status:"pending"},children:[(0,r.jsxs)(j.Z,{gutter:16,children:[(0,r.jsx)(y.Z,{span:12,children:(0,r.jsx)(n.Z.Item,{name:"orderNumber",label:"关联订单号",rules:[{required:!0,message:"请输入关联订单号"}],children:(0,r.jsx)(i.default,{placeholder:"请输入关联订单号"})})}),(0,r.jsx)(y.Z,{span:12,children:(0,r.jsx)(n.Z.Item,{name:"deliveryDate",label:"发货日期",rules:[{required:!0,message:"请选择发货日期"}],children:(0,r.jsx)(i.default,{placeholder:"请选择发货日期"})})})]}),(0,r.jsxs)(j.Z,{gutter:16,children:[(0,r.jsx)(y.Z,{span:8,children:(0,r.jsx)(n.Z.Item,{name:"customerId",label:"客户ID",rules:[{required:!0,message:"请输入客户ID"}],children:(0,r.jsx)(i.default,{placeholder:"请输入客户ID"})})}),(0,r.jsx)(y.Z,{span:8,children:(0,r.jsx)(n.Z.Item,{name:"customerName",label:"客户名称",rules:[{required:!0,message:"请输入客户名称"}],children:(0,r.jsx)(i.default,{placeholder:"请输入客户名称"})})}),(0,r.jsx)(y.Z,{span:8,children:(0,r.jsx)(n.Z.Item,{name:"contactPerson",label:"联系人",rules:[{required:!0,message:"请输入联系人"}],children:(0,r.jsx)(i.default,{placeholder:"请输入联系人"})})})]}),(0,r.jsx)(n.Z.Item,{name:"deliveryAddress",label:"送货地址",rules:[{required:!0,message:"请输入送货地址"}],children:(0,r.jsx)(i.default,{placeholder:"请输入详细送货地址"})}),(0,r.jsxs)(j.Z,{gutter:16,children:[(0,r.jsx)(y.Z,{span:8,children:(0,r.jsx)(n.Z.Item,{name:"contactPhone",label:"联系电话",rules:[{required:!0,message:"请输入联系电话"}],children:(0,r.jsx)(i.default,{placeholder:"请输入联系电话"})})}),(0,r.jsx)(y.Z,{span:8,children:(0,r.jsx)(n.Z.Item,{name:"driverName",label:"司机姓名",rules:[{required:!0,message:"请输入司机姓名"}],children:(0,r.jsx)(i.default,{placeholder:"请输入司机姓名"})})}),(0,r.jsx)(y.Z,{span:8,children:(0,r.jsx)(n.Z.Item,{name:"vehicleNumber",label:"车牌号",rules:[{required:!0,message:"请输入车牌号"}],children:(0,r.jsx)(i.default,{placeholder:"请输入车牌号"})})})]}),(0,r.jsxs)(j.Z,{gutter:16,children:[(0,r.jsx)(y.Z,{span:8,children:(0,r.jsx)(n.Z.Item,{name:"vehicleType",label:"车型",rules:[{required:!0,message:"请选择车型"}],children:(0,r.jsxs)(a.default,{placeholder:"请选择车型",children:[(0,r.jsx)(B,{value:"厢式货车",children:"厢式货车"}),(0,r.jsx)(B,{value:"平板货车",children:"平板货车"}),(0,r.jsx)(B,{value:"集装箱车",children:"集装箱车"}),(0,r.jsx)(B,{value:"冷藏车",children:"冷藏车"})]})})}),(0,r.jsx)(y.Z,{span:8,children:(0,r.jsx)(n.Z.Item,{name:"totalWeight",label:"总重量(吨)",rules:[{required:!0,message:"请输入总重量"}],children:(0,r.jsx)(b.Z,{min:0,step:.1,style:{width:"100%"},placeholder:"请输入总重量"})})}),(0,r.jsx)(y.Z,{span:8,children:(0,r.jsx)(n.Z.Item,{name:"totalVolume",label:"总体积(m\xb3)",rules:[{required:!0,message:"请输入总体积"}],children:(0,r.jsx)(b.Z,{min:0,step:.1,style:{width:"100%"},placeholder:"请输入总体积"})})})]}),(0,r.jsx)(n.Z.Item,{name:"remark",label:"备注",children:(0,r.jsx)(F,{rows:3,placeholder:"请输入备注信息"})})]})]}),(0,r.jsx)(f.Z,{title:"送货单详情",open:K,onCancel:()=>J(!1),footer:[(0,r.jsx)(m.ZP,{onClick:()=>J(!1),children:"关闭"},"close")],width:1e3,children:$&&$.deliveryNumber&&$.orderNumber&&(0,r.jsxs)("div",{children:[(0,r.jsxs)(I.Z,{column:3,bordered:!0,children:[(0,r.jsx)(I.Z.Item,{label:"送货单号",children:$.deliveryNumber}),(0,r.jsx)(I.Z.Item,{label:"关联订单",children:$.orderNumber}),(0,r.jsx)(I.Z.Item,{label:"客户名称",children:$.customerName}),(0,r.jsx)(I.Z.Item,{label:"发货日期",children:$.deliveryDate}),(0,r.jsx)(I.Z.Item,{label:"联系人",children:$.contactPerson}),(0,r.jsx)(I.Z.Item,{label:"联系电话",children:$.contactPhone}),(0,r.jsx)(I.Z.Item,{label:"司机姓名",children:$.driverName}),(0,r.jsx)(I.Z.Item,{label:"车牌号",children:$.vehicleNumber}),(0,r.jsx)(I.Z.Item,{label:"车型",children:$.vehicleType}),(0,r.jsxs)(I.Z.Item,{label:"总重量",children:[$.totalWeight,"吨"]}),(0,r.jsxs)(I.Z.Item,{label:"总体积",children:[$.totalVolume,"m\xb3"]}),(0,r.jsx)(I.Z.Item,{label:"装载率",children:(0,r.jsxs)("span",{style:{color:$.loadingRate>=85?"#3f8600":"#cf1322",fontWeight:"bold"},children:[$.loadingRate,"%"]})}),(0,r.jsx)(I.Z.Item,{label:"状态",children:ei($.status)}),(0,r.jsx)(I.Z.Item,{label:"创建时间",children:new Date($.createdAt).toLocaleString()}),(0,r.jsx)(I.Z.Item,{label:"更新时间",children:new Date($.updatedAt).toLocaleString()}),(0,r.jsx)(I.Z.Item,{label:"送货地址",span:3,children:$.deliveryAddress}),(0,r.jsx)(I.Z.Item,{label:"备注",span:3,children:$.remark||"无"})]}),(0,r.jsxs)("div",{style:{marginTop:16},children:[(0,r.jsx)("h4",{children:"配载分析"}),(()=>{let e=eo($.totalWeight,$.totalVolume,$.loadingRate);return(0,r.jsx)(w.Z,{message:"装载率: ".concat($.loadingRate,"%"),description:e.message,type:e.type,showIcon:!0})})()]}),(0,r.jsxs)("div",{style:{marginTop:16},children:[(0,r.jsx)("h4",{children:"发货明细"}),(0,r.jsx)(Z.Z,{dataSource:$.items,rowKey:"id",pagination:!1,size:"small",columns:[{title:"产品型号",dataIndex:"productModelCode",key:"productModelCode",width:120},{title:"产品名称",dataIndex:"productName",key:"productName",width:150},{title:"计划数量",dataIndex:"plannedQuantity",key:"plannedQuantity",width:100,render:(e,t)=>"".concat(e.toLocaleString()," ").concat(t.unit)},{title:"实装数量",dataIndex:"actualQuantity",key:"actualQuantity",width:100,render:(e,t)=>"".concat(e.toLocaleString()," ").concat(t.unit)},{title:"余量",dataIndex:"remainingQuantity",key:"remainingQuantity",width:100,render:(e,t)=>(0,r.jsxs)("span",{style:{color:e>0?"#faad14":"#52c41a"},children:[e.toLocaleString()," ",t.unit]})},{title:"批次号",dataIndex:"batchNumber",key:"batchNumber",width:120},{title:"库位",dataIndex:"warehouseLocation",key:"warehouseLocation",width:100},{title:"托盘号",dataIndex:"palletNumber",key:"palletNumber",width:100}]})]})]})}),(0,r.jsx)(f.Z,{title:"仓库任务管理",open:U,onCancel:()=>G(!1),footer:[(0,r.jsx)(m.ZP,{onClick:()=>G(!1),children:"关闭"},"close")],width:900,children:$&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w.Z,{message:"任务执行流程",description:"拣货 → 包装 → 装车 → 发货，系统自动推荐最优库位和执行路径",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,r.jsxs)(d.default,{size:"small",style:{marginBottom:24},children:[(0,r.jsx)(W,{title:"拣货",status:"finish",icon:(0,r.jsx)(S.Z,{}),description:"从库位拣取商品"}),(0,r.jsx)(W,{title:"包装",status:"process",icon:(0,r.jsx)(T.Z,{}),description:"商品包装和标识"}),(0,r.jsx)(W,{title:"装车",status:"wait",icon:(0,r.jsx)(C.Z,{}),description:"装载到运输车辆"}),(0,r.jsx)(W,{title:"发货",status:"wait",icon:(0,r.jsx)(A.Z,{}),description:"扫码确认发货"})]}),(0,r.jsx)(Z.Z,{dataSource:$.warehouseTasks,rowKey:"id",pagination:!1,size:"small",columns:[{title:"任务类型",dataIndex:"taskType",key:"taskType",width:100,render:e=>({pick:"拣货",pack:"包装",load:"装车",return:"退货"})[e]||e},{title:"产品型号",dataIndex:"productModelCode",key:"productModelCode",width:120},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,render:e=>e.toLocaleString()},{title:"源库位",dataIndex:"fromLocation",key:"fromLocation",width:100},{title:"推荐库位",dataIndex:"recommendedLocation",key:"recommendedLocation",width:100,render:e=>(0,r.jsx)(o.Z,{color:"blue",children:e})},{title:"优先级",dataIndex:"priority",key:"priority",width:80,render:e=>en(e)},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>ed(e)},{title:"操作员",dataIndex:"operator",key:"operator",width:100},{title:"完成时间",dataIndex:"completedAt",key:"completedAt",width:150,render:e=>e?new Date(e).toLocaleString():"-"}]}),(0,r.jsxs)("div",{style:{marginTop:16},children:[(0,r.jsx)("h4",{children:"任务执行时间线"}),(0,r.jsx)(k.Z,{children:$.warehouseTasks.map(e=>(0,r.jsx)(k.Z.Item,{color:"completed"===e.status?"green":"in_progress"===e.status?"blue":"gray",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{style:{fontWeight:"bold"},children:"pick"===e.taskType?"拣货任务":"pack"===e.taskType?"包装任务":"load"===e.taskType?"装车任务":"退货任务"}),(0,r.jsxs)("div",{children:["产品: ",e.productModelCode," | 数量: ",e.quantity.toLocaleString()]}),(0,r.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["库位: ",e.fromLocation," → ",e.recommendedLocation]}),(0,r.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["操作员: ",e.operator||"待分配"," | 状态: ","completed"===e.status?"已完成":"in_progress"===e.status?"进行中":"待处理"]}),e.completedAt&&(0,r.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["完成时间: ",new Date(e.completedAt).toLocaleString()]})]})},e.id))})]})]})})]})}},12102:function(e,t,l){"use strict";var r=l(57437),s=l(2265),a=l(27296);t.Z=e=>{let{value:t,options:l,children:i,onChange:d,...n}=e,o=(0,s.useRef)(null),c=(0,s.useRef)(void 0),u=s.useMemo(()=>{let e=new Set;return l&&Array.isArray(l)&&l.forEach(t=>e.add(t.value)),i&&s.Children.forEach(i,t=>{var l;(null==t?void 0:null===(l=t.props)||void 0===l?void 0:l.value)!==void 0&&e.add(t.props.value)}),e},[l,i]),h=s.useMemo(()=>{if(null!=t&&""!==t&&("string"!=typeof t||""!==t.trim())){if(u.has(t))return c.current=t,t;console.warn("FormSelect: 值不在可用选项中",{value:t,availableValues:Array.from(u),placeholder:n.placeholder})}},[t,u,n.placeholder]);(0,s.useEffect)(()=>{""===t&&d&&setTimeout(()=>{d(void 0,void 0)},0)},[t,d]),(0,s.useEffect)(()=>{if(o.current){let e=o.current.nativeElement||o.current;if(e){let t=e.querySelector('input[type="hidden"]');t&&""===t.value&&d&&d(void 0,void 0)}}},[h,d]);let m=""===h?void 0:h;return(0,r.jsx)(a.default,{ref:o,...n,value:m,onChange:(e,t)=>{let l=""===e?void 0:e;console.log("FormSelect onChange:",{placeholder:n.placeholder,originalValue:e,safeValue:l,option:t,isEmptyString:""===e,isUndefined:void 0===e,isValidValue:u.has(e),availableValues:Array.from(u)}),d&&d(l,t)},options:l,children:i})}}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,9427,1157,4863,8236,9617,1330,5470,8847,2971,4938,1744],function(){return e(e.s=16478)}),_N_E=e.O()}]);