exports.id=996,exports.ids=[996],exports.modules={14945:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},6548:(e,t,r)=>{Promise.resolve().then(r.bind(r,46621))},46621:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AntdProvider:()=>x});var s=r(95344),a=r(3729),i=r(90263),o=r(32979),n=r(23834),c=r(48869),l=r.n(c);r(25434);var d=r(26772),u=r.n(d),h=r(43158),g=r(67023);let m=(0,h.Ue)()((0,g.mW)((0,g.tJ)((e,t)=>({employees:[],selectedEmployee:null,employeesLoading:!1,departments:[],selectedDepartment:null,departmentsLoading:!1,roles:[],selectedRole:null,rolesLoading:!1,permissions:[],filters:{},setEmployees:t=>{e({employees:t})},setSelectedEmployee:t=>e({selectedEmployee:t}),setEmployeesLoading:t=>e({employeesLoading:t}),setDepartments:t=>{e({departments:t})},setSelectedDepartment:t=>e({selectedDepartment:t}),setDepartmentsLoading:t=>e({departmentsLoading:t}),setRoles:t=>{e({roles:t})},setSelectedRole:t=>e({selectedRole:t}),setRolesLoading:t=>e({rolesLoading:t}),setPermissions:t=>e({permissions:t}),setFilters:t=>e(e=>({filters:{...e.filters,...t}})),clearFilters:()=>e({filters:{}}),getEmployeesByDepartment:e=>t().employees.filter(t=>t.department===e),getDepartmentEmployeeCount:e=>t().employees.filter(t=>t.department===e).length,generateNextEmployeeCode:()=>{let e=t().employees.map(e=>e.employeeCode),r=e.filter(e=>/^A\d{3}$/.test(e));if(e.filter(e=>/^A\d{4}$/.test(e)),r.length<999)for(let t=1;t<=999;t++){let r=`A${t.toString().padStart(3,"0")}`;if(!e.includes(r))return r}for(let t=1e3;t<=9999;t++){let r=`A${t}`;if(!e.includes(r))return r}return"A0001"}}),{name:"employee-store",partialize:e=>({employees:e.employees,departments:e.departments,roles:e.roles,permissions:e.permissions}),version:1,migrate:(e,t)=>0===t?{employees:e.employees||[],departments:e.departments||[],roles:e.roles||[],permissions:e.permissions||[]}:e}))),p={customers:[],selectedCustomer:null,customerVisits:[],selectedVisit:null,customerContacts:[],loading:!1,visitsLoading:!1,contactsLoading:!1,filters:{},pagination:{current:1,pageSize:10,total:0}},R=(0,h.Ue)()((0,g.mW)((0,g.tJ)((e,t)=>({...p,setCustomers:t=>e({customers:t}),getCustomerById:e=>t().customers.find(t=>t.id===e),setSelectedCustomer:t=>e({selectedCustomer:t}),setCustomerVisits:t=>e({customerVisits:t}),getVisitsByCustomer:e=>t().customerVisits.filter(t=>t.customerId===e),setSelectedVisit:t=>e({selectedVisit:t}),setCustomerContacts:t=>e({customerContacts:t}),getContactsByCustomer:e=>t().customerContacts.filter(t=>t.customerId===e),setLoading:t=>e({loading:t}),setVisitsLoading:t=>e({visitsLoading:t}),setContactsLoading:t=>e({contactsLoading:t}),setFilters:t=>e(e=>({filters:{...e.filters,...t}})),clearFilters:()=>e({filters:{}}),getFilteredCustomers:()=>{let{customers:e,filters:r}=t();return e.filter(e=>{if(r.customerLevel&&e.customerLevel!==r.customerLevel||r.customerCategory&&e.customerCategory!==r.customerCategory||r.status&&e.status!==r.status||r.region&&e.region!==r.region)return!1;if(r.searchKeyword){let t=r.searchKeyword.toLowerCase();return e.customerName.toLowerCase().includes(t)||e.customerCode.toLowerCase().includes(t)||e.contactPerson?.toLowerCase().includes(t)}return!0})},setPagination:t=>e(e=>({pagination:{...e.pagination,...t}})),getCustomerStatistics:()=>{let{customers:e}=t(),r=e.length,s=e.filter(e=>"A"===e.customerLevel).length,a=e.filter(e=>"B"===e.customerLevel).length,i=e.filter(e=>"C"===e.customerLevel).length,o=e.reduce((e,t)=>e+(t.annualSalesAmount||0),0);return{total:r,aLevel:s,bLevel:a,cLevel:i,totalSales:o,totalCredit:e.reduce((e,t)=>e+(t.creditLimit||0),0),usedCredit:e.reduce((e,t)=>e+(t.usedCredit||0),0),activeCustomers:e.filter(e=>"active"===e.status).length}},checkCreditLimit:(e,r)=>{let s=t().getCustomerById(e);return!!s&&(s.usedCredit||0)+r<=(s.creditLimit||0)},evaluateCustomerLevel:e=>{let r=t().getCustomerById(e);if(!r)return"C";let s=r.annualSalesAmount||0;return s>=1e6?"A":s>=5e5?"B":"C"},fetchAll:async t=>{e({loading:!0}),e({loading:!1})},fetchById:async t=>{e({loading:!0}),e({loading:!1})},fetchCustomerVisits:async t=>{e({visitsLoading:!0}),e({visitsLoading:!1})},fetchCustomerContacts:async t=>{e({contactsLoading:!0}),e({contactsLoading:!1})},fetchCustomers:async e=>t().fetchAll(e),fetchCustomerById:async e=>t().fetchById(e),reset:()=>e(p)}),{name:"customer-store",partialize:e=>({customers:e.customers,customerVisits:e.customerVisits,customerContacts:e.customerContacts,filters:e.filters,pagination:e.pagination})}),{name:"customer-store-devtools"}));var y=r(56870),E=r(5045),f=r(37637);class S{static async syncSingleProductInventory(e){try{let t=O.getState().productInventory.find(t=>t.id===e);if(!t)return{success:!1,message:"产品库存记录不存在",updatedFields:[]};let s=await f.dataAccessManager.products.getByCode(t.productCode);if("success"!==s.status||!s.data)return{success:!1,message:`产品编码 ${t.productCode} 在产品数据模块中不存在`,updatedFields:[]};let a=s.data,i=[],o={};if(t.productName!==a.modelName&&(o.productName=a.modelName,i.push("产品名称")),!(i.length>0))return{success:!0,message:"数据已是最新，无需同步",updatedFields:[]};{let{dataAccessManager:t}=await Promise.resolve().then(r.bind(r,37637));return await t.inventory.update(e,o),{success:!0,message:`成功同步 ${i.join("、")}`,updatedFields:i}}}catch(e){return{success:!1,message:`同步失败: ${e instanceof Error?e.message:"未知错误"}`,updatedFields:[]}}}static async batchSyncProductInventories(){try{let e=O.getState().productInventory,t=[],r=0,s=0;for(let a of e){let e=await this.syncSingleProductInventory(a.id);t.push({productCode:a.productCode,productName:a.productName,success:e.success,message:e.message,updatedFields:e.updatedFields}),e.success?r++:s++}return{success:0===s,totalCount:e.length,syncedCount:r,failedCount:s,details:t}}catch(e){return{success:!1,totalCount:0,syncedCount:0,failedCount:0,details:[]}}}static async handleProductModelChange(e,t){try{let s=O.getState().productInventory.filter(t=>t.productCode===e.modelCode);if(0===s.length)return{success:!0,affectedInventories:0,message:"没有相关的库存记录需要同步"};switch(t){case"create":return{success:!0,affectedInventories:s.length,message:`产品 ${e.modelCode} 已创建，相关库存记录已关联`};case"update":for(let t of s)if(t.productName!==e.modelName){let{dataAccessManager:s}=await Promise.resolve().then(r.bind(r,37637));await s.inventory.update(t.productCode,{productName:e.modelName})}return{success:!0,affectedInventories:s.length,message:`已同步 ${s.length} 条库存记录的产品信息`};case"delete":return{success:!1,affectedInventories:s.length,message:`产品已删除，但仍有 ${s.length} 条库存记录需要处理`};case"status_change":return e.status,{success:!0,affectedInventories:s.length,message:`产品状态已变更为 ${"active"===e.status?"激活":"停用"}`};default:return{success:!1,affectedInventories:s.length,message:"未知的变更类型"}}}catch(e){return{success:!1,affectedInventories:0,message:`处理失败: ${e instanceof Error?e.message:"未知错误"}`}}}static async checkAndRepairDataInconsistency(){try{let e=O.getState().productInventory,t=[],s=0,a=0;for(let i of e){let e=await E.InventoryValueCalculationService.validateProductConsistency(i);if(!e.isValid)for(let o of(s+=e.issues.length,e.issues)){let e=!1,s="需要手动处理";if(o.includes("名称不一致")){let t=await E.InventoryValueCalculationService.syncProductName(i);if(t){let{dataAccessManager:o}=await Promise.resolve().then(r.bind(r,37637));await o.inventory.update(i.productCode,{productName:t}),e=!0,s="已自动同步产品名称",a++}}t.push({productCode:i.productCode,issueType:this.getIssueType(o),description:o,fixed:e,action:s})}}return{success:!0,totalChecked:e.length,issuesFound:s,issuesFixed:a,remainingIssues:s-a,details:t}}catch(e){return{success:!1,totalChecked:0,issuesFound:0,issuesFixed:0,remainingIssues:0,details:[]}}}static getIssueType(e){return e.includes("不存在")?"产品不存在":e.includes("已停用")?"产品已停用":e.includes("名称不一致")?"名称不一致":e.includes("价格")?"价格问题":"其他问题"}static async validateNewProductInventory(e){let t=[],r=[];try{let s=await E.InventoryValueCalculationService.getProductDetails(e.productCode);return s.productModel?(s.isActive||r.push(`产品 ${e.productCode} 已停用`),e.productName!==s.productModel.modelName&&r.push(`建议使用产品数据模块中的标准名称: "${s.productModel.modelName}"`),s.hasValidPrice||r.push(`产品 ${e.productCode} 的价格未设置，库存价值将显示为0`)):t.push(`产品编码 ${e.productCode} 在产品数据模块中不存在`),e.productCode.trim()||t.push("产品编码不能为空"),e.productName.trim()||t.push("产品名称不能为空"),e.currentStock<0&&t.push("当前库存不能为负数"),e.safetyStock<0&&t.push("安全库存不能为负数"),e.maxStock<=e.safetyStock&&t.push("最大库存必须大于安全库存"),{isValid:0===t.length,errors:t,warnings:r}}catch(e){return{isValid:!1,errors:["验证过程中发生错误"],warnings:[]}}}static async getDataSyncStatusReport(){try{let e=O.getState().productInventory,t=await E.InventoryValueCalculationService.batchValidateProductConsistency(e);return{totalInventories:e.length,consistentCount:t.validCount,inconsistentCount:t.invalidCount,missingProductCount:t.summary.missingProducts,inactiveProductCount:t.summary.inactiveProducts,priceIssueCount:t.summary.priceIssues,lastSyncTime:new Date().toISOString()}}catch(e){return{totalInventories:0,consistentCount:0,inconsistentCount:0,missingProductCount:0,inactiveProductCount:0,priceIssueCount:0}}}}let D={productInventory:[],selectedProducts:[],materialInventory:[],selectedMaterials:[],stockAdjustments:[],replenishmentAlerts:[],inventoryChecks:[],currentCheck:null,loading:!1,adjustmentLoading:!1,checkLoading:!1,filters:{}},O=(0,h.Ue)()((0,g.mW)((0,g.tJ)((e,t)=>({...D,setProductInventory:t=>e({productInventory:t}),setSelectedProducts:t=>e({selectedProducts:t}),setMaterialInventory:t=>e({materialInventory:t}),setSelectedMaterials:t=>e({selectedMaterials:t}),getStockAdjustmentHistory:e=>{let{stockAdjustments:r}=t();return r.filter(t=>t.itemCode===e).sort((e,t)=>new Date(t.adjustmentDate).getTime()-new Date(e.adjustmentDate).getTime())},getActiveAlerts:()=>{let{replenishmentAlerts:e}=t();return e.filter(e=>"pending"===e.status)},setCurrentCheck:t=>e({currentCheck:t}),updateCheckItem:(t,r,s)=>e(e=>({inventoryChecks:e.inventoryChecks.map(e=>e.id===t?{...e,items:e.items.map(e=>e.id===r?{...e,...s}:e)}:e),currentCheck:e.currentCheck?.id===t&&e.currentCheck?{...e.currentCheck,items:e.currentCheck.items.map(e=>e.id===r?{...e,...s}:e)}:e.currentCheck})),setLoading:t=>e({loading:t}),setAdjustmentLoading:t=>e({adjustmentLoading:t}),setCheckLoading:t=>e({checkLoading:t}),setFilters:t=>e(e=>({filters:{...e.filters,...t}})),clearFilters:()=>e({filters:{}}),getWarehouseStatistics:()=>{let{productInventory:e,materialInventory:s,replenishmentAlerts:a,inventoryChecks:i}=t(),{InventoryValueCalculationService:o}=r(5045),n=o.batchCalculateProductValues(e);return{totalProducts:e.length,totalMaterials:s.length,totalProductValue:n.summary.totalValue,totalMaterialValue:s.reduce((e,t)=>e+t.totalValue,0),lowStockProducts:e.filter(e=>"low"===e.status).length,lowStockMaterials:s.filter(e=>"low"===e.status).length,activeAlerts:a.filter(e=>"pending"===e.status).length,pendingChecks:i.filter(e=>"planning"===e.status||"in_progress"===e.status).length}},adjustStock:(e,r,s,a,i)=>{Date.now().toString(),new Date().toISOString(),"product"===r?t().productInventory.find(t=>t.productCode===e):t().materialInventory.find(t=>t.materialCode===e)},transferStock:(e,r,s,a,i,o,n)=>{Date.now().toString(),new Date().toISOString(),"product"===r?t().productInventory.find(t=>t.productCode===e):t().materialInventory.find(t=>t.materialCode===e)},checkLowStock:()=>{let{productInventory:s,materialInventory:a}=t(),i=[];s.forEach(e=>{e.currentStock<=e.safetyStock&&i.push({id:`alert_${Date.now()}_${e.id}`,itemCode:e.productCode,itemName:e.productName,itemType:"product",currentStock:e.currentStock,minStock:e.safetyStock,shortage:e.safetyStock-e.currentStock,urgencyLevel:e.currentStock<.5*e.safetyStock?"high":"medium",suggestedQuantity:e.maxStock-e.currentStock,estimatedCost:(()=>{let{InventoryValueCalculationService:t}=r(5045),s=t.calculateProductValue(e);return(e.maxStock-e.currentStock)*s.unitPrice})(),supplierName:e.supplier||"未知供应商",leadTime:7,status:"pending"})}),a.forEach(e=>{e.currentStock<=e.minStock&&i.push({id:`alert_${Date.now()}_${e.id}`,itemCode:e.materialCode,itemName:e.materialName,itemType:"material",currentStock:e.currentStock,minStock:e.minStock,shortage:e.minStock-e.currentStock,urgencyLevel:e.currentStock<.5*e.minStock?"high":"medium",suggestedQuantity:e.maxStock-e.currentStock,estimatedCost:(e.maxStock-e.currentStock)*e.unitPrice,supplierName:e.supplierName,leadTime:5,status:"pending"})}),e(e=>({replenishmentAlerts:[...e.replenishmentAlerts,...i]}))},getProductInventoryWithValue:()=>{let{productInventory:e}=t(),{results:r}=E.InventoryValueCalculationService.batchCalculateProductValues(e);return r.map(e=>({...e,category:"未分类"}))},getProductInventoryByCode:e=>{let{productInventory:r}=t();return r.find(t=>t.productCode===e)},syncProductInventoryData:async()=>{try{let e=await S.batchSyncProductInventories();return{success:e.success,syncedCount:e.syncedCount,errors:e.details.filter(e=>!e.success).map(e=>e.message)}}catch(e){return{success:!1,syncedCount:0,errors:["同步过程中发生错误"]}}},validateProductInventoryConsistency:()=>{let{productInventory:e}=t(),r=e.filter(e=>e.productCode&&e.productName).length,s=e.length-r;return{validCount:r,invalidCount:s,issues:s>0?["存在产品编码或名称为空的库存记录"]:[]}},reset:()=>e(D)}),{name:"warehouse-store",partialize:e=>({productInventory:e.productInventory,materialInventory:e.materialInventory,stockAdjustments:e.stockAdjustments,replenishmentAlerts:e.replenishmentAlerts,inventoryChecks:e.inventoryChecks,filters:e.filters})}),{name:"warehouse-store"}));var C=r(69224);let v={employees:!1,customers:!1,products:!1,inventory:!1,orders:!1},N=(e,t=!1)=>{if(t||!v[e.flagKey]){if(!t&&e.checkExistingData()){v[e.flagKey]=!0;return}try{e.initializeData(),v[e.flagKey]=!0}catch(t){console.error(`❌ [dataInitializationService] ${e.name}初始化失败:`,t),v[e.flagKey]=!1}}},I=(e=!1)=>{let t=m.getState();N({name:"员工数据",flagKey:"employees",checkExistingData:()=>t.employees.length>0,initializeData:()=>{t.setDepartments(C.seedDepartments),t.setRoles(C.seedRoles),t.setPermissions(C.seedPermissions),t.setEmployees(C.seedEmployees)},seedData:C.seedEmployees,logEmoji:"\uD83D\uDC65"},e)},T=(e=!1)=>{let t=R.getState();N({name:"客户数据",flagKey:"customers",checkExistingData:()=>t.customers.length>0,initializeData:()=>{t.setCustomers(C.seedCustomers)},seedData:C.seedCustomers,logEmoji:"\uD83C\uDFE2"},e)},_=(e=!1)=>{let t=y.rm.getState();N({name:"产品数据",flagKey:"products",checkExistingData:()=>t.productModels.length>0,initializeData:()=>{t.setProductModels(C.seedProductModels)},seedData:C.seedProductModels,logEmoji:"\uD83D\uDCE6"},e)},A=(e=!1)=>{console.log("\uD83D\uDCCB [dataInitializationService] initializeOrderData 被调用，force:",e),console.trace("\uD83D\uDD0D [dataInitializationService] initializeOrderData 调用栈:"),console.log("\uD83D\uDCCB [dataInitializationService] 订单数据由dataAccessManager自动管理")},w=(e=!1)=>{console.log("\uD83D\uDE80 [dataInitializationService] 开始初始化所有主数据，force:",e);try{I(e),T(e),_(e),B(e),A(e),console.log("✅ [dataInitializationService] 所有主数据初始化完成")}catch(e){console.error("❌ [dataInitializationService] 初始化失败:",e)}},B=(e=!1)=>{let t=O.getState();N({name:"库存数据",flagKey:"inventory",checkExistingData:()=>t.productInventory.length>0,initializeData:()=>{t.setProductInventory(C.seedProductInventory)},seedData:C.seedProductInventory,logEmoji:"\uD83D\uDCCA"},e)},k=!1,P=null,b=async()=>{if(console.log("\uD83D\uDE80 [DataInitializationProvider] performInitialization 被调用"),console.trace("\uD83D\uDD0D [DataInitializationProvider] 调用栈:"),k){console.log("⏭️ [DataInitializationProvider] 模块已初始化，跳过");return}try{console.log("\uD83D\uDD27 [DataInitializationProvider] 开始初始化过程"),k=!0,console.log("\uD83D\uDCCB [DataInitializationProvider] 调用 initializeAllMasterData()"),w(),console.log("✅ [DataInitializationProvider] 初始化完成")}catch(e){throw console.error("❌ [DataInitializationProvider] 初始化失败:",e),k=!1,e}},L=({children:e})=>{let t=(0,a.useRef)(0),r=(0,a.useRef)(!1),i=(0,a.useRef)(!1);return t.current+=1,i.current||(console.log("\uD83D\uDD04 [DataInitializationProvider] 组件首次渲染"),i.current=!0),(0,a.useEffect)(()=>{!r.current&&(P||(console.log("\uD83D\uDE80 [DataInitializationProvider] 开始组件级初始化"),r.current=!0,console.log("\uD83D\uDCCB [DataInitializationProvider] 创建初始化Promise"),(P=b()).catch(e=>{P=null,r.current=!1})))},[]),s.jsx(s.Fragment,{children:e})};l().extend(u()),l().locale("zh-cn");let x=({children:e})=>{let t=(0,a.useMemo)(()=>({token:{colorPrimary:"#0ea5e9",colorSuccess:"#10b981",colorWarning:"#f59e0b",colorError:"#ef4444",colorInfo:"#3b82f6",borderRadius:8,fontFamily:'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif'},components:{Layout:{headerBg:"#ffffff",siderBg:"#ffffff",bodyBg:"#f9fafb"},Menu:{itemSelectedBg:"#e6f7ff",itemHoverBg:"#f0f9ff",itemSelectedColor:"#0ea5e9"},Button:{borderRadius:8},Card:{borderRadius:12}}}),[]),r=(0,a.useMemo)(()=>({strict:!1}),[]);return s.jsx(i.ZP,{locale:n.Z,warning:r,theme:t,children:s.jsx(o.Z,{children:s.jsx(L,{children:e})})})}},69224:(e,t,r)=>{"use strict";r.r(t),r.d(t,{seedCustomers:()=>a,seedDepartments:()=>n,seedEmployees:()=>s,seedPermissions:()=>l,seedProductInventory:()=>o,seedProductModels:()=>i,seedRoles:()=>c,seedSalesOrders:()=>d});let s=[{id:"emp_001",employeeCode:"A001",name:"张明华",department:"生产部",position:"生产主管",phone:"138********",email:"<EMAIL>",directSupervisor:"emp_004",subordinates:[],isDepartmentHead:!1,role:"manager",permissions:["production_read","production_write","employee_read"],status:"active",hireDate:"2022-03-15",remark:"经验丰富的生产管理人员，负责车间日常生产调度",createdAt:"2022-03-15",updatedAt:"2024-01-15"},{id:"emp_002",employeeCode:"A002",name:"李晓红",department:"销售部",position:"销售经理",phone:"139********",email:"<EMAIL>",directSupervisor:"emp_004",subordinates:["emp_003"],isDepartmentHead:!0,role:"manager",permissions:["sales_read","sales_write","customer_read","customer_write","employee_read"],status:"active",hireDate:"2021-08-20",remark:"优秀的销售团队负责人，客户关系维护能力强",createdAt:"2021-08-20",updatedAt:"2024-01-15"},{id:"emp_003",employeeCode:"A003",name:"王建国",department:"销售部",position:"销售代表",phone:"13765432109",email:"<EMAIL>",directSupervisor:"emp_002",subordinates:[],isDepartmentHead:!1,role:"employee",permissions:["sales_read","customer_read"],status:"active",hireDate:"2023-01-10",remark:"新入职销售人员，主要负责华东地区客户开发",createdAt:"2023-01-10",updatedAt:"2024-01-15"},{id:"emp_005",employeeCode:"A005",name:"系统默认",department:"销售部",position:"系统销售员",phone:"13800000000",email:"<EMAIL>",directSupervisor:"emp_002",subordinates:[],isDepartmentHead:!1,role:"sales",permissions:["sales_read","sales_write","customer_read","customer_write"],status:"active",hireDate:"2024-01-01",remark:"系统默认销售员，用于新建订单时的默认业务员选择",createdAt:"2024-01-01",updatedAt:"2024-01-01"},{id:"emp_004",employeeCode:"A004",name:"陈总",department:"管理层",position:"总经理",phone:"***********",email:"<EMAIL>",directSupervisor:void 0,subordinates:["emp_001","emp_002"],isDepartmentHead:!0,role:"admin",permissions:["admin_all"],status:"active",hireDate:"2020-01-01",remark:"公司创始人兼总经理，全面负责公司运营管理",createdAt:"2020-01-01",updatedAt:"2024-01-15"}],a=[{id:"cust_001",customerCode:"KH0001",customerName:"上海绿色餐饮有限公司",contactPerson:"刘经理",contactPhone:"021-********",referrer:"行业展会",salesRepresentative:"emp_002",bankName:"中国工商银行上海分行",bankAccount:"********90********9",taxNumber:"91310000********9X",customerLevel:"A",customerCategory:"important",creditLimit:5e5,usedCredit:12e4,annualSalesAmount:12e5,contactEmail:"<EMAIL>",address:"上海市浦东新区张江高科技园区",paymentTerms:"月结30天",discountRate:.05,preferredProducts:["P00001","P00002"],status:"active",remark:"重要客户，连锁餐饮企业，订单量大且稳定",createdAt:"2023-06-15",updatedAt:"2024-01-15"},{id:"cust_002",customerCode:"KH0002",customerName:"杭州美食城餐具采购中心",contactPerson:"张采购",contactPhone:"0571-********",referrer:"老客户介绍",salesRepresentative:"emp_003",bankName:"中国建设银行杭州分行",bankAccount:"9********09********",taxNumber:"913300009********Y",customerLevel:"B",customerCategory:"general",creditLimit:2e5,usedCredit:45e3,annualSalesAmount:48e4,contactEmail:"<EMAIL>",address:"杭州市西湖区文三路美食城",paymentTerms:"现款现货",discountRate:.03,preferredProducts:["P00003","P00004"],status:"active",remark:"美食城集中采购，主要采购方形饭盒和托盘",createdAt:"2023-09-20",updatedAt:"2024-01-15"},{id:"cust_003",customerCode:"KH0003",customerName:"南京校园餐具配送有限公司",contactPerson:"赵主任",contactPhone:"025-********",referrer:"网络搜索",salesRepresentative:"emp_002",bankName:"中国农业银行南京分行",bankAccount:"5555666677778888999",taxNumber:"91320000555666777Z",customerLevel:"B",customerCategory:"general",creditLimit:15e4,usedCredit:3e4,annualSalesAmount:36e4,contactEmail:"<EMAIL>",address:"南京市鼓楼区中央路学校后勤服务中心",paymentTerms:"季度结算",discountRate:.04,preferredProducts:["P00001","P00003"],status:"active",remark:"学校餐具配送商，注重产品安全性和环保性",createdAt:"2023-11-10",updatedAt:"2024-01-15"},{id:"cust_004",customerCode:"KH0004",customerName:"苏州快餐连锁总部",contactPerson:"孙总监",contactPhone:"0512-********",referrer:"李晓红",salesRepresentative:"emp_003",bankName:"中国银行苏州分行",bankAccount:"1111222233334444555",taxNumber:"91320500111222333A",customerLevel:"A",customerCategory:"important",creditLimit:8e5,usedCredit:2e5,annualSalesAmount:18e5,contactEmail:"<EMAIL>",address:"苏州市工业园区现代大道快餐总部大厦",paymentTerms:"月结45天",discountRate:.06,preferredProducts:["P00002","P00004"],status:"active",remark:"大型快餐连锁企业，年采购量大，是重点维护客户",createdAt:"2023-04-25",updatedAt:"2024-01-15"}],i=[{id:"prod_001",modelCode:"P00001",modelName:"圆形餐盘202mm",formingMold:"M-JX-05",formingMoldQuantity:8,hotPressMold:"M-RY-12",hotPressMoldQuantity:4,formingPiecePrice:8.5,hotPressPiecePrice:5.2,productPrice:.18,productWeight:15.2,boxSpecification:"35\xd725\xd718 cm",packingQuantity:120,piecesPerMold:8,moldId:"M-JX-05",status:"active",createdAt:"2023-01-15",updatedAt:"2024-01-15"},{id:"prod_002",modelCode:"P00002",modelName:"圆形餐盘180mm",formingMold:"M-JX-05",formingMoldQuantity:10,hotPressMold:"M-RY-15",hotPressMoldQuantity:6,formingPiecePrice:7.8,hotPressPiecePrice:4.5,productPrice:.15,productWeight:12.8,boxSpecification:"32\xd722\xd716 cm",packingQuantity:150,piecesPerMold:10,moldId:"M-JX-05",status:"active",createdAt:"2023-02-20",updatedAt:"2024-01-15"},{id:"prod_003",modelCode:"P00003",modelName:"方形饭盒150mm",formingMold:"M-JX-08",formingMoldQuantity:6,hotPressMold:"M-RY-18",hotPressMoldQuantity:3,formingPiecePrice:9.2,hotPressPiecePrice:6,productPrice:.22,productWeight:18.5,boxSpecification:"40\xd730\xd720 cm",packingQuantity:100,piecesPerMold:6,moldId:"M-JX-08",status:"active",createdAt:"2023-03-10",updatedAt:"2024-01-15"},{id:"prod_004",modelCode:"P00004",modelName:"托盘300mm",formingMold:"M-JX-12",formingMoldQuantity:4,hotPressMold:"M-RY-25",hotPressMoldQuantity:2,formingPiecePrice:12,hotPressPiecePrice:8.5,productPrice:.35,productWeight:28,boxSpecification:"50\xd735\xd725 cm",packingQuantity:80,piecesPerMold:4,moldId:"M-JX-12",status:"active",createdAt:"2023-04-05",updatedAt:"2024-01-15"},{id:"prod_005",modelCode:"P00005",modelName:"一次性水杯250ml",formingMold:"M-JX-15",formingMoldQuantity:12,hotPressMold:"M-RY-30",hotPressMoldQuantity:8,formingPiecePrice:6.5,hotPressPiecePrice:3.8,productPrice:.08,productWeight:8.5,boxSpecification:"28\xd720\xd715 cm",packingQuantity:200,piecesPerMold:12,moldId:"M-JX-15",status:"active",createdAt:"2023-05-12",updatedAt:"2024-01-15"},{id:"prod_006",modelCode:"P00006",modelName:"保温饭盒套装",formingMold:"M-JX-20",formingMoldQuantity:2,hotPressMold:"M-RY-35",hotPressMoldQuantity:1,formingPiecePrice:25,hotPressPiecePrice:15,productPrice:2.5,productWeight:450,boxSpecification:"45\xd735\xd730 cm",packingQuantity:20,piecesPerMold:2,moldId:"M-JX-20",status:"active",createdAt:"2023-06-18",updatedAt:"2024-01-15"}],o=[{id:"inv_001",productCode:"P00001",productName:"圆形餐盘202mm",specification:"直径202mm，厚度2.5mm，环保材质",currentStock:280,safetyStock:80,maxStock:500,location:"A区-01-01",batchNumber:"B20241201001",productionDate:"2024-12-01",expiryDate:"2026-12-01",status:"normal",lastUpdated:"2024-12-15T10:30:00.000Z",supplier:"华南包装材料有限公司"},{id:"inv_002",productCode:"P00002",productName:"圆形餐盘180mm",specification:"直径180mm，厚度2.3mm，食品级材质",currentStock:45,safetyStock:60,maxStock:400,location:"A区-01-02",batchNumber:"B20241128002",productionDate:"2024-11-28",expiryDate:"2026-11-28",status:"low",lastUpdated:"2024-12-14T16:45:00.000Z",supplier:"华南包装材料有限公司"},{id:"inv_003",productCode:"P00003",productName:"方形饭盒150mm",specification:"150\xd7150\xd745mm，带盖设计，微波炉适用",currentStock:520,safetyStock:100,maxStock:600,location:"B区-02-01",batchNumber:"B20241205003",productionDate:"2024-12-05",expiryDate:"2026-12-05",status:"high",lastUpdated:"2024-12-16T09:15:00.000Z",supplier:"绿色包装科技股份有限公司"},{id:"inv_004",productCode:"P00004",productName:"托盘300mm",specification:"300\xd7200\xd725mm，承重5kg，防滑设计",currentStock:150,safetyStock:50,maxStock:300,location:"C区-03-01",batchNumber:"B20241210004",productionDate:"2024-12-10",expiryDate:"2027-12-10",status:"normal",lastUpdated:"2024-12-16T14:20:00.000Z",supplier:"环保餐具制造有限公司"},{id:"inv_005",productCode:"P00005",productName:"一次性水杯250ml",specification:"容量250ml，双层隔热，可降解材质",currentStock:380,safetyStock:120,maxStock:800,location:"D区-04-01",batchNumber:"B20241208005",productionDate:"2024-12-08",expiryDate:"2025-12-08",status:"normal",lastUpdated:"2024-12-15T11:30:00.000Z",supplier:"生态包装材料公司"},{id:"inv_006",productCode:"P00006",productName:"保温饭盒套装",specification:"三层设计，304不锈钢内胆，保温6小时",currentStock:25,safetyStock:40,maxStock:200,location:"E区-05-01",batchNumber:"B20241203006",productionDate:"2024-12-03",expiryDate:"2029-12-03",status:"low",lastUpdated:"2024-12-16T08:45:00.000Z",supplier:"优质餐具制造商"}],n=[{id:"dept_001",departmentCode:"MGMT",departmentName:"管理层",parentDepartmentId:void 0,departmentHead:"emp_004",description:"公司高级管理层，负责战略决策和整体运营",employeeCount:1,level:1,status:"active",createdAt:"2020-01-01",updatedAt:"2024-01-15"},{id:"dept_002",departmentCode:"SALES",departmentName:"销售部",parentDepartmentId:"dept_001",departmentHead:"emp_002",description:"负责产品销售、客户关系维护和市场开拓",employeeCount:2,level:2,status:"active",createdAt:"2020-01-01",updatedAt:"2024-01-15"},{id:"dept_003",departmentCode:"PROD",departmentName:"生产部",parentDepartmentId:"dept_001",departmentHead:"emp_001",description:"负责产品生产、质量控制和生产计划管理",employeeCount:1,level:2,status:"active",createdAt:"2020-01-01",updatedAt:"2024-01-15"}],c=[{id:"role_001",roleCode:"ADMIN",roleName:"系统管理员",description:"拥有系统所有权限，可以管理用户、角色和系统配置",permissions:["admin_all"],isSystemRole:!0,status:"active",createdAt:"2020-01-01",updatedAt:"2024-01-15"},{id:"role_002",roleCode:"MANAGER",roleName:"部门经理",description:"部门管理权限，可以管理本部门员工和业务数据",permissions:["department_manage","employee_read","employee_write"],isSystemRole:!0,status:"active",createdAt:"2020-01-01",updatedAt:"2024-01-15"},{id:"role_003",roleCode:"EMPLOYEE",roleName:"普通员工",description:"基础员工权限，可以查看和操作相关业务数据",permissions:["basic_read","basic_write"],isSystemRole:!0,status:"active",createdAt:"2020-01-01",updatedAt:"2024-01-15"}],l=[{id:"perm_001",permissionCode:"admin_all",permissionName:"系统管理员权限",module:"system",action:"admin",description:"系统所有功能的完全访问权限",isSystemPermission:!0,createdAt:"2020-01-01"},{id:"perm_002",permissionCode:"sales_read",permissionName:"销售数据查看",module:"sales",action:"read",description:"查看销售订单、客户信息等销售相关数据",isSystemPermission:!0,createdAt:"2020-01-01"},{id:"perm_003",permissionCode:"sales_write",permissionName:"销售数据编辑",module:"sales",action:"write",description:"创建和编辑销售订单、客户信息等",isSystemPermission:!0,createdAt:"2020-01-01"},{id:"perm_004",permissionCode:"production_read",permissionName:"生产数据查看",module:"production",action:"read",description:"查看生产计划、工单、库存等生产相关数据",isSystemPermission:!0,createdAt:"2020-01-01"},{id:"perm_005",permissionCode:"production_write",permissionName:"生产数据编辑",module:"production",action:"write",description:"创建和编辑生产计划、工单等",isSystemPermission:!0,createdAt:"2020-01-01"}],d=[{id:"order_001",orderNumber:"XSDD202507040001",customerId:"cust_002",customerName:"杭州美食城餐具采购中心",customerContact:"张采购",orderDate:"2025-07-05",deliveryDate:"2025-07-15",promisedDeliveryDate:"2025-07-15",totalAmount:28500,discountAmount:1500,finalAmount:27e3,status:"pending",productionStatus:"not_started",paymentStatus:"unpaid",paymentTerms:"月结30天",salesRepresentative:"emp_002",customerCreditLevel:"A",mrpStatus:"not_started",items:[{id:"item_001",orderNumber:"XSDD202507040001",productName:"圆形餐盘202mm",productCode:"P00001",quantity:1e3,unit:"个",unitPrice:.18,totalPrice:180,deliveryQuantity:0,remainingQuantity:1e3,moldCode:"M-JX-05",productionWorkstation:"",batchNumber:"",deliveryDate:"2025-07-15",remark:"白色，食品级材质"},{id:"item_002",orderNumber:"XSDD202507040001",productName:"方形餐盒180mm",productCode:"P00002",quantity:800,unit:"个",unitPrice:.22,totalPrice:176,deliveryQuantity:0,remainingQuantity:800,moldCode:"M-JX-05",productionWorkstation:"",batchNumber:"",deliveryDate:"2025-07-15",remark:"透明盖，密封性好"}],changes:[],remark:"重要客户，优先生产",createdAt:"2025-07-05T09:30:00.000Z",updatedAt:"2025-07-05T09:30:00.000Z"},{id:"order_002",orderNumber:"XSDD202507040002",customerId:"cust_001",customerName:"上海绿色餐饮有限公司",customerContact:"刘经理",orderDate:"2025-07-04",deliveryDate:"2025-07-12",promisedDeliveryDate:"2025-07-12",totalAmount:15600,discountAmount:800,finalAmount:14800,status:"confirmed",productionStatus:"not_started",paymentStatus:"unpaid",paymentTerms:"货到付款",salesRepresentative:"emp_002",customerCreditLevel:"B",mrpStatus:"not_started",items:[{id:"item_003",orderNumber:"XSDD202507040002",productName:"汤碗深型150mm",productCode:"P00003",quantity:600,unit:"个",unitPrice:.25,totalPrice:150,deliveryQuantity:0,remainingQuantity:600,moldCode:"M-SB-08",productionWorkstation:"",batchNumber:"",deliveryDate:"2025-07-12",remark:"深色，耐高温"}],changes:[],remark:"连锁餐饮客户，按时交货",createdAt:"2025-07-04T14:20:00.000Z",updatedAt:"2025-07-04T16:45:00.000Z"},{id:"order_003",orderNumber:"XSDD202507030001",customerId:"cust_003",customerName:"北京快餐连锁总部",customerContact:"王总监",orderDate:"2025-07-03",deliveryDate:"2025-07-10",promisedDeliveryDate:"2025-07-10",totalAmount:42e3,discountAmount:2e3,finalAmount:4e4,status:"confirmed",productionStatus:"not_started",paymentStatus:"partial",paymentTerms:"预付50%，余款月结",salesRepresentative:"emp_003",customerCreditLevel:"C",mrpStatus:"not_started",items:[{id:"item_004",orderNumber:"XSDD202507030001",productName:"圆形餐盘202mm",productCode:"P00001",quantity:2e3,unit:"个",unitPrice:.18,totalPrice:360,deliveryQuantity:0,remainingQuantity:2e3,moldCode:"M-JX-05",productionWorkstation:"",batchNumber:"",deliveryDate:"2025-07-10",remark:"白色，统一规格"},{id:"item_005",orderNumber:"XSDD202507030001",productName:"打包盒长方形",productCode:"P00004",quantity:1500,unit:"个",unitPrice:.28,totalPrice:420,deliveryQuantity:0,remainingQuantity:1500,moldCode:"M-DB-15",productionWorkstation:"",batchNumber:"",deliveryDate:"2025-07-10",remark:"加厚型，适合外卖"}],changes:[],remark:"大客户订单，质量要求高",createdAt:"2025-07-03T11:15:00.000Z",updatedAt:"2025-07-03T15:30:00.000Z"},{id:"order_004",orderNumber:"XSDD202507020001",customerId:"cust_004",customerName:"深圳环保餐具批发商",customerContact:"李批发",orderDate:"2025-07-02",deliveryDate:"2025-07-08",promisedDeliveryDate:"2025-07-08",totalAmount:18900,discountAmount:900,finalAmount:18e3,status:"completed",productionStatus:"completed",paymentStatus:"paid",paymentTerms:"现金结算",salesRepresentative:"emp_002",customerCreditLevel:"A",mrpStatus:"completed",items:[{id:"item_006",orderNumber:"XSDD202507020001",productName:"方形餐盒180mm",productCode:"P00002",quantity:1200,unit:"个",unitPrice:.22,totalPrice:264,deliveryQuantity:1200,remainingQuantity:0,moldCode:"M-JX-05",productionWorkstation:"",batchNumber:"BATCH_20250702_001",deliveryDate:"2025-07-08",remark:"已完成生产交付"}],changes:[],remark:"已完成订单，客户满意",createdAt:"2025-07-02T08:45:00.000Z",updatedAt:"2025-07-08T17:20:00.000Z"}]},85287:(e,t,r)=>{"use strict";r.d(t,{s:()=>a});let s={dateFormat:"YYYYMMDD",sequenceLength:4,enableRandomSequence:!0,maxRetries:10};class a{static{this.ORDER_NUMBER_PATTERNS={SALES_ORDER:/^XSDD\d{8}\d{4}$/,PRODUCTION_ORDER:/^PO-XSDD\d{8}\d{4}$/,WORK_ORDER:/^WO-PO-XSDD\d{8}\d{4}$/}}static{this.ORDER_NUMBER_FORMATS={SALES_ORDER:"XSDD + YYYYMMDD + 4位序号",PRODUCTION_ORDER:"PO-{销售订单号}",WORK_ORDER:"WO-{生产订单号}"}}static{this.config=s}static updateConfig(e){this.config={...this.config,...e}}static getDateString(){let e=new Date;return"YYYY-MM-DD"===this.config.dateFormat?e.toISOString().slice(0,10):e.toISOString().slice(0,10).replace(/-/g,"")}static generateSequence(){return this.config.enableRandomSequence?Math.floor(Math.random()*(Math.pow(10,this.config.sequenceLength)-1)).toString().padStart(this.config.sequenceLength,"0"):Date.now().toString().slice(-this.config.sequenceLength)}static generateSalesOrderId(){let e=this.getDateString(),t=this.generateSequence();return`XSDD${e}${t}`}static generateProductionOrderId(e){if(!this.validateSalesOrderNumber(e))throw Error(`无效的销售订单号格式: ${e}`);return`PO-${e}`}static generateWorkOrderId(e){if(!e||!e.startsWith("PO-"))throw Error(`无效的生产订单号格式: ${e}`);return`WO-${e}`}static generateWorkOrderIdFromSalesOrder(e){if(!this.validateSalesOrderNumber(e))throw Error(`无效的销售订单号格式: ${e}`);return`WO-${e}`}static extractSalesOrderNumber(e){if(!e||!e.startsWith("PO-"))throw Error(`无效的生产订单号格式: ${e}`);return e.replace("PO-","")}static extractSalesOrderNumberFromWorkOrder(e){if(!e||!e.startsWith("WO-"))throw Error(`无效的工单号格式: ${e}`);return e.replace("WO-","")}static validateSalesOrderNumber(e){return!!e&&"string"==typeof e&&this.ORDER_NUMBER_PATTERNS.SALES_ORDER.test(e)}static validateProductionOrderNumber(e){return!!e&&"string"==typeof e&&this.ORDER_NUMBER_PATTERNS.PRODUCTION_ORDER.test(e)}static validateWorkOrderNumber(e){return!!e&&"string"==typeof e&&this.ORDER_NUMBER_PATTERNS.WORK_ORDER.test(e)}static{this.batchCounter=0}static generateBatchNumber(){let e=this.getDateString();this.batchCounter++;let t=(Date.now().toString().slice(-3)+this.batchCounter.toString().padStart(1,"0")).slice(-4);return`PC${e}${t}`}static generateProductCode(e=[]){let t=e.filter(e=>/^P\d{5}$/.test(e)).map(e=>parseInt(e.substring(1),10)).sort((e,t)=>t-e),r=t.length>0?t[0]+1:1;return`P${r.toString().padStart(5,"0")}`}static generateEmployeeCode(e=[]){let t=e.filter(e=>/^A\d{3}$/.test(e)).map(e=>parseInt(e.substring(1),10)).sort((e,t)=>t-e),r=t.length>0?t[0]+1:1;return`A${r.toString().padStart(3,"0")}`}static generateCustomerCode(e=[]){let t=e.filter(e=>/^C\d{4}$/.test(e)).map(e=>parseInt(e.substring(1),10)).sort((e,t)=>t-e),r=t.length>0?t[0]+1:1;return`C${r.toString().padStart(4,"0")}`}static getIdType(e){return this.validateSalesOrderNumber(e)?"sales_order":this.validateProductionOrderNumber(e)?"production_order":this.validateWorkOrderNumber(e)?"work_order":/^P\d{5}$/.test(e)?"product":/^A\d{3}$/.test(e)?"employee":/^C\d{4}$/.test(e)?"customer":/^PC\d{8}\d{4}$/.test(e)?"batch":"unknown"}static formatIdForDisplay(e){switch(this.getIdType(e)){case"sales_order":return`销售订单: ${e}`;case"production_order":return`生产订单: ${e}`;case"work_order":return`工单: ${e}`;case"product":return`产品: ${e}`;case"employee":return`员工: ${e}`;case"customer":return`客户: ${e}`;case"batch":return`批次: ${e}`;default:return e}}}},53738:(e,t,r)=>{"use strict";r.d(t,{Bx:()=>s,Gn:()=>a});let s={OK:200,CREATED:201,NO_CONTENT:204,BAD_REQUEST:400,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,CONFLICT:409,INTERNAL_SERVER_ERROR:500,NOT_IMPLEMENTED:501,SERVICE_UNAVAILABLE:503},a="v1"},37637:(e,t,r)=>{"use strict";r.r(t),r.d(t,{DEFAULT_CACHE_CONFIG:()=>k,DataAccessManager:()=>b,costCalculations:()=>F,customers:()=>M,dataAccessManager:()=>L,employees:()=>$,inventory:()=>W,productionOrders:()=>U,productionWorkOrders:()=>V,products:()=>x,workstations:()=>Q});var s=r(53738),a=r(53200),i=r(69224);class o{static getInstance(){return o.instance||(o.instance=new o,o.instance.initializeTestData()),o.instance}getAllProducts(){return[...this.products]}addProduct(e){this.products.push(e)}updateProduct(e,t){let r=this.products.findIndex(t=>t.id===e);return -1===r?null:(this.products[r]={...this.products[r],...t},this.products[r])}removeProduct(e){let t=this.products.findIndex(t=>t.id===e);return -1!==t&&(this.products.splice(t,1),!0)}findProductById(e){return this.products.find(t=>t.id===e)}findProductByCode(e){return this.products.find(t=>t.modelCode===e)}findProductsByCategory(e){return[]}findProductsByMold(e){return this.products.filter(t=>t.formingMold===e||t.moldId===e)}getActiveProducts(){return this.products.filter(e=>"active"===e.status)}reloadData(){this.initializeTestData()}searchProducts(e){let t=e.toLowerCase();return this.products.filter(e=>e.modelName.toLowerCase().includes(t)||e.modelCode.toLowerCase().includes(t))}initializeTestData(){this.products=[...i.seedProductModels]}constructor(){this.products=[]}}class n{constructor(){this.storage=o.getInstance()}static getInstance(){return n.instance||(n.instance=new n),n.instance}createResponse(e,t,r=s.Bx.OK){return a.N5.createSuccess(e,t,r)}createErrorResponse(e,t=s.Bx.INTERNAL_SERVER_ERROR){return a.N5.createStandardError(e,t)}async getProducts(e){try{let t=this.storage.getAllProducts();if(e?.filters&&(e.filters.status&&(t=t.filter(t=>t.status===e.filters.status)),e.filters.category),e?.search){let r=e.search.toLowerCase();t=t.filter(e=>e.modelCode.toLowerCase().includes(r)||e.modelName.toLowerCase().includes(r))}e?.sortBy&&t.sort((t,r)=>{let s=t[e.sortBy],a=r[e.sortBy],i="desc"===e.sortOrder?-1:1;return"string"==typeof s?s.localeCompare(a)*i:(s-a)*i});let r=e?.page||1,s=e?.pageSize||20,a=t.length,i=(r-1)*s,o=t.slice(i,i+s);return this.createResponse({items:o,pagination:{current:r,pageSize:s,total:a,totalPages:Math.ceil(a/s)}})}catch(e){return this.createErrorResponse("获取产品列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getProductById(e){try{let t=this.storage.findProductById(e);if(!t)return this.createErrorResponse(`产品ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createResponse(t)}catch(e){return this.createErrorResponse("获取产品详情失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getProductByCode(e){try{let t=this.storage.findProductByCode(e);if(!t)return this.createErrorResponse(`产品编码 ${e} 不存在`,s.Bx.NOT_FOUND);return this.createResponse(t)}catch(e){return this.createErrorResponse("获取产品详情失败",s.Bx.INTERNAL_SERVER_ERROR)}}async createProduct(e){try{if(e.modelCode&&this.storage.findProductByCode(e.modelCode))return this.createErrorResponse(`产品编码 ${e.modelCode} 已存在`,s.Bx.CONFLICT);let t=a._l.addCreation({...e,id:`product_${Date.now()}_${Math.random().toString(36).substring(2,11)}`});return this.storage.addProduct(t),this.createResponse(t,"success",s.Bx.CREATED)}catch(e){return this.createErrorResponse("创建产品失败",s.Bx.INTERNAL_SERVER_ERROR)}}async updateProduct(e,t){try{let r=this.storage.findProductById(e);if(!r)return this.createErrorResponse(`产品ID ${e} 不存在`,s.Bx.NOT_FOUND);if(t.modelCode&&t.modelCode!==r.modelCode){let r=this.storage.findProductByCode(t.modelCode);if(r&&r.id!==e)return this.createErrorResponse(`产品编码 ${t.modelCode} 已存在`,s.Bx.CONFLICT)}let i=a._l.addUpdate({...r,...t}),o=this.storage.updateProduct(e,i);if(o)return this.createResponse(o);return this.createErrorResponse("更新产品失败",s.Bx.INTERNAL_SERVER_ERROR)}catch(e){return this.createErrorResponse("更新产品失败",s.Bx.INTERNAL_SERVER_ERROR)}}async deleteProduct(e){try{if(!this.storage.findProductById(e))return this.createErrorResponse(`产品ID ${e} 不存在`,s.Bx.NOT_FOUND);if(this.storage.removeProduct(e))return this.createResponse(void 0,"success",s.Bx.NO_CONTENT);return this.createErrorResponse("删除产品失败",s.Bx.INTERNAL_SERVER_ERROR)}catch(e){return this.createErrorResponse("删除产品失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getActiveProducts(){try{let e=await this.getProducts({filters:{status:"active"}});if("success"===e.status&&e.data)return this.createResponse(e.data.items);return e}catch(e){return this.createErrorResponse("获取启用产品列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getProductsByCategory(e){try{let t=await this.getProducts({filters:{category:e}});if("success"===t.status&&t.data)return this.createResponse(t.data.items);return t}catch(e){return this.createErrorResponse("根据分类获取产品失败",s.Bx.INTERNAL_SERVER_ERROR)}}async searchProducts(e){try{let t=await this.getProducts({search:e});if("success"===t.status&&t.data)return this.createResponse(t.data.items);return t}catch(e){return this.createErrorResponse("搜索产品失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getProductsByMold(e){try{let t=this.storage.findProductsByMold(e);return this.createResponse(t)}catch(e){return this.createErrorResponse("根据模具获取产品失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getMoldUsageStatistics(){try{let e=this.storage.getAllProducts(),t={};return e.forEach(e=>{e.moldId&&(t[e.moldId]=(t[e.moldId]||0)+1),e.formingMold&&e.formingMold!==e.moldId&&(t[e.formingMold]=(t[e.formingMold]||0)+1)}),this.createResponse(t)}catch(e){return this.createErrorResponse("获取模具使用统计失败",s.Bx.INTERNAL_SERVER_ERROR)}}}class c{static getInstance(){return c.instance||(c.instance=new c,c.instance.initializeTestData()),c.instance}getAllCustomers(){return[...this.customers]}addCustomer(e){this.customers.push(e)}updateCustomer(e,t){let r=this.customers.findIndex(t=>t.id===e);return -1===r?null:(this.customers[r]={...this.customers[r],...t},this.customers[r])}removeCustomer(e){let t=this.customers.findIndex(t=>t.id===e);return -1!==t&&(this.customers.splice(t,1),!0)}findCustomerById(e){return this.customers.find(t=>t.id===e)}findCustomerByCode(e){return this.customers.find(t=>t.customerCode===e)}getActiveCustomers(){return this.customers.filter(e=>"active"===e.status)}reloadData(){this.initializeTestData()}initializeTestData(){this.customers=[...i.seedCustomers]}constructor(){this.customers=[]}}class l{constructor(){this.storage=c.getInstance()}static getInstance(){return l.instance||(l.instance=new l),l.instance}createResponse(e,t,r=s.Bx.OK){return a.N5.createSuccess(e,t,r)}createErrorResponse(e,t=s.Bx.INTERNAL_SERVER_ERROR){return a.N5.createStandardError(e,t)}async getCustomers(e){try{let t=this.storage.getAllCustomers();if(e?.filters&&(e.filters.status&&(t=t.filter(t=>t.status===e.filters.status)),e.filters.customerType&&(t=t.filter(t=>t.customerType===e.filters.customerType)),e.filters.region&&(t=t.filter(t=>t.region===e.filters.region))),e?.search){let r=e.search.toLowerCase();t=t.filter(e=>e.customerCode.toLowerCase().includes(r)||e.customerName.toLowerCase().includes(r)||e.contactPerson?.toLowerCase().includes(r)||e.phone?.toLowerCase().includes(r))}e?.sortBy&&t.sort((t,r)=>{let s=t[e.sortBy],a=r[e.sortBy],i="desc"===e.sortOrder?-1:1;return"string"==typeof s?s.localeCompare(a)*i:(s-a)*i});let r=e?.page||1,s=e?.pageSize||20,a=t.length,i=(r-1)*s,o=t.slice(i,i+s);return this.createResponse({items:o,pagination:{current:r,pageSize:s,total:a,totalPages:Math.ceil(a/s)}})}catch(e){return this.createErrorResponse("获取客户列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getCustomerById(e){try{let t=this.storage.findCustomerById(e);if(!t)return this.createErrorResponse(`客户ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createResponse(t)}catch(e){return this.createErrorResponse("获取客户详情失败",s.Bx.INTERNAL_SERVER_ERROR)}}async createCustomer(e){try{if(e.customerCode&&this.storage.findCustomerByCode(e.customerCode))return this.createErrorResponse(`客户编码 ${e.customerCode} 已存在`,s.Bx.CONFLICT);let t=a._l.addCreation({...e,id:`customer_${Date.now()}_${Math.random().toString(36).substring(2,11)}`});return this.storage.addCustomer(t),this.createResponse(t,"客户创建成功",s.Bx.CREATED)}catch(e){return this.createErrorResponse("创建客户失败",s.Bx.INTERNAL_SERVER_ERROR)}}async updateCustomer(e,t){try{let r=this.storage.findCustomerById(e);if(!r)return this.createErrorResponse(`客户ID ${e} 不存在`,s.Bx.NOT_FOUND);if(t.customerCode&&t.customerCode!==r.customerCode){let r=this.storage.findCustomerByCode(t.customerCode);if(r&&r.id!==e)return this.createErrorResponse(`客户编码 ${t.customerCode} 已存在`,s.Bx.CONFLICT)}let i=a._l.addUpdate({...r,...t}),o=this.storage.updateCustomer(e,i);if(o)return this.createResponse(o);return this.createErrorResponse("更新客户失败",s.Bx.INTERNAL_SERVER_ERROR)}catch(e){return this.createErrorResponse("更新客户失败",s.Bx.INTERNAL_SERVER_ERROR)}}async deleteCustomer(e){try{if(!this.storage.findCustomerById(e))return this.createErrorResponse(`客户ID ${e} 不存在`,s.Bx.NOT_FOUND);if(this.storage.removeCustomer(e))return this.createResponse(void 0,"客户删除成功",s.Bx.NO_CONTENT);return this.createErrorResponse("删除客户失败",s.Bx.INTERNAL_SERVER_ERROR)}catch(e){return this.createErrorResponse("删除客户失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getActiveCustomers(){try{let e=await this.getCustomers({filters:{status:"active"}});if("success"===e.status&&e.data)return this.createResponse(e.data.items);return e}catch(e){return this.createErrorResponse("获取启用客户列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async searchCustomers(e){try{let t=await this.getCustomers({search:e});if("success"===t.status&&t.data)return this.createResponse(t.data.items);return t}catch(e){return this.createErrorResponse("搜索客户失败",s.Bx.INTERNAL_SERVER_ERROR)}}}class d{static getInstance(){return d.instance||(d.instance=new d,d.instance.initializeTestData()),d.instance}getAllEmployees(){return[...this.employees]}addEmployee(e){this.employees.push(e)}updateEmployee(e,t){let r=this.employees.findIndex(t=>t.id===e);return -1===r?null:(this.employees[r]={...this.employees[r],...t},this.employees[r])}removeEmployee(e){let t=this.employees.findIndex(t=>t.id===e);return -1!==t&&(this.employees.splice(t,1),!0)}findEmployeeById(e){return this.employees.find(t=>t.id===e)}findEmployeesByDepartment(e){return this.employees.filter(t=>t.department===e)}findEmployeesByRole(e){return this.employees.filter(t=>t.role===e)}findSalesEmployees(){return this.employees.filter(e=>"sales"===e.role||"sales"===e.department||e.role?.toLowerCase().includes("sales")||e.department?.toLowerCase().includes("sales"))}getActiveEmployees(){return this.employees.filter(e=>"active"===e.status)}reloadData(){this.initializeTestData()}initializeTestData(){this.employees=[...i.seedEmployees]}constructor(){this.employees=[]}}class u{constructor(){this.storage=d.getInstance()}static getInstance(){return u.instance||(u.instance=new u),u.instance}createResponse(e,t,r=s.Bx.OK){return a.N5.createSuccess(e,t,r)}createErrorResponse(e,t=s.Bx.INTERNAL_SERVER_ERROR){return a.N5.createStandardError(e,t)}async getEmployees(e){try{let t=this.storage.getAllEmployees();if(e?.filters&&(e.filters.status&&(t=t.filter(t=>t.status===e.filters.status)),e.filters.department&&(t=t.filter(t=>t.department===e.filters.department)),e.filters.role&&(t=t.filter(t=>t.role===e.filters.role))),e?.search){let r=e.search.toLowerCase();t=t.filter(e=>e.employeeCode.toLowerCase().includes(r)||e.name.toLowerCase().includes(r)||e.phone?.toLowerCase().includes(r)||e.email?.toLowerCase().includes(r))}e?.sortBy&&t.sort((t,r)=>{let s=t[e.sortBy],a=r[e.sortBy],i="desc"===e.sortOrder?-1:1;return"string"==typeof s?s.localeCompare(a)*i:(s-a)*i});let r=e?.page||1,s=e?.pageSize||20,a=t.length,i=(r-1)*s,o=t.slice(i,i+s);return this.createResponse({items:o,pagination:{current:r,pageSize:s,total:a,totalPages:Math.ceil(a/s)}})}catch(e){return this.createErrorResponse("获取员工列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getEmployeeById(e){try{let t=this.storage.findEmployeeById(e);if(!t)return this.createErrorResponse(`员工ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createResponse(t)}catch(e){return this.createErrorResponse("获取员工详情失败",s.Bx.INTERNAL_SERVER_ERROR)}}async createEmployee(e){try{if(e.employeeCode&&this.storage.getAllEmployees().find(t=>t.employeeCode===e.employeeCode))return this.createErrorResponse(`员工编码 ${e.employeeCode} 已存在`,s.Bx.CONFLICT);let t=a._l.addCreation({...e,id:`employee_${Date.now()}_${Math.random().toString(36).substring(2,11)}`});return this.storage.addEmployee(t),this.createResponse(t,"员工创建成功",s.Bx.CREATED)}catch(e){return this.createErrorResponse("创建员工失败",s.Bx.INTERNAL_SERVER_ERROR)}}async updateEmployee(e,t){try{let r=this.storage.findEmployeeById(e);if(!r)return this.createErrorResponse(`员工ID ${e} 不存在`,s.Bx.NOT_FOUND);if(t.employeeCode&&t.employeeCode!==r.employeeCode&&this.storage.getAllEmployees().find(r=>r.employeeCode===t.employeeCode&&r.id!==e))return this.createErrorResponse(`员工编码 ${t.employeeCode} 已存在`,s.Bx.CONFLICT);let i=a._l.addUpdate({...r,...t}),o=this.storage.updateEmployee(e,i);if(o)return this.createResponse(o);return this.createErrorResponse("更新员工失败",s.Bx.INTERNAL_SERVER_ERROR)}catch(e){return this.createErrorResponse("更新员工失败",s.Bx.INTERNAL_SERVER_ERROR)}}async deleteEmployee(e){try{if(!this.storage.findEmployeeById(e))return this.createErrorResponse(`员工ID ${e} 不存在`,s.Bx.NOT_FOUND);if(this.storage.removeEmployee(e))return this.createResponse(void 0,"员工删除成功",s.Bx.NO_CONTENT);return this.createErrorResponse("删除员工失败",s.Bx.INTERNAL_SERVER_ERROR)}catch(e){return this.createErrorResponse("删除员工失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getActiveEmployees(){try{let e=await this.getEmployees({filters:{status:"active"}});if("success"===e.status&&e.data)return this.createResponse(e.data.items);return e}catch(e){return this.createErrorResponse("获取启用员工列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getEmployeesByDepartment(e){try{let t=await this.getEmployees({filters:{department:e}});if("success"===t.status&&t.data)return this.createResponse(t.data.items);return t}catch(e){return this.createErrorResponse("根据部门获取员工失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getEmployeesByRole(e){try{let t=await this.getEmployees({filters:{role:e}});if("success"===t.status&&t.data)return this.createResponse(t.data.items);return t}catch(e){return this.createErrorResponse("根据角色获取员工失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getSalesEmployees(){try{let e=this.storage.findSalesEmployees().filter(e=>"active"===e.status);return this.createResponse(e)}catch(e){return this.createErrorResponse("获取销售员工列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}}class h{static getInstance(){return h.instance||(h.instance=new h,h.instance.initializeWithSeedData()),h.instance}getAllInventory(){return[...this.inventory]}addInventory(e){this.inventory.push(e)}updateInventory(e,t){let r=this.inventory.findIndex(t=>t.id===e);return -1===r?null:(this.inventory[r]={...this.inventory[r],...t},this.inventory[r])}findInventoryById(e){return this.inventory.find(t=>t.id===e)}findInventoryByProductCode(e){return this.inventory.find(t=>t.productCode===e)}initializeWithSeedData(){try{this.inventory=[...i.seedProductInventory],console.log(`库存数据初始化完成，加载 ${this.inventory.length} 条记录`)}catch(e){console.warn("库存数据初始化失败，使用空数据集"),this.inventory=[]}}constructor(){this.inventory=[]}}class g{constructor(){this.storage=h.getInstance()}static getInstance(){return g.instance||(g.instance=new g),g.instance}createResponse(e,t,r=s.Bx.OK){return a.N5.createSuccess(e,t,r)}createErrorResponse(e,t=s.Bx.INTERNAL_SERVER_ERROR){return a.N5.createStandardError(e,t)}async getProductInventory(e){try{let t=await this.fetchInventoryFromDataSource();if(e?.filters){if(e.filters.lowStock){let r=e.filters.threshold||10;t=t.filter(e=>e.currentStock<=r)}e.filters.productCode&&(t=t.filter(t=>t.productCode===e.filters.productCode))}if(e?.search){let r=e.search.toLowerCase();t=t.filter(e=>e.productCode.toLowerCase().includes(r)||e.productName.toLowerCase().includes(r))}e?.sortBy&&t.sort((t,r)=>{let s=t[e.sortBy],a=r[e.sortBy],i="desc"===e.sortOrder?-1:1;return"string"==typeof s?s.localeCompare(a)*i:(s-a)*i});let r=e?.page||1,s=e?.pageSize||20,a=t.length,i=(r-1)*s,o=t.slice(i,i+s);return this.createResponse({items:o,pagination:{current:r,pageSize:s,total:a,totalPages:Math.ceil(a/s)}})}catch(e){return this.createErrorResponse("获取产品库存列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getInventoryByProductCode(e){try{let t=await this.fetchInventoryByProductCodeFromDataSource(e);if(!t)return this.createErrorResponse(`产品编码 ${e} 的库存记录不存在`,s.Bx.NOT_FOUND);return this.createResponse(t)}catch(e){return this.createErrorResponse("获取产品库存失败",s.Bx.INTERNAL_SERVER_ERROR)}}async updateInventory(e,t){try{let r=await this.fetchInventoryByProductCodeFromDataSource(e);if(!r)return this.createErrorResponse(`产品编码 ${e} 的库存记录不存在`,s.Bx.NOT_FOUND);if(void 0!==t.currentStock&&t.currentStock<0)return this.createErrorResponse("库存数量不能为负数",s.Bx.BAD_REQUEST);let i=a._l.addUpdate({...r,...t});return await this.updateInventoryInDataSource(r.id,i),this.createResponse(i)}catch(e){return this.createErrorResponse("更新库存失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getLowStockProducts(e=10){try{let t=await this.getProductInventory({filters:{lowStock:!0,threshold:e}});if("success"===t.status&&t.data)return this.createResponse(t.data.items);return t}catch(e){return this.createErrorResponse("获取低库存产品失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getInventoryValue(){try{let e=await this.fetchInventoryFromDataSource(),t=0,r=[];for(let s of e){let e=await L.products.getByCode(s.productCode),a="success"===e.status&&e.data?e.data.productPrice:0,i=s.currentStock*a;t+=i,r.push({productCode:s.productCode,productName:s.productName,currentStock:s.currentStock,unitPrice:a,totalValue:i})}return this.createResponse({totalValue:t,details:r})}catch(e){return this.createErrorResponse("获取库存价值统计失败",s.Bx.INTERNAL_SERVER_ERROR)}}async syncProductInfo(e){try{let t=await this.fetchInventoryByProductCodeFromDataSource(e);if(!t)return this.createErrorResponse(`产品编码 ${e} 的库存记录不存在`,s.Bx.NOT_FOUND);let r=await L.products.getByCode(e);if("success"!==r.status||!r.data)return this.createErrorResponse(`产品编码 ${e} 在产品数据模块中不存在`,s.Bx.NOT_FOUND);let a=r.data,i={};if(t.productName!==a.modelName&&(i.productName=a.modelName),Object.keys(i).length>0)return await this.updateInventory(e,i);return this.createResponse(t,"数据已是最新，无需同步",s.Bx.OK)}catch(e){return this.createErrorResponse("同步产品信息失败",s.Bx.INTERNAL_SERVER_ERROR)}}async fetchInventoryFromDataSource(){try{let e=this.storage.getAllInventory();if(!e||0===e.length)return[];let t=e.filter(e=>!!e.id&&!!e.productCode&&!!e.productName&&"number"==typeof e.currentStock&&!(e.currentStock<0));return t.length!==e.length&&console.log(`数据完整性检查：原始数据 ${e.length} 条，有效数据 ${t.length} 条`),[...t]}catch(e){return[]}}async fetchInventoryByProductCodeFromDataSource(e){try{if(!e||"string"!=typeof e)return null;return this.storage.findInventoryByProductCode(e)||null}catch(e){return null}}async updateInventoryInDataSource(e,t){try{if(!e||!t)throw Error("更新库存数据时参数无效");if(!this.storage.updateInventory(e,t))throw Error("更新库存数据失败")}catch(e){throw e}}}var m=r(41517);class p{static getInstance(){return p.instance?(console.log("\uD83D\uDD27 [SalesOrderStorage] 返回现有单例实例，当前订单数量:",p.instance.orders.length),console.log("\uD83D\uDD27 [SalesOrderStorage] 实例ID:",p.instance.__instanceId)):(console.log("\uD83D\uDD27 [SalesOrderStorage] 创建新的单例实例"),p.instance=new p,p.instance.__instanceId=Math.random().toString(36).substr(2,9),console.log("\uD83D\uDD27 [SalesOrderStorage] 实例ID:",p.instance.__instanceId),p.instance.initializeTestData()),p.instance}getAllOrders(){return console.log("\uD83D\uDCCA [SalesOrderStorage] 获取所有订单，当前数量:",this.orders.length),console.log("\uD83D\uDCCA [SalesOrderStorage] 实例ID:",this.__instanceId),[...this.orders]}addOrder(e){console.log("➕ [SalesOrderStorage] 添加新订单:",e.orderNumber,"当前数量:",this.orders.length,"→",this.orders.length+1),console.log("➕ [SalesOrderStorage] 实例ID:",this.__instanceId),this.orders.push(e),console.log("✅ [SalesOrderStorage] 订单添加完成，新数量:",this.orders.length)}updateOrder(e,t){let r=this.orders.findIndex(t=>t.id===e);return -1===r?null:(this.orders[r]={...this.orders[r],...t},this.orders[r])}removeOrder(e){let t=this.orders.findIndex(t=>t.id===e);return -1!==t&&(this.orders.splice(t,1),!0)}findOrderById(e){return this.orders.find(t=>t.id===e)}findOrderByNumber(e){return this.orders.find(t=>t.orderNumber===e)}findOrdersByStatus(e){return this.orders.filter(t=>t.status===e)}findOrdersByCustomer(e){return this.orders.filter(t=>t.customerId===e)}findOrdersBySalesRep(e){return this.orders.filter(t=>t.salesRepresentative===e)}findOrdersByDateRange(e,t){return this.orders.filter(r=>{let s=new Date(r.orderDate),a=new Date(e),i=new Date(t);return s>=a&&s<=i})}checkOrderNumberExists(e){return this.orders.some(t=>t.orderNumber===e)}initializeTestData(){console.log("\uD83D\uDD04 [SalesOrderStorage] 开始初始化测试数据，当前订单数量:",this.orders.length),console.trace("\uD83D\uDD0D [SalesOrderStorage] 初始化调用栈:");try{let{seedSalesOrders:e}=r(69224);if(e&&e.length>0){let t=[...e].sort((e,t)=>new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime());console.log("⚠️ [SalesOrderStorage] 即将覆盖现有订单数据!"),console.log("   覆盖前订单数量:",this.orders.length),console.log("   种子数据数量:",t.length),this.orders=t,console.log(`📋 [SalesOrderStorage] 初始化完成，最终订单数量: ${this.orders.length}`)}else this.orders=[],console.log("\uD83D\uDCCB [SalesOrderStorage] 未找到种子数据，使用空数据集")}catch(e){console.warn("⚠️ [SalesOrderStorage] 无法加载种子数据，使用空数据集:",e),this.orders=[]}}constructor(){this.orders=[]}}class R{constructor(){this.storage=p.getInstance(),this.__serviceId=Math.random().toString(36).substr(2,9),console.log("\uD83D\uDD27 [OrderDataAccessService] 服务实例创建，ID:",this.__serviceId),console.log("\uD83D\uDD27 [OrderDataAccessService] 关联的存储实例ID:",this.storage.__instanceId)}static getInstance(){return R.instance?console.log("\uD83D\uDD27 [OrderDataAccessService] 返回现有服务实例，ID:",R.instance.__serviceId):(console.log("\uD83D\uDD27 [OrderDataAccessService] 创建新的服务单例实例"),R.instance=new R),R.instance}createSuccessResponse(e,t){return m.N5.createSuccess(e,t||"操作成功")}createErrorResponse(e,t=s.Bx.INTERNAL_SERVER_ERROR){return m.N5.createStandardError(e,t)}async getOrders(e){try{console.log("\uD83D\uDD0D [OrderDataAccessService] getOrders 被调用，服务ID:",this.__serviceId),console.log("\uD83D\uDD0D [OrderDataAccessService] 使用的存储实例ID:",this.storage.__instanceId);let t=this.storage.getAllOrders();e?.filters&&(t=t.filter(t=>{for(let[r,s]of Object.entries(e.filters||{}))if(s&&t[r]!==s)return!1;return!0})),e?.sortBy&&(t=[...t].sort((t,r)=>{let s=t[e.sortBy],a=r[e.sortBy];return null==s||null==a?0:s<a?"desc"===e.sortOrder?1:-1:s>a?"desc"===e.sortOrder?-1:1:0}));let{page:r=1,pageSize:s=20}=e||{},a=(r-1)*s,i={items:t.slice(a,a+s),pagination:{current:r,pageSize:s,total:t.length,totalPages:Math.ceil(t.length/s)}};return this.createSuccessResponse(i,"获取订单列表成功")}catch(e){return this.createErrorResponse("获取订单列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getOrderById(e){try{let t=this.storage.findOrderById(e);if(!t)return this.createErrorResponse(`订单ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(t,"获取订单成功")}catch(e){return this.createErrorResponse("获取订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getOrderByNumber(e){try{let t=this.storage.findOrderByNumber(e);if(!t)return this.createErrorResponse(`订单号 ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(t,"获取订单成功")}catch(e){return this.createErrorResponse("获取订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async createOrder(e){try{if(console.log("\uD83D\uDD0D [OrderDataAccessService] createOrder 被调用，服务ID:",this.__serviceId),console.log("\uD83D\uDD0D [OrderDataAccessService] 使用的存储实例ID:",this.storage.__instanceId),e.orderNumber&&this.storage.findOrderByNumber(e.orderNumber))return m.N5.handleDuplicate("销售订单",e.orderNumber,"创建订单");let t=a.IZ.transformSalesOrder(e,{generateId:!0,generateTimestamps:!0,validateData:!0});if(!t.success)return this.createErrorResponse(t.error,s.Bx.BAD_REQUEST);let r=t.data;return t.warnings&&t.warnings.length>0&&console.log("⚠️ [OrderDataAccessService] 数据转换警告:",t.warnings),this.storage.addOrder(r),this.notifyOrderCreated(r),this.createSuccessResponse(r,"订单创建成功")}catch(e){return this.createErrorResponse("创建订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async updateOrder(e,t){try{let r=this.storage.findOrderById(e);if(!r)return this.createErrorResponse(`订单ID ${e} 不存在`,s.Bx.NOT_FOUND);let i=a.IZ.transformUpdate(r,t,{generateTimestamps:!0,validateData:!1});if(!i.success)return this.createErrorResponse(i.error,s.Bx.INTERNAL_SERVER_ERROR);let o=i.data,n=this.storage.updateOrder(e,o);if(n)return this.notifyOrderUpdated(n),this.createSuccessResponse(n,"订单更新成功");return this.createErrorResponse("订单更新失败",s.Bx.INTERNAL_SERVER_ERROR)}catch(e){return this.createErrorResponse("更新订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async deleteOrder(e){try{if(!this.storage.findOrderById(e))return this.createErrorResponse(`订单ID ${e} 不存在`,s.Bx.NOT_FOUND);if(this.storage.removeOrder(e))return this.notifyOrderDeleted(e),this.createSuccessResponse(void 0,"订单删除成功");return this.createErrorResponse("订单删除失败",s.Bx.INTERNAL_SERVER_ERROR)}catch(e){return this.createErrorResponse("删除订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getOrdersByStatus(e){try{let t=this.storage.findOrdersByStatus(e);return this.createSuccessResponse(t,`获取${e}状态订单成功`)}catch(e){return this.createErrorResponse("根据状态获取订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getOrdersByCustomer(e){try{let t=this.storage.findOrdersByCustomer(e);return this.createSuccessResponse(t,"获取客户订单成功")}catch(e){return this.createErrorResponse("根据客户获取订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getOrdersBySalesRep(e){try{let t=this.storage.findOrdersBySalesRep(e);return this.createSuccessResponse(t,"获取销售代表订单成功")}catch(e){return this.createErrorResponse("根据销售代表获取订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getOrdersByDateRange(e,t){try{let r=this.storage.findOrdersByDateRange(e,t);return this.createSuccessResponse(r,"获取日期范围订单成功")}catch(e){return this.createErrorResponse("根据日期范围获取订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async checkOrderNumberExists(e){try{let t=this.storage.checkOrderNumberExists(e);return this.createSuccessResponse(t,t?"订单号已存在":"订单号可用")}catch(e){return this.createErrorResponse("检查订单号存在性失败",s.Bx.INTERNAL_SERVER_ERROR)}}notifyOrderCreated(e){try{let{dataChangeNotifier:t}=r(43471);t.notifyOrderCreated(e,"OrderDataAccessService"),console.log(`[OrderDataAccessService] 已发布订单创建事件: ${e.id}`)}catch(e){console.warn("[OrderDataAccessService] 发布订单创建事件失败，但不影响主要功能:",e)}}notifyOrderUpdated(e){try{let{dataChangeNotifier:t}=r(43471);t.notifyOrderUpdated(e,"OrderDataAccessService"),console.log(`[OrderDataAccessService] 已发布订单更新事件: ${e.id}`)}catch(e){console.warn("[OrderDataAccessService] 发布订单更新事件失败，但不影响主要功能:",e)}}notifyOrderDeleted(e){try{let{dataChangeNotifier:t}=r(43471);t.notifyOrderDeleted(e,"OrderDataAccessService"),console.log(`[OrderDataAccessService] 已发布订单删除事件: ${e}`)}catch(e){console.warn("[OrderDataAccessService] 发布订单删除事件失败，但不影响主要功能:",e)}}}class y{constructor(){this.apiVersion=s.Gn}static getInstance(){return y.instance||(y.instance=new y),y.instance}createSuccessResponse(e,t="操作成功"){return a.N5.createSuccess(e,t)}createErrorResponse(e,t=s.Bx.INTERNAL_SERVER_ERROR){return a.N5.createStandardError(e,t)}async getConfigurations(){try{let e=localStorage.getItem("work-time-configurations");if(e)try{let t=JSON.parse(e);if(Array.isArray(t)&&t.length>0)return!t.some(e=>e.isDefault)&&t.length>0&&(t[0].isDefault=!0,localStorage.setItem("work-time-configurations",JSON.stringify(t))),this.createSuccessResponse(t,"获取工作时间配置成功")}catch(e){localStorage.removeItem("work-time-configurations")}let t=[this.createSystemDefaultConfiguration()];try{localStorage.setItem("work-time-configurations",JSON.stringify(t))}catch(e){}return this.createSuccessResponse(t,"获取默认工作时间配置成功")}catch(e){return this.createErrorResponse("获取工作时间配置失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getConfigurationById(e){try{let t=await this.getConfigurations();if("success"!==t.status||!t.data)return this.createErrorResponse("获取工作时间配置失败",s.Bx.INTERNAL_SERVER_ERROR);let r=t.data.find(t=>t.id===e);if(!r)return this.createErrorResponse(`工作时间配置ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(r,"获取工作时间配置成功")}catch(e){return this.createErrorResponse("获取工作时间配置失败",s.Bx.INTERNAL_SERVER_ERROR)}}async createConfiguration(e){try{let t=a._l.addCreation({...e,id:`config_${Date.now()}`}),r=await this.getConfigurations();if("success"!==r.status||!r.data)return this.createErrorResponse("获取现有配置失败",s.Bx.INTERNAL_SERVER_ERROR);let i=r.data;return i.push(t),localStorage.setItem("work-time-configurations",JSON.stringify(i)),this.createSuccessResponse(t,"创建工作时间配置成功")}catch(e){return this.createErrorResponse("创建工作时间配置失败",s.Bx.INTERNAL_SERVER_ERROR)}}async updateConfiguration(e,t){try{let r=await this.getConfigurations();if("success"!==r.status||!r.data)return this.createErrorResponse("获取现有配置失败",s.Bx.INTERNAL_SERVER_ERROR);let i=r.data,o=i.findIndex(t=>t.id===e);if(-1===o)return this.createErrorResponse(`工作时间配置ID ${e} 不存在`,s.Bx.NOT_FOUND);let n=a._l.addUpdate({...i[o],...t});return i[o]=n,localStorage.setItem("work-time-configurations",JSON.stringify(i)),this.createSuccessResponse(n,"更新工作时间配置成功")}catch(e){return this.createErrorResponse("更新工作时间配置失败",s.Bx.INTERNAL_SERVER_ERROR)}}async deleteConfiguration(e){try{let t=await this.getConfigurations();if("success"!==t.status||!t.data)return this.createErrorResponse("获取现有配置失败",s.Bx.INTERNAL_SERVER_ERROR);let r=t.data,a=r.filter(t=>t.id!==e);if(a.length===r.length)return this.createErrorResponse(`工作时间配置ID ${e} 不存在`,s.Bx.NOT_FOUND);return localStorage.setItem("work-time-configurations",JSON.stringify(a)),this.createSuccessResponse(!0,"删除工作时间配置成功")}catch(e){return this.createErrorResponse("删除工作时间配置失败",s.Bx.INTERNAL_SERVER_ERROR)}}async addWorkTimeSlot(e,t){try{let r=await this.getConfigurationById(e);if("success"!==r.status||!r.data)return this.createErrorResponse("工作时间配置不存在",s.Bx.NOT_FOUND);let a=[...r.data.workTimeSlots,t],i=this.calculateWorkingMinutes(a,[]),o=await this.updateConfiguration(e,{workTimeSlots:a,...i});if("success"!==o.status)return this.createErrorResponse("更新配置失败",s.Bx.INTERNAL_SERVER_ERROR);return this.createSuccessResponse(t,"添加工作时间段成功")}catch(e){return this.createErrorResponse("添加工作时间段失败",s.Bx.INTERNAL_SERVER_ERROR)}}async updateWorkTimeSlot(e,t,r){try{let a=await this.getConfigurationById(e);if("success"!==a.status||!a.data)return this.createErrorResponse("工作时间配置不存在",s.Bx.NOT_FOUND);let i=a.data,o=i.workTimeSlots.findIndex(e=>e.id===t);if(-1===o)return this.createErrorResponse("工作时间段不存在",s.Bx.NOT_FOUND);let n={...i.workTimeSlots[o],...r},c=[...i.workTimeSlots];c[o]=n;let l=this.calculateWorkingMinutes(c,[]),d=await this.updateConfiguration(e,{workTimeSlots:c,...l});if("success"!==d.status)return this.createErrorResponse("更新配置失败",s.Bx.INTERNAL_SERVER_ERROR);return this.createSuccessResponse(n,"更新工作时间段成功")}catch(e){return this.createErrorResponse("更新工作时间段失败",s.Bx.INTERNAL_SERVER_ERROR)}}async deleteWorkTimeSlot(e,t){try{let r=await this.getConfigurationById(e);if("success"!==r.status||!r.data)return this.createErrorResponse("工作时间配置不存在",s.Bx.NOT_FOUND);let a=r.data,i=a.workTimeSlots.filter(e=>e.id!==t);if(i.length===a.workTimeSlots.length)return this.createErrorResponse("工作时间段不存在",s.Bx.NOT_FOUND);let o=this.calculateWorkingMinutes(i,[]),n=await this.updateConfiguration(e,{workTimeSlots:i,...o});if("success"!==n.status)return this.createErrorResponse("更新配置失败",s.Bx.INTERNAL_SERVER_ERROR);return this.createSuccessResponse(!0,"删除工作时间段成功")}catch(e){return this.createErrorResponse("删除工作时间段失败",s.Bx.INTERNAL_SERVER_ERROR)}}calculateWorkingMinutes(e,t=[]){let r=e.filter(e=>e.isActive).reduce((e,t)=>{let[r,s]=t.startTime.split(":").map(Number),[a,i]=t.endTime.split(":").map(Number);return e+(60*a+i-(60*r+s))},0);return{totalWorkingMinutes:r,totalBreakMinutes:0,effectiveWorkingMinutes:r}}validateTimeSlot(e,t){try{let[r,s]=e.split(":").map(Number),[a,i]=t.split(":").map(Number);return 60*a+i>60*r+s}catch(e){return!1}}async getDefaultConfiguration(){try{let e=await this.getConfigurations();if("success"===e.status&&e.data){let t=e.data.find(e=>e.isDefault);if(t)return this.createSuccessResponse(t,"获取默认工作时间配置成功");if(e.data.length>0)return this.createSuccessResponse(e.data[0],"获取工作时间配置成功")}let t=this.createSystemDefaultConfiguration();try{let e=localStorage.getItem("work-time-configurations"),r=e?JSON.parse(e):[];r.some(e=>"default_config"===e.id)||(r.push(t),localStorage.setItem("work-time-configurations",JSON.stringify(r)))}catch(e){}return this.createSuccessResponse(t,"获取系统默认工作时间配置成功")}catch(e){return this.createErrorResponse("获取默认工作时间配置失败",s.Bx.INTERNAL_SERVER_ERROR)}}createSystemDefaultConfiguration(){return{id:"default_config",configName:"标准工作时间",description:"上午/下午分段工作时间配置（上午6:30-11:00，下午11:30-17:00）",workTimeSlots:[{id:"work_slot_morning",name:"上午工作时间段",startTime:"06:30",endTime:"11:00",isActive:!0,description:"上午工作时间"},{id:"work_slot_afternoon",name:"下午工作时间段",startTime:"11:30",endTime:"17:00",isActive:!0,description:"下午工作时间"}],breakTimeSlots:[],totalWorkingMinutes:600,totalBreakMinutes:0,effectiveWorkingMinutes:600,globalSettings:{defaultStartTime:"06:30",defaultEndTime:"17:00",defaultLunchBreakStart:"11:00",defaultLunchBreakEnd:"11:30",allowOvertime:!0,maxOvertimeHours:4,minBreakDuration:15},applicableWorkstations:[],isDefault:!0,isActive:!0,createdBy:"system",createdAt:a._l.now()}}}var E=r(69915);class f{static getInstance(){return f.instance||(f.instance=new f,f.instance.initializeTestData()),f.instance}getAllOrders(){return this.orders}addOrder(e){this.orders.push(e)}updateOrder(e,t){let r=this.orders.findIndex(t=>t.id===e);return -1===r?null:(this.orders[r]={...this.orders[r],...t},this.orders[r])}removeOrder(e){let t=this.orders.findIndex(t=>t.id===e);return -1!==t&&(this.orders.splice(t,1),!0)}findOrderById(e){return this.orders.find(t=>t.id===e)}findOrderByNumber(e){return this.orders.find(t=>t.orderNumber===e)}findOrdersByStatus(e){return this.orders.filter(t=>t.status===e)}findOrdersBySalesOrderId(e){return this.orders.filter(t=>t.sourceOrderIds?.includes(e)||t.salesOrderNumber===e)}deleteOrder(e){let t=this.orders.findIndex(t=>t.id===e);return -1!==t&&(this.orders.splice(t,1),!0)}resetOrderStatusToInitial(){console.log("\uD83D\uDD04 [ProductionOrderStorage] 开始重置订单状态到初始状态");let e=0;this.orders.forEach(t=>{"in_plan"!==t.status&&"in_progress"!==t.status&&(t.status="in_plan",t.updatedAt=a._l.now(),e++)}),console.log(`🔄 [ProductionOrderStorage] 已重置 ${e} 个订单状态到初始状态`)}initializeTestData(){this.orders=[{id:"PO-XSDD202412050001",orderNumber:"PO-XSDD202412050001",productCode:"P00001",productName:"圆形餐盘-白色",formingMoldNumber:"M001",isSharedMold:!1,moldGroup:void 0,productItems:[],plannedQuantity:500,producedQuantity:0,startDate:"2024-12-05",endDate:"2024-12-15",deliveryDate:"2024-12-15",status:"in_plan",workstation:"成型工位1",customerName:"华润万家",customerId:"customer_001",customerCreditLevel:"A",prioritySource:"auto",salesOrderNumber:"XSDD202412050001",sourceOrderIds:["sales_order_1"],sourceOrderNumbers:["XSDD202412050001"],...a._l.creation()},{id:"PO-XSDD202412050002",orderNumber:"PO-XSDD202412050002",productCode:"P00002",productName:"方形餐盘-蓝色",formingMoldNumber:"M002",isSharedMold:!1,moldGroup:void 0,productItems:[],plannedQuantity:300,producedQuantity:0,startDate:"2024-12-06",endDate:"2024-12-18",deliveryDate:"2024-12-18",status:"in_plan",workstation:"成型工位2",customerName:"沃尔玛",customerId:"customer_002",customerCreditLevel:"B",prioritySource:"auto",salesOrderNumber:"XSDD202412050002",sourceOrderIds:["sales_order_2"],sourceOrderNumbers:["XSDD202412050002"],...a._l.creation()},{id:"PO-XSDD202412050003",orderNumber:"PO-XSDD202412050003",productCode:"P00003",productName:"深盘-绿色",formingMoldNumber:"M003",isSharedMold:!1,moldGroup:void 0,productItems:[],plannedQuantity:200,producedQuantity:0,startDate:"2024-12-07",endDate:"2024-12-20",deliveryDate:"2024-12-20",status:"in_plan",workstation:"成型工位3",customerName:"家乐福",customerId:"customer_003",customerCreditLevel:"C",prioritySource:"auto",salesOrderNumber:"XSDD202412050003",sourceOrderIds:["sales_order_3"],sourceOrderNumbers:["XSDD202412050003"],...a._l.creation()},{id:"PO-XSDD202412050004",orderNumber:"PO-XSDD202412050004",productCode:"P00004",productName:"汤碗-红色",formingMoldNumber:"M004",isSharedMold:!1,moldGroup:void 0,productItems:[],plannedQuantity:400,producedQuantity:150,startDate:"2024-12-08",endDate:"2024-12-12",deliveryDate:"2024-12-12",status:"in_progress",workstation:"成型工位1",customerName:"大润发",customerId:"customer_004",customerCreditLevel:"A",prioritySource:"manual",salesOrderNumber:"XSDD202412050004",sourceOrderIds:["sales_order_4"],sourceOrderNumbers:["XSDD202412050004"],...a._l.creation()}]}constructor(){this.orders=[]}}class S{constructor(){this.storage=f.getInstance()}static getInstance(){return S.instance||(S.instance=new S),S.instance}createSuccessResponse(e,t){return a.N5.createSuccess(e,t||"操作成功")}createErrorResponse(e,t=s.Bx.INTERNAL_SERVER_ERROR){return a.N5.createStandardError(e,t)}processQueryWithParams(e,t){let r=[...e];if(t?.filters){let{status:e,customerCreditLevel:s,salesOrderId:a}=t.filters;e&&(r=r.filter(t=>t.status===e)),s&&(r=r.filter(e=>e.customerCreditLevel===s)),a&&(r=r.filter(e=>e.salesOrderNumber===a))}if(t?.search){let e=t.search.toLowerCase();r=r.filter(t=>t.orderNumber.toLowerCase().includes(e)||t.productName.toLowerCase().includes(e)||t.customerName?.toLowerCase().includes(e))}if(t?.sortBy){let{sortBy:e,sortOrder:s="asc"}=t;r.sort((t,r)=>{let a=t[e],i=r[e];return a<i?"asc"===s?-1:1:a>i?"asc"===s?1:-1:0})}let{page:s=1,pageSize:a=20}=t||{},i=(s-1)*a;return{items:r.slice(i,i+a),pagination:{current:s,pageSize:a,total:r.length,totalPages:Math.ceil(r.length/a)}}}async getAll(e){try{let t=this.storage.getAllOrders(),r=this.processQueryWithParams(t,e);return this.createSuccessResponse(r,"获取生产订单列表成功")}catch(e){return this.createErrorResponse("获取生产订单列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getById(e){try{let t=this.storage.findOrderById(e);if(!t)return this.createErrorResponse(`生产订单ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(t,"获取生产订单成功")}catch(e){return this.createErrorResponse("获取生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getByOrderNumber(e){try{let t=this.storage.findOrderByNumber(e);if(!t)return this.createErrorResponse(`生产订单号 ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(t,"获取生产订单成功")}catch(e){return this.createErrorResponse("获取生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async createFromMRP(e){try{if(!e.mrpExecutionId)return this.createErrorResponse("生产订单只能通过MRP流程创建，缺少MRP执行ID",s.Bx.BAD_REQUEST);if(!e.salesOrderNumber)return this.createErrorResponse("生产订单必须关联销售订单，缺少销售订单号",s.Bx.BAD_REQUEST);if(!e.mrpExecutedBy)return this.createErrorResponse("缺少MRP执行人信息",s.Bx.BAD_REQUEST);if(e.orderNumber&&this.storage.findOrderByNumber(e.orderNumber))return this.createErrorResponse(`生产订单号 ${e.orderNumber} 已存在`,s.Bx.BAD_REQUEST);let t=a.IZ.transformProductionOrder(e,{generateId:!1,generateTimestamps:!0,validateData:!0});if(!t.success)return this.createErrorResponse(t.error,s.Bx.BAD_REQUEST);let r=t.data;return t.warnings&&t.warnings.length>0&&console.log("⚠️ [ProductionOrderDataAccessService] 数据转换警告:",t.warnings),this.storage.addOrder(r),console.log(`✅ [ProductionOrderDataAccessService] MRP生产订单创建成功: ${r.orderNumber}, MRP执行ID: ${e.mrpExecutionId}`),this.createSuccessResponse(r,"生产订单创建成功")}catch(e){return console.error("❌ [ProductionOrderDataAccessService] MRP生产订单创建失败:",e),this.createErrorResponse("创建生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async update(e,t){try{if(t.orderNumber){let r=this.storage.findOrderByNumber(t.orderNumber);if(r&&r.id!==e)return this.createErrorResponse(`生产订单号 ${t.orderNumber} 已存在`,s.Bx.BAD_REQUEST)}let r=this.storage.updateOrder(e,a._l.addUpdate(t));if(!r)return this.createErrorResponse(`生产订单ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(r,"生产订单更新成功")}catch(e){return this.createErrorResponse("更新生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async delete(e){try{if(this.storage.findOrderById(e),!this.storage.removeOrder(e))return this.createErrorResponse(`生产订单ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(!0,"生产订单删除成功")}catch(e){return this.createErrorResponse("删除生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getByStatus(e,t){try{let r=this.storage.findOrdersByStatus(e),s=this.processQueryWithParams(r,t);return this.createSuccessResponse(s,`获取${e}状态生产订单成功`)}catch(e){return this.createErrorResponse("根据状态获取生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getBySalesOrderId(e,t){try{let r=this.storage.findOrdersBySalesOrderId(e),s=this.processQueryWithParams(r,t);return this.createSuccessResponse(s,"获取相关生产订单成功")}catch(e){return this.createErrorResponse("根据销售订单ID获取生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getActiveOrders(e){try{let t=this.storage.getAllOrders().filter(e=>"planned"===e.status||"in_progress"===e.status),r=this.processQueryWithParams(t,e);return this.createSuccessResponse(r,"获取活跃生产订单成功")}catch(e){return this.createErrorResponse("获取活跃生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getByPriority(e,t){return this.getByCustomerCreditLevel(e,t)}async getByCustomerCreditLevel(e,t){try{let r=this.storage.getAllOrders().filter(t=>t.customerCreditLevel===e),s=this.processQueryWithParams(r,t);return this.createSuccessResponse(s,`获取${e}级客户生产订单成功`)}catch(e){return this.createErrorResponse("根据客户信用等级获取生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async resetAllOrderStatusToInitial(){try{console.log("\uD83D\uDD04 [ProductionOrderService] 开始重置所有订单状态");let e=this.storage.getAllOrders().filter(e=>"in_plan"!==e.status).length;this.storage.resetOrderStatusToInitial();let t=this.storage.getAllOrders().filter(e=>"in_plan"!==e.status).length,r=e-t;return console.log(`🔄 [ProductionOrderService] 订单状态重置完成，重置了 ${r} 个订单`),this.createSuccessResponse({resetCount:r},"订单状态重置成功")}catch(e){return console.error("[ProductionOrderService] 重置订单状态失败:",e),this.createErrorResponse("重置订单状态失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getStatistics(){try{let e=this.storage.getAllOrders(),t=e.length,r=e.filter(e=>"in_progress"===e.status).length,s=e.filter(e=>"completed"===e.status).length,a=e.reduce((e,t)=>e+(t.producedQuantity||0),0),i=e.filter(e=>e.plannedQuantity>0),o=i.reduce((e,t)=>{let r=(t.producedQuantity||0)/t.plannedQuantity;return e+r},0),n=i.length>0?Math.round(o/i.length*100)/100:0,c={};return e.forEach(e=>{c[e.status]=(c[e.status]||0)+1}),this.createSuccessResponse({totalOrders:t,activeOrders:r,completedOrders:s,totalProduction:a,averageEfficiency:n,ordersByStatus:c,productionTrend:[{date:"2024-12-01",orders:5,production:1200},{date:"2024-12-02",orders:3,production:800},{date:"2024-12-03",orders:7,production:1500},{date:"2024-12-04",orders:4,production:1e3},{date:"2024-12-05",orders:t,production:a}]},"获取生产统计数据成功")}catch(e){return this.createErrorResponse("获取生产统计数据失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getOrdersByDateRange(e,t,r){try{let s=this.storage.getAllOrders().filter(r=>{let s=new Date(r.createdAt),a=new Date(e),i=new Date(t);return s>=a&&s<=i}),a=this.processQueryWithParams(s,r);return this.createSuccessResponse(a,"获取日期范围内生产订单成功")}catch(e){return this.createErrorResponse("根据日期范围获取生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async batchUpdate(e){try{let t=[],r=[];for(let s of e){let e=this.storage.findOrderById(s.id);if(!e){r.push(`生产订单ID ${s.id} 不存在`);continue}let i=a._l.addUpdate({...e,...s.data});this.storage.updateOrder(s.id,i),t.push(i)}if(r.length>0)return this.createErrorResponse(`部分更新失败: ${r.join(", ")}`,s.Bx.BAD_REQUEST);return this.createSuccessResponse(t,`成功批量更新${t.length}个生产订单`)}catch(e){return this.createErrorResponse("批量更新生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async batchUpdateStatus(e,t){try{let r=e.map(e=>async()=>{let r=this.storage.findOrderById(e);if(!r)throw Error(`生产订单ID ${e} 不存在`);let s=a._l.addUpdate({...r,status:t});return this.storage.updateOrder(e,s),s}),i=e.map((e,t)=>`更新订单状态-${t+1}`),{successful:o,failed:n}=await E.AO.executeBatch(r,i),c=o.map(e=>e.data).filter(Boolean);if(n.length>0){let e=n.map(e=>e.error).join(", ");return this.createErrorResponse(`部分状态更新失败: ${e}`,s.Bx.BAD_REQUEST)}return this.createSuccessResponse(c,`成功批量更新${c.length}个生产订单状态为${t}`)}catch(e){return this.createErrorResponse("批量更新生产订单状态失败",s.Bx.INTERNAL_SERVER_ERROR)}}async batchDelete(e){try{let t=e.map(e=>async()=>{if(!this.storage.findOrderById(e))throw Error(`生产订单ID ${e} 不存在`);return this.storage.deleteOrder(e),e}),r=e.map((e,t)=>`删除订单-${t+1}`),{successful:a,failed:i}=await E.AO.executeBatch(t,r);if(i.length>0){let e=i.map(e=>e.error).join(", ");return this.createErrorResponse(`部分删除失败: ${e}`,s.Bx.BAD_REQUEST)}return this.createSuccessResponse(!0,`成功批量删除${a.length}个生产订单`)}catch(e){return this.createErrorResponse("批量删除生产订单失败",s.Bx.INTERNAL_SERVER_ERROR)}}}var D=r(85287);class O{static getInstance(){return O.instance||(O.instance=new O,O.instance.initializeTestData()),O.instance}getAllWorkOrders(){return[...this.workOrders]}addWorkOrder(e){this.workOrders.push(e)}updateWorkOrder(e,t){let r=this.workOrders.findIndex(t=>t.id===e);return -1===r?(console.warn(`⚠️ [ProductionWorkOrderStorage] 工单 ${e} 不存在，无法更新`),null):(this.workOrders[r]={...this.workOrders[r],...t},console.log(`✅ [ProductionWorkOrderStorage] 工单 ${e} 更新成功`),this.workOrders[r])}removeWorkOrder(e){let t=this.workOrders.findIndex(t=>t.id===e);return -1!==t&&(this.workOrders.splice(t,1),!0)}findWorkOrderById(e){let t=this.workOrders.find(t=>t.id===e);return t||(console.warn(`⚠️ [ProductionWorkOrderStorage] 工单 ${e} 不存在，当前存储的工单数量: ${this.workOrders.length}`),console.log(`📋 [ProductionWorkOrderStorage] 当前工单列表:`,this.workOrders.map(e=>e.id))),t}findWorkOrderByBatchNumber(e){return this.workOrders.find(t=>t.batchNumber===e)}findWorkOrdersByStatus(e){return this.workOrders.filter(t=>t.status===e)}findWorkOrdersBySourceOrderId(e){return this.workOrders.filter(t=>t.sourceOrderId===e)}findWorkOrdersByWorkstation(e){return this.workOrders.filter(t=>t.workstation===e)}initializeTestData(){this.workOrders=[],console.log("\uD83D\uDCCB [ProductionWorkOrderStorage] 初始化完成，工单存储已准备就绪")}constructor(){this.workOrders=[]}}class C{constructor(){this.storage=O.getInstance()}static getInstance(){return C.instance||(C.instance=new C),C.instance}createSuccessResponse(e,t){return a.N5.createSuccess(e,t||"操作成功")}createErrorResponse(e,t=s.Bx.INTERNAL_SERVER_ERROR){return a.N5.createStandardError(e,t)}processQueryWithParams(e,t){let r=[...e];if(t?.filters){let{status:e,workstation:s,sourceOrderId:a}=t.filters;e&&(r=r.filter(t=>t.status===e)),s&&(r=r.filter(e=>e.workstation===s)),a&&(r=r.filter(e=>e.sourceOrderId===a))}if(t?.search){let e=t.search.toLowerCase();r=r.filter(t=>t.batchNumber.toLowerCase().includes(e)||t.productName.toLowerCase().includes(e)||t.workstation?.toLowerCase().includes(e)||t.customerName?.toLowerCase().includes(e))}if(t?.sortBy){let{sortBy:e,sortOrder:s="asc"}=t;r.sort((t,r)=>{let a=t[e],i=r[e];return a<i?"asc"===s?-1:1:a>i?"asc"===s?1:-1:0})}let{page:s=1,pageSize:a=20}=t||{},i=(s-1)*a;return{items:r.slice(i,i+a),pagination:{current:s,pageSize:a,total:r.length,totalPages:Math.ceil(r.length/a)}}}async getAll(e){try{let t=this.storage.getAllWorkOrders(),r=this.processQueryWithParams(t,e);return this.createSuccessResponse(r,"获取生产工单列表成功")}catch(e){return this.createErrorResponse("获取生产工单列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getById(e){try{let t=this.storage.findWorkOrderById(e);if(!t)return this.createErrorResponse(`生产工单ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(t,"获取生产工单成功")}catch(e){return this.createErrorResponse("获取生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getByBatchNumber(e){try{let t=this.storage.findWorkOrderByBatchNumber(e);if(!t)return this.createErrorResponse(`生产工单批次号 ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(t,"获取生产工单成功")}catch(e){return this.createErrorResponse("根据批次号获取生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async create(e){try{let t;if(!e.productCode||!e.productName)return this.createErrorResponse(`工单产品信息不完整：productCode=${e.productCode}, productName=${e.productName}`,s.Bx.BAD_REQUEST);if(!e.sourceOrderNumber)return this.createErrorResponse("缺少源订单号，无法创建工单",s.Bx.BAD_REQUEST);if(!e.plannedMoldCount||e.plannedMoldCount<=0)return this.createErrorResponse(`计划模数无效：${e.plannedMoldCount}`,s.Bx.BAD_REQUEST);if(e.batchNumber&&this.storage.findWorkOrderByBatchNumber(e.batchNumber))return this.createErrorResponse(`生产工单批次号 ${e.batchNumber} 已存在`,s.Bx.BAD_REQUEST);if(e.sourceOrderNumber)t=D.s.generateWorkOrderId(e.sourceOrderNumber),console.log(`🔍 [工单创建调试] 生成工单ID: ${t}，源订单号: ${e.sourceOrderNumber}`);else throw Error("缺少源订单号，无法生成工单ID");let i=1,o=t;for(console.log(`🔍 [工单创建调试] 检查工单ID冲突: ${t}`);this.storage.findWorkOrderById(t);)console.log(`⚠️ [工单创建调试] 工单ID ${t} 已存在，尝试: ${o}-${i}`),t=`${o}-${i}`,i++;console.log(`✅ [工单创建调试] 最终工单ID: ${t}`);let n=a._l.addCreation({...e,id:t});if(console.log(`🔍 [工单创建调试] 准备添加工单到存储: ${n.id}`),this.storage.addWorkOrder(n),console.log(`✅ [工单创建调试] 工单已添加到存储: ${n.id}`),!this.storage.findWorkOrderById(n.id))throw console.error(`❌ [工单创建调试] 验证失败：工单 ${n.id} 添加后无法找到`),Error(`工单创建验证失败: ${n.id}`);if(console.log(`✅ [工单创建调试] 验证成功：工单 ${n.id} 已正确存储`),n.productCode!==e.productCode||n.productName!==e.productName)return this.createErrorResponse(`工单数据映射错误：期望产品 ${e.productCode}-${e.productName}，实际得到 ${n.productCode}-${n.productName}`,s.Bx.INTERNAL_SERVER_ERROR);this.notifyWorkOrderCreated(n);let{dataAccessManager:c}=r(37637);return c.clearDataTypeCache("workOrders",[n.id]),console.log(`✅ 工单创建成功: ID=${t}, 批次=${n.batchNumber}, 产品=${n.productCode}-${n.productName}, 源订单=${n.sourceOrderNumber}`),this.createSuccessResponse(n,"生产工单创建成功")}catch(e){return this.createErrorResponse("创建生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async update(e,t){try{if(t.batchNumber){let r=this.storage.findWorkOrderByBatchNumber(t.batchNumber);if(r&&r.id!==e)return this.createErrorResponse(`生产工单批次号 ${t.batchNumber} 已存在`,s.Bx.BAD_REQUEST)}let i=this.storage.updateWorkOrder(e,a._l.addUpdate(t));if(!i)return this.createErrorResponse(`生产工单ID ${e} 不存在`,s.Bx.NOT_FOUND);let{dataAccessManager:o}=r(37637);return o.clearDataTypeCache("workOrders",[i.id]),this.notifyWorkOrderUpdated(i),this.createSuccessResponse(i,"生产工单更新成功")}catch(e){return this.createErrorResponse("更新生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async delete(e){try{if(this.storage.findWorkOrderById(e),!this.storage.removeWorkOrder(e))return this.createErrorResponse(`生产工单ID ${e} 不存在`,s.Bx.NOT_FOUND);let{dataAccessManager:t}=r(37637);return t.clearDataTypeCache("workOrders",[e]),this.notifyWorkOrderDeleted(e),this.createSuccessResponse(!0,"生产工单删除成功")}catch(e){return this.createErrorResponse("删除生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getByStatus(e,t){try{let r=this.storage.findWorkOrdersByStatus(e),s=this.processQueryWithParams(r,t);return this.createSuccessResponse(s,`获取${e}状态生产工单成功`)}catch(e){return this.createErrorResponse("根据状态获取生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getBySourceOrderId(e,t){try{let r=this.storage.findWorkOrdersBySourceOrderId(e),s=this.processQueryWithParams(r,t);return this.createSuccessResponse(s,"获取相关生产工单成功")}catch(e){return this.createErrorResponse("根据源订单ID获取生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getByWorkstation(e,t){try{let r=this.storage.findWorkOrdersByWorkstation(e),s=this.processQueryWithParams(r,t);return this.createSuccessResponse(s,"获取工位生产工单成功")}catch(e){return this.createErrorResponse("根据工位获取生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getByCreditLevel(e,t){try{let r=this.storage.getAllWorkOrders().filter(t=>t.customerCreditLevel===e),s=this.processQueryWithParams(r,t);return this.createSuccessResponse(s,`获取${e}级客户生产工单成功`)}catch(e){return this.createErrorResponse("根据客户信用等级获取生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getByPriority(e,t){return this.getByCreditLevel(e,t)}async getActiveWorkOrders(e){try{let t=this.storage.getAllWorkOrders().filter(e=>"scheduled"===e.status||"in_progress"===e.status),r=this.processQueryWithParams(t,e);return this.createSuccessResponse(r,"获取活跃生产工单成功")}catch(e){return this.createErrorResponse("获取活跃生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getStatistics(){try{let e=this.storage.getAllWorkOrders(),t=e.length,r=e.filter(e=>"in_progress"===e.status).length,s=e.filter(e=>"completed"===e.status).length,a=e.reduce((e,t)=>e+(t.completedMoldCount||0),0),i=e.filter(e=>e.executionRate>0),o=i.reduce((e,t)=>e+t.executionRate,0),n=i.length>0?Math.round(o/i.length*100)/100:0,c={};e.forEach(e=>{c[e.status]=(c[e.status]||0)+1});let l={};e.forEach(e=>{let t=e.workstation||"未分配";l[t]=(l[t]||0)+1});let d=new Map;e.forEach(e=>{let t=e.workstation||"未分配";d.has(t)||d.set(t,{moldCount:0,totalRate:0,count:0});let r=d.get(t);r.moldCount+=e.completedMoldCount||0,r.totalRate+=e.executionRate||0,r.count+=1});let u=Array.from(d.entries()).map(([e,t])=>({workstation:e,efficiency:t.count>0?Math.round(t.totalRate/t.count*100)/100:0,moldCount:t.moldCount}));return this.createSuccessResponse({totalWorkOrders:t,activeWorkOrders:r,completedWorkOrders:s,totalMolds:a,averageExecutionRate:n,workOrdersByStatus:c,workOrdersByWorkstation:l,productionEfficiency:u},"获取生产工单统计数据成功")}catch(e){return this.createErrorResponse("获取生产工单统计数据失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getWorkstationEfficiency(){try{let e=this.storage.getAllWorkOrders(),t=new Map;e.forEach(e=>{let r=e.workstation||"未分配";t.has(r)||t.set(r,{totalRate:0,count:0});let s=t.get(r);s.totalRate+=e.executionRate||0,s.count+=1});let r=Array.from(t.entries()).map(([e,t])=>({workstation:e,efficiency:t.count>0?Math.round(t.totalRate/t.count*100)/100:0}));return this.createSuccessResponse(r,"获取工位效率数据成功")}catch(e){return this.createErrorResponse("获取工位效率数据失败",s.Bx.INTERNAL_SERVER_ERROR)}}async batchUpdateStatus(e,t){try{let r=e.map(e=>async()=>{if(!this.storage.findWorkOrderById(e))throw Error(`生产工单ID ${e} 不存在`);let r=a._l.addUpdate({status:t}),s=this.storage.updateWorkOrder(e,r);if(!s)throw Error(`生产工单ID ${e} 不存在`);return s}),i=e.map((e,t)=>`更新工单状态-${t+1}`),{successful:o,failed:n}=await E.AO.executeBatch(r,i),c=o.map(e=>e.data).filter(Boolean);if(n.length>0){let e=n.map(e=>e.error).join(", ");return this.createErrorResponse(`部分状态更新失败: ${e}`,s.Bx.BAD_REQUEST)}return this.createSuccessResponse(c,`成功批量更新${c.length}个生产工单状态为${t}`)}catch(e){return this.createErrorResponse("批量更新生产工单状态失败",s.Bx.INTERNAL_SERVER_ERROR)}}async batchUpdate(e){try{let t=[],r=[];for(let s of e){if(!this.storage.findWorkOrderById(s.id)){r.push(`生产工单ID ${s.id} 不存在`);continue}let e=a._l.addUpdate(s.data),i=this.storage.updateWorkOrder(s.id,e);if(!i){r.push(`生产工单ID ${s.id} 不存在`);continue}t.push(i)}if(r.length>0)return this.createErrorResponse(`部分更新失败: ${r.join(", ")}`,s.Bx.BAD_REQUEST);return this.createSuccessResponse(t,`成功批量更新${t.length}个生产工单`)}catch(e){return this.createErrorResponse("批量更新生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async batchDelete(e){try{let t=[],r=0;for(let s of e){if(!this.storage.findWorkOrderById(s)){t.push(`生产工单ID ${s} 不存在`);continue}this.storage.removeWorkOrder(s),r++}if(t.length>0)return this.createErrorResponse(`部分删除失败: ${t.join(", ")}`,s.Bx.BAD_REQUEST);return this.createSuccessResponse(!0,`成功批量删除${r}个生产工单`)}catch(e){return this.createErrorResponse("批量删除生产工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}async generateFromOrder(e,t=130){try{return this.createErrorResponse("工单生成功能需要集成生产订单服务",s.Bx.NOT_IMPLEMENTED)}catch(e){return this.createErrorResponse("从生产订单生成工单失败",s.Bx.INTERNAL_SERVER_ERROR)}}notifyWorkOrderCreated(e){try{let{dataChangeNotifier:t}=r(43471);t.notifyProductionWorkOrderCreated(e,"ProductionWorkOrderDataAccessService"),console.log(`[ProductionWorkOrderDataAccessService] 已发布工单创建事件: ${e.id}`)}catch(e){console.warn("[ProductionWorkOrderDataAccessService] 发布工单创建事件失败，但不影响主要功能:",e)}}notifyWorkOrderUpdated(e){try{let{dataChangeNotifier:t}=r(43471);t.notifyProductionWorkOrderUpdated(e,"ProductionWorkOrderDataAccessService"),console.log(`[ProductionWorkOrderDataAccessService] 已发布工单更新事件: ${e.id}`)}catch(e){console.warn("[ProductionWorkOrderDataAccessService] 发布工单更新事件失败，但不影响主要功能:",e)}}notifyWorkOrderDeleted(e){try{let{dataChangeNotifier:t}=r(43471);t.notifyProductionWorkOrderDeleted(e,"ProductionWorkOrderDataAccessService"),console.log(`[ProductionWorkOrderDataAccessService] 已发布工单删除事件: ${e}`)}catch(e){console.warn("[ProductionWorkOrderDataAccessService] 发布工单删除事件失败，但不影响主要功能:",e)}}}var v=r(23218);class N{static getInstance(){return N.instance||(N.instance=new N,N.instance.initializeTestData()),N.instance}getAllCalculations(){return[...this.costCalculations]}getAllReconciliations(){return[...this.costReconciliations]}addCalculation(e){this.costCalculations.push(e)}addReconciliation(e){this.costReconciliations.push(e)}findCalculationById(e){return this.costCalculations.find(t=>t.id===e)}findReconciliationById(e){return this.costReconciliations.find(t=>t.id===e)}findCalculationsByProduct(e){return this.costCalculations.filter(t=>t.productModelCode===e)}findPendingReconciliations(){return this.costReconciliations.filter(e=>"pending"===e.status)}updateCalculation(e,t){let r=this.costCalculations.findIndex(t=>t.id===e);-1!==r&&(this.costCalculations[r]=t)}updateReconciliation(e,t){let r=this.costReconciliations.findIndex(t=>t.id===e);-1!==r&&(this.costReconciliations[r]=t)}deleteCalculation(e){let t=this.costCalculations.findIndex(t=>t.id===e);return -1!==t&&(this.costCalculations.splice(t,1),!0)}deleteReconciliation(e){let t=this.costReconciliations.findIndex(t=>t.id===e);return -1!==t&&(this.costReconciliations.splice(t,1),!0)}initializeTestData(){this.costCalculations=[{id:"cost_calc_001",calculationDate:"2024-12-05",productionOrderId:"order_001",productModelCode:"JJS-001",totalMolds:100,formingCost:{totalMolds:100,pieceRate:8,totalWage:800,operatorRecords:[]},hotPressCost:{totalMolds:100,pieceRate:2.5,totalWage:250,employeeBindings:[]},materialCost:{materialCode:"MAT-JJS-001",materialName:"JJS-001原料",usedQuantity:.025,unitPrice:1500,totalCost:37.5},totalCost:1087.5,unitCost:10.875,createdAt:a._l.now()},{id:"cost_calc_002",calculationDate:"2024-12-05",productionOrderId:"order_002",productModelCode:"JJS-002",totalMolds:80,formingCost:{totalMolds:80,pieceRate:8,totalWage:640,operatorRecords:[]},hotPressCost:{totalMolds:80,pieceRate:2.5,totalWage:200,employeeBindings:[]},materialCost:{materialCode:"MAT-JJS-002",materialName:"JJS-002原料",usedQuantity:.02,unitPrice:1500,totalCost:30},totalCost:870,unitCost:10.875,createdAt:a._l.now()}],this.costReconciliations=[{id:"cost_recon_001",reconciliationDate:"2024-12-05",productModelCode:"JJS-001",hotPressQuantityTotal:100,warehouseQuantity:95,onSiteQuantity:3,bindingQuantityTotal:100,quantityVariance:2,variancePercentage:2,isVarianceAcceptable:!0,materialTheoreticalUsage:25,materialActualUsage:26,materialVariance:1,status:"pending",createdAt:a._l.now()}]}constructor(){this.costCalculations=[],this.costReconciliations=[]}}class I{constructor(){this.storage=N.getInstance()}static getInstance(){return I.instance||(I.instance=new I),I.instance}createSuccessResponse(e,t="操作成功"){return a.N5.createSuccess(e,t)}createErrorResponse(e,t=s.Bx.INTERNAL_SERVER_ERROR){return a.N5.createStandardError(e,t)}async getAllCalculations(e){try{let t=this.storage.getAllCalculations();if(e?.search){let r=e.search.toLowerCase();t=t.filter(e=>e.productModelCode.toLowerCase().includes(r))}let r=e?.page||1,s=e?.pageSize||10,a=(r-1)*s,i={items:t.slice(a,a+s),pagination:{current:r,pageSize:s,total:t.length,totalPages:Math.ceil(t.length/s)}};return this.createSuccessResponse(i,"获取成本计算列表成功")}catch(e){return this.createErrorResponse("获取成本计算列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getCalculationById(e){try{if(!e)return this.createErrorResponse("成本计算ID不能为空",s.Bx.BAD_REQUEST);let t=this.storage.findCalculationById(e);if(!t)return this.createErrorResponse(`成本计算ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(t,"获取成本计算成功")}catch(e){return this.createErrorResponse("获取成本计算失败",s.Bx.INTERNAL_SERVER_ERROR)}}async createCalculation(e){try{let t=a._l.addCreation({...e,id:`cost_calc_${Date.now()}_${Math.random().toString(36).substr(2,9)}`});return this.storage.addCalculation(t),this.createSuccessResponse(t,"成本计算创建成功")}catch(e){return this.createErrorResponse("创建成本计算失败",s.Bx.INTERNAL_SERVER_ERROR)}}async updateCalculation(e,t){try{let r=this.storage.findCalculationById(e);if(!r)return this.createErrorResponse(`成本计算ID ${e} 不存在`,s.Bx.NOT_FOUND);let i=a._l.addUpdate({...r,...t});return this.storage.updateCalculation(e,i),this.createSuccessResponse(i,"成本计算更新成功")}catch(e){return this.createErrorResponse("更新成本计算失败",s.Bx.INTERNAL_SERVER_ERROR)}}async deleteCalculation(e){try{if(!this.storage.deleteCalculation(e))return this.createErrorResponse(`成本计算ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(!0,"成本计算删除成功")}catch(e){return this.createErrorResponse("删除成本计算失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getCalculationsByProduct(e){try{let t=this.storage.findCalculationsByProduct(e);return this.createSuccessResponse(t,"获取产品成本计算成功")}catch(e){return this.createErrorResponse("根据产品获取成本计算失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getPendingReconciliations(){try{let e=this.storage.findPendingReconciliations();return this.createSuccessResponse(e,"获取待对账记录成功")}catch(e){return this.createErrorResponse("获取待对账记录失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getStatistics(){try{let e=this.storage.getAllCalculations(),t=this.storage.getAllReconciliations(),r=e.length,s=e.reduce((e,t)=>e+t.totalCost,0),a=e.reduce((e,t)=>e+t.totalMolds,0),i=a>0?s/a:0,o=e.map(e=>({productModelCode:e.productModelCode,productName:e.productModelCode,totalCost:e.totalCost,unitCost:e.unitCost})),n={total:t.length,pending:t.filter(e=>"pending"===e.status).length,completed:t.filter(e=>"approved"===e.status).length,averageVariance:t.length>0?t.reduce((e,t)=>e+t.variancePercentage,0)/t.length:0};return this.createSuccessResponse({totalCalculations:r,totalCost:s,averageUnitCost:i,costTrend:i>.05?"up":i<.04?"down":"stable",costByProduct:o,reconciliationSummary:n},"获取成本统计数据成功")}catch(e){return this.createErrorResponse("获取成本统计数据失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getCostSummary(){try{let e=this.storage.getAllCalculations(),t=e.reduce((e,t)=>e+t.totalCost,0),r=e.reduce((e,t)=>e+t.totalMolds,0),s=r>0?t/r:0;return this.createSuccessResponse({totalCost:t,averageUnitCost:s,costTrend:s>.05?"up":s<.04?"down":"stable"},"获取成本汇总成功")}catch(e){return this.createErrorResponse("获取成本汇总失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getAllReconciliations(e){return this.createErrorResponse("功能开发中",s.Bx.NOT_IMPLEMENTED)}async getReconciliationById(e){return this.createErrorResponse("功能开发中",s.Bx.NOT_IMPLEMENTED)}async createReconciliation(e){return this.createErrorResponse("功能开发中",s.Bx.NOT_IMPLEMENTED)}async updateReconciliation(e,t){return this.createErrorResponse("功能开发中",s.Bx.NOT_IMPLEMENTED)}async deleteReconciliation(e){return this.createErrorResponse("功能开发中",s.Bx.NOT_IMPLEMENTED)}}var T=r(43471);class _{constructor(e={}){this.pendingBatches=new Map,this.batchTimers=new Map,this.requestQueue=[],this.activeRequests=0,this.pendingRequests=new Map,this.config={batchTimeout:100,maxBatchSize:50,enableRequestMerging:!0,enablePreloading:!0,preloadThreshold:.8,concurrentRequestLimit:10,...e},this.metrics={totalRequests:0,batchedRequests:0,averageResponseTime:0,memoryUsage:0,concurrentRequests:0}}static getInstance(e){return _.instance||(_.instance=new _(e)),_.instance}async batchCreateProductionOrders(e){let t=performance.now();try{let r=Math.min(this.config.maxBatchSize,e.length),s=this.chunkArray(e,r),a=(await Promise.all(s.map(e=>this.processBatchCreate("productionOrders",e)))).flat(),i=performance.now()-t;return this.updateMetrics("batchCreate",i,a.length),{status:"success",data:a,message:`成功批量创建${a.length}个生产订单`,code:200,timestamp:new Date().toISOString(),version:"v1"}}catch(e){return{status:"error",data:void 0,message:e instanceof Error?e.message:"批量创建失败",code:500,timestamp:new Date().toISOString(),version:"v1"}}}async batchUpdateProductionOrders(e){let t=performance.now();try{let r=Math.min(this.config.maxBatchSize,e.length),s=this.chunkArray(e,r),a=(await Promise.all(s.map(e=>this.processBatchUpdate("productionOrders",e)))).flat(),i=performance.now()-t;return this.updateMetrics("batchUpdate",i,a.length),this.invalidateRelatedCache("productionOrders",e.map(e=>e.id)),{status:"success",data:a,message:`成功批量更新${a.length}个生产订单`,code:200,timestamp:new Date().toISOString(),version:"v1"}}catch(e){return{status:"error",data:void 0,message:e instanceof Error?e.message:"批量更新失败",code:500,timestamp:new Date().toISOString(),version:"v1"}}}async preloadRelatedData(e,t){if(this.config.enablePreloading)try{switch(e){case"productionOrders":Array.from(new Set(t.map(e=>e.workstation).filter(Boolean))).length,t.map(e=>e.id);break;case"workstations":t.map(e=>e.id)}await Promise.all([])}catch(e){}}async deduplicateAndMergeRequests(e,t){if(this.metrics.totalRequests++,this.pendingRequests.has(e))return this.pendingRequests.get(e);let r=t();this.pendingRequests.set(e,r);try{return await r}finally{this.pendingRequests.delete(e)}}getPerformanceMetrics(){return{...this.metrics,memoryUsage:this.calculateMemoryUsage(),concurrentRequests:this.activeRequests}}resetMetrics(){this.metrics={totalRequests:0,batchedRequests:0,averageResponseTime:0,memoryUsage:0,concurrentRequests:0}}async processBatchCreate(e,t){let r=[];for(let s of t)try{let t;switch(e){case"productionOrders":throw Error("生产订单只能通过MRP流程创建，不支持批量创建");case"productionWorkOrders":t=await L.productionWorkOrders.create(s);break;default:throw Error(`不支持的数据类型: ${e}`)}"success"===t.status&&r.push(t.data)}catch(e){}return r}async processBatchUpdate(e,t){let r=[];for(let{id:s,data:a}of t)try{let t;switch(e){case"productionOrders":t=await L.productionOrders.update(s,a);break;case"productionWorkOrders":t=await L.productionWorkOrders.update(s,a);break;default:throw Error(`不支持的数据类型: ${e}`)}"success"===t.status&&r.push(t.data)}catch(e){}return r}async executeRequest(e,t,r=!1){this.activeRequests++,this.metrics.totalRequests++;let s=performance.now();try{let r=await t();console.log("\uD83D\uDD0D [PerformanceOptimizer] 请求完成:",e);let a=performance.now()-s;return this.updateAverageResponseTime(a),r}finally{if(this.activeRequests--,this.requestQueue.length>0){let e=this.requestQueue.shift();e&&e()}}}chunkArray(e,t){let r=[];for(let s=0;s<e.length;s+=t)r.push(e.slice(s,s+t));return r}invalidateRelatedCache(e,t){console.log(`[PerformanceOptimizer] 缓存清理请求: ${e}, IDs: ${t.join(", ")}`)}updateMetrics(e,t,r){e.startsWith("batch")&&(this.metrics.batchedRequests+=r),this.updateAverageResponseTime(t)}updateAverageResponseTime(e){let t=this.metrics.averageResponseTime,r=this.metrics.totalRequests;this.metrics.averageResponseTime=(t*(r-1)+e)/r}calculateMemoryUsage(){return 1024*this.pendingRequests.size}isWriteOperation(e){return["create","update","delete","remove","add","insert","createOrder","updateOrder","deleteOrder","createProductionOrder","updateProductionOrder","deleteProductionOrder","createWorkOrder","updateWorkOrder","deleteWorkOrder","updateStatus","updateProgress","updateQuantity","batchCreate","batchUpdate","batchDelete"].some(t=>e.toLowerCase().includes(t.toLowerCase()))}}let A=_.getInstance();class w{constructor(){this.alerts=[],this.suggestions=[],this.requestTimes=[],this.errorCount=0,this.totalRequests=0,this.slowQueryThreshold=1e3,this.monitoringInterval=null,this.requests=[],this.metrics={requestCount:0,averageResponseTime:0,errorRate:0,cacheHitRate:0,slowQueries:[],memoryUsage:0,concurrentRequests:0},this.startMonitoring()}static getInstance(){return w.instance||(w.instance=new w),w.instance}recordRequest(e,t,r,s){this.totalRequests++,this.requestTimes.push(t),this.requests.push({operation:e,duration:t,success:r,timestamp:Date.now(),parameters:s}),this.requests.length>1e3&&(this.requests=this.requests.slice(-500)),!r&&this.errorCount++,t>this.slowQueryThreshold&&(this.metrics.slowQueries.push({operation:e,duration:t,timestamp:new Date,parameters:s}),this.metrics.slowQueries.length>100&&(this.metrics.slowQueries=this.metrics.slowQueries.slice(-50))),this.updateMetrics(),this.performThresholdCheck()}getMetrics(){return{...this.metrics}}getAlerts(){return[...this.alerts]}getSlowQueries(){return[...this.metrics.slowQueries]}getErrorRate(){return this.metrics.errorRate}checkPerformanceThresholds(){this.performThresholdCheck()}getPerformanceTrends(){let e=[],t=this.requests.slice(-100);if(t.length>=10){let r=t.map(e=>e.duration),s=r.reduce((e,t)=>e+t,0)/r.length,a=r.slice(-10).reduce((e,t)=>e+t,0)/10;e.push({metric:"responseTime",direction:a>s?"increasing":"decreasing",change:Math.abs(a-s),description:`响应时间${a>s?"上升":"下降"} ${Math.abs(a-s).toFixed(2)}ms`});let i=t.slice(-10).filter(e=>!e.success).length,o=t.filter(e=>!e.success).length,n=i/10,c=o/t.length;e.push({metric:"errorRate",direction:n>c?"increasing":"decreasing",change:Math.abs(n-c),description:`错误率${n>c?"上升":"下降"} ${(100*Math.abs(n-c)).toFixed(2)}%`})}return e}getOptimizationSuggestions(){let e=[],t=this.getMetrics();return t.averageResponseTime>1e3&&e.push({type:"performance",priority:"high",title:"响应时间过长",description:`平均响应时间 ${t.averageResponseTime.toFixed(2)}ms 超过建议值 1000ms`,recommendation:"考虑优化数据查询逻辑、增加缓存或优化数据库索引",impact:"high",expectedImprovement:"响应时间减少50-70%",implementation:"1. 优化SQL查询 2. 增加Redis缓存 3. 使用CDN"}),t.cacheHitRate<.5&&e.push({type:"cache",priority:"medium",title:"缓存命中率偏低",description:`缓存命中率 ${(100*t.cacheHitRate).toFixed(2)}% 低于建议值 50%`,recommendation:"检查缓存策略配置，考虑增加缓存时间或优化缓存键设计",impact:"medium",expectedImprovement:"缓存命中率提升至80%以上",implementation:"1. 调整TTL配置 2. 优化缓存键策略 3. 实现预热机制"}),t.errorRate>.05&&e.push({type:"reliability",priority:"high",title:"错误率过高",description:`错误率 ${(100*t.errorRate).toFixed(2)}% 超过建议值 5%`,recommendation:"检查错误日志，修复数据访问逻辑中的问题",impact:"high",expectedImprovement:"错误率降低至1%以下",implementation:"1. 增强错误处理 2. 添加重试机制 3. 优化数据验证"}),t.memoryUsage>.8&&e.push({type:"memory",priority:"high",title:"内存使用率过高",description:`内存使用率 ${(100*t.memoryUsage).toFixed(2)}% 超过建议值 80%`,recommendation:"考虑清理缓存、优化数据结构或增加内存限制",impact:"high",expectedImprovement:"内存使用降低30-50%",implementation:"1. 清理过期缓存 2. 优化对象引用 3. 实现内存池"}),e}generatePerformanceReport(){let e=this.generateRecommendations();return{summary:this.generateSummary(),metrics:this.getMetrics(),alerts:this.getAlerts(),suggestions:this.getOptimizationSuggestions(),recommendations:e}}reset(){this.metrics={requestCount:0,averageResponseTime:0,errorRate:0,cacheHitRate:0,slowQueries:[],memoryUsage:0,concurrentRequests:0},this.alerts=[],this.suggestions=[],this.requestTimes=[],this.errorCount=0,this.totalRequests=0}setSlowQueryThreshold(e){this.slowQueryThreshold=e}startMonitoring(){this.monitoringInterval=setInterval(()=>{this.updateMetrics(),this.checkPerformanceThresholds(),this.cleanupOldData()},3e4)}updateMetrics(){if(this.metrics.requestCount=this.totalRequests,this.requestTimes.length>0){let e=this.requestTimes.reduce((e,t)=>e+t,0);this.metrics.averageResponseTime=e/this.requestTimes.length}this.metrics.errorRate=this.totalRequests>0?this.errorCount/this.totalRequests*100:0;try{let{dataAccessManager:e}=r(37637),t=e.getCacheStatistics();this.metrics.cacheHitRate=100*t.hitRate}catch(e){this.metrics.cacheHitRate=0}this.metrics.memoryUsage=this.estimateMemoryUsage()}performThresholdCheck(){let e=new Date;this.metrics.averageResponseTime>2e3&&this.addAlert({type:"warning",message:"平均响应时间过长",metric:"averageResponseTime",value:this.metrics.averageResponseTime,threshold:2e3,timestamp:e}),this.metrics.errorRate>5&&this.addAlert({type:"error",message:"错误率过高",metric:"errorRate",value:this.metrics.errorRate,threshold:5,timestamp:e}),this.metrics.cacheHitRate<70&&this.addAlert({type:"warning",message:"缓存命中率较低",metric:"cacheHitRate",value:this.metrics.cacheHitRate,threshold:70,timestamp:e});let t=this.metrics.slowQueries.filter(e=>Date.now()-e.timestamp.getTime()<3e5);t.length>10&&this.addAlert({type:"warning",message:"慢查询数量过多",metric:"slowQueries",value:t.length,threshold:10,timestamp:e})}addAlert(e){this.alerts.push(e),this.alerts.length>50&&(this.alerts=this.alerts.slice(-25))}generateOptimizationSuggestions(){this.suggestions=[],this.metrics.cacheHitRate<70&&this.suggestions.push({type:"cache",priority:"high",title:"缓存命中率较低",description:"缓存命中率较低，建议优化缓存策略",recommendation:"增加缓存时间、优化缓存键生成策略、启用预加载",impact:"high",expectedImprovement:"提升响应速度30-50%",implementation:"增加缓存时间、优化缓存键生成策略、启用预加载"}),this.metrics.slowQueries.length>5&&this.suggestions.push({type:"performance",priority:"high",title:"慢查询过多",description:"存在较多慢查询，建议优化查询逻辑",recommendation:"添加索引、优化查询条件、使用批量操作",impact:"high",expectedImprovement:"减少响应时间50-70%",implementation:"添加索引、优化查询条件、使用批量操作"}),this.metrics.averageResponseTime>1e3&&this.suggestions.push({type:"performance",priority:"medium",title:"响应时间较长",description:"响应时间较长，建议启用数据预加载",recommendation:"实现智能预加载、使用懒加载策略",impact:"medium",expectedImprovement:"减少用户等待时间40-60%",implementation:"实现智能预加载、使用懒加载策略"}),this.totalRequests>100&&this.identifyBatchableOperations().length>0&&this.suggestions.push({type:"performance",priority:"medium",title:"批量操作优化",description:"发现可批量处理的操作，建议使用批量API",recommendation:"合并相似操作、使用批量创建/更新接口",impact:"medium",expectedImprovement:"减少网络请求60-80%",implementation:"合并相似操作、使用批量创建/更新接口"})}generateRecommendations(){let e=[];return this.metrics.cacheHitRate<50&&e.push("立即优化缓存策略，当前命中率过低"),this.metrics.errorRate>10&&e.push("紧急处理错误率问题，检查网络连接和服务状态"),this.metrics.averageResponseTime>3e3&&e.push("响应时间严重超标，建议立即进行性能优化"),this.metrics.slowQueries.length>20&&e.push("慢查询过多，建议重构数据访问逻辑"),0===e.length&&e.push("性能表现良好，继续保持当前优化策略"),e}generateSummary(){let e=this.metrics.cacheHitRate>80?"优秀":this.metrics.cacheHitRate>60?"良好":"需要改进",t=this.metrics.averageResponseTime<500?"优秀":this.metrics.averageResponseTime<1e3?"良好":"需要改进",r=this.metrics.errorRate<1?"优秀":this.metrics.errorRate<5?"良好":"需要改进";return`数据访问性能总体评估：缓存性能${e}，响应速度${t}，错误率${r}`}identifyBatchableOperations(){let e=this.metrics.slowQueries.map(e=>e.operation).reduce((e,t)=>(e[t]=(e[t]||0)+1,e),{});return Object.keys(e).filter(t=>e[t]>3)}estimateMemoryUsage(){try{let{dataAccessManager:e}=r(37637),t=e.getCacheStatistics();return 1024*t.size}catch(e){return 0}}cleanupOldData(){let e=Date.now()-36e5;this.metrics.slowQueries=this.metrics.slowQueries.filter(t=>t.timestamp.getTime()>e),this.alerts=this.alerts.filter(t=>t.timestamp.getTime()>e),this.requestTimes.length>1e3&&(this.requestTimes=this.requestTimes.slice(-500))}}let B=w.getInstance(),k={enableCaching:!0,defaultTTL:3e5,maxSize:1e3,strategies:{orders:{ttl:12e4,enabled:!0,priority:"high",maxEntries:200},products:{ttl:6e5,enabled:!0,priority:"medium",maxEntries:300},workstations:{ttl:3e4,enabled:!0,priority:"critical",maxEntries:50},statistics:{ttl:3e4,enabled:!0,priority:"low",maxEntries:100},customers:{ttl:9e5,enabled:!0,priority:"medium",maxEntries:150},employees:{ttl:18e5,enabled:!0,priority:"medium",maxEntries:100}},monitoring:{enabled:!0,reportInterval:6e4},smartEviction:{enabled:!0,algorithm:"adaptive",thresholds:{memoryWarning:.7,memoryCritical:.85}},prewarming:{enabled:!0,strategies:["workstations","orders","products"],scheduleInterval:3e4}},P={enableLogging:!0,enableCaching:!0,defaultTTL:3e5,maxCacheSize:1e3,retryAttempts:3,retryDelay:1e3};class b{constructor(e={}){this.accessLogs=[],this.cache=new Map,this.cacheConfig=k,this.requestCache=new Map,this.cacheStats={hits:0,misses:0,totalRequests:0,evictions:0,prewarmHits:0},this.cacheAccessHistory=new Map,this.prewarmTimer=null,this.lastMemoryCheck=0,this.memoryPressure=!1,this.performanceMetrics={totalCalls:0,successCalls:0,errorCalls:0,averageResponseTime:0,minResponseTime:0,maxResponseTime:0,cacheHitRate:0,slowQueries:[],methodStats:new Map,hourlyStats:[]},this.recentCalls=[],this.methodStats=new Map,this.performanceBatch=[],this.batchReportTimer=null,this.BATCH_REPORT_INTERVAL=5e3,this.systemConfig={enableCaching:!0,cacheTimeout:3e5,enableLogging:!0,logLevel:"INFO",enablePerformanceMonitoring:!0,maxConcurrentRequests:10,retryAttempts:3,retryDelay:1e3},this.config={...P,...e},this.__managerId=Math.random().toString(36).substr(2,9),console.log("✅ [DataAccessManager] 创建实例，ID:",this.__managerId,"内置缓存系统已启用"),this.config.enableCaching?(console.log("\uD83D\uDD27 [DataAccessManager] 内置缓存系统已启用"),this.initializeAdvancedCaching()):console.log("\uD83D\uDD27 [DataAccessManager] 缓存已禁用"),this.initializePerformanceMonitoring(),this.productService=n.getInstance(),this.customerService=l.getInstance(),this.employeeService=u.getInstance(),this.inventoryService=g.getInstance(),this.orderService=R.getInstance(),console.log("\uD83D\uDD27 [DataAccessManager] 初始化OrderService，管理器ID:",this.__managerId),console.log("\uD83D\uDD27 [DataAccessManager] OrderService实例ID:",this.orderService.__serviceId),this.workTimeService=y.getInstance(),this.productionOrderService=S.getInstance(),this.productionWorkOrderService=C.getInstance(),this.workstationService=v.D.getInstance(),this.costCalculationService=I.getInstance(),this.initializeDataChangeNotification()}static getInstance(e){return b.instance||(b.instance=new b(e)),b.instance}logAccess(e,t,r,s,i,o,n){if(!this.config.enableLogging)return;let c={timestamp:a._l.now(),service:e,method:t,params:r,duration:s,success:i,error:o};this.accessLogs.push(c),this.accessLogs.length>1e3&&(this.accessLogs=this.accessLogs.slice(-1e3))}addToPerformanceBatch(e,t,r,s,a){this.performanceBatch.push({service:e,method:t,duration:r,success:s,fromCache:a,timestamp:Date.now()}),this.batchReportTimer||(this.batchReportTimer=setTimeout(()=>{this.flushPerformanceBatch()},this.BATCH_REPORT_INTERVAL))}flushPerformanceBatch(){if(0===this.performanceBatch.length){this.batchReportTimer=null;return}console.log("\uD83D\uDCCA [DataAccessManager] 批量性能报告:",{totalOperations:this.performanceBatch.length,successCount:this.performanceBatch.filter(e=>e.success).length,cacheHits:this.performanceBatch.filter(e=>e.fromCache).length,averageTime:Math.round(this.performanceBatch.reduce((e,t)=>e+t.duration,0)/this.performanceBatch.length),services:new Set(this.performanceBatch.map(e=>e.service)).size,timeRange:{start:new Date(Math.min(...this.performanceBatch.map(e=>e.timestamp))).toLocaleTimeString(),end:new Date(Math.max(...this.performanceBatch.map(e=>e.timestamp))).toLocaleTimeString()}}),this.performanceBatch=[],this.batchReportTimer=null}shouldUseCache(e){return"getById"!==e&&["get","find","search","list","statistics","utilization"].some(t=>e.toLowerCase().includes(t))}shouldCacheResult(e,t){return this.shouldUseCache(e)&&t?.status==="success"}generateCacheKey(e,t,r){let s;let a=`${e}:${t}`;return r&&("object"!=typeof r||0!==Object.keys(r).length)?(s="object"==typeof r?JSON.stringify(Object.keys(r).sort().reduce((e,t)=>(e[t]=r[t],e),{})):String(r),`${a}:${Buffer.from(s).toString("base64").slice(0,32)}`):a}getDataTypeFromService(e){return e.includes("ProductionOrder")?"orders":e.includes("ProductionWorkOrder")?"workOrders":e.includes("OrderService")||e.includes("SalesOrder")?"orders":e.includes("Workstation")?"workstations":"statistics"}getAccessFrequency(e){return this.accessLogs.filter(e=>Date.now()-new Date(e.timestamp).getTime()<3e5).filter(t=>`${t.service}.${t.method}`===e).length}shouldPreload(e,t){return e.includes("getAll")&&t?.data?.items?.length>0}getFromCache(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt?(this.cache.delete(e),null):(t.accessCount++,t.lastAccessed=Date.now(),t.data):null}setToCache(e,t,r,s){this.cache.size>=this.cacheConfig.maxSize&&this.smartEvictEntries();let a=this.cacheConfig.strategies[r]||{ttl:this.cacheConfig.defaultTTL,priority:"medium"},i=this.getDataType(r,s),o=a.priority||"medium",n=this.estimateDataSize(t),c=this.predictAccessPattern(e,r),l={data:t,expiresAt:Date.now()+a.ttl,accessCount:1,lastAccessed:Date.now(),createdAt:Date.now(),priority:o,dataType:i,estimatedSize:n,accessPattern:c,refreshable:this.isRefreshableData(r,s)};this.cache.set(e,l),this.recordCacheAccess(e)}smartEvictEntries(){let e=Array.from(this.cache.entries()),t=Date.now();switch(this.cacheConfig.smartEviction.algorithm){case"lru":this.evictByLRU(e);break;case"lfu":this.evictByLFU(e);break;default:this.evictAdaptive(e,t)}this.cacheStats.evictions++}evictAdaptive(e,t){let r=e.map(([e,r])=>{let s;s=0+(({critical:100,high:70,medium:40,low:10})[r.priority]||40);let a=(t-r.createdAt)/864e5;return s+=Math.min(r.accessCount/Math.max(a,.1)*10,50)+Math.max(50-(t-r.lastAccessed)/36e5*2,0),s-=Math.min(r.estimatedSize/1e3,20),{key:e,entry:r,score:s}});r.sort((e,t)=>e.score-t.score);let s=Math.max(Math.floor(.15*e.length),1);for(let e=0;e<s&&e<r.length;e++)this.cache.delete(r[e].key),this.cacheAccessHistory.delete(r[e].key)}evictByLRU(e){e.sort((e,t)=>e[1].lastAccessed-t[1].lastAccessed);let t=Math.floor(.1*e.length);for(let r=0;r<t;r++)this.cache.delete(e[r][0]),this.cacheAccessHistory.delete(e[r][0])}evictByLFU(e){e.sort((e,t)=>e[1].accessCount-t[1].accessCount);let t=Math.floor(.1*e.length);for(let r=0;r<t;r++)this.cache.delete(e[r][0]),this.cacheAccessHistory.delete(e[r][0])}evictOldestEntries(){let e=Array.from(this.cache.entries());e.sort((e,t)=>e[1].lastAccessed-t[1].lastAccessed);let t=Math.floor(.1*e.length);for(let r=0;r<t;r++)this.cache.delete(e[r][0])}async executeWithCache(e,t,r,s){if(!this.config.enableCaching||!["get","find","search","list","statistics"].some(e=>t.toLowerCase().includes(e)))return s();let a=this.generateCacheKey(e,t,r);if(this.requestCache.has(a))return this.requestCache.get(a);let i=this.cache.get(a);if(i&&Date.now()<i.expiresAt)return this.cacheStats.hits++,i.accessCount++,i.lastAccessed=Date.now(),this.recordCacheAccess(a),i.data;let o=s();this.requestCache.set(a,o);try{let r=await o;return this.cacheStats.misses++,this.shouldCacheResult(t,r)&&this.setToCache(a,r,e,t),r}finally{this.requestCache.delete(a)}}initializeAdvancedCaching(){console.log("\uD83D\uDD27 [DataAccessManager] 启动智能缓存管理"),this.cacheConfig.prewarming.enabled&&this.startCachePrewarming(),this.startMemoryMonitoring(),console.log("✅ [DataAccessManager] 智能缓存管理已启动")}startCachePrewarming(){this.prewarmTimer&&clearInterval(this.prewarmTimer),this.prewarmTimer=setInterval(()=>{this.performCachePrewarming()},this.cacheConfig.prewarming.scheduleInterval),setTimeout(()=>this.performCachePrewarming(),5e3)}async performCachePrewarming(){if(this.memoryPressure){console.log("\uD83D\uDD27 [DataAccessManager] 内存压力过高，跳过预热");return}console.log("\uD83D\uDD25 [DataAccessManager] 开始缓存预热");try{for(let e of this.cacheConfig.prewarming.strategies)await this.prewarmStrategy(e)}catch(e){console.error("❌ [DataAccessManager] 缓存预热失败:",e)}}async prewarmStrategy(e){switch(e){case"workstations":await this.prewarmWorkstations();break;case"orders":await this.prewarmOrders();break;case"products":await this.prewarmProducts()}}async prewarmWorkstations(){try{await this.workstations.getActiveWorkstations(),this.cacheStats.prewarmHits++,console.log("\uD83D\uDD25 [DataAccessManager] 工位数据预热完成")}catch(e){console.warn("⚠️ [DataAccessManager] 工位数据预热失败:",e)}}async prewarmOrders(){try{await this.orders.getAll({limit:50}),this.cacheStats.prewarmHits++,console.log("\uD83D\uDD25 [DataAccessManager] 订单数据预热完成")}catch(e){console.warn("⚠️ [DataAccessManager] 订单数据预热失败:",e)}}async prewarmProducts(){try{await this.products.getActive(),this.cacheStats.prewarmHits++,console.log("\uD83D\uDD25 [DataAccessManager] 产品数据预热完成")}catch(e){console.warn("⚠️ [DataAccessManager] 产品数据预热失败:",e)}}startMemoryMonitoring(){setInterval(()=>{this.checkMemoryPressure()},3e4)}checkMemoryPressure(){let e=Date.now();if(!(e-this.lastMemoryCheck<1e4)){this.lastMemoryCheck=e;try{let e=this.cache.size/this.cacheConfig.maxSize;e>this.cacheConfig.smartEviction.thresholds.memoryCritical?(this.memoryPressure=!0,this.handleCriticalMemoryPressure()):e>this.cacheConfig.smartEviction.thresholds.memoryWarning?(this.memoryPressure=!1,this.handleMemoryWarning()):this.memoryPressure=!1}catch(e){console.warn("⚠️ [DataAccessManager] 内存压力检查失败:",e)}}}handleCriticalMemoryPressure(){console.warn("\uD83D\uDEA8 [DataAccessManager] 检测到紧急内存压力，执行强制清理"),this.smartEvictEntries(),this.prewarmTimer&&(clearInterval(this.prewarmTimer),this.prewarmTimer=null)}handleMemoryWarning(){console.warn("⚠️ [DataAccessManager] 检测到内存压力警告，执行适度清理"),this.cleanupLowPriorityCache()}cleanupLowPriorityCache(){let e=Array.from(this.cache.entries()),t=0;for(let[r,s]of e)"low"===s.priority&&Date.now()>s.expiresAt-6e4&&(this.cache.delete(r),this.cacheAccessHistory.delete(r),t++);console.log(`🧹 [DataAccessManager] 清理了${t}个低优先级缓存条目`)}getDataType(e,t){return t?t.includes("statistics")||t.includes("utilization")?"statistics":t.includes("list")||t.includes("getAll")?"list":t.includes("getById")||t.includes("getBy")?"detail":e:e}predictAccessPattern(e,t){let r=this.cacheAccessHistory.get(e)||[];if(r.length<2)return"workstations"===t||"statistics"===t?"frequent":"orders"===t||"products"===t?"occasional":"rare";let s=r.filter(e=>Date.now()-e<36e5).length;return s>10?"frequent":s>3?"occasional":"rare"}estimateDataSize(e){try{let t=JSON.stringify(e);return 2*t.length}catch{return 1e3}}isRefreshableData(e,t){return!!(t?.includes("statistics")||t?.includes("status")||t?.includes("utilization"))||"workstations"===e}recordCacheAccess(e){let t=this.cacheAccessHistory.get(e)||[];t.push(Date.now()),t.length>100&&t.splice(0,t.length-100),this.cacheAccessHistory.set(e,t)}getCacheTTL(e,t){return({orders:12e4,products:6e5,workstations:3e4,statistics:3e4,customers:9e5,employees:18e5})[e]||this.config.defaultTTL||3e5}initializePerformanceMonitoring(){this.performanceMetrics={totalCalls:0,successCalls:0,errorCalls:0,averageResponseTime:0,minResponseTime:1/0,maxResponseTime:0,cacheHitRate:0,slowQueries:[],methodStats:new Map,hourlyStats:[]},this.startPerformanceCleanupTask()}startPerformanceCleanupTask(){setInterval(()=>{this.cleanupPerformanceData()},36e5)}cleanupPerformanceData(){let e=Date.now(),t=e-36e5,r=e-864e5;this.recentCalls=this.recentCalls.filter(e=>e.timestamp>t),this.performanceMetrics.slowQueries=this.performanceMetrics.slowQueries.filter(e=>e.timestamp>r).slice(-100),this.updateHourlyStats()}updateHourlyStats(){let e=new Date,t=`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")} ${String(e.getHours()).padStart(2,"0")}:00`,r=Date.now()-36e5,s=this.recentCalls.filter(e=>e.timestamp>r);if(s.length>0){let e=s.reduce((e,t)=>e+t.duration,0),r=s.filter(e=>"error"===e.status).length,a={hour:t,calls:s.length,averageTime:e/s.length,errorRate:r/s.length},i=this.performanceMetrics.hourlyStats.findIndex(e=>e.hour===t);i>=0?this.performanceMetrics.hourlyStats[i]=a:this.performanceMetrics.hourlyStats.push(a),this.performanceMetrics.hourlyStats=this.performanceMetrics.hourlyStats.slice(-24)}}updatePerformanceMetrics(e,t,r,s=!1){this.performanceMetrics.totalCalls++,r?this.performanceMetrics.successCalls++:this.performanceMetrics.errorCalls++,this.performanceMetrics.minResponseTime=Math.min(this.performanceMetrics.minResponseTime,t),this.performanceMetrics.maxResponseTime=Math.max(this.performanceMetrics.maxResponseTime,t);let a=this.performanceMetrics.averageResponseTime*(this.performanceMetrics.totalCalls-1)+t;if(this.performanceMetrics.averageResponseTime=a/this.performanceMetrics.totalCalls,s){let e=this.performanceMetrics.totalCalls*this.performanceMetrics.cacheHitRate+1;this.performanceMetrics.cacheHitRate=e/this.performanceMetrics.totalCalls}else{let e=this.performanceMetrics.totalCalls*this.performanceMetrics.cacheHitRate;this.performanceMetrics.cacheHitRate=e/this.performanceMetrics.totalCalls}let i=this.methodStats.get(e)||{calls:0,totalTime:0,averageTime:0,successRate:0,lastCall:0,errors:0};i.calls++,i.totalTime+=t,i.averageTime=i.totalTime/i.calls,i.lastCall=Date.now(),r?i.successRate=(i.successRate*(i.calls-1)+1)/i.calls:(i.errors++,i.successRate=i.successRate*(i.calls-1)/i.calls),this.methodStats.set(e,i),t>1e3&&(this.performanceMetrics.slowQueries.push({method:e,params:{},duration:t,timestamp:Date.now(),cached:s}),this.performanceMetrics.slowQueries.length>100&&(this.performanceMetrics.slowQueries=this.performanceMetrics.slowQueries.slice(-50))),this.recentCalls.push({method:e,duration:t,status:r?"success":"error",timestamp:Date.now(),cached:s}),this.recentCalls.length>1e3&&(this.recentCalls=this.recentCalls.slice(-500))}async executeWithLogging(e,t,r,s){let a=Date.now(),i=`${e}.${t}`;try{let o=await this.executeWithCache(e,t,r,s),n=Date.now()-a;return this.updatePerformanceMetrics(i,n,!0,!1),this.logAccess(e,t,r,n,!0),B.recordRequest(i,n,!0,r),o}catch(n){let s=Date.now()-a,o=n instanceof Error?n.message:String(n);throw this.updatePerformanceMetrics(i,s,!1,!1),this.logAccess(e,t,r,s,!1,o),B.recordRequest(i,s,!1,r),n}}get products(){return{getAll:e=>this.executeWithLogging("ProductService","getProducts",e,()=>this.productService.getProducts(e)),getById:e=>this.executeWithLogging("ProductService","getProductById",{id:e},()=>this.productService.getProductById(e)),getByCode:e=>this.executeWithLogging("ProductService","getProductByCode",{code:e},()=>this.productService.getProductByCode(e)),create:e=>this.executeWithLogging("ProductService","createProduct",e,()=>this.productService.createProduct(e)),update:(e,t)=>this.executeWithLogging("ProductService","updateProduct",{id:e,updates:t},()=>this.productService.updateProduct(e,t)),delete:e=>this.executeWithLogging("ProductService","deleteProduct",{id:e},()=>this.productService.deleteProduct(e)),getActive:()=>this.executeWithLogging("ProductService","getActiveProducts",{},()=>this.productService.getActiveProducts()),getByCategory:e=>this.executeWithLogging("ProductService","getProductsByCategory",{category:e},()=>this.productService.getProductsByCategory(e)),search:e=>this.executeWithLogging("ProductService","searchProducts",{keyword:e},()=>this.productService.searchProducts(e)),getByMold:e=>this.executeWithLogging("ProductService","getProductsByMold",{moldId:e},()=>this.productService.getProductsByMold(e)),getMoldUsage:()=>this.executeWithLogging("ProductService","getMoldUsageStatistics",{},()=>this.productService.getMoldUsageStatistics())}}get customers(){return{getAll:e=>this.executeWithLogging("CustomerService","getCustomers",e,()=>this.customerService.getCustomers(e)),getById:e=>this.executeWithLogging("CustomerService","getCustomerById",{id:e},()=>this.customerService.getCustomerById(e)),create:e=>this.executeWithLogging("CustomerService","createCustomer",e,()=>this.customerService.createCustomer(e)),update:(e,t)=>this.executeWithLogging("CustomerService","updateCustomer",{id:e,updates:t},()=>this.customerService.updateCustomer(e,t)),delete:e=>this.executeWithLogging("CustomerService","deleteCustomer",{id:e},()=>this.customerService.deleteCustomer(e)),getActive:()=>this.executeWithLogging("CustomerService","getActiveCustomers",{},()=>this.customerService.getActiveCustomers()),search:e=>this.executeWithLogging("CustomerService","searchCustomers",{keyword:e},()=>this.customerService.searchCustomers(e))}}get employees(){return{getAll:e=>this.executeWithLogging("EmployeeService","getEmployees",e,()=>this.employeeService.getEmployees(e)),getById:e=>this.executeWithLogging("EmployeeService","getEmployeeById",{id:e},()=>this.employeeService.getEmployeeById(e)),create:e=>this.executeWithLogging("EmployeeService","createEmployee",e,()=>this.employeeService.createEmployee(e)),update:(e,t)=>this.executeWithLogging("EmployeeService","updateEmployee",{id:e,updates:t},()=>this.employeeService.updateEmployee(e,t)),delete:e=>this.executeWithLogging("EmployeeService","deleteEmployee",{id:e},()=>this.employeeService.deleteEmployee(e)),getActive:()=>this.executeWithLogging("EmployeeService","getActiveEmployees",{},()=>this.employeeService.getActiveEmployees()),getByDepartment:e=>this.executeWithLogging("EmployeeService","getEmployeesByDepartment",{department:e},()=>this.employeeService.getEmployeesByDepartment(e)),getByRole:e=>this.executeWithLogging("EmployeeService","getEmployeesByRole",{role:e},()=>this.employeeService.getEmployeesByRole(e)),getSales:()=>this.executeWithLogging("EmployeeService","getSalesEmployees",{},()=>this.employeeService.getSalesEmployees())}}get inventory(){return{getAll:e=>this.executeWithLogging("InventoryService","getProductInventory",e,()=>this.inventoryService.getProductInventory(e)),getByProductCode:e=>this.executeWithLogging("InventoryService","getInventoryByProductCode",{productCode:e},()=>this.inventoryService.getInventoryByProductCode(e)),update:(e,t)=>this.executeWithLogging("InventoryService","updateInventory",{productCode:e,updates:t},()=>this.inventoryService.updateInventory(e,t)),getLowStock:e=>this.executeWithLogging("InventoryService","getLowStockProducts",{threshold:e},()=>this.inventoryService.getLowStockProducts(e)),getValue:()=>this.executeWithLogging("InventoryService","getInventoryValue",{},()=>this.inventoryService.getInventoryValue())}}get orders(){return{getAll:e=>(console.log("\uD83D\uDD0D [DataAccessManager] orders.getAll 被调用，管理器ID:",this.__managerId),console.log("\uD83D\uDD0D [DataAccessManager] 使用的OrderService实例ID:",this.orderService.__serviceId),this.executeWithLogging("OrderService","getOrders",e,()=>this.orderService.getOrders(e))),getById:e=>this.executeWithLogging("OrderService","getOrderById",{id:e},()=>this.orderService.getOrderById(e)),getByNumber:e=>this.executeWithLogging("OrderService","getOrderByNumber",{orderNumber:e},()=>this.orderService.getOrderByNumber(e)),create:e=>(console.log("\uD83D\uDD0D [DataAccessManager] orders.create 被调用，管理器ID:",this.__managerId),console.log("\uD83D\uDD0D [DataAccessManager] 使用的OrderService实例ID:",this.orderService.__serviceId),this.executeWithLogging("OrderService","createOrder",e,()=>this.orderService.createOrder(e))),update:(e,t)=>this.executeWithLogging("OrderService","updateOrder",{id:e,updates:t},()=>this.orderService.updateOrder(e,t)),delete:e=>this.executeWithLogging("OrderService","deleteOrder",{id:e},()=>this.orderService.deleteOrder(e)),getByStatus:e=>this.executeWithLogging("OrderService","getOrdersByStatus",{status:e},()=>this.orderService.getOrdersByStatus(e)),getByCustomer:e=>this.executeWithLogging("OrderService","getOrdersByCustomer",{customerId:e},()=>this.orderService.getOrdersByCustomer(e)),getBySalesRep:e=>this.executeWithLogging("OrderService","getOrdersBySalesRep",{salesRepId:e},()=>this.orderService.getOrdersBySalesRep(e)),getByDateRange:(e,t)=>this.executeWithLogging("OrderService","getOrdersByDateRange",{startDate:e,endDate:t},()=>this.orderService.getOrdersByDateRange(e,t))}}get workTime(){return{getConfigurations:()=>this.executeWithLogging("WorkTimeService","getConfigurations",{},()=>this.workTimeService.getConfigurations()),getById:e=>this.executeWithLogging("WorkTimeService","getConfigurationById",{id:e},()=>this.workTimeService.getConfigurationById(e)),create:e=>this.executeWithLogging("WorkTimeService","createConfiguration",e,()=>this.workTimeService.createConfiguration(e)),update:(e,t)=>this.executeWithLogging("WorkTimeService","updateConfiguration",{id:e,...t},()=>this.workTimeService.updateConfiguration(e,t)),delete:e=>this.executeWithLogging("WorkTimeService","deleteConfiguration",{id:e},()=>this.workTimeService.deleteConfiguration(e)),addWorkTimeSlot:(e,t)=>this.executeWithLogging("WorkTimeService","addWorkTimeSlot",{configId:e,slot:t},()=>this.workTimeService.addWorkTimeSlot(e,t)),updateWorkTimeSlot:(e,t,r)=>this.executeWithLogging("WorkTimeService","updateWorkTimeSlot",{configId:e,slotId:t,...r},()=>this.workTimeService.updateWorkTimeSlot(e,t,r)),deleteWorkTimeSlot:(e,t)=>this.executeWithLogging("WorkTimeService","deleteWorkTimeSlot",{configId:e,slotId:t},()=>this.workTimeService.deleteWorkTimeSlot(e,t)),calculateWorkingMinutes:(e,t)=>{let r=Date.now(),s=this.workTimeService.calculateWorkingMinutes(e,t),a=Date.now()-r;return this.logAccess("WorkTimeService","calculateWorkingMinutes",{workTimeSlots:e,breakTimeSlots:t},a,!0),s},validateTimeSlot:(e,t)=>{let r=Date.now(),s=this.workTimeService.validateTimeSlot(e,t),a=Date.now()-r;return this.logAccess("WorkTimeService","validateTimeSlot",{startTime:e,endTime:t},a,!0),s},getDefault:()=>this.executeWithLogging("WorkTimeService","getDefaultConfiguration",{},()=>this.workTimeService.getDefaultConfiguration())}}get productionOrders(){return{getAll:e=>this.executeWithLogging("ProductionOrderService","getAll",e,()=>this.productionOrderService.getAll(e)),getById:e=>this.executeWithLogging("ProductionOrderService","getById",{id:e},()=>this.productionOrderService.getById(e)),getByOrderNumber:e=>this.executeWithLogging("ProductionOrderService","getByOrderNumber",{orderNumber:e},()=>this.productionOrderService.getByOrderNumber(e)),createFromMRP:e=>this.executeWithLogging("ProductionOrderService","createFromMRP",e,()=>this.productionOrderService.createFromMRP(e)),create:e=>this.executeWithLogging("ProductionOrderService","createFromMRP",e,()=>{let t={...e,mrpExecutionId:e.mrpExecutionId||`mrp_${Date.now()}`,mrpExecutedBy:e.mrpExecutedBy||"test-user",mrpExecutedAt:e.mrpExecutedAt||new Date().toISOString()};return this.productionOrderService.createFromMRP(t)}),update:(e,t)=>this.executeWithLogging("ProductionOrderService","update",{id:e,...t},()=>this.productionOrderService.update(e,t)),delete:e=>this.executeWithLogging("ProductionOrderService","delete",{id:e},()=>this.productionOrderService.delete(e)),getByStatus:e=>this.executeWithLogging("ProductionOrderService","getByStatus",{status:e},()=>this.productionOrderService.getByStatus(e)),getBySalesOrderId:e=>this.executeWithLogging("ProductionOrderService","getBySalesOrderId",{salesOrderId:e},()=>this.productionOrderService.getBySalesOrderId(e)),getStatistics:()=>this.executeWithLogging("ProductionOrderService","getStatistics",{},()=>this.productionOrderService.getStatistics())}}get productionWorkOrders(){return{getAll:e=>this.executeWithLogging("ProductionWorkOrderService","getAll",e,()=>this.productionWorkOrderService.getAll(e)),getById:e=>this.executeWithLogging("ProductionWorkOrderService","getById",{id:e},()=>this.productionWorkOrderService.getById(e)),getByBatchNumber:e=>this.executeWithLogging("ProductionWorkOrderService","getByBatchNumber",{batchNumber:e},()=>this.productionWorkOrderService.getByBatchNumber(e)),create:e=>this.executeWithLogging("ProductionWorkOrderService","create",e,()=>this.productionWorkOrderService.create(e)),update:(e,t)=>this.executeWithLogging("ProductionWorkOrderService","update",{id:e,...t},()=>this.productionWorkOrderService.update(e,t)),delete:e=>this.executeWithLogging("ProductionWorkOrderService","delete",{id:e},()=>this.productionWorkOrderService.delete(e)),getByStatus:e=>this.executeWithLogging("ProductionWorkOrderService","getByStatus",{status:e},()=>this.productionWorkOrderService.getByStatus(e)),getBySourceOrderId:e=>this.executeWithLogging("ProductionWorkOrderService","getBySourceOrderId",{sourceOrderId:e},()=>this.productionWorkOrderService.getBySourceOrderId(e)),getByWorkstation:e=>this.executeWithLogging("ProductionWorkOrderService","getByWorkstation",{workstation:e},()=>this.productionWorkOrderService.getByWorkstation(e))}}get workstations(){return{getAll:e=>this.executeWithLogging("WorkstationService","getAll",e,()=>this.workstationService.getAll()),getWorkstations:e=>this.executeWithLogging("WorkstationService","getWorkstations",e,()=>this.workstationService.getWorkstations(e)),getById:e=>this.executeWithLogging("WorkstationService","getWorkstationById",{id:e},()=>this.workstationService.getWorkstationById(e)),getActiveWorkstations:()=>this.executeWithLogging("WorkstationService","getActiveWorkstations",{},()=>this.workstationService.getActiveWorkstations()),create:e=>this.executeWithLogging("WorkstationService","create",e,()=>this.workstationService.create(e)),update:(e,t)=>this.executeWithLogging("WorkstationService","update",{id:e,...t},()=>this.workstationService.update(e,t)),delete:e=>this.executeWithLogging("WorkstationService","delete",{id:e},()=>this.workstationService.delete(e)),getStatus:e=>this.executeWithLogging("WorkstationService","getWorkstationStatus",{id:e},()=>this.workstationService.getWorkstationStatus(e)),updateStatus:(e,t)=>this.executeWithLogging("WorkstationService","updateWorkstationStatus",{id:e,...t},()=>this.workstationService.updateWorkstationStatus(e,t)),addToQueue:(e,t)=>this.executeWithLogging("WorkstationService","addToQueue",{workstationId:e,batchNumber:t},()=>this.workstationService.addToQueue(e,t)),migrateBatchNumberFormats:()=>this.executeWithLogging("WorkstationService","migrateBatchNumberFormats",{},()=>this.workstationService.migrateBatchNumberFormats()),resetAllWorkstationsToIdle:()=>this.executeWithLogging("WorkstationService","resetAllWorkstationsToIdle",{},()=>this.workstationService.resetAllWorkstationsToIdle())}}getAccessLogs(e){return e?this.accessLogs.slice(-e):[...this.accessLogs]}clearAccessLogs(){this.accessLogs=[]}getStatistics(){let e=this.accessLogs.length,t=this.accessLogs.filter(e=>e.success).length,r=e>0?this.accessLogs.reduce((e,t)=>e+t.duration,0)/e:0;return{totalRequests:e,successfulRequests:t,failedRequests:e-t,successRate:e>0?t/e*100:0,averageDuration:Math.round(r),config:this.config}}initializeDataChangeNotification(){}getDataChangeNotifier(){return T.dataChangeNotifier}get costCalculations(){return{getAllCalculations:e=>this.executeWithLogging("CostCalculationService","getAllCalculations",e,()=>this.costCalculationService.getAllCalculations(e)),getCalculationById:e=>this.executeWithLogging("CostCalculationService","getCalculationById",{id:e},()=>this.costCalculationService.getCalculationById(e)),createCalculation:e=>this.executeWithLogging("CostCalculationService","createCalculation",e,()=>this.costCalculationService.createCalculation(e)),updateCalculation:(e,t)=>this.executeWithLogging("CostCalculationService","updateCalculation",{id:e,...t},()=>this.costCalculationService.updateCalculation(e,t)),deleteCalculation:e=>this.executeWithLogging("CostCalculationService","deleteCalculation",{id:e},()=>this.costCalculationService.deleteCalculation(e)),getCalculationsByProduct:e=>this.executeWithLogging("CostCalculationService","getCalculationsByProduct",{productModelCode:e},()=>this.costCalculationService.getCalculationsByProduct(e)),getPendingReconciliations:()=>this.executeWithLogging("CostCalculationService","getPendingReconciliations",{},()=>this.costCalculationService.getPendingReconciliations()),getStatistics:()=>this.executeWithLogging("CostCalculationService","getStatistics",{},()=>this.costCalculationService.getStatistics()),getCostSummary:()=>this.executeWithLogging("CostCalculationService","getCostSummary",{},()=>this.costCalculationService.getCostSummary())}}updateConfig(e){this.config={...this.config,...e},!1===e.enableCaching&&(this.cache.clear(),this.requestCache.clear(),this.config.enableLogging&&console.log("[DataAccessManager] 缓存已禁用并清空")),this.config.enableLogging&&console.log("[DataAccessManager] 配置已更新:",this.config)}clearServiceCache(e,t){if(!this.config.enableCaching)return this.config.enableLogging&&console.log("[DataAccessManager] 缓存未启用，无需清理"),0;let r=0,s=`${e}:`;for(let[e]of Array.from(this.cache))e.startsWith(s)&&(this.cache.delete(e),r++);return this.config.enableLogging&&console.log(`[DataAccessManager] 清除服务缓存: ${e}${t?` (${t})`:""}，删除了 ${r} 个缓存项`),r}clearDataTypeCache(e,t){if(!this.config.enableCaching)return this.config.enableLogging&&console.log("[DataAccessManager] 缓存未启用，无需清理"),0;let r=0,s={products:"ProductService",orders:"OrderService",workstations:"WorkstationService",statistics:"StatisticsService"}[e]||e;for(let[e]of Array.from(this.cache))e.startsWith(`${s}:`)&&(t&&t.length>0?t.some(t=>e.includes(t))&&(this.cache.delete(e),r++):(this.cache.delete(e),r++));return this.config.enableLogging&&console.log(`[DataAccessManager] 清除数据类型缓存: ${e}，影响ID: ${t?.join(", ")||"全部"}，删除了 ${r} 个缓存项`),r}clearAllCache(){if(!this.config.enableCaching){this.config.enableLogging&&console.log("[DataAccessManager] 缓存未启用，无需清理");return}let e=this.cache.size;this.cache.clear(),this.requestCache.clear(),this.config.enableLogging&&console.log(`[DataAccessManager] 清除所有缓存，删除了 ${e} 个缓存项`)}getCacheStatistics(){let e=Array.from(this.cache.values()),t=e.reduce((e,t)=>e+t.estimatedSize,0),r=e.length>0?t/e.length:0,s={critical:0,high:0,medium:0,low:0};return e.forEach(e=>{s[e.priority]++}),{enabled:this.config.enableCaching,size:this.cache.size,hits:this.cacheStats.hits,misses:this.cacheStats.misses,hitRate:this.cacheStats.hits/(this.cacheStats.hits+this.cacheStats.misses)||0,totalRequests:this.cacheStats.totalRequests,evictions:this.cacheStats.evictions,prewarmHits:this.cacheStats.prewarmHits,memoryPressure:this.memoryPressure,averageEntrySize:r,priorityDistribution:s}}updateCacheConfig(e){this.cacheConfig={...this.cacheConfig,...e},console.log("[DataAccessManager] 缓存配置已更新")}get batch(){return{createProductionOrders:e=>A.batchCreateProductionOrders(e),updateProductionOrders:e=>A.batchUpdateProductionOrders(e),createProductionWorkOrders:async e=>{let t=performance.now();try{let r=e.map(e=>async()=>{let t=await this.productionWorkOrders.create(e);if("success"!==t.status)throw Error(t.message||"创建工单失败");return t.data}),s=e.map((e,t)=>`批量创建工单-${t+1}`),a=await E.AO.executeBatch(r,s),i=performance.now()-t,{successful:o,failed:n}=a,c=o.map(e=>e.data).filter(Boolean);return B.recordRequest("batch.createProductionWorkOrders",i,0===n.length),{status:"success",data:c,message:`成功批量创建${c.length}个生产工单${n.length>0?`，${n.length}个失败`:""}`,batchResult:{successful:o.length,failed:n.length,successRate:a.successRate,totalDuration:i}}}catch(r){let e=performance.now()-t;throw B.recordRequest("batch.createProductionWorkOrders",e,!1),r}},updateWorkstationStatuses:async e=>{let t=performance.now();try{let r=await Promise.all(e.map(({id:e,status:t})=>this.workstations.updateStatus(e,t))),s=performance.now()-t;return B.recordRequest("batch.updateWorkstationStatuses",s,!0),this.clearDataTypeCache("workstations",e.map(e=>e.id)),{status:"success",data:r.filter(e=>"success"===e.status).map(e=>e.data),message:`成功批量更新${r.length}个工位状态`}}catch(r){let e=performance.now()-t;throw B.recordRequest("batch.updateWorkstationStatuses",e,!1),r}}}}get performance(){return{getMetrics:()=>B.getMetrics(),getAlerts:()=>B.getAlerts(),getSuggestions:()=>B.getOptimizationSuggestions(),generateReport:()=>B.generatePerformanceReport(),reset:()=>B.reset(),optimizeCache:()=>{this.evictOldestEntries(),console.log("[DataAccessManager] 缓存优化完成")},getCacheMetrics:()=>this.getCacheStatistics(),getOptimizerMetrics:()=>A.getPerformanceMetrics()}}getPerformanceMetrics(){return this.performanceMetrics.methodStats=this.methodStats,{...this.performanceMetrics}}getRealTimeMetrics(){let e=E.AO.getStatus(),t=this.analyzeSystemHealth();return{currentConcurrency:e.running,queueLength:e.queued,recentCalls:[...this.recentCalls.slice(-50)],systemHealth:t}}analyzeSystemHealth(){let e=[],t=[],r=this.recentCalls.length>0?this.recentCalls.filter(e=>"error"===e.status).length/this.recentCalls.length:0;r>.1&&(e.push(`错误率过高: ${(100*r).toFixed(2)}%`),t.push("检查网络连接和服务状态")),this.performanceMetrics.averageResponseTime>2e3&&(e.push(`平均响应时间过长: ${this.performanceMetrics.averageResponseTime.toFixed(0)}ms`),t.push("考虑优化查询或增加缓存")),this.performanceMetrics.cacheHitRate<.3&&(e.push(`缓存命中率较低: ${(100*this.performanceMetrics.cacheHitRate).toFixed(2)}%`),t.push("检查缓存策略和TTL设置"));let s=E.AO.getStatus();s.queued>10&&(e.push(`批量操作队列积压: ${s.queued}个任务`),t.push("考虑增加并发数或优化任务处理"));let a="healthy";return e.length>0&&(a=r>.2||this.performanceMetrics.averageResponseTime>5e3?"critical":"warning"),{status:a,issues:e,recommendations:t}}resetPerformanceMetrics(){this.initializePerformanceMonitoring(),console.log("[DataAccessManager] 性能指标已重置")}getSlowQueries(e=20){return this.performanceMetrics.slowQueries.sort((e,t)=>t.duration-e.duration).slice(0,e)}getMethodStatistics(){return Array.from(this.methodStats.entries()).map(([e,t])=>({method:e,calls:t.calls,averageTime:t.averageTime,successRate:t.successRate,lastCall:new Date(t.lastCall).toLocaleString()})).sort((e,t)=>t.calls-e.calls)}exportPerformanceReport(){return{timestamp:new Date().toISOString(),summary:this.getPerformanceMetrics(),realTime:this.getRealTimeMetrics(),topMethods:this.getMethodStatistics().slice(0,10),slowQueries:this.getSlowQueries(10)}}get batch(){return{createProductionOrders:async e=>{let t=[],r=[];for(let s of e)try{let e=await this.productionOrders.create(s);"success"===e.status?t.push(e.data):r.push(e.message||"创建失败")}catch(e){r.push(e instanceof Error?e.message:"未知错误")}return 0===r.length?{status:"success",data:t,message:`成功创建${t.length}个生产订单`}:{status:"error",data:t,message:`创建过程中发生错误: ${r.join(", ")}`,errors:r}},updateProductionOrders:async e=>{let t=[],r=[];for(let{id:s,data:a}of e)try{let e=await this.productionOrders.update(s,a);"success"===e.status?t.push(e.data):r.push(`ID ${s}: ${e.message||"更新失败"}`)}catch(e){r.push(`ID ${s}: ${e instanceof Error?e.message:"未知错误"}`)}return 0===r.length?{status:"success",data:t,message:`成功更新${t.length}个生产订单`}:{status:"error",data:t,message:`更新过程中发生错误: ${r.join(", ")}`,errors:r}},deleteProductionOrders:async e=>{let t=[],r=[];for(let s of e)try{let e=await this.productionOrders.delete(s);"success"===e.status?t.push(s):r.push(`ID ${s}: ${e.message||"删除失败"}`)}catch(e){r.push(`ID ${s}: ${e instanceof Error?e.message:"未知错误"}`)}return 0===r.length?{status:"success",data:!0,message:`成功删除${t.length}个生产订单`}:{status:"error",data:!1,message:`删除过程中发生错误: ${r.join(", ")}`,errors:r}},getProductionOrders:async e=>{let t=[],r=[];for(let s of e)try{let e=await this.productionOrders.getById(s);"success"===e.status?t.push(e.data):r.push(`ID ${s}: ${e.message||"查询失败"}`)}catch(e){r.push(`ID ${s}: ${e instanceof Error?e.message:"未知错误"}`)}return 0===r.length?{status:"success",data:t,message:`成功查询${t.length}个生产订单`}:{status:"error",data:t,message:`查询过程中发生错误: ${r.join(", ")}`,errors:r}}}}getConfig(){return{...this.systemConfig}}updateConfig(e){this.systemConfig={...this.systemConfig,...e},console.log("[DataAccessManager] 系统配置已更新:",e),void 0!==e.enableCaching&&console.log(`[DataAccessManager] 缓存状态: ${e.enableCaching?"已启用":"已禁用"}`),void 0!==e.cacheTimeout&&console.log(`[DataAccessManager] 缓存超时时间: ${e.cacheTimeout}ms`),void 0!==e.enableLogging&&console.log(`[DataAccessManager] 日志状态: ${e.enableLogging?"已启用":"已禁用"}`),void 0!==e.logLevel&&console.log(`[DataAccessManager] 日志级别: ${e.logLevel}`)}resetConfig(){this.systemConfig={enableCaching:!0,cacheTimeout:3e5,enableLogging:!0,logLevel:"INFO",enablePerformanceMonitoring:!0,maxConcurrentRequests:10,retryAttempts:3,retryDelay:1e3},console.log("[DataAccessManager] 系统配置已重置")}}let L=b.getInstance(),{products:x,customers:M,employees:$,inventory:W,productionOrders:U,productionWorkOrders:V,workstations:Q,costCalculations:F}=L},43471:(e,t,r)=>{"use strict";r.r(t),r.d(t,{dataChangeNotifier:()=>p});var s,a,i,o,n=r(82361);!function(e){e.NETWORK_ERROR="network_error",e.TIMEOUT_ERROR="timeout_error",e.AUTH_ERROR="auth_error",e.DATA_ERROR="data_error",e.UNKNOWN_ERROR="unknown_error"}(s||(s={}));class c extends n.EventEmitter{constructor(e){super(),this.errorHistory=[],this.retryTimers=new Map,this.maxHistorySize=100,this.config={enableLogging:!0,enableNotification:!0,autoRetry:!0,retryConfig:{maxRetries:3,baseDelay:1e3,maxDelay:3e4,backoffFactor:2,retryableErrors:["network_error","timeout_error","unknown_error"]},...e}}handleError(e,t,r){let s=this.createSyncError(e,t);return this.recordError(s),this.emit("error",s),this.config.autoRetry&&s.canRetry&&r&&this.scheduleRetry(t,s,r),s}createSyncError(e,t){let r=this.classifyError(e),s=this.findExistingError(t),a=s?s.retryCount+1:0;return{type:r,message:this.extractErrorMessage(e),code:e?.code||e?.status?.toString(),timestamp:Date.now(),retryCount:a,maxRetries:this.config.retryConfig.maxRetries,canRetry:this.canRetry(r,a),originalError:e}}classifyError(e){return e?"NetworkError"===e.name||e.message?.includes("fetch")||e.message?.includes("network")?"network_error":"TimeoutError"===e.name||e.message?.includes("timeout")?"timeout_error":401===e.status||403===e.status?"auth_error":e.status>=400&&e.status<500?"data_error":"unknown_error":"unknown_error"}extractErrorMessage(e){return"string"==typeof e?e:e?.message?e.message:e?.statusText?e.statusText:"未知错误"}findExistingError(e){return this.errorHistory.filter(t=>t.message.includes(e)).sort((e,t)=>t.timestamp-e.timestamp)[0]}canRetry(e,t){return this.config.retryConfig.retryableErrors.includes(e)&&t<this.config.retryConfig.maxRetries}recordError(e){this.errorHistory.push(e),this.errorHistory.length>this.maxHistorySize&&(this.errorHistory=this.errorHistory.slice(-this.maxHistorySize)),this.config.enableLogging&&console.log({type:e.type,message:e.message,retryCount:e.retryCount,timestamp:new Date(e.timestamp).toISOString()})}scheduleRetry(e,t,r){this.retryTimers.has(e)&&clearTimeout(this.retryTimers.get(e));let s=setTimeout(async()=>{try{await r(),this.emit("retrySuccess",{context:e,retryCount:t.retryCount+1})}catch(t){this.handleError(t,e,r)}finally{this.retryTimers.delete(e)}},Math.min(this.config.retryConfig.baseDelay*Math.pow(this.config.retryConfig.backoffFactor,t.retryCount),this.config.retryConfig.maxDelay));this.retryTimers.set(e,s)}async manualRetry(e,t){try{await t(),this.emit("manualRetrySuccess",{context:e})}catch(r){throw this.handleError(r,e,t),r}}getErrorHistory(){return[...this.errorHistory]}getRecentErrors(e=10){return this.errorHistory.sort((e,t)=>t.timestamp-e.timestamp).slice(0,e)}clearErrorHistory(){this.errorHistory=[]}getErrorStats(){let e=Date.now()-36e5,t=this.errorHistory.filter(t=>t.timestamp>e),r=this.errorHistory.reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{});return{totalErrors:this.errorHistory.length,errorsByType:r,recentErrorRate:t.length}}cleanup(){this.retryTimers.forEach(e=>clearTimeout(e)),this.retryTimers.clear(),this.removeAllListeners()}}let l=new c;!function(e){e.ONLINE="online",e.OFFLINE="offline",e.UNKNOWN="unknown"}(a||(a={}));class d extends n.EventEmitter{constructor(e){super(),this.cache=new Map,this.offlineStatus="unknown",this.statusCheckInterval=null,this.config={maxSize:1e3,defaultTTL:864e5,enableCompression:!1,enableChecksum:!0,storageKey:"erp_offline_cache",...e},this.initializeOfflineDetection(),this.loadFromStorage()}initializeOfflineDetection(){}updateOfflineStatus(e){if(this.offlineStatus!==e){let t=this.offlineStatus;this.offlineStatus=e,this.emit("statusChanged",{oldStatus:t,newStatus:e}),"online"===e&&"offline"===t?this.emit("backOnline"):"offline"===e&&"online"===t&&this.emit("wentOffline")}}async checkNetworkStatus(){try{let e=await fetch("/",{method:"HEAD",cache:"no-cache",signal:AbortSignal.timeout(5e3)});this.updateOfflineStatus(e.ok?"online":"offline")}catch(e){this.updateOfflineStatus("offline")}}getOfflineStatus(){return this.offlineStatus}isOnline(){return"online"===this.offlineStatus}isOffline(){return"offline"===this.offlineStatus}set(e,t,r){let s=Date.now(),a=s+(r||this.config.defaultTTL),i={key:e,data:t,timestamp:s,expiresAt:a,version:"1.0",checksum:this.config.enableChecksum?this.generateChecksum(t):void 0};this.cache.size>=this.config.maxSize&&this.evictOldestItems(),this.cache.set(e,i),this.saveToStorage(),this.emit("itemCached",{key:e,size:this.cache.size})}get(e){let t=this.cache.get(e);return t?Date.now()>t.expiresAt||this.config.enableChecksum&&t.checksum&&this.generateChecksum(t.data)!==t.checksum?(this.cache.delete(e),this.saveToStorage(),null):t.data:null}delete(e){let t=this.cache.delete(e);return t&&(this.saveToStorage(),this.emit("itemRemoved",{key:e,size:this.cache.size})),t}has(e){let t=this.cache.get(e);return!!t&&(!(Date.now()>t.expiresAt)||(this.cache.delete(e),this.saveToStorage(),!1))}clear(){this.cache.clear(),this.saveToStorage(),this.emit("cacheCleared")}clearExpired(){let e=Date.now(),t=0;for(let[r,s]of this.cache.entries())e>s.expiresAt&&(this.cache.delete(r),t++);return t>0&&(this.saveToStorage(),this.emit("expiredItemsCleared",{count:t})),t}getStats(){let e=Array.from(this.cache.values()).map(e=>e.timestamp);return{size:this.cache.size,maxSize:this.config.maxSize,hitRate:0,oldestItem:e.length>0?Math.min(...e):null,newestItem:e.length>0?Math.max(...e):null}}evictOldestItems(){let e=Array.from(this.cache.entries()).sort(([,e],[,t])=>e.timestamp-t.timestamp),t=Math.ceil(.1*e.length);for(let r=0;r<t;r++)this.cache.delete(e[r][0])}generateChecksum(e){let t=JSON.stringify(e),r=0;for(let e=0;e<t.length;e++)r=(r<<5)-r+t.charCodeAt(e),r&=r;return r.toString(16)}saveToStorage(){}loadFromStorage(){}destroy(){this.statusCheckInterval&&(clearInterval(this.statusCheckInterval),this.statusCheckInterval=null),this.removeAllListeners(),this.clear()}}let u=new d;(function(e){e.PRODUCT_UPDATED="product_updated",e.PRODUCT_DELETED="product_deleted",e.CUSTOMER_UPDATED="customer_updated",e.CUSTOMER_DELETED="customer_deleted",e.EMPLOYEE_UPDATED="employee_updated",e.EMPLOYEE_DELETED="employee_deleted",e.ORDER_CREATED="order_created",e.ORDER_UPDATED="order_updated",e.ORDER_DELETED="order_deleted",e.PRODUCTION_ORDER_CREATED="production_order_created",e.PRODUCTION_ORDER_UPDATED="production_order_updated",e.PRODUCTION_ORDER_DELETED="production_order_deleted",e.PRODUCTION_WORK_ORDER_CREATED="production_work_order_created",e.PRODUCTION_WORK_ORDER_UPDATED="production_work_order_updated",e.PRODUCTION_WORK_ORDER_DELETED="production_work_order_deleted"})(i||(i={})),function(e){e.DISCONNECTED="disconnected",e.CONNECTING="connecting",e.CONNECTED="connected",e.SYNCING="syncing",e.ERROR="error"}(o||(o={}));class h extends n.EventEmitter{constructor(){super(),this.ws=null,this.status="disconnected",this.reconnectAttempts=0,this.heartbeatTimer=null,this.debounceTimers=new Map,this.pendingEvents=[],this.isOnline=!0,this.config={reconnectInterval:3e3,maxReconnectAttempts:5,heartbeatInterval:3e4,debounceDelay:500},this.setupOnlineStatusDetection(),this.setupErrorHandling(),this.setupOfflineCache()}async initialize(){if("connected"!==this.status&&"connecting"!==this.status){this.setStatus("connecting");try{await this.connect()}catch(e){this.setStatus("error"),this.scheduleReconnect()}}}async connect(){return new Promise((e,t)=>{try{this.simulateConnection(),e()}catch(e){t(e)}})}simulateConnection(){setTimeout(()=>{this.setStatus("connected"),this.reconnectAttempts=0,this.startHeartbeat(),this.processPendingEvents(),this.emit("connected")},1e3)}setStatus(e){if(this.status!==e){let t=this.status;this.status=e,this.emit("statusChanged",{oldStatus:t,newStatus:e})}}getStatus(){return this.status}publishDataChange(e){let t={...e,timestamp:Date.now()};if(!this.isOnline||"connected"!==this.status){this.pendingEvents.push(t);let e=`pending_event_${t.timestamp}_${Math.random()}`;u.set(e,t,864e5);return}this.debounceEvent(t)}debounceEvent(e){let t=`${e.entityType}_${e.entityId}`;this.debounceTimers.has(t)&&clearTimeout(this.debounceTimers.get(t));let r=setTimeout(()=>{this.processEvent(e),this.debounceTimers.delete(t)},this.config.debounceDelay);this.debounceTimers.set(t,r)}processEvent(e){try{switch(this.setStatus("syncing"),this.emit("dataChange",e),e.type){case"product_updated":this.emit("productUpdated",e);break;case"customer_updated":this.emit("customerUpdated",e);break;case"employee_updated":this.emit("employeeUpdated",e);break;case"order_created":this.emit("orderCreated",e);break;case"order_updated":this.emit("orderUpdated",e);break;case"order_deleted":this.emit("orderDeleted",e);break;case"production_order_created":this.emit("productionOrderCreated",e);break;case"production_order_updated":this.emit("productionOrderUpdated",e);break;case"production_order_deleted":this.emit("productionOrderDeleted",e)}setTimeout(()=>{"syncing"===this.status&&this.setStatus("connected")},100)}catch(t){l.handleError(t,`处理数据变更事件: ${e.type}`,async()=>this.processEvent(e))}}processPendingEvents(){if(0===this.pendingEvents.length)return;let e=[...this.pendingEvents];this.pendingEvents=[],e.forEach(e=>{this.debounceEvent(e)})}startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>{"connected"===this.status&&this.emit("heartbeat")},this.config.heartbeatInterval)}stopHeartbeat(){this.heartbeatTimer&&(clearInterval(this.heartbeatTimer),this.heartbeatTimer=null)}scheduleReconnect(){if(this.reconnectAttempts>=this.config.maxReconnectAttempts){this.setStatus("error");return}this.reconnectAttempts++,setTimeout(()=>{this.isOnline&&this.initialize()},this.config.reconnectInterval)}setupOnlineStatusDetection(){}setupErrorHandling(){l.on("error",e=>{this.setStatus("error"),this.emit("syncError",e)}),l.on("retrySuccess",({context:e})=>{"error"===this.status&&this.setStatus("connected")})}setupOfflineCache(){u.on("statusChanged",({newStatus:e})=>{e===a.ONLINE&&"disconnected"===this.status?this.initialize():e===a.OFFLINE&&this.setStatus("disconnected")}),u.on("backOnline",()=>{this.processPendingEvents()})}async manualRefresh(){this.emit("manualRefresh"),this.publishDataChange({type:"product_updated",entityId:"all",entityType:"product",data:{refreshAll:!0},source:"manual"}),this.publishDataChange({type:"customer_updated",entityId:"all",entityType:"customer",data:{refreshAll:!0},source:"manual"}),this.publishDataChange({type:"employee_updated",entityId:"all",entityType:"employee",data:{refreshAll:!0},source:"manual"})}destroy(){this.stopHeartbeat(),this.debounceTimers.forEach(e=>clearTimeout(e)),this.debounceTimers.clear(),this.ws&&(this.ws.close(),this.ws=null),this.setStatus("disconnected"),this.removeAllListeners()}}let g=new h;class m{constructor(){this.listeners=new Map,this.listenerMetrics=new Map,this.autoCleanupInterval=null,this.setupRealtimeListeners(),this.startAutoCleanup()}setupRealtimeListeners(){g.on("productUpdated",e=>{this.notifyListeners("onProductUpdated",e.data)}),g.on("customerUpdated",e=>{this.notifyListeners("onCustomerUpdated",e.data)}),g.on("employeeUpdated",e=>{this.notifyListeners("onEmployeeUpdated",e.data)}),g.on("productionOrderCreated",e=>{this.notifyListeners("onProductionOrderCreated",e.data)}),g.on("productionOrderUpdated",e=>{this.notifyListeners("onProductionOrderUpdated",e.data)}),g.on("productionOrderDeleted",e=>{this.notifyListeners("onProductionOrderDeleted",e.entityId)}),g.on("productionWorkOrderCreated",e=>{this.notifyListeners("onProductionWorkOrderCreated",e.data)}),g.on("productionWorkOrderUpdated",e=>{this.notifyListeners("onProductionWorkOrderUpdated",e.data)}),g.on("productionWorkOrderDeleted",e=>{this.notifyListeners("onProductionWorkOrderDeleted",e.entityId)}),g.on("dataChange",e=>{this.handleDataChange(e)})}handleDataChange(e){let{type:t,entityId:r,data:s}=e;switch(t){case i.PRODUCT_UPDATED:"all"===r&&s.refreshAll?this.notifyListeners("onProductUpdated",{refreshAll:!0}):this.notifyListeners("onProductUpdated",s);break;case i.PRODUCT_DELETED:this.notifyListeners("onProductDeleted",r);break;case i.CUSTOMER_UPDATED:"all"===r&&s.refreshAll?this.notifyListeners("onCustomerUpdated",{refreshAll:!0}):this.notifyListeners("onCustomerUpdated",s);break;case i.CUSTOMER_DELETED:this.notifyListeners("onCustomerDeleted",r);break;case i.EMPLOYEE_UPDATED:"all"===r&&s.refreshAll?this.notifyListeners("onEmployeeUpdated",{refreshAll:!0}):this.notifyListeners("onEmployeeUpdated",s);break;case i.EMPLOYEE_DELETED:this.notifyListeners("onEmployeeDeleted",r);break;case i.ORDER_CREATED:this.notifyListeners("onOrderCreated",s);break;case i.ORDER_UPDATED:this.notifyListeners("onOrderUpdated",s);break;case i.ORDER_DELETED:this.notifyListeners("onOrderDeleted",r);break;case i.PRODUCTION_ORDER_CREATED:this.notifyListeners("onProductionOrderCreated",s);break;case i.PRODUCTION_ORDER_UPDATED:this.notifyListeners("onProductionOrderUpdated",s);break;case i.PRODUCTION_ORDER_DELETED:this.notifyListeners("onProductionOrderDeleted",r);break;case i.PRODUCTION_WORK_ORDER_CREATED:this.notifyListeners("onProductionWorkOrderCreated",s);break;case i.PRODUCTION_WORK_ORDER_UPDATED:this.notifyListeners("onProductionWorkOrderUpdated",s);break;case i.PRODUCTION_WORK_ORDER_DELETED:this.notifyListeners("onProductionWorkOrderDeleted",r)}}notifyListeners(e,t){this.listeners.forEach((r,s)=>{try{let a=r[e];if("function"==typeof a){a(t);let e=this.listenerMetrics.get(s);e&&(e.lastActivity=Date.now(),e.callCount++)}}catch(e){console.error(`[DataChangeNotifier] 监听器 ${s} 执行失败:`,e)}})}registerListener(e,t,r){this.listeners.set(e,t),this.listenerMetrics.set(e,{registeredAt:Date.now(),lastActivity:Date.now(),callCount:0,componentName:r}),console.log(`[DataChangeNotifier] 注册监听器: ${e}${r?` (${r})`:""}`)}unregisterListener(e){let t=this.listenerMetrics.get(e);if(this.listeners.delete(e),this.listenerMetrics.delete(e),t){let r=Date.now()-t.registeredAt;console.log(`[DataChangeNotifier] 注销监听器: ${e}, 存活时间: ${r}ms, 调用次数: ${t.callCount}`)}}notifyProductUpdated(e,t="system"){g.publishDataChange({type:i.PRODUCT_UPDATED,entityId:e.id,entityType:"product",data:e,source:t})}notifyProductDeleted(e,t="system"){g.publishDataChange({type:i.PRODUCT_DELETED,entityId:e,entityType:"product",data:{deleted:!0},source:t})}notifyCustomerUpdated(e,t="system"){g.publishDataChange({type:i.CUSTOMER_UPDATED,entityId:e.id,entityType:"customer",data:e,source:t})}notifyCustomerDeleted(e,t="system"){g.publishDataChange({type:i.CUSTOMER_DELETED,entityId:e,entityType:"customer",data:{deleted:!0},source:t})}notifyEmployeeUpdated(e,t="system"){g.publishDataChange({type:i.EMPLOYEE_UPDATED,entityId:e.id,entityType:"employee",data:e,source:t})}notifyEmployeeDeleted(e,t="system"){g.publishDataChange({type:i.EMPLOYEE_DELETED,entityId:e,entityType:"employee",data:{deleted:!0},source:t})}notifyOrderCreated(e,t="system"){this.invalidateSalesOrderCaches("created",e),g.publishDataChange({type:i.ORDER_CREATED,entityId:e.id,entityType:"order",data:e,source:t})}notifyOrderUpdated(e,t="system"){this.invalidateSalesOrderCaches("updated",e),g.publishDataChange({type:i.ORDER_UPDATED,entityId:e.id,entityType:"order",data:e,source:t})}notifyOrderDeleted(e,t="system"){this.invalidateSalesOrderCaches("deleted",{id:e}),g.publishDataChange({type:i.ORDER_DELETED,entityId:e,entityType:"order",data:{deleted:!0},source:t})}notifyProductionOrderCreated(e,t="system"){this.invalidateProductionOrderCaches("created",e),g.publishDataChange({type:i.PRODUCTION_ORDER_CREATED,entityId:e.id,entityType:"production_order",data:e,source:t,affectedModules:["production","scheduling"]})}notifyProductionOrderUpdated(e,t="system"){this.invalidateProductionOrderCaches("updated",e),g.publishDataChange({type:i.PRODUCTION_ORDER_UPDATED,entityId:e.id,entityType:"production_order",data:e,source:t,affectedModules:["production","scheduling"]})}notifyProductionOrderDeleted(e,t="system"){this.invalidateProductionOrderCaches("deleted",{id:e}),g.publishDataChange({type:i.PRODUCTION_ORDER_DELETED,entityId:e,entityType:"production_order",data:{deleted:!0},source:t,affectedModules:["production","scheduling"]})}invalidateProductionOrderCaches(e,t){try{let{dataAccessManager:s}=r(37637);s.clearDataTypeCache("orders",t.id?[t.id]:[]),s.clearServiceCache("StatisticsService"),console.log(`[DataChangeNotifier] 生产订单缓存清理完成: ${e}, ID: ${t.id}`)}catch(e){console.error("[DataChangeNotifier] 生产订单缓存清理失败:",e)}}notifyProductionWorkOrderCreated(e,t="system"){this.invalidateProductionWorkOrderCaches("created",e),g.publishDataChange({type:i.PRODUCTION_WORK_ORDER_CREATED,entityId:e.id,entityType:"production_work_order",data:e,source:t,affectedModules:["production","scheduling","workstation"]})}notifyProductionWorkOrderUpdated(e,t="system"){this.invalidateProductionWorkOrderCaches("updated",e),g.publishDataChange({type:i.PRODUCTION_WORK_ORDER_UPDATED,entityId:e.id,entityType:"production_work_order",data:e,source:t,affectedModules:["production","scheduling","workstation"]})}notifyProductionWorkOrderDeleted(e,t="system"){this.invalidateProductionWorkOrderCaches("deleted",{id:e}),g.publishDataChange({type:i.PRODUCTION_WORK_ORDER_DELETED,entityId:e,entityType:"production_work_order",data:{deleted:!0},source:t,affectedModules:["production","scheduling","workstation"]})}invalidateProductionWorkOrderCaches(e,t){try{let{dataAccessManager:s}=r(37637);s.clearDataTypeCache("workOrders",t.id?[t.id]:[]),s.clearServiceCache("StatisticsService"),console.log(`[DataChangeNotifier] 工单缓存清理完成: ${e}, ID: ${t.id}`)}catch(e){console.error("[DataChangeNotifier] 工单缓存清理失败:",e)}}invalidateSalesOrderCaches(e,t){try{let{dataAccessManager:s}=r(37637);s.clearDataTypeCache("orders",t.id?[t.id]:[]),s.clearServiceCache("StatisticsService"),console.log(`[DataChangeNotifier] 销售订单缓存清理完成: ${e}, ID: ${t.id}`)}catch(e){console.error("[DataChangeNotifier] 销售订单缓存清理失败:",e)}}getListenerCount(){return this.listeners.size}clearAllListeners(){console.log(`[DataChangeNotifier] 清除所有监听器，共 ${this.listeners.size} 个`),this.listeners.clear(),this.listenerMetrics.clear()}startAutoCleanup(){this.autoCleanupInterval=setInterval(()=>{this.performAutoCleanup()},3e5)}performAutoCleanup(){let e=this.detectLeakedListeners();e.length>0&&(console.warn(`[DataChangeNotifier] 检测到 ${e.length} 个可能泄漏的监听器`),e.forEach(e=>{let t=this.listenerMetrics.get(e);console.warn(`[DataChangeNotifier] 清理可能泄漏的监听器: ${e}`,{registeredAt:t?.registeredAt,lastActivity:t?.lastActivity,callCount:t?.callCount,componentName:t?.componentName}),this.unregisterListener(e)}))}detectLeakedListeners(){let e=Date.now(),t=[];return this.listenerMetrics.forEach((r,s)=>{let a=e-r.lastActivity,i=e-r.registeredAt;a>6e5&&0===r.callCount&&t.push(s),i>18e5&&r.callCount<5&&t.push(s)}),t}getListenerStats(){let e=Date.now(),t=Array.from(this.listenerMetrics.entries()).map(([t,r])=>({listenerId:t,registeredAt:r.registeredAt,lastActivity:r.lastActivity,callCount:r.callCount,componentName:r.componentName,runtime:e-r.registeredAt,timeSinceLastActivity:e-r.lastActivity})),r=t.filter(e=>e.timeSinceLastActivity<3e5).length,s=t.filter(e=>e.timeSinceLastActivity>=3e5).length;return{totalListeners:this.listeners.size,activeListeners:r,staleListeners:s,metrics:t}}destroy(){this.autoCleanupInterval&&(clearInterval(this.autoCleanupInterval),this.autoCleanupInterval=null),this.clearAllListeners(),console.log("[DataChangeNotifier] 通知器已销毁")}}let p=new m},51178:(e,t,r)=>{"use strict";r.d(t,{dataSyncService:()=>o});var s=r(37637),a=r(53200);class i{constructor(e={}){this.syncQueue=[],this.isProcessing=!1,this.eventListeners=new Map,this.config={enableRealTimeSync:!0,syncInterval:5e3,maxRetries:3,retryDelay:1e3,enableConflictResolution:!0,conflictResolutionStrategy:"latest_wins",...e},this.config.enableRealTimeSync&&this.startRealTimeSync()}static getInstance(e){return i.instance||(i.instance=new i(e)),i.instance}startRealTimeSync(){this.syncInterval&&clearInterval(this.syncInterval),this.syncInterval=setInterval(()=>{this.processSyncQueue()},this.config.syncInterval)}stopRealTimeSync(){this.syncInterval&&(clearInterval(this.syncInterval),this.syncInterval=void 0)}addChangeEvent(e){let t={...e,timestamp:a._l.now()};this.syncQueue.push(t),this.emitEvent("dataChange",t)}async processSyncQueue(){if(this.isProcessing||0===this.syncQueue.length)return;this.isProcessing=!0;let e=[...this.syncQueue];this.syncQueue=[];try{for(let t of e)await this.processChangeEvent(t)}catch(e){}finally{this.isProcessing=!1}}async processChangeEvent(e){try{switch(e.module){case"product":await this.syncProductChanges(e);break;case"customer":await this.syncCustomerChanges(e);break;case"employee":await this.syncEmployeeChanges(e);break;case"inventory":await this.syncInventoryChanges(e);break;case"sales":await this.syncSalesChanges(e);break;case"production":await this.syncProductionChanges(e);break;case"scheduling":await this.syncSchedulingChanges(e);break;case"workstation":await this.syncWorkstationChanges(e)}}catch(e){}}async syncProductChanges(e){if("update"===e.type&&e.newData)try{let t=await s.dataAccessManager.inventory.getByProductCode(e.entityId);if("success"===t.status&&t.data){let r={};e.newData.modelName&&t.data.productName!==e.newData.modelName&&(r.productName=e.newData.modelName),Object.keys(r).length>0&&await s.dataAccessManager.inventory.update(e.entityId,r)}}catch(e){}this.emitEvent("productChange",e)}async syncCustomerChanges(e){this.emitEvent("customerChange",e)}async syncEmployeeChanges(e){this.emitEvent("employeeChange",e)}async syncInventoryChanges(e){this.emitEvent("inventoryChange",e)}async performFullSync(){let e=Date.now(),t=0,r=0,i=[];try{let o=await s.dataAccessManager.products.getActive();if("success"===o.status&&o.data)for(let e of o.data)try{let r=await s.dataAccessManager.inventory.getByProductCode(e.modelCode);if("success"===r.status&&r.data){let a=r.data,i={};a.productName!==e.modelName&&(i.productName=e.modelName),Object.keys(i).length>0&&(await s.dataAccessManager.inventory.update(e.modelCode,i),t++)}}catch(t){r++,i.push(`同步产品 ${e.modelCode} 失败: ${t}`)}let n=Date.now()-e;return{success:0===r,message:`全量同步完成，耗时 ${n}ms`,syncedItems:t,failedItems:r,errors:i,timestamp:a._l.now()}}catch(e){return{success:!1,message:`全量同步失败: ${e}`,syncedItems:t,failedItems:r+1,errors:[...i,String(e)],timestamp:a._l.now()}}}addEventListener(e,t){this.eventListeners.has(e)||this.eventListeners.set(e,[]),this.eventListeners.get(e).push(t)}removeEventListener(e,t){let r=this.eventListeners.get(e);if(r){let e=r.indexOf(t);e>-1&&r.splice(e,1)}}emitEvent(e,t){let r=this.eventListeners.get(e);r&&r.forEach(e=>{try{e(t)}catch(e){}})}getSyncStatistics(){return{queueLength:this.syncQueue.length,isProcessing:this.isProcessing,config:this.config,eventListeners:Array.from(this.eventListeners.keys()).map(e=>({eventType:e,listenerCount:this.eventListeners.get(e)?.length||0}))}}updateConfig(e){this.config={...this.config,...e},this.config.enableRealTimeSync&&!this.syncInterval?this.startRealTimeSync():!this.config.enableRealTimeSync&&this.syncInterval&&this.stopRealTimeSync()}async syncProductionChanges(e){try{e.entityType}catch(e){}}async syncSchedulingChanges(e){}async syncWorkstationChanges(e){}setupStoreSync(){console.log("[DataSyncService] 数据同步服务已启动（符合规范模式）")}triggerStoreSync(){console.log("[DataSyncService] 手动数据同步已触发（符合规范模式）")}async syncSalesChanges(e){try{"sales_order"===e.entityType&&await this.handleSalesOrderChange(e)}catch(e){}}async handleSalesOrderChange(e){try{if(await this.clearRelatedCaches(e),e.newData?.changeType)switch(e.newData.changeType){case"quantity_change":await this.syncQuantityChangeToProduction(e);break;case"delivery_date_change":await this.syncDeliveryDateChangeToProduction(e)}}catch(e){console.warn("销售订单变更同步失败:",e)}}async syncQuantityChangeToProduction(e){try{let{dataAccessManager:t}=await Promise.resolve().then(r.bind(r,37637));t.clearDataTypeCache("production_orders"),t.clearDataTypeCache("production_work_orders"),console.log(`[DataSyncService] 销售订单数量变更已同步到生产模块: ${e.entityId}`)}catch(e){console.warn("数量变更同步到生产模块失败:",e)}}async syncDeliveryDateChangeToProduction(e){try{let{dataAccessManager:t}=await Promise.resolve().then(r.bind(r,37637));t.clearDataTypeCache("production_orders"),t.clearDataTypeCache("production_work_orders"),t.clearDataTypeCache("scheduling"),console.log(`[DataSyncService] 销售订单交期变更已同步到生产和排程模块: ${e.entityId}`)}catch(e){console.warn("交期变更同步到生产模块失败:",e)}}async clearRelatedCaches(e){try{let{dataAccessManager:t}=await Promise.resolve().then(r.bind(r,37637));t.clearDataTypeCache("sales_orders",[e.entityId]),e.newData?.orderNumber&&t.clearServiceCache("ProductionOrderService")}catch(e){console.warn("清除相关缓存失败:",e)}}async notifyModuleDataChange(e,t){try{console.log(`[DataSyncService] 通知模块 ${e} 数据已变更:`,{entityType:t.entityType,entityId:t.entityId,operation:t.type})}catch(t){console.warn(`通知模块 ${e} 失败:`,t)}}}let o=i.getInstance()},23218:(e,t,r)=>{"use strict";r.d(t,{D:()=>n});var s=r(53738),a=r(53200),i=r(19101);class o{constructor(){this.workstations=[],this.initializeTestData()}static getInstance(){return o.instance||(o.instance=new o),o.instance}initializeTestData(){let e=new Date().toISOString();this.workstations=[{id:"ws_001",code:"WS001",name:"注塑工位1",status:"active",description:"主要用于小型产品注塑",currentMoldNumber:null,currentBatchNumber:null,batchNumberQueue:[],lastEndTime:null,version:1,lastModifiedBy:"system",lastModifiedAt:e,createdAt:"2024-01-01T00:00:00.000Z",updatedAt:e},{id:"ws_002",code:"WS002",name:"注塑工位2",status:"active",description:"主要用于中型产品注塑",currentMoldNumber:null,currentBatchNumber:null,batchNumberQueue:[],lastEndTime:null,version:1,lastModifiedBy:"system",lastModifiedAt:e,createdAt:"2024-01-01T00:00:00.000Z",updatedAt:e},{id:"ws_003",code:"WS003",name:"注塑工位3",status:"active",description:"主要用于大型产品注塑",currentMoldNumber:null,currentBatchNumber:null,batchNumberQueue:[],lastEndTime:null,version:1,lastModifiedBy:"system",lastModifiedAt:e,createdAt:"2024-01-01T00:00:00.000Z",updatedAt:e},{id:"ws_004",code:"WS004",name:"包装工位1",status:"active",description:"产品包装和质检",currentMoldNumber:null,currentBatchNumber:null,batchNumberQueue:[],lastEndTime:null,version:1,lastModifiedBy:"system",lastModifiedAt:e,createdAt:"2024-01-01T00:00:00.000Z",updatedAt:e}],console.log(`📍 WorkstationStorage: 初始化 ${this.workstations.length} 个工位（支持版本控制）`)}getAllWorkstations(){return[...this.workstations]}getWorkstationById(e){return this.workstations.find(t=>t.id===e)}getWorkstationByCode(e){return this.workstations.find(t=>t.code===e)}addWorkstation(e){let t=new Date().toISOString(),r={...e,version:e.version||1,lastModifiedBy:e.lastModifiedBy||"system",lastModifiedAt:e.lastModifiedAt||t,updatedAt:t};this.workstations.push(r),console.log(`➕ [WorkstationStorage] 添加工位 ${r.code}，初始版本: ${r.version}`)}updateWorkstationWithVersion(e,t,r,s="system"){let a=this.workstations.findIndex(t=>t.id===e);if(-1===a)return{success:!1,error:"工位不存在"};let i=this.workstations[a];if(i.version!==r)return{success:!1,currentVersion:i.version,error:`版本冲突：期望版本 ${r}，当前版本 ${i.version}`};let o=new Date().toISOString();return this.workstations[a]={...i,...t,version:i.version+1,lastModifiedBy:s,lastModifiedAt:o,updatedAt:o},console.log(`🔄 [WorkstationStorage] 工位 ${e} 更新成功，版本 ${r} → ${i.version+1}`),{success:!0,currentVersion:i.version+1}}removeWorkstation(e){let t=this.workstations.findIndex(t=>t.id===e);return -1!==t&&(this.workstations.splice(t,1),!0)}getActiveWorkstations(){return this.workstations.filter(e=>"active"===e.status)}getAvailableWorkstations(){return this.workstations.filter(e=>"active"===e.status&&!e.currentBatchNumber&&!e.currentMoldNumber)}addToWorkstationQueue(e,t){let r=this.getWorkstationById(e);return!!r&&(r.batchNumberQueue||(r.batchNumberQueue=[]),r.batchNumberQueue.includes(t)||(r.batchNumberQueue.push(t),r.updatedAt=new Date().toISOString()),!0)}removeFromWorkstationQueue(e,t){let r=this.getWorkstationById(e);if(!r)return!1;r.batchNumberQueue||(r.batchNumberQueue=[]);let s=r.batchNumberQueue.indexOf(t);return s>-1&&(r.batchNumberQueue.splice(s,1),r.updatedAt=new Date().toISOString()),!0}clearWorkstationQueue(e){let t=this.getWorkstationById(e);return!!t&&(t.batchNumberQueue=[],t.updatedAt=new Date().toISOString(),!0)}cleanAllWorkstationQueues(){this.workstations.forEach(e=>{e.batchNumberQueue=[],e.updatedAt=new Date().toISOString()})}resetAllWorkstationsToIdle(){let e=0,t=[];return console.log("\uD83D\uDD0D 开始检查工位状态，当前工位数量:",this.workstations.length),this.workstations.forEach(r=>{let s=[],a=!1;console.log(`🔍 检查工位 ${r.code}:`,{currentMoldNumber:r.currentMoldNumber,currentBatchNumber:r.currentBatchNumber,batchNumberQueue:r.batchNumberQueue,lastEndTime:r.lastEndTime}),null!==r.currentMoldNumber&&void 0!==r.currentMoldNumber&&(r.currentMoldNumber=null,s.push("currentMoldNumber"),a=!0),null!==r.currentBatchNumber&&void 0!==r.currentBatchNumber&&(r.currentBatchNumber=null,s.push("currentBatchNumber"),a=!0),r.batchNumberQueue&&r.batchNumberQueue.length>0&&(r.batchNumberQueue=[],s.push("batchNumberQueue"),a=!0),null!==r.lastEndTime&&void 0!==r.lastEndTime&&(r.lastEndTime=null,s.push("lastEndTime"),a=!0),a||s.length>0?(r.updatedAt=new Date().toISOString(),r.version=(r.version||1)+1,r.lastModifiedBy="system",r.lastModifiedAt=new Date().toISOString(),e++,t.push({workstationId:r.id,workstationCode:r.code,clearedFields:s}),console.log(`✅ 工位 ${r.code} 已重置，清除字段:`,s)):console.log(`⏭️ 工位 ${r.code} 已经是空闲状态，跳过`)}),console.log(`🔄 工位重置完成，总共重置了 ${e} 个工位`),{resetCount:e,details:t}}checkWorkstationCodeExists(e,t){return this.workstations.some(r=>r.code===e&&r.id!==t)}migrateBatchNumberFormats(){let e=0,t=0,r=[];return this.workstations.forEach(s=>{s.batchNumberQueue||(s.batchNumberQueue=[]);let a=s.batchNumberQueue.filter(e=>e.startsWith("PC"));if(a.length>0){let i=a.map(e=>e.replace(/^PC/,"PO-"));s.batchNumberQueue=s.batchNumberQueue.filter(e=>!e.startsWith("PC")).concat(i),s.updatedAt=new Date().toISOString(),e++,t+=a.length,r.push({workstationId:s.id,workstationCode:s.code,originalItems:a,migratedItems:i})}}),{migratedWorkstations:e,totalMigratedItems:t,details:r}}}class n{constructor(){this.storage=o.getInstance()}static getInstance(){return n.instance||(n.instance=new n),n.instance}createSuccessResponse(e,t="操作成功"){return a.N5.createSuccess(e,t)}createErrorResponse(e,t=s.Bx.INTERNAL_SERVER_ERROR){return a.N5.createStandardError(e,t)}async getWorkstations(e){try{let t=this.storage.getAllWorkstations();if(e?.search){let r=e.search.toLowerCase();t=t.filter(e=>e.code.toLowerCase().includes(r)||e.name.toLowerCase().includes(r)||e.description&&e.description.toLowerCase().includes(r))}if(e?.filters?.status&&(t=t.filter(t=>t.status===e.filters.status)),e?.sortBy){let r=e.sortBy,s=e.sortOrder||"asc";t.sort((e,t)=>{let a=e[r],i=t[r];return null==a&&null==i?0:null==a?"asc"===s?-1:1:null==i?"asc"===s?1:-1:a<i?"asc"===s?-1:1:a>i?"asc"===s?1:-1:0})}let{page:r=1,pageSize:s=20}=e||{},a=(r-1)*s,i={items:t.slice(a,a+s),pagination:{current:r,pageSize:s,total:t.length,totalPages:Math.ceil(t.length/s)}};return this.createSuccessResponse(i,"获取工位列表成功")}catch(e){return this.createErrorResponse("获取工位列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getWorkstationById(e){try{if(!e)return this.createErrorResponse("工位ID不能为空",s.Bx.BAD_REQUEST);let t=this.storage.getWorkstationById(e);if(!t)return this.createErrorResponse(`工位ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(t,"获取工位成功")}catch(e){return this.createErrorResponse("获取工位失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getActiveWorkstations(){try{let e=this.storage.getActiveWorkstations();return this.createSuccessResponse(e,"获取活跃工位列表成功")}catch(e){return this.createErrorResponse("获取活跃工位列表失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getWorkstationStatus(e){try{if(!e)return this.createErrorResponse("工位ID不能为空",s.Bx.BAD_REQUEST);let t=this.storage.getWorkstationById(e);if(!t)return this.createErrorResponse(`工位ID ${e} 不存在`,s.Bx.NOT_FOUND);let r={id:t.id,code:t.code,name:t.name,status:t.status,currentMoldNumber:t.currentMoldNumber,currentBatchNumber:t.currentBatchNumber,lastEndTime:t.lastEndTime,batchNumberQueue:t.batchNumberQueue||[],isAvailable:"active"===t.status,updatedAt:t.updatedAt};return this.createSuccessResponse(r,"获取工位状态成功")}catch(e){return this.createErrorResponse("获取工位状态失败",s.Bx.INTERNAL_SERVER_ERROR)}}async updateWorkstationStatus(e,t){try{if(!e)return this.createErrorResponse("工位ID不能为空",s.Bx.BAD_REQUEST);let r=this.storage.getWorkstationById(e);if(!r)return this.createErrorResponse(`工位ID ${e} 不存在`,s.Bx.NOT_FOUND);let i=a._l.addUpdate(t),o=this.storage.updateWorkstationWithVersion(e,i,r.version,"scheduling-system");if(!o.success){if(o.error?.includes("版本冲突"))return this.createErrorResponse(`WORKSTATION_BUSY: ${o.error}`,s.Bx.CONFLICT);return this.createErrorResponse(o.error||"更新失败",s.Bx.INTERNAL_SERVER_ERROR)}console.log(`✅ [WorkstationDataAccessService] 工位 ${e} 状态更新成功，版本: ${r.version} → ${o.currentVersion}`);let n=this.storage.getWorkstationById(e);if(!n)return this.createErrorResponse("工位状态更新后无法获取最新数据",s.Bx.INTERNAL_SERVER_ERROR);return this.createSuccessResponse(n,"更新工位状态成功")}catch(e){return this.createErrorResponse("更新工位状态失败",s.Bx.INTERNAL_SERVER_ERROR)}}async addToQueue(e,t){try{if(!e)return this.createErrorResponse("工位ID不能为空",s.Bx.BAD_REQUEST);if(!t)return this.createErrorResponse("批次号不能为空",s.Bx.BAD_REQUEST);if(!this.storage.getWorkstationById(e))return this.createErrorResponse(`工位ID ${e} 不存在`,s.Bx.NOT_FOUND);this.storage.addToWorkstationQueue(e,t);let r=this.storage.getWorkstationById(e);if(!r)return this.createErrorResponse("工位队列更新后无法获取最新数据",s.Bx.INTERNAL_SERVER_ERROR);return this.createSuccessResponse(r,"批次号添加到工位队列成功")}catch(e){return this.createErrorResponse("添加批次号到工位队列失败",s.Bx.INTERNAL_SERVER_ERROR)}}async migrateBatchNumberFormats(){try{this.storage.migrateBatchNumberFormats();let e=0,t=0,r=[],s=this.storage.getAllWorkstations();for(let a of s){let s=a.batchNumberQueue||[];if(0===s.length)continue;let i=[],o=[],n=[],c=!1;if(s.forEach(e=>{if(e.startsWith("PC")&&14===e.length){let r=e.replace("PC","PO");i.push(r),o.push(e),n.push(r),c=!0,t++}else i.push(e)}),c){e++,r.push({workstationId:a.id,workstationCode:a.code,originalItems:o,migratedItems:n});let t=this.storage.updateWorkstationWithVersion(a.id,{batchNumberQueue:i},a.version,"batch-migration-system");t.success||console.warn(`工位 ${a.code} 队列更新失败: ${t.error}`)}}let a={totalWorkstations:s.length,migratedWorkstations:e,migratedItems:t,details:r};return this.createSuccessResponse(a,`批次号格式迁移完成：${e}个工位，${t}个批次号`)}catch(e){return this.createErrorResponse("批次号格式迁移失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getAll(){try{let e=this.storage.getAllWorkstations();0===e.length&&(console.warn("[WorkstationDataAccessService] 工位数据为空，请检查 WorkstationStorage 的数据初始化"),console.warn("[WorkstationDataAccessService] 建议检查 WorkstationStorage.initializeTestData() 方法"));let t={items:e,pagination:{current:1,pageSize:e.length,total:e.length,totalPages:1}};return this.createSuccessResponse(t,"获取所有工位成功")}catch(e){return this.createErrorResponse("获取所有工位失败",s.Bx.INTERNAL_SERVER_ERROR)}}async create(e){try{if(!e)return this.createErrorResponse("工位数据不能为空",s.Bx.BAD_REQUEST);let t=(0,i.E4)(e);if(!t.isValid)return this.createErrorResponse(`工位数据验证失败: ${t.errors.join(", ")}`,s.Bx.BAD_REQUEST);if(this.storage.getWorkstationByCode(e.code))return this.createErrorResponse(`工位编码 ${e.code} 已存在`,s.Bx.CONFLICT);let r=a._l.now(),o={...e,id:e.id||`ws_${Date.now()}`,status:e.status||"active",currentMoldNumber:e.currentMoldNumber||null,currentBatchNumber:e.currentBatchNumber||null,batchNumberQueue:e.batchNumberQueue||[],lastEndTime:e.lastEndTime||null,version:1,lastModifiedBy:"system",lastModifiedAt:r,createdAt:e.createdAt||r,updatedAt:r};return this.storage.addWorkstation(o),console.log(`➕ [WorkstationDataAccessService] 创建工位 ${o.code}，初始版本: ${o.version}`),this.createSuccessResponse(o,"工位创建成功")}catch(e){return this.createErrorResponse("创建工位失败",s.Bx.INTERNAL_SERVER_ERROR)}}async update(e,t,r,o="user"){try{if(!e)return this.createErrorResponse("工位ID不能为空",s.Bx.BAD_REQUEST);if(!t||0===Object.keys(t).length)return this.createErrorResponse("更新数据不能为空",s.Bx.BAD_REQUEST);let n=this.storage.getWorkstationById(e);if(!n)return this.createErrorResponse(`工位ID ${e} 不存在`,s.Bx.NOT_FOUND);let c={...n,...t},l=(0,i.E4)(c);if(!l.isValid)return this.createErrorResponse(`工位数据验证失败: ${l.errors.join(", ")}`,s.Bx.BAD_REQUEST);if(t.code&&t.code!==n.code&&this.storage.checkWorkstationCodeExists(t.code,e))return this.createErrorResponse(`工位编码 ${t.code} 已存在`,s.Bx.CONFLICT);if(void 0!==r){let a=this.storage.updateWorkstationWithVersion(e,t,r,o);if(!a.success){if(a.error?.includes("版本冲突"))return this.createErrorResponse(`WORKSTATION_BUSY: ${a.error}`,s.Bx.CONFLICT);return this.createErrorResponse(a.error||"更新失败",s.Bx.INTERNAL_SERVER_ERROR)}console.log(`🔄 [WorkstationDataAccessService] 工位 ${e} 乐观锁更新成功，版本: ${r} → ${a.currentVersion}`)}else{let r=this.storage.getWorkstationById(e);if(!r)return this.createErrorResponse(`工位ID ${e} 不存在`,s.Bx.NOT_FOUND);let i=a._l.addUpdate(t),o=this.storage.updateWorkstationWithVersion(e,i,r.version,"system");if(!o.success)return this.createErrorResponse(o.error||"更新失败",s.Bx.INTERNAL_SERVER_ERROR);console.log(`✅ [WorkstationDataAccessService] 工位 ${e} 自动版本控制更新成功，版本: ${r.version} → ${o.currentVersion}`)}let d=this.storage.getWorkstationById(e);if(!d)return this.createErrorResponse("工位更新后无法获取最新数据",s.Bx.INTERNAL_SERVER_ERROR);return this.createSuccessResponse(d,"工位更新成功")}catch(e){return console.error("[WorkstationDataAccessService] 更新工位失败:",e),this.createErrorResponse("更新工位失败",s.Bx.INTERNAL_SERVER_ERROR)}}async delete(e){try{if(!e)return this.createErrorResponse("工位ID不能为空",s.Bx.BAD_REQUEST);let t=this.storage.getWorkstationById(e);if(!t)return this.createErrorResponse(`工位ID ${e} 不存在`,s.Bx.NOT_FOUND);let r=[];if(r.length>0)return this.createErrorResponse(`WORKSTATION_BUSY: 工位 ${t.code} 正在使用中，有 ${r.length} 个活跃工单，无法删除`,s.Bx.CONFLICT);return this.storage.removeWorkstation(e),this.createSuccessResponse(!0,"工位删除成功")}catch(e){return this.createErrorResponse("删除工位失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getAvailableWorkstations(){try{let e=this.storage.getAvailableWorkstations();return this.createSuccessResponse(e,"获取可用工位成功")}catch(e){return this.createErrorResponse("获取可用工位失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getCapacityAnalysis(){try{let e=this.storage.getAllWorkstations().map(e=>({workstationId:e.id,workstationName:e.name,maxCapacity:100,currentCapacity:e.batchNumberQueue?.length||0,availableCapacity:100-(e.batchNumberQueue?.length||0),capacityUtilization:(e.batchNumberQueue?.length||0)/100*100,recommendedActions:[]}));return this.createSuccessResponse(e,"获取工位容量分析成功")}catch(e){return this.createErrorResponse("获取工位容量分析失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getCapacityAnalysisById(e){try{let t=this.storage.getWorkstationById(e);if(!t)return this.createErrorResponse(`工位ID ${e} 不存在`,s.Bx.NOT_FOUND);let r={workstationId:t.id,workstationName:t.name,maxCapacity:100,currentCapacity:t.batchNumberQueue?.length||0,availableCapacity:100-(t.batchNumberQueue?.length||0),capacityUtilization:(t.batchNumberQueue?.length||0)/100*100,recommendedActions:[]};return this.createSuccessResponse(r,"获取工位容量分析成功")}catch(e){return this.createErrorResponse("获取工位容量分析失败",s.Bx.INTERNAL_SERVER_ERROR)}}async batchUpdate(e){try{let t=[];for(let r of e){let e=await this.update(r.id,r.data);"success"===e.status&&e.data&&t.push(e.data)}return this.createSuccessResponse(t,"批量更新工位成功")}catch(e){return this.createErrorResponse("批量更新工位失败",s.Bx.INTERNAL_SERVER_ERROR)}}async batchUpdateStatus(e,t){try{let r=[];for(let s of e){let e=await this.update(s,{status:t});"success"===e.status&&e.data&&r.push(e.data)}return this.createSuccessResponse(r,"批量更新工位状态成功")}catch(e){return this.createErrorResponse("批量更新工位状态失败",s.Bx.INTERNAL_SERVER_ERROR)}}async createSnapshot(){try{let e=this.storage.getAllWorkstations(),t={timestamp:a._l.now(),workstations:e.map(e=>({...e}))};return this.createSuccessResponse(t,"创建工位快照成功")}catch(e){return this.createErrorResponse("创建工位快照失败",s.Bx.INTERNAL_SERVER_ERROR)}}async restoreSnapshot(e){try{return this.createErrorResponse("快照恢复功能暂未实现",s.Bx.NOT_IMPLEMENTED)}catch(e){return this.createErrorResponse("恢复工位快照失败",s.Bx.INTERNAL_SERVER_ERROR)}}async removeFromQueue(e,t){try{if(!e||!t)return this.createErrorResponse("工位ID和批次号不能为空",s.Bx.BAD_REQUEST);this.storage.removeFromWorkstationQueue(e,t);let r=this.storage.getWorkstationById(e);if(!r)return this.createErrorResponse(`工位ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(r,"从工位队列移除批次号成功")}catch(e){return this.createErrorResponse("从工位队列移除批次号失败",s.Bx.INTERNAL_SERVER_ERROR)}}async clearQueue(e){try{if(!e)return this.createErrorResponse("工位ID不能为空",s.Bx.BAD_REQUEST);this.storage.clearWorkstationQueue(e);let t=this.storage.getWorkstationById(e);if(!t)return this.createErrorResponse(`工位ID ${e} 不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(t,"清空工位队列成功")}catch(e){return this.createErrorResponse("清空工位队列失败",s.Bx.INTERNAL_SERVER_ERROR)}}async cleanQueues(){try{return this.storage.cleanAllWorkstationQueues(),this.createSuccessResponse(!0,"清理工位队列成功")}catch(e){return this.createErrorResponse("清理工位队列失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getUtilization(){try{let e=this.storage.getAllWorkstations(),t=[],r=e.map(e=>{let r=t.filter(t=>t.workstation===e.name||t.workstationCode===e.code),s=r.filter(e=>"in_progress"===e.status),a=e.batchNumberQueue?.length||0,i=s.length>0?85:0,o=s.length+a,n=r.filter(e=>"completed"===e.status).length>0?8:0,c=r.reduce((e,t)=>e+(t.completedMoldCount||0),0);return{workstationId:e.id,workstationName:e.name,utilizationRate:i,currentLoad:o,queueLength:a,averageProcessingTime:n,efficiency:c>0?Math.min(95,60+2*c):0}});return this.createSuccessResponse(r,"获取工位利用率成功")}catch(e){return this.createErrorResponse("获取工位利用率失败",s.Bx.INTERNAL_SERVER_ERROR)}}async getUtilizationById(e){try{let t=await this.getUtilization();if("success"!==t.status||!t.data)return this.createErrorResponse("获取工位利用率失败",s.Bx.INTERNAL_SERVER_ERROR);let r=t.data.find(t=>t.workstationId===e);if(!r)return this.createErrorResponse(`工位ID ${e} 的利用率数据不存在`,s.Bx.NOT_FOUND);return this.createSuccessResponse(r,"获取工位利用率成功")}catch(e){return this.createErrorResponse("根据ID获取工位利用率失败",s.Bx.INTERNAL_SERVER_ERROR)}}async resetAllWorkstationsToIdle(){try{console.log("\uD83D\uDD04 开始重置所有工位状态到初始空闲状态...");let e=this.storage.resetAllWorkstationsToIdle(),{dataAccessManager:t}=r(37637);return t.clearDataTypeCache("workstations"),console.log(`✅ 所有工位状态重置完成，重置了 ${e.resetCount} 个工位`),e.details.length>0&&console.log("\uD83D\uDCCB 重置详情:",e.details.map(e=>({工位:`${e.workstationCode} (${e.workstationId})`,清除字段:e.clearedFields.join(", ")}))),this.createSuccessResponse(e,`成功重置 ${e.resetCount} 个工位状态`)}catch(e){return console.error("❌ 重置所有工位状态失败:",e),this.createErrorResponse("重置工位状态失败",s.Bx.INTERNAL_SERVER_ERROR)}}}},5045:(e,t,r)=>{"use strict";r.r(t),r.d(t,{InventoryValueCalculationService:()=>a});var s=r(37637);class a{static async calculateProductValue(e){try{let t=await s.dataAccessManager.products.getByCode(e.productCode);if("success"!==t.status||!t.data)return{unitPrice:0,totalValue:0,priceSource:"default"};let r=t.data;if(!r||"active"!==r.status)return{unitPrice:0,totalValue:0,priceSource:"default"};{let t=r.productPrice||0,s=t*e.currentStock;return{unitPrice:t,totalValue:s,priceSource:"product_model",lastPriceUpdate:r.updatedAt}}}catch(e){return{unitPrice:0,totalValue:0,priceSource:"default"}}}static calculateProductValueSync(e){return{unitPrice:10,totalValue:10*e.currentStock,priceSource:"default",lastPriceUpdate:void 0}}static batchCalculateProductValues(e){let t=e.map(e=>{let t=this.calculateProductValueSync(e);return{...e,...t}}),r={totalProducts:t.length,totalValue:t.reduce((e,t)=>e+t.totalValue,0),validPriceCount:t.filter(e=>"product_model"===e.priceSource).length,invalidPriceCount:t.filter(e=>"default"===e.priceSource).length};return{results:t,summary:r}}static async getProductDetails(e){try{let t=await s.dataAccessManager.products.getByCode(e);if("success"!==t.status||!t.data)return{isActive:!1,hasValidPrice:!1};{let e=t.data;return{productModel:e,category:e.category||"未分类",isActive:"active"===e.status,hasValidPrice:(e.productPrice||0)>0}}}catch(e){return{isActive:!1,hasValidPrice:!1}}}static async validateProductConsistency(e){let t=[],r=[];try{let s=await this.getProductDetails(e.productCode);if(s.productModel){let a=s.productModel;s.isActive||(t.push(`产品 ${e.productCode} 已停用`),r.push("请激活产品或从库存中移除该产品")),e.productName!==a.modelName&&(t.push(`产品名称不一致：库存中为"${e.productName}"，产品数据中为"${a.modelName}"`),r.push("建议同步产品名称以保持数据一致性")),s.hasValidPrice||(t.push(`产品 ${e.productCode} 的价格未设置或为0`),r.push("请在产品数据模块中设置正确的产品价格"))}else t.push(`产品编码 ${e.productCode} 在产品数据模块中不存在`),r.push("请检查产品编码是否正确，或在产品数据模块中添加该产品");return{isValid:0===t.length,issues:t,suggestions:r}}catch(e){return{isValid:!1,issues:["验证过程中发生错误"],suggestions:["请检查系统状态并重试"]}}}static async batchValidateProductConsistency(e){let t=await Promise.all(e.map(async e=>{let t=await this.validateProductConsistency(e);return{productCode:e.productCode,productName:e.productName,...t}})),r=t.filter(e=>e.isValid).length,s=t.length-r,a=t.reduce((e,t)=>e+t.issues.length,0),i={missingProducts:t.filter(e=>e.issues.some(e=>e.includes("不存在"))).length,inactiveProducts:t.filter(e=>e.issues.some(e=>e.includes("已停用"))).length,nameInconsistencies:t.filter(e=>e.issues.some(e=>e.includes("名称不一致"))).length,priceIssues:t.filter(e=>e.issues.some(e=>e.includes("价格"))).length};return{validCount:r,invalidCount:s,totalIssues:a,detailedResults:t,summary:i}}static async syncProductName(e){try{let t=await this.getProductDetails(e.productCode);if(t.productModel)return t.productModel.modelName;return null}catch(e){return null}}static async getInventoryValueStatistics(e){let{results:t}=this.batchCalculateProductValues(e);if(0===t.length)return{totalValue:0,averageValue:0,highestValue:{productCode:"",value:0},lowestValue:{productCode:"",value:0},categoryBreakdown:[]};let r=t.reduce((e,t)=>e+t.totalValue,0),s=r/t.length,a=t.sort((e,t)=>t.totalValue-e.totalValue),i={productCode:a[0].productCode,value:a[0].totalValue},o={productCode:a[a.length-1].productCode,value:a[a.length-1].totalValue},n=new Map;for(let e of t){let t=(await this.getProductDetails(e.productCode)).category||"未分类";if(n.has(t)){let r=n.get(t);n.set(t,{value:r.value+e.totalValue,count:r.count+1})}else n.set(t,{value:e.totalValue,count:1})}return{totalValue:r,averageValue:s,highestValue:i,lowestValue:o,categoryBreakdown:Array.from(n.entries()).map(([e,t])=>({category:e,value:t.value,count:t.count}))}}}},56870:(e,t,r)=>{"use strict";r.d(t,{Tx:()=>o,rm:()=>i});var s=r(43158),a=r(67023);let i=(0,s.Ue)()((0,a.mW)((0,a.tJ)(e=>({materials:[],selectedMaterial:null,materialsLoading:!1,productModels:[],selectedProductModel:null,productModelsLoading:!1,setMaterials:t=>e({materials:t}),setSelectedMaterial:t=>e({selectedMaterial:t}),setMaterialsLoading:t=>e({materialsLoading:t}),setProductModels:t=>{e({productModels:t})},setSelectedProductModel:t=>e({selectedProductModel:t}),setProductModelsLoading:t=>e({productModelsLoading:t}),clearAll:()=>e({materials:[],selectedMaterial:null,materialsLoading:!1,productModels:[],selectedProductModel:null,productModelsLoading:!1}),initializeFromStorage:()=>{}}),{name:"master-data-store",partialize:e=>({materials:e.materials,productModels:e.productModels}),version:1,migrate:(e,t)=>0===t?{materials:e.materials||[],productModels:e.productModels||[]}:e}),{name:"master-data-store-devtools"})),o={clearLocalStorage:()=>{localStorage.removeItem("master-data-storage")},exportData:()=>{let e=i.getState(),t={materials:e.materials,productModels:e.productModels,exportTime:new Date().toISOString(),version:"1.0"},r=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),s=URL.createObjectURL(r),a=document.createElement("a");a.href=s,a.download=`master-data-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s)},importData:e=>new Promise((t,r)=>{let s=new FileReader;s.onload=e=>{try{let r=JSON.parse(e.target?.result),s=i.getState();r.materials&&Array.isArray(r.materials)&&s.setMaterials(r.materials),r.productModels&&Array.isArray(r.productModels)&&s.setProductModels(r.productModels),t()}catch(e){r(e)}},s.onerror=()=>r(Error("文件读取失败")),s.readAsText(e)}),getStorageInfo:()=>{let e=i.getState(),t=localStorage.getItem("master-data-storage");return{materialsCount:e.materials.length,productModelsCount:e.productModels.length,storageSize:t?new Blob([t]).size:0,lastUpdated:t?JSON.parse(t).state?.updatedAt:null}}}},41517:(e,t,r)=>{"use strict";r.d(t,{N5:()=>n});var s,a=r(53738),i=r(62158);!function(e){e.VALIDATION_ERROR="VALIDATION_ERROR",e.NOT_FOUND="NOT_FOUND",e.DUPLICATE_RESOURCE="DUPLICATE_RESOURCE",e.PERMISSION_DENIED="PERMISSION_DENIED",e.BUSINESS_RULE_VIOLATION="BUSINESS_RULE_VIOLATION",e.EXTERNAL_SERVICE_ERROR="EXTERNAL_SERVICE_ERROR",e.INTERNAL_ERROR="INTERNAL_ERROR",e.TIMEOUT_ERROR="TIMEOUT_ERROR",e.NETWORK_ERROR="NETWORK_ERROR"}(s||(s={}));class o{static{this.DEFAULT_CONFIG={enableLogging:!0,enableStackTrace:!1,enableContextCapture:!0,logLevel:"error"}}static createBusinessError(e,t,r,s,a){return{type:e,code:t,message:r,details:a?.message,context:s?{operation:s.operation||"unknown",entityType:s.entityType,entityId:s.entityId,userId:s.userId,timestamp:new Date().toISOString(),additionalData:s.additionalData}:void 0,originalError:a}}static handleError(e,t,r={}){let s;let a={...this.DEFAULT_CONFIG,...r};return s=this.isBusinessError(e)?e:this.convertToBusinessError(e,t),a.enableLogging&&this.logError(s,a),this.createErrorResponse(s,a)}static handleValidationError(e,t,r){let s=this.createBusinessError("VALIDATION_ERROR","VALIDATION_FAILED",`数据验证失败: ${e.join(", ")}`,{operation:t,entityType:r,additionalData:{validationErrors:e}});return this.handleError(s,t)}static handleNotFoundError(e,t,r){let s=this.createBusinessError("NOT_FOUND","RESOURCE_NOT_FOUND",`${e} ${t} 不存在`,{operation:r,entityType:e,entityId:t});return this.handleError(s,r)}static handleDuplicateError(e,t,r){let s=this.createBusinessError("DUPLICATE_RESOURCE","RESOURCE_DUPLICATE",`${e} ${t} 已存在`,{operation:r,entityType:e,additionalData:{identifier:t}});return this.handleError(s,r)}static handleBusinessRuleViolation(e,t,r,s){let a=this.createBusinessError("BUSINESS_RULE_VIOLATION","BUSINESS_RULE_VIOLATION",`业务规则违反: ${t}`,{operation:r,entityType:s,additionalData:{ruleName:e,ruleDescription:t}});return this.handleError(a,r)}static handleInternalError(e,t,r){let s=this.createBusinessError("INTERNAL_ERROR","INTERNAL_ERROR","系统内部错误",{operation:t,entityType:r},e);return this.handleError(s,t)}static handleBatchErrors(e,t){let r=e.map(({error:e,operation:t,entityId:r})=>this.isBusinessError(e)?e:this.convertToBusinessError(e,t,r)),s=`批量操作失败 (${t}): ${r.length} 个错误`,a=this.createBusinessError("INTERNAL_ERROR","BATCH_OPERATION_FAILED",s,{operation:t,additionalData:{errorCount:r.length,errors:r.map(e=>({type:e.type,code:e.code,message:e.message}))}});return this.handleError(a,t)}static isBusinessError(e){return e&&"object"==typeof e&&"type"in e&&"code"in e}static convertToBusinessError(e,t,r){let s="INTERNAL_ERROR",a="INTERNAL_ERROR";return e.message.includes("not found")||e.message.includes("不存在")?(s="NOT_FOUND",a="RESOURCE_NOT_FOUND"):e.message.includes("already exists")||e.message.includes("已存在")?(s="DUPLICATE_RESOURCE",a="RESOURCE_DUPLICATE"):e.message.includes("validation")||e.message.includes("验证")?(s="VALIDATION_ERROR",a="VALIDATION_FAILED"):e.message.includes("timeout")||e.message.includes("超时")?(s="TIMEOUT_ERROR",a="OPERATION_TIMEOUT"):(e.message.includes("network")||e.message.includes("网络"))&&(s="NETWORK_ERROR",a="NETWORK_ERROR"),this.createBusinessError(s,a,e.message||"未知错误",{operation:t,entityId:r},e)}static logError(e,t){let r={type:e.type,code:e.code,message:e.message,context:e.context,...t.enableStackTrace&&e.originalError&&{stack:e.originalError.stack}};switch(t.logLevel){case"error":console.error(`❌ [BusinessErrorHandler] ${e.code}:`,r);break;case"warn":console.warn(`⚠️ [BusinessErrorHandler] ${e.code}:`,r);break;case"info":console.info(`ℹ️ [BusinessErrorHandler] ${e.code}:`,r)}}static createErrorResponse(e,t){return{status:"error",code:({VALIDATION_ERROR:a.Bx.BAD_REQUEST,NOT_FOUND:a.Bx.NOT_FOUND,DUPLICATE_RESOURCE:a.Bx.CONFLICT,PERMISSION_DENIED:a.Bx.FORBIDDEN,BUSINESS_RULE_VIOLATION:a.Bx.BAD_REQUEST,EXTERNAL_SERVICE_ERROR:a.Bx.SERVICE_UNAVAILABLE,INTERNAL_ERROR:a.Bx.INTERNAL_SERVER_ERROR,TIMEOUT_ERROR:a.Bx.SERVICE_UNAVAILABLE,NETWORK_ERROR:a.Bx.SERVICE_UNAVAILABLE})[e.type]||a.Bx.INTERNAL_SERVER_ERROR,message:e.message,timestamp:i._.now(),version:a.Gn,...t.enableStackTrace&&e.originalError&&{error:e.originalError.message},...t.enableContextCapture&&e.context&&{context:e.context}}}static createSuccessResponse(e,t="操作成功",r=a.Bx.OK){return{status:"success",code:r,data:e,message:t,timestamp:i._.now(),version:a.Gn}}static createStandardErrorResponse(e,t=a.Bx.INTERNAL_SERVER_ERROR){return{status:"error",code:t,message:e,timestamp:i._.now(),version:a.Gn}}static getErrorStatistics(){return{totalErrors:0,errorsByType:{},recentErrors:[]}}}let n={handle:o.handleError,handleValidation:o.handleValidationError,handleNotFound:o.handleNotFoundError,handleDuplicate:o.handleDuplicateError,handleBusinessRule:o.handleBusinessRuleViolation,handleInternal:o.handleInternalError,handleBatch:o.handleBatchErrors,createError:o.createBusinessError,getStats:o.getErrorStatistics,createSuccess:o.createSuccessResponse,createStandardError:o.createStandardErrorResponse,handleError:o.handleError}},53200:(e,t,r)=>{"use strict";r.d(t,{N5:()=>l.N5,IZ:()=>n,_l:()=>a._}),r(58357);var s=r(85287),a=r(62158);class i{static{this.DEFAULT_CONFIG={generateId:!0,generateTimestamps:!0,validateData:!0,preserveOriginalFields:!0}}static transformSalesOrderData(e,t={}){let r={...i.DEFAULT_CONFIG,...t},o=[];try{let t;if(r.validateData){let t=i.validateSalesOrderData(e);if(!t.isValid)return{success:!1,error:`销售订单数据验证失败: ${t.errors.join(", ")}`};o.push(...t.warnings)}let n=e.orderNumber;return r.generateId&&!n&&(n=s.s.generateSalesOrderId(),o.push("自动生成了销售订单号")),t=r.generateTimestamps?a._.addCreation({...e,id:n,orderNumber:n}):{...e,id:n,orderNumber:n,createdAt:"",updatedAt:""},{success:!0,data:t,warnings:o.length>0?o:void 0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"数据转换失败"}}}static transformProductionOrderData(e,t={}){let r={...i.DEFAULT_CONFIG,...t},s=[];try{let t;if(r.validateData){let t=i.validateProductionOrderData(e);if(!t.isValid)return{success:!1,error:`生产订单数据验证失败: ${t.errors.join(", ")}`};s.push(...t.warnings)}let o=e.orderNumber;if(!o)return{success:!1,error:"MRP生成的生产订单必须包含订单号"};return t=r.generateTimestamps?a._.addCreation({...e,id:o,orderNumber:o}):{...e,id:o,orderNumber:o,createdAt:"",updatedAt:""},{success:!0,data:t,warnings:s.length>0?s:void 0}}catch(e){return{success:!1,error:e instanceof Error?e.message:"数据转换失败"}}}static transformOrderUpdateData(e,t,r={}){let s={...i.DEFAULT_CONFIG,...r};try{let r;return r=s.generateTimestamps?a._.addUpdate({...e,...t}):{...e,...t},{success:!0,data:r}}catch(e){return{success:!1,error:e instanceof Error?e.message:"更新数据转换失败"}}}static validateSalesOrderData(e){let t=[],r=[];return e.customerId||t.push("客户ID不能为空"),e.customerName||t.push("客户名称不能为空"),e.items&&0!==e.items.length||t.push("订单项目不能为空"),e.orderNumber&&!s.s.validateSalesOrderNumber(e.orderNumber)&&t.push("销售订单号格式不正确"),e.orderNumber||r.push("未提供订单号，将自动生成"),{isValid:0===t.length,errors:t,warnings:r}}static validateProductionOrderData(e){let t=[];return e.orderNumber||t.push("生产订单号不能为空"),e.productCode||t.push("产品编码不能为空"),e.productName||t.push("产品名称不能为空"),(!e.plannedQuantity||e.plannedQuantity<=0)&&t.push("计划数量必须大于0"),e.orderNumber&&!s.s.validateProductionOrderNumber(e.orderNumber)&&t.push("生产订单号格式不正确"),{isValid:0===t.length,errors:t,warnings:[]}}}class o{static mapOrderStatusDisplay(e){return({pending:"待审核",confirmed:"已审核",completed:"已完成",cancelled:"已取消",in_plan:"计划中",planned:"已计划",in_progress:"生产中"})[e]||e}static mapPriorityDisplay(e){return({high:{text:"高",color:"red"},medium:{text:"中",color:"orange"},low:{text:"低",color:"green"},urgent:{text:"紧急",color:"magenta"}})[e]||{text:e,color:"default"}}static safeFieldMapping(e,t,r={}){let s={};for(let[a,i]of Object.entries(t)){let t=e[a];null!=t?s[i]=t:void 0!==r[i]&&(s[i]=r[i])}return s}static formatCurrency(e,t="\xa5",r=2){if("number"!=typeof e||isNaN(e))return`${t}0.${"0".repeat(r)}`;let s=e.toFixed(r).split(".");return s[0]=s[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),`${t}${s.join(".")}`}static parseCurrency(e){if(!e||"string"!=typeof e)return 0;let t=parseFloat(e.replace(/[¥$€£,]/g,""));return isNaN(t)?0:t}static formatDateTime(e,t){if(!e||"string"!=typeof e)return e||"";try{let t=new Date(e);if(isNaN(t.getTime()))return e;let r=t.getFullYear(),s=String(t.getMonth()+1).padStart(2,"0"),a=String(t.getDate()).padStart(2,"0"),i=String(t.getHours()).padStart(2,"0"),o=String(t.getMinutes()).padStart(2,"0"),n=String(t.getSeconds()).padStart(2,"0");return`${r}-${s}-${a} ${i}:${o}:${n}`}catch(t){return e}}static{this.mapStatusToDisplay=o.mapOrderStatusDisplay}}let n={transformSalesOrder:i.transformSalesOrderData,transformProductionOrder:i.transformProductionOrderData,transformUpdate:i.transformOrderUpdateData,mapStatusDisplay:o.mapOrderStatusDisplay,mapPriorityDisplay:o.mapPriorityDisplay,safeMapping:o.safeFieldMapping,transformOrderForDisplay:e=>({...e,statusDisplay:o.mapOrderStatusDisplay(e.status||""),priorityDisplay:o.mapPriorityDisplay(e.priority||"medium")})};class c{static validateOrderNumber(e,t,r={}){let a=[],i=[];if(!e||""===e.trim())return a.push("订单号不能为空"),{isValid:!1,errors:a,warnings:i};let o=!1,n="";switch(t){case"sales":o=s.s.validateSalesOrderNumber(e),n=`销售订单号格式错误，请使用格式：${s.s.ORDER_NUMBER_FORMATS.SALES_ORDER}`;break;case"production":o=s.s.validateProductionOrderNumber(e),n=`生产订单号格式错误，请使用格式：${s.s.ORDER_NUMBER_FORMATS.PRODUCTION_ORDER}`;break;case"work":o=s.s.validateWorkOrderNumber(e),n=`工单号格式错误，请使用格式：${s.s.ORDER_NUMBER_FORMATS.WORK_ORDER}`}return o||a.push(n),r.checkUniqueness&&o&&!r.checkUniqueness(e)&&a.push("订单号已存在，请使用其他订单号"),{isValid:0===a.length,errors:a,warnings:i.length>0?i:void 0}}static validateProductCode(e){let t=[];return e&&""!==e.trim()?/^P\d{5}$/.test(e)||t.push("产品编码格式错误，请使用格式：P + 5位数字（如：P00001）"):t.push("产品编码不能为空"),{isValid:0===t.length,errors:t}}static validateQuantity(e,t="数量",r={}){let s=[],a="string"==typeof e?parseFloat(e):e;return isNaN(a)||a<=0?s.push(`${t}必须是大于0的数字`):(void 0!==r.min&&a<r.min&&s.push(`${t}不能小于${r.min}`),void 0!==r.max&&a>r.max&&s.push(`${t}不能大于${r.max}`)),{isValid:0===s.length,errors:s}}static validateDate(e,t="日期",r={}){let s;let a=[];try{if(s="string"==typeof e?new Date(e):e,isNaN(s.getTime()))throw Error("Invalid date")}catch{return a.push(`${t}格式不正确`),{isValid:!1,errors:a}}let i=new Date,o=new Date(i.getFullYear(),i.getMonth(),i.getDate()),n=new Date(s.getFullYear(),s.getMonth(),s.getDate());return!r.allowPast&&n<o&&a.push(`${t}不能是过去的日期`),!r.allowFuture&&n>o&&a.push(`${t}不能是未来的日期`),r.minDate&&s<r.minDate&&a.push(`${t}不能早于${r.minDate.toLocaleDateString()}`),r.maxDate&&s>r.maxDate&&a.push(`${t}不能晚于${r.maxDate.toLocaleDateString()}`),{isValid:0===a.length,errors:a}}static validateSalesOrder(e){let t=[],r=[],s={};if(e.orderNumber){let r=this.validateOrderNumber(e.orderNumber,"sales");r.isValid||(s.orderNumber=r.errors,t.push(...r.errors))}if(!e.customerId){let e=["客户ID不能为空"];s.customerId=e,t.push(...e)}if(!e.customerName){let e=["客户名称不能为空"];s.customerName=e,t.push(...e)}if(e.items&&0!==e.items.length)e.items.forEach((e,r)=>{if(e.productCode){let a=this.validateProductCode(e.productCode);a.isValid||(s[`items.${r}.productCode`]=a.errors,t.push(...a.errors.map(e=>`第${r+1}项：${e}`)))}else{let e=[`第${r+1}项产品编码不能为空`];s[`items.${r}.productCode`]=e,t.push(...e)}let a=this.validateQuantity(e.quantity,`第${r+1}项数量`);a.isValid||(s[`items.${r}.quantity`]=a.errors,t.push(...a.errors))});else{let e=["订单项目不能为空"];s.items=e,t.push(...e)}if(e.deliveryDate){let r=this.validateDate(e.deliveryDate,"交货日期",{allowPast:!1,allowFuture:!0});r.isValid||(s.deliveryDate=r.errors,t.push(...r.errors))}return{isValid:0===t.length,errors:t,warnings:r.length>0?r:void 0,fieldErrors:Object.keys(s).length>0?s:void 0}}static validateProductionOrder(e){let t=[],r={};if(e.orderNumber){let s=this.validateOrderNumber(e.orderNumber,"production");s.isValid||(r.orderNumber=s.errors,t.push(...s.errors))}else{let e=["生产订单号不能为空"];r.orderNumber=e,t.push(...e)}if(e.productCode){let s=this.validateProductCode(e.productCode);s.isValid||(r.productCode=s.errors,t.push(...s.errors))}else{let e=["产品编码不能为空"];r.productCode=e,t.push(...e)}if(!e.productName){let e=["产品名称不能为空"];r.productName=e,t.push(...e)}if(void 0!==e.plannedQuantity){let s=this.validateQuantity(e.plannedQuantity,"计划数量");s.isValid||(r.plannedQuantity=s.errors,t.push(...s.errors))}else{let e=["计划数量不能为空"];r.plannedQuantity=e,t.push(...e)}return{isValid:0===t.length,errors:t,fieldErrors:Object.keys(r).length>0?r:void 0}}static createFormRules(e,t={}){let r=[];switch(e){case"orderNumber":if(r.push({required:!0,message:`请输入${t.fieldName||"订单号"}`}),t.orderType){let e,a;switch(t.orderType){case"sales":e=s.s.ORDER_NUMBER_PATTERNS.SALES_ORDER,a=`销售订单号格式错误，请使用格式：${s.s.ORDER_NUMBER_FORMATS.SALES_ORDER}`;break;case"production":e=s.s.ORDER_NUMBER_PATTERNS.PRODUCTION_ORDER,a=`生产订单号格式错误，请使用格式：${s.s.ORDER_NUMBER_FORMATS.PRODUCTION_ORDER}`;break;case"work":e=s.s.ORDER_NUMBER_PATTERNS.WORK_ORDER,a=`工单号格式错误，请使用格式：${s.s.ORDER_NUMBER_FORMATS.WORK_ORDER}`;break;default:e=/.*/,a="订单号格式错误"}r.push({pattern:e,message:a})}t.checkUniqueness&&r.push({validator:(e,r)=>r?t.checkUniqueness(r)?Promise.resolve():Promise.reject(Error("订单号已存在，请使用其他订单号")):Promise.resolve()});break;case"productCode":r.push({required:!0,message:"请输入产品编码"},{pattern:/^P\d{5}$/,message:"产品编码格式错误，请使用格式：P + 5位数字（如：P00001）"});break;case"quantity":r.push({required:!0,message:`请输入${t.fieldName||"数量"}`},{type:"number",min:t.min||.01,message:`${t.fieldName||"数量"}必须大于${t.min||0}`}),t.max&&r.push({type:"number",max:t.max,message:`${t.fieldName||"数量"}不能大于${t.max}`});break;case"date":r.push({required:!0,message:`请选择${t.fieldName||"日期"}`}),t.allowPast||r.push({validator:(e,r)=>{if(!r)return Promise.resolve();let s=new Date;return(s.setHours(0,0,0,0),new Date(r)<s)?Promise.reject(Error(`${t.fieldName||"日期"}不能是过去的日期`)):Promise.resolve()}});break;case"required":r.push({required:!0,message:`请输入${t.fieldName||"此字段"}`})}return r}}c.validateOrderNumber,c.validateProductCode,c.validateQuantity,c.validateDate,c.validateSalesOrder,c.validateProductionOrder,c.createFormRules,r(49221);var l=r(41517)},58357:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d});var s=r(37637);let a={in_plan:{allowedTransitions:["planned","cancelled"],triggers:{planned:"创建工单时自动转换",cancelled:"手动取消订单时"}},planned:{allowedTransitions:["in_progress","cancelled"],triggers:{in_progress:"当任一工单开始生产时",cancelled:"手动取消订单时"}},in_progress:{allowedTransitions:["completed","cancelled"],triggers:{completed:"当所有关联工单都完成时",cancelled:"手动取消订单时"}},completed:{allowedTransitions:[],triggers:{}},cancelled:{allowedTransitions:[],triggers:{}}},i={pending:{allowedTransitions:["scheduled","in_progress","cancelled"],triggers:{scheduled:"自动排程或手动排程",in_progress:"直接开始生产（跳过排程）",cancelled:"手动取消工单"}},scheduled:{allowedTransitions:["in_progress","cancelled","pending"],triggers:{in_progress:"按排程开始生产",cancelled:"取消排程和工单",pending:"取消排程，回到待开始状态"}},in_progress:{allowedTransitions:["completed","paused","cancelled","exception"],triggers:{completed:"生产完成",paused:"暂停生产",cancelled:"取消生产",exception:"生产过程中出现异常"}},paused:{allowedTransitions:["in_progress","cancelled","exception"],triggers:{in_progress:"恢复生产",cancelled:"取消生产",exception:"暂停期间出现异常"}},completed:{allowedTransitions:[],triggers:{}},cancelled:{allowedTransitions:[],triggers:{}},exception:{allowedTransitions:["pending","cancelled"],triggers:{pending:"异常处理完成，重新开始",cancelled:"异常无法处理，取消工单"}}},o=(e,t)=>-1!==a[e].allowedTransitions.indexOf(t),n=(e,t)=>-1!==i[e].allowedTransitions.indexOf(t),c=(e,t)=>i[e].triggers[t];class l{static{this.DEFAULT_CONFIG={enableAutoTransition:!0,enableErrorLogging:!0,throwOnError:!1,enablePerformanceTracking:!0,enablePreValidation:!0}}static async handleWorkOrderStatusChange(e,t={}){let r=Date.now(),a={...this.DEFAULT_CONFIG,...t},i={success:!1,orderStatusChanged:!1,timestamp:new Date().toISOString()};a.enableErrorLogging&&console.log(`🔄 [StatusTransitionManager] 开始处理工单状态转换:`,{workOrderId:e.workOrderId,statusChange:`${e.oldStatus} → ${e.newStatus}`,sourceOrderId:e.sourceOrderId,operatorId:e.operatorId});try{if(a.enablePreValidation){let t=await this.validateTransition(e);if(!t.canTransition)throw Error(`状态转换预检查失败: ${t.errors.join(", ")}`);t.expectedOrderStatusChange&&a.enableErrorLogging&&console.log(`📋 [StatusTransitionManager] 预期订单状态变更: ${e.sourceOrderId} → ${t.expectedOrderStatusChange}`)}if(!a.enableAutoTransition)return i.success=!0,a.enablePerformanceTracking&&(i.duration=Date.now()-r),i;let t=await s.dataAccessManager.productionWorkOrders.getAll();if("success"!==t.status||!t.data)throw Error(`获取工单数据失败: ${t.message||"未知错误"}`);let o=(t.data.items||[]).filter(t=>t.sourceOrderId===e.sourceOrderId);i.affectedWorkOrders=o.length,a.enableErrorLogging&&console.log(`📊 [StatusTransitionManager] 找到 ${o.length} 个关联工单`);let n=this.calculateOrderStatusFromWorkOrders(o,e);if(a.enableErrorLogging&&n&&console.log(`🎯 [StatusTransitionManager] 计算得出新订单状态: ${n}`),n){let t={status:n};e.operatorId&&(t.lastModifiedBy=e.operatorId),e.reason&&(t.statusChangeReason=e.reason);let r=await s.dataAccessManager.productionOrders.update(e.sourceOrderId,t);if("success"===r.status)i.orderStatusChanged=!0,i.newOrderStatus=n,a.enableErrorLogging&&console.log(`✅ [StatusTransitionManager] 订单状态自动转换成功: ${e.sourceOrderId} → ${n}`);else throw Error(`订单状态更新失败: ${r.message||"未知错误"}`)}return i.success=!0,a.enablePerformanceTracking&&(i.duration=Date.now()-r),a.enableErrorLogging&&console.log(`✅ [StatusTransitionManager] 状态转换完成`,{workOrderId:e.workOrderId,orderStatusChanged:i.orderStatusChanged,newOrderStatus:i.newOrderStatus,duration:i.duration,affectedWorkOrders:i.affectedWorkOrders}),i}catch(s){let t=s instanceof Error?s.message:"未知错误";if(i.error=t,i.success=!1,a.enablePerformanceTracking&&(i.duration=Date.now()-r),a.enableErrorLogging&&console.error(`❌ [StatusTransitionManager] 状态转换失败:`,{workOrderId:e.workOrderId,statusChange:`${e.oldStatus} → ${e.newStatus}`,sourceOrderId:e.sourceOrderId,error:t,duration:i.duration,stack:s instanceof Error?s.stack:void 0}),a.throwOnError)throw s;return i}}static async handleWorkOrderCreation(e,t,r={}){let a=Date.now(),i={...this.DEFAULT_CONFIG,...r},n={success:!1,orderStatusChanged:!1,timestamp:new Date().toISOString()};i.enableErrorLogging&&console.log(`🔄 [StatusTransitionManager] 开始处理工单创建状态转换:`,{orderId:e,currentStatus:t});try{if(!i.enableAutoTransition)return n.success=!0,i.enablePerformanceTracking&&(n.duration=Date.now()-a),n;if(i.enablePreValidation&&"in_plan"===t&&!o(t,"planned"))throw Error(`无效的状态转换: ${t} → planned`);if("in_plan"===t){let t=await s.dataAccessManager.productionOrders.update(e,{status:"planned"});if("success"===t.status)n.orderStatusChanged=!0,n.newOrderStatus="planned",i.enableErrorLogging&&console.log(`✅ [StatusTransitionManager] 工单创建状态转换成功: ${e} → planned`);else throw Error(`订单状态更新失败: ${t.message||"未知错误"}`)}return n.success=!0,i.enablePerformanceTracking&&(n.duration=Date.now()-a),n}catch(s){let r=s instanceof Error?s.message:"未知错误";if(n.error=r,n.success=!1,i.enablePerformanceTracking&&(n.duration=Date.now()-a),i.enableErrorLogging&&console.error(`❌ [StatusTransitionManager] 工单创建状态转换失败:`,{orderId:e,currentStatus:t,error:r,duration:n.duration,stack:s instanceof Error?s.stack:void 0}),i.throwOnError)throw s;return n}}static async handleBatchWorkOrderCreationTransition(e,t={}){let r=Date.now(),s={...this.DEFAULT_CONFIG,...t},a=[];s.enableErrorLogging&&console.log(`🚀 [StatusTransitionManager] 开始批量工单创建状态转换:`,{orderCount:e.orders.length,operatorId:e.operatorId,reason:e.reason});for(let r=0;r<e.orders.length;r+=5){let i=e.orders.slice(r,r+5).map(async e=>{let r=await this.handleWorkOrderCreation(e.id,e.status,{...t,enableErrorLogging:!1});return{orderId:e.id,orderNumber:e.orderNumber,result:r}}),o=await Promise.all(i);if(a.push(...o),s.enableErrorLogging){let t=Math.min(r+5,e.orders.length);console.log(`📊 [StatusTransitionManager] 批量处理进度: ${t}/${e.orders.length}`)}}let i=a.filter(e=>e.result.success).length,o=a.length-i,n=a.filter(e=>e.result.orderStatusChanged).length,c=Date.now()-r;return s.enableErrorLogging&&console.log(`✅ [StatusTransitionManager] 批量转换完成:`,{total:a.length,successful:i,failed:o,statusChanged:n,duration:c,avgDuration:Math.round(c/a.length)}),a}static getTransitionStatistics(e){let t=e.length,r=e.filter(e=>e.success).length,s=e.filter(e=>e.orderStatusChanged).length,a=e.filter(e=>e.duration).map(e=>e.duration),i=a.reduce((e,t)=>e+t,0),o=a.length>0?i/a.length:0,n={};e.filter(e=>e.error).forEach(e=>{let t=e.error.split(":")[0];n[t]=(n[t]||0)+1});let c=a.sort((e,t)=>e-t);return{total:t,successful:r,failed:t-r,orderStatusChanged:s,successRate:Math.round(100*(t>0?r/t*100:0))/100,averageDuration:Math.round(o),totalDuration:i,errorTypes:n,performanceMetrics:{fastest:c[0]||0,slowest:c[c.length-1]||0,median:c[Math.floor(c.length/2)]||0}}}static async validateTransition(e){let t;let r=[],a=[];if(e.workOrderId?.trim()||r.push("工单ID不能为空"),e.sourceOrderId?.trim()||r.push("源订单ID不能为空"),e.oldStatus||r.push("原状态不能为空"),e.newStatus||r.push("新状态不能为空"),e.oldStatus===e.newStatus&&r.push("原状态与新状态不能相同"),e.oldStatus&&e.newStatus){if(n(e.oldStatus,e.newStatus)){let t=c(e.oldStatus,e.newStatus);t&&a.push(`状态转换触发条件: ${t}`)}else r.push(`无效的工单状态转换: ${e.oldStatus} → ${e.newStatus}`)}if(0===r.length)try{let r=await s.dataAccessManager.productionWorkOrders.getAll();if("success"===r.status&&r.data){let s=(r.data.items||[]).filter(t=>t.sourceOrderId===e.sourceOrderId),i=s.map(t=>t.id===e.workOrderId?{...t,status:e.newStatus}:t);t=this.calculateOrderStatusFromWorkOrders(i,e)||void 0;let o=s.filter(e=>"in_progress"===e.status||"scheduled"===e.status);o.length>5&&a.push(`该订单有 ${o.length} 个活跃工单，建议注意并发操作`)}else a.push("无法获取工单数据进行深度验证")}catch(e){a.push(`业务规则验证时发生错误: ${e instanceof Error?e.message:"未知错误"}`)}return{canTransition:0===r.length,errors:r,warnings:a,expectedOrderStatusChange:t}}static validateTransitionContext(e){let t=[];return e.workOrderId?.trim()||t.push("工单ID不能为空"),e.sourceOrderId?.trim()||t.push("源订单ID不能为空"),e.oldStatus||t.push("原状态不能为空"),e.newStatus||t.push("新状态不能为空"),e.oldStatus===e.newStatus&&t.push("原状态与新状态不能相同"),{isValid:0===t.length,errors:t}}static calculateOrderStatusFromWorkOrders(e,t){if(0===e.length)return null;let r={pending:e.filter(e=>"pending"===e.status).length,scheduled:e.filter(e=>"scheduled"===e.status).length,in_progress:e.filter(e=>"in_progress"===e.status).length,completed:e.filter(e=>"completed"===e.status).length,paused:e.filter(e=>"paused"===e.status).length,cancelled:e.filter(e=>"cancelled"===e.status).length,exception:e.filter(e=>"exception"===e.status).length},s=e.length;return r.completed===s?"completed":r.in_progress>0||r.paused>0?"in_progress":r.scheduled>0?null:r.pending>0?null:r.cancelled===s?"cancelled":null}static canTransition(e,t){return e!==t&&n(e,t)}static generateTransitionId(e){let t=Date.now(),r=`${e.workOrderId}-${e.oldStatus}-${e.newStatus}-${t}`;return`TXN-${r.substring(0,16).toUpperCase()}`}}let d={handleWorkOrderStatusChange:l.handleWorkOrderStatusChange,handleWorkOrderCreation:l.handleWorkOrderCreation,handleBatchCreation:l.handleBatchWorkOrderCreationTransition,validateTransition:l.validateTransition,getStatistics:l.getTransitionStatistics,validateContext:l.validateTransitionContext,canTransition:l.canTransition,generateTransitionId:l.generateTransitionId}},62158:(e,t,r)=>{"use strict";r.d(t,{_:()=>a});class s{static{this.DEFAULT_CONFIG={format:"iso"}}static generateCurrentTimestamp(){return new Date().toISOString()}static generateCreationTimestamps(){let e=this.generateCurrentTimestamp();return{createdAt:e,updatedAt:e}}static generateUpdateTimestamp(){return{updatedAt:this.generateCurrentTimestamp()}}static generateBatchTimestamps(e){let t=this.generateCurrentTimestamp(),r=[];for(let s=0;s<e;s++)r.push({createdAt:t,updatedAt:t});return{timestamps:r,generatedAt:t,count:e}}static addCreationTimestamps(e){let t=this.generateCreationTimestamps();return{...e,...t}}static addUpdateTimestamp(e){let t=this.generateUpdateTimestamp();return{...e,...t}}static addBatchCreationTimestamps(e){let t=this.generateBatchTimestamps(e.length);return e.map((e,r)=>({...e,...t.timestamps[r]}))}static validateTimestamp(e){try{let t=new Date(e);return!isNaN(t.getTime())&&e===t.toISOString()}catch{return!1}}static getTimestampStatistics(){return{currentTime:this.generateCurrentTimestamp(),timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,format:"ISO 8601"}}}let a={now:()=>s.generateCurrentTimestamp(),creation:()=>s.generateCreationTimestamps(),update:()=>s.generateUpdateTimestamp(),addCreation:e=>s.addCreationTimestamps(e),addUpdate:e=>s.addUpdateTimestamp(e),addBatchCreation:e=>s.addBatchCreationTimestamps(e),validate:e=>s.validateTimestamp(e),getStats:()=>s.getTimestampStatistics()}},49221:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var s=r(37637),a=r(51178);class i{static{this.DEFAULT_CONFIG={enableRollback:!0,enableDataSync:!0,enableLogging:!0,timeoutMs:3e4}}static{this.activeTransactions=new Map}static async beginTransaction(e,t){let r=`tx-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,s={transactionId:r,operations:e,metadata:t,createdAt:new Date().toISOString()};return this.activeTransactions.set(r,s),console.log(`🔄 [TransactionManager] 开始事务: ${r}, 操作数量: ${e.length}`),r}static async executeTransaction(e,t={}){let r={...this.DEFAULT_CONFIG,...t},s=this.activeTransactions.get(e);if(!s)throw Error(`事务不存在: ${e}`);let a={success:!1,transactionId:e,completedOperations:0,totalOperations:s.operations.length,errors:[]},i=[];try{let t=new Promise((e,t)=>{setTimeout(()=>t(Error("事务执行超时")),r.timeoutMs)}),o=this.executeOperations(s.operations,i,r);await Promise.race([o,t]),a.completedOperations=s.operations.length,a.success=!0,r.enableDataSync&&await this.triggerDataSyncEvents(s),r.enableLogging&&console.log(`✅ [TransactionManager] 事务执行成功: ${e}`)}catch(s){let t=s instanceof Error?s.message:"未知错误";if(a.errors.push(t),r.enableLogging&&console.error(`❌ [TransactionManager] 事务执行失败: ${e}`,s),r.enableRollback&&i.length>0)try{await this.executeRollback(i,r),r.enableLogging&&console.log(`🔄 [TransactionManager] 事务回滚成功: ${e}`)}catch(s){let t=s instanceof Error?s.message:"回滚失败";a.errors.push(`回滚失败: ${t}`),r.enableLogging&&console.error(`❌ [TransactionManager] 事务回滚失败: ${e}`,s)}a.rollbackData=i}finally{this.activeTransactions.delete(e)}return a}static async executeOperations(e,t,r){for(let s of e)await this.executeOperation(s,t,r)}static async executeOperation(e,t,r){let{type:s,entity:a,entityId:i,data:o,originalData:n}=e;try{switch(a){case"salesOrder":await this.executeSalesOrderOperation(s,i,o,n,t);break;case"productionOrder":await this.executeProductionOrderOperation(s,i,o,n,t);break;case"workOrder":await this.executeWorkOrderOperation(s,i,o,n,t);break;default:throw Error(`不支持的实体类型: ${a}`)}r.enableLogging&&console.log(`✅ [TransactionManager] 操作执行成功: ${e.id} (${s} ${a})`)}catch(t){throw r.enableLogging&&console.error(`❌ [TransactionManager] 操作执行失败: ${e.id}`,t),t}}static async executeSalesOrderOperation(e,t,r,a,i){switch(e){case"update":let o=await s.dataAccessManager.orders.update(t,r);if("success"!==o.status)throw Error(`销售订单更新失败: ${o.message}`);a&&i.push({type:"update",entity:"salesOrder",entityId:t,data:a});break;case"delete":throw Error("销售订单删除操作暂不支持");default:throw Error(`不支持的销售订单操作: ${e}`)}}static async executeProductionOrderOperation(e,t,r,a,i){if("update"===e){let e=await s.dataAccessManager.productionOrders.update(t,r);if("success"!==e.status)throw Error(`生产订单更新失败: ${e.message}`);a&&i.push({type:"update",entity:"productionOrder",entityId:t,data:a})}else throw Error(`不支持的生产订单操作: ${e}`)}static async executeWorkOrderOperation(e,t,r,a,i){if("update"===e){let e=await s.dataAccessManager.productionWorkOrders.update(t,r);if("success"!==e.status)throw Error(`工单更新失败: ${e.message}`);a&&i.push({type:"update",entity:"workOrder",entityId:t,data:a})}else throw Error(`不支持的工单操作: ${e}`)}static async executeRollback(e,t){for(let r=e.length-1;r>=0;r--){let a=e[r];try{switch(a.entity){case"salesOrder":await s.dataAccessManager.orders.update(a.entityId,a.data);break;case"productionOrder":await s.dataAccessManager.productionOrders.update(a.entityId,a.data);break;case"workOrder":await s.dataAccessManager.productionWorkOrders.update(a.entityId,a.data)}t.enableLogging&&console.log(`🔄 [TransactionManager] 回滚操作成功: ${a.entity} ${a.entityId}`)}catch(e){throw t.enableLogging&&console.error(`❌ [TransactionManager] 回滚操作失败: ${a.entity} ${a.entityId}`,e),e}}}static async triggerDataSyncEvents(e){let t=new Set;for(let r of e.operations)switch(r.entity){case"salesOrder":t.add("orders"),t.add("production");break;case"productionOrder":t.add("production"),t.add("workOrders");break;case"workOrder":t.add("workOrders"),t.add("scheduling")}for(let r of t)a.dataSyncService.addChangeEvent({module:r,entityType:"transaction",entityId:e.transactionId,type:"update",newData:{transactionId:e.transactionId,operationCount:e.operations.length,affectedEntities:e.operations.map(e=>({entity:e.entity,entityId:e.entityId,type:e.type}))}})}static getActiveTransactionStats(){let e=new Date().getTime(),t=Array.from(this.activeTransactions.values()).map(t=>({id:t.transactionId,operationCount:t.operations.length,createdAt:t.createdAt,age:e-new Date(t.createdAt).getTime()}));return{count:t.length,transactions:t}}static cleanupTimeoutTransactions(e=3e5){let t=new Date().getTime(),r=0;for(let[s,a]of this.activeTransactions.entries()){let i=t-new Date(a.createdAt).getTime();i>e&&(this.activeTransactions.delete(s),r++,console.warn(`🧹 [TransactionManager] 清理超时事务: ${s}, 年龄: ${i}ms`))}return r}static generateTransactionId(){let e=Date.now(),t=Math.random().toString(36).substring(2,15);return`TXN-${e}-${t.toUpperCase()}`}}let o={begin:i.beginTransaction,execute:i.executeTransaction,getStats:i.getActiveTransactionStats,cleanup:i.cleanupTimeoutTransactions,generateTransactionId:i.generateTransactionId}},69915:(e,t,r)=>{"use strict";r.d(t,{AO:()=>n});class s{constructor(e){this.running=0,this.queue=[],this.taskId=0,this.config={enableLogging:!1,...e},this.config.enableLogging&&console.log("[ConcurrencyController] 并发控制器已创建",{maxConcurrent:this.config.maxConcurrent,retryAttempts:this.config.retryAttempts,retryDelay:this.config.retryDelay})}async execute(e,t){return new Promise((r,s)=>{let a=++this.taskId,i=t||`Task-${a}`,o=async()=>{let t=Date.now();try{this.running++,this.config.enableLogging&&console.log(`[ConcurrencyController] 开始执行任务: ${i} (并发数: ${this.running}/${this.config.maxConcurrent})`);let s=await this.executeWithRetry(e,i),a=Date.now()-t;this.config.enableLogging&&console.log(`[ConcurrencyController] 任务完成: ${i} (耗时: ${a}ms)`),r(s)}catch(a){let e=Date.now()-t,r=a instanceof Error?a.message:String(a);this.config.enableLogging&&console.error(`[ConcurrencyController] 任务失败: ${i} (耗时: ${e}ms, 错误: ${r})`),s(a)}finally{this.running--,this.processQueue()}};this.running<this.config.maxConcurrent?o():(this.config.enableLogging&&console.log(`[ConcurrencyController] 任务排队: ${i} (队列长度: ${this.queue.length+1})`),this.queue.push(o))})}async executeBatch(e,t){let r=Date.now(),s=[];this.config.enableLogging&&console.log(`[ConcurrencyController] 开始批量执行 ${e.length} 个任务`);let a=e.map(async(e,r)=>{let a=t?.[r]||`BatchTask-${r+1}`,i=Date.now();try{let t=await this.execute(e,a),r=Date.now()-i,o={success:!0,data:t,duration:r,retryCount:0};return s.push(o),o}catch(r){let e=Date.now()-i,t={success:!1,error:r instanceof Error?r.message:String(r),duration:e,retryCount:0};return s.push(t),t}}),i=await Promise.allSettled(a),o=[],n=[];i.forEach(e=>{"fulfilled"===e.status?e.value.success?o.push(e.value):n.push(e.value):n.push({success:!1,error:e.reason?.message||"Unknown error",duration:0,retryCount:0})});let c=Date.now()-r,l=o.length/e.length;return this.config.enableLogging&&console.log(`[ConcurrencyController] 批量执行完成`,{总任务数:e.length,成功数:o.length,失败数:n.length,成功率:`${(100*l).toFixed(2)}%`,总耗时:`${c}ms`}),{successful:o,failed:n,totalDuration:c,successRate:l}}async executeWithRetry(e,t){let r;for(let s=0;s<=this.config.retryAttempts;s++)try{if(this.config.timeout)return await this.withTimeout(e(),this.config.timeout);return await e()}catch(e){if(r=e,s<this.config.retryAttempts){let e=this.config.retryDelay*(s+1);this.config.enableLogging&&console.warn(`[ConcurrencyController] 任务重试: ${t} (第${s+1}次重试，${e}ms后重试)`),await new Promise(t=>setTimeout(t,e))}}throw r}async withTimeout(e,t){return Promise.race([e,new Promise((e,r)=>setTimeout(()=>r(Error(`任务超时 (${t}ms)`)),t))])}processQueue(){if(this.queue.length>0&&this.running<this.config.maxConcurrent){let e=this.queue.shift();e&&e()}}getStatus(){return{running:this.running,queued:this.queue.length,maxConcurrent:this.config.maxConcurrent,config:this.config}}clearQueue(){let e=this.queue.length;return this.queue=[],this.config.enableLogging&&e>0&&console.log(`[ConcurrencyController] 已清空队列，删除了 ${e} 个待执行任务`),e}updateConfig(e){let t={...this.config};this.config={...this.config,...e},this.config.enableLogging&&console.log("[ConcurrencyController] 配置已更新",{old:t,new:this.config})}}let a=null,i=null,o=null,n=new s({maxConcurrent:5,retryAttempts:2,retryDelay:1e3,timeout:3e4,enableLogging:!1});i||(i=new s({maxConcurrent:10,retryAttempts:1,retryDelay:500,timeout:1e4,enableLogging:!1})),o||(o=new s({maxConcurrent:2,retryAttempts:3,retryDelay:2e3,timeout:6e4,enableLogging:!1}))},19101:(e,t,r)=>{"use strict";r.d(t,{E4:()=>a,zX:()=>i});let s={code:[{required:!0,message:"请输入工位编码"},{pattern:/^[A-Z]\d{1,3}$/,message:"工位编码格式：字母+数字，如A1、B123"},{max:10,message:"工位编码过长：最多10个字符"}],name:[{required:!0,message:"请输入工位名称"},{max:50,message:"工位名称过长：最多50个字符"}],description:[{max:200,message:"工位描述过长：最多200个字符"}],status:[{pattern:/^(active|inactive|maintenance|error)$/,message:"工位状态无效"}]};function a(e){let t=[];for(let r of s.code){if(r.required&&!e.code){t.push(r.message);break}r.pattern&&e.code&&!r.pattern.test(e.code)&&t.push(r.message),r.max&&e.code&&e.code.length>r.max&&t.push(r.message)}for(let r of s.name){if(r.required&&!e.name){t.push(r.message);break}r.max&&e.name&&e.name.length>r.max&&t.push(r.message)}if(e.description)for(let r of s.description||[])r.max&&e.description.length>r.max&&t.push(r.message);if(e.status)for(let r of s.status||[])r.pattern&&!r.pattern.test(e.status)&&t.push(r.message);return{isValid:0===t.length,errors:t}}function i(e){let t=s[e];return t?t.map(e=>({required:e.required,pattern:e.pattern,min:e.min,max:e.max,message:e.message})):[]}},14499:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>u});var s=r(25036),a=r(53640),i=r.n(a);r(5023);var o=r(86843);let n=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\components\providers\AntdProvider.tsx`),{__esModule:c,$$typeof:l}=n;n.default;let d=(0,o.createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\components\providers\AntdProvider.tsx#AntdProvider`);r(86274);let u={title:"ERP管理系统",description:"现代化企业资源规划管理系统"};function h({children:e}){return s.jsx("html",{lang:"zh-CN",children:s.jsx("body",{className:i().className,children:s.jsx(d,{children:e})})})}},5023:()=>{}};