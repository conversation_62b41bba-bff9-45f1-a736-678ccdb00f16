(()=>{var e={};e.id=2735,e.ids=[2735],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},13608:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>o});var r=s(50482),l=s(69108),c=s(62563),a=s.n(c),n=s(68300),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);s.d(t,i);let o=["",{children:["warehouse",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,80895)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\products\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,76485)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\products\\page.tsx"],u="/warehouse/products/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/warehouse/products/page",pathname:"/warehouse/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},93565:(e,t,s)=>{Promise.resolve().then(s.bind(s,18088))},3448:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(65651),l=s(3729);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M193 796c0 17.7 14.3 32 32 32h574c17.7 0 32-14.3 32-32V563c0-176.2-142.8-319-319-319S193 386.8 193 563v233zm72-233c0-136.4 110.6-247 247-247s247 110.6 247 247v193H404V585c0-5.5-4.5-10-10-10h-44c-5.5 0-10 4.5-10 10v171h-75V563zm-48.1-252.5l39.6-39.6c3.1-3.1 3.1-8.2 0-11.3l-67.9-67.9a8.03 8.03 0 00-11.3 0l-39.6 39.6a8.03 8.03 0 000 11.3l67.9 67.9c3.1 3.1 8.1 3.1 11.3 0zm669.6-79.2l-39.6-39.6a8.03 8.03 0 00-11.3 0l-67.9 67.9a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l67.9-67.9c3.1-3.2 3.1-8.2 0-11.3zM832 892H192c-17.7 0-32 14.3-32 32v24c0 4.4 3.6 8 8 8h688c4.4 0 8-3.6 8-8v-24c0-17.7-14.3-32-32-32zM484 180h56c4.4 0 8-3.6 8-8V76c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v96c0 4.4 3.6 8 8 8z"}}]},name:"alert",theme:"outlined"};var a=s(49809);let n=l.forwardRef(function(e,t){return l.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:c}))})},55741:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(65651),l=s(3729);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};var a=s(49809);let n=l.forwardRef(function(e,t){return l.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:c}))})},3745:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(65651),l=s(3729);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var a=s(49809);let n=l.forwardRef(function(e,t){return l.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:c}))})},54649:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(65651),l=s(3729);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var a=s(49809);let n=l.forwardRef(function(e,t){return l.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:c}))})},15595:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(65651),l=s(3729);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"};var a=s(49809);let n=l.forwardRef(function(e,t){return l.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:c}))})},89645:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(65651),l=s(3729);let c={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var a=s(49809);let n=l.forwardRef(function(e,t){return l.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:c}))})},94505:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(65651),l=s(3729);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};var a=s(49809);let n=l.forwardRef(function(e,t){return l.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:c}))})},96291:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(65651),l=s(3729);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var a=s(49809);let n=l.forwardRef(function(e,t){return l.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:c}))})},37372:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(65651),l=s(3729);let c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var a=s(49809);let n=l.forwardRef(function(e,t){return l.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:c}))})},18088:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>U});var r=s(95344),l=s(3729),c=s(97854),a=s(97557),n=s(32979),i=s(7618),o=s(39470),d=s(87049),u=s(10707),x=s(51410),h=s(11157),p=s(63724),m=s(27976),j=s(83984),v=s(43896),f=s(284),g=s(36527),Z=s(16408),y=s(6025),w=s(37372),C=s(3448),S=s(15595),k=s(3745),P=s(94505),b=s(54649),z=s(46116),N=s(2778),M=s(70469),V=s(96291),q=s(55741),L=s(89645),_=s(58535),E=s(48869),I=s.n(E),H=s(37637),R=s(51221);let{Option:A}=c.default,{RangePicker:$}=a.default,B=()=>{let{message:e,modal:t}=n.Z.useApp(),[s,a]=(0,l.useState)(!1),[E,$]=(0,l.useState)([]),[B,U]=(0,l.useState)(!1),[D,F]=(0,l.useState)(null),[T,G]=(0,l.useState)(""),[O,Q]=(0,l.useState)(void 0),[J,K]=(0,l.useState)([]),[W,X]=(0,l.useState)({totalValue:0,totalProducts:0,lowStockCount:0,outOfStockCount:0}),[Y]=i.Z.useForm(),ee=async()=>{a(!0);try{let e=await (0,R.Ro)(()=>H.dataAccessManager.inventory.getAll(),"获取库存数据");if(e&&e.items){let t=e.items.map(e=>({id:e.id||`inv-${e.productCode}`,productCode:e.productCode,productName:e.productName||e.productCode,productModel:e.productModel||"",category:e.category||"未分类",currentStock:e.currentStock||0,reservedStock:e.reservedStock||0,availableStock:(e.currentStock||0)-(e.reservedStock||0),safetyStock:e.safetyStock||0,unitCost:e.unitCost||0,totalValue:(e.currentStock||0)*(e.unitCost||0),lastUpdated:e.lastUpdated||new Date().toISOString(),location:e.location||"默认仓库",supplier:e.supplier||"",status:et(e)}));K(t);let s=es(t);X(s)}}catch(t){console.error("加载库存数据失败:",t),e.error("加载库存数据失败")}finally{a(!1)}},et=e=>{let t=e.currentStock||0,s=e.safetyStock||0;return 0===t?"shortage":t<s?"warning":t>3*s?"excess":"normal"},es=e=>({totalValue:e.reduce((e,t)=>e+t.totalValue,0),totalProducts:e.length,lowStockCount:e.filter(e=>"warning"===e.status).length,outOfStockCount:e.filter(e=>"shortage"===e.status).length});(0,l.useEffect)(()=>{ee()},[]);let er=e=>{let{currentStock:t,safetyStock:s,status:l}=e,c=3*s;switch(l){case"shortage":return{color:"red",text:"库存不足",icon:r.jsx(w.Z,{}),percentage:c>0?t/c*100:0};case"excess":return{color:"orange",text:"库存过多",icon:r.jsx(C.Z,{}),percentage:c>0?t/c*100:0};case"warning":return{color:"volcano",text:"库存预警",icon:r.jsx(S.Z,{}),percentage:c>0?t/c*100:0};default:return{color:"green",text:"正常",icon:r.jsx(k.Z,{}),percentage:c>0?t/c*100:0}}},el=e=>{ec(e)},ec=e=>{t.info({title:"产品库存详情",width:600,content:r.jsx("div",{className:"space-y-4",children:(0,r.jsxs)(p.Z,{gutter:[16,16],children:[r.jsx(m.Z,{span:12,children:(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"产品编码:"})," ",e.productCode]})}),r.jsx(m.Z,{span:12,children:(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"产品名称:"})," ",e.productName]})}),r.jsx(m.Z,{span:12,children:(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"产品型号:"})," ",e.productModel]})}),r.jsx(m.Z,{span:12,children:(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"分类:"})," ",e.category]})}),r.jsx(m.Z,{span:12,children:(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"当前库存:"})," ",e.currentStock]})}),r.jsx(m.Z,{span:12,children:(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"安全库存:"})," ",e.safetyStock]})}),r.jsx(m.Z,{span:12,children:(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"库位:"})," ",e.location]})}),r.jsx(m.Z,{span:12,children:(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"可用库存:"})," ",e.availableStock]})}),r.jsx(m.Z,{span:12,children:(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"预留库存:"})," ",e.reservedStock]})}),r.jsx(m.Z,{span:12,children:(0,r.jsxs)("div",{children:[r.jsx("strong",{children:"供应商:"})," ",e.supplier]})})]})})})},ea=e=>{F(e),Y.setFieldsValue({productCode:e.productCode,productName:e.productName,currentStock:e.currentStock,adjustmentType:"adjustment"}),U(!0)},en=async()=>{try{a(!0),await ee(),e.success("数据同步成功")}catch(t){console.error("数据同步失败:",t),e.error("数据同步失败，请稍后重试")}finally{a(!1)}},ei=async()=>{try{a(!0);let t=J.filter(e=>!e.productCode||!e.productName||e.currentStock<0||e.unitCost<0);0===t.length?e.success("数据验证通过，所有产品数据一致"):(e.warning(`发现 ${t.length} 个数据不一致问题`),console.log("数据验证问题:",t))}catch(t){console.error("数据验证失败:",t),e.error("数据验证失败，请稍后重试")}finally{a(!1)}},eo={totalProducts:J.length,totalValue:W.totalValue,lowStockCount:W.lowStockCount,highStockCount:J.filter(e=>"excess"===e.status).length,normalStockCount:J.filter(e=>"normal"===e.status).length,validPriceCount:J.filter(e=>e.unitCost>0).length,invalidPriceCount:J.filter(e=>0===e.unitCost).length};return(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("div",{className:"page-header",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(N.Z,{className:"text-2xl text-blue-600 mr-3"}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"page-title",children:"产品库存管理"}),r.jsx("p",{className:"page-description",children:"管理成品库存，包括产品入库、出库、库存查询、库存预警等"})]})]})}),(0,r.jsxs)(p.Z,{gutter:[16,16],children:[r.jsx(m.Z,{xs:24,sm:6,children:r.jsx(j.Z,{children:r.jsx(v.Z,{title:"产品总数",value:eo.totalProducts,suffix:"种",prefix:r.jsx(N.Z,{}),valueStyle:{color:"#1890ff"}})})}),r.jsx(m.Z,{xs:24,sm:6,children:r.jsx(j.Z,{children:r.jsx(v.Z,{title:"库存总值",value:eo.totalValue,precision:2,prefix:"\xa5",valueStyle:{color:"#52c41a"}})})}),r.jsx(m.Z,{xs:24,sm:6,children:r.jsx(j.Z,{children:r.jsx(v.Z,{title:"库存不足",value:eo.lowStockCount,suffix:"种",valueStyle:{color:"#ff4d4f"},prefix:r.jsx(w.Z,{})})})}),r.jsx(m.Z,{xs:24,sm:6,children:r.jsx(j.Z,{children:r.jsx(v.Z,{title:"库存过多",value:eo.highStockCount,suffix:"种",valueStyle:{color:"#fa8c16"},prefix:r.jsx(C.Z,{})})})})]}),(0,r.jsxs)(p.Z,{gutter:[16,16],children:[r.jsx(m.Z,{xs:24,sm:6,children:r.jsx(j.Z,{children:r.jsx(v.Z,{title:"有效价格",value:eo.validPriceCount,suffix:`/ ${eo.totalProducts}`,valueStyle:{color:eo.validPriceCount===eo.totalProducts?"#52c41a":"#fa8c16"},prefix:r.jsx(k.Z,{})})})}),r.jsx(m.Z,{xs:24,sm:6,children:r.jsx(j.Z,{children:r.jsx(v.Z,{title:"价格缺失",value:eo.invalidPriceCount,suffix:"种",valueStyle:{color:eo.invalidPriceCount>0?"#ff4d4f":"#52c41a"},prefix:r.jsx(S.Z,{})})})}),r.jsx(m.Z,{xs:24,sm:12,children:r.jsx(j.Z,{children:(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,r.jsxs)("div",{children:[r.jsx("div",{style:{fontSize:"14px",color:"#666"},children:"数据一致性"}),(0,r.jsxs)("div",{style:{fontSize:"24px",fontWeight:"bold",color:eo.validPriceCount===eo.totalProducts?"#52c41a":"#fa8c16"},children:[eo.totalProducts>0?Math.round(eo.validPriceCount/eo.totalProducts*100):0,"%"]})]}),(0,r.jsxs)("div",{style:{fontSize:"12px",color:"#999"},children:[(0,r.jsxs)("div",{children:["✅ 价格有效: ",eo.validPriceCount]}),(0,r.jsxs)("div",{children:["❌ 价格缺失: ",eo.invalidPriceCount]})]})]})})})]}),r.jsx(j.Z,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[r.jsx(f.default,{placeholder:"搜索产品编码、名称或型号",prefix:r.jsx(M.Z,{}),value:T,onChange:e=>G(e.target.value),className:"w-full sm:w-64"}),(0,r.jsxs)(c.default,{placeholder:"库存状态",value:O,onChange:Q,className:"w-full sm:w-32",allowClear:!0,children:[r.jsx(A,{value:"",children:"全部状态"}),r.jsx(A,{value:"normal",children:"正常"}),r.jsx(A,{value:"warning",children:"库存预警"}),r.jsx(A,{value:"shortage",children:"库存不足"}),r.jsx(A,{value:"excess",children:"库存过多"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(h.ZP,{icon:r.jsx(V.Z,{}),onClick:()=>ee(),children:"刷新"}),r.jsx(h.ZP,{icon:r.jsx(q.Z,{}),children:"库存报表"}),r.jsx(h.ZP,{icon:r.jsx(L.Z,{}),children:"导出数据"}),r.jsx(h.ZP,{icon:r.jsx(V.Z,{}),onClick:en,loading:s,children:"同步数据"}),r.jsx(h.ZP,{icon:r.jsx(C.Z,{}),onClick:ei,loading:s,children:"数据验证"}),r.jsx(h.ZP,{type:"primary",icon:r.jsx(_.Z,{}),children:"产品入库"})]})]}),r.jsx(g.Z,{columns:[{title:"产品编码",dataIndex:"productCode",key:"productCode",width:120,fixed:"left",render:e=>r.jsx("span",{className:"font-mono text-blue-600",children:e})},{title:"产品名称",key:"productName",width:200,render:(e,t)=>(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:t.productName}),(0,r.jsxs)("div",{className:"text-gray-500 text-sm",children:["型号: ",t.productModel]}),(0,r.jsxs)("div",{className:"text-gray-400 text-xs",children:["分类: ",t.category]})]})},{title:"当前库存",dataIndex:"currentStock",key:"currentStock",width:120,render:(e,t)=>{let s=er(t);return(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"text-lg font-bold",children:e.toLocaleString()}),r.jsx(o.Z,{status:s.color,text:s.text})]})}},{title:"库存水位",key:"stockLevel",width:150,render:(e,t)=>{let s=er(t);return(0,r.jsxs)("div",{children:[r.jsx(d.Z,{percent:s.percentage,size:"small",status:"normal"===t.status?"success":"exception",format:()=>`${t.currentStock}/${3*t.safetyStock}`}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["安全库存: ",t.safetyStock]})]})}},{title:"库存价值",key:"value",width:120,render:(e,t)=>(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"font-medium",children:["\xa5",t.totalValue.toLocaleString()]}),(0,r.jsxs)("div",{className:"text-gray-500 text-sm",children:["单价: \xa5",t.unitCost.toLocaleString()]}),0===t.unitCost&&r.jsx("div",{className:"text-red-400 text-xs",children:"价格未设置"})]})},{title:"库位信息",key:"locationInfo",width:150,render:(e,t)=>(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:t.location}),(0,r.jsxs)("div",{className:"text-gray-500 text-sm",children:["供应商: ",t.supplier]})]})},{title:"可用库存",dataIndex:"availableStock",key:"availableStock",width:100,render:e=>r.jsx("span",{className:"font-medium text-blue-600",children:e.toLocaleString()})},{title:"最后更新",dataIndex:"lastUpdated",key:"lastUpdated",width:140,render:e=>r.jsx("span",{className:"text-gray-500",children:I()(e).format("MM-DD HH:mm")})},{title:"操作",key:"action",width:180,fixed:"right",render:(e,t)=>(0,r.jsxs)(u.Z,{size:"small",children:[r.jsx(x.Z,{title:"库存调整",children:r.jsx(h.ZP,{type:"text",icon:r.jsx(P.Z,{}),size:"small",onClick:()=>ea(t),children:"调整"})}),r.jsx(x.Z,{title:"编辑信息",children:r.jsx(h.ZP,{type:"text",icon:r.jsx(b.Z,{}),size:"small",onClick:()=>el(t),children:"编辑"})}),r.jsx(x.Z,{title:"查看详情",children:r.jsx(h.ZP,{type:"text",icon:r.jsx(z.Z,{}),size:"small",onClick:()=>ec(t),children:"详情"})})]})}],dataSource:J.filter(e=>{let t=!T||e.productCode.toLowerCase().includes(T.toLowerCase())||e.productName.toLowerCase().includes(T.toLowerCase())||e.productModel.toLowerCase().includes(T.toLowerCase()),s=!O||""===O||e.status===O;return t&&s}),rowKey:"id",loading:s,rowSelection:{selectedRowKeys:E,onChange:$,getCheckboxProps:e=>({disabled:"shortage"===e.status})},pagination:{total:J.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`},scroll:{x:1400},size:"small"})]})}),r.jsx(Z.Z,{title:"库存调整",open:B,onCancel:()=>{U(!1),F(null),Y.resetFields()},footer:[r.jsx(h.ZP,{onClick:()=>U(!1),children:"取消"},"cancel"),r.jsx(h.ZP,{type:"primary",onClick:()=>{Y.validateFields().then(t=>{e.success("库存调整成功"),U(!1),Y.resetFields()})},children:"确认调整"},"submit")],width:600,children:(0,r.jsxs)(i.Z,{form:Y,layout:"vertical",className:"space-y-4",children:[(0,r.jsxs)(p.Z,{gutter:16,children:[r.jsx(m.Z,{span:12,children:r.jsx(i.Z.Item,{label:"产品编码",name:"productCode",children:r.jsx(f.default,{disabled:!0})})}),r.jsx(m.Z,{span:12,children:r.jsx(i.Z.Item,{label:"产品名称",name:"productName",children:r.jsx(f.default,{disabled:!0})})})]}),(0,r.jsxs)(p.Z,{gutter:16,children:[r.jsx(m.Z,{span:12,children:r.jsx(i.Z.Item,{label:"当前库存",name:"currentStock",children:r.jsx(y.Z,{disabled:!0,style:{width:"100%"}})})}),r.jsx(m.Z,{span:12,children:r.jsx(i.Z.Item,{label:"调整类型",name:"adjustmentType",rules:[{required:!0,message:"请选择调整类型"}],children:(0,r.jsxs)(c.default,{children:[r.jsx(A,{value:"in",children:"入库"}),r.jsx(A,{value:"out",children:"出库"}),r.jsx(A,{value:"adjustment",children:"库存调整"}),r.jsx(A,{value:"transfer",children:"库位转移"})]})})})]}),(0,r.jsxs)(p.Z,{gutter:16,children:[r.jsx(m.Z,{span:12,children:r.jsx(i.Z.Item,{label:"调整数量",name:"adjustmentQuantity",rules:[{required:!0,message:"请输入调整数量"},{type:"number",min:1,message:"数量必须大于0"}],children:r.jsx(y.Z,{style:{width:"100%"},placeholder:"请输入调整数量",min:1})})}),r.jsx(m.Z,{span:12,children:r.jsx(i.Z.Item,{label:"调整原因",name:"reason",rules:[{required:!0,message:"请输入调整原因"}],children:(0,r.jsxs)(c.default,{children:[r.jsx(A,{value:"盘点调整",children:"盘点调整"}),r.jsx(A,{value:"损耗调整",children:"损耗调整"}),r.jsx(A,{value:"质量问题",children:"质量问题"}),r.jsx(A,{value:"生产需要",children:"生产需要"}),r.jsx(A,{value:"销售出库",children:"销售出库"}),r.jsx(A,{value:"其他",children:"其他"})]})})})]}),r.jsx(i.Z.Item,{label:"备注",name:"remark",children:r.jsx(f.default.TextArea,{rows:3,placeholder:"请输入调整备注信息"})})]})})]})};function U(){return r.jsx(n.Z,{children:r.jsx(B,{})})}},51221:(e,t,s)=>{"use strict";s.d(t,{Ro:()=>r});let r=async(e,t)=>{try{let t=await e();if("success"===t.status)return t.data||null;return t.code,t.message,t.message,null}catch(e){return e instanceof Error?e.message:String(e),null}}},76485:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(25036),l=s(38834);function c({children:e}){return r.jsx(l.Z,{children:e})}},80895:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>c,__esModule:()=>l,default:()=>a});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\warehouse\products\page.tsx`),{__esModule:l,$$typeof:c}=r,a=r.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,2345,7049,7557,6274,996,6133],()=>s(13608));module.exports=r})();