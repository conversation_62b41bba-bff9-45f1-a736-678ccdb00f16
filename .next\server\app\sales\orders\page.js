(()=>{var e={};e.id=288,e.ids=[288],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},2868:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var a=r(50482),s=r(69108),l=r(62563),i=r.n(l),n=r(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d=["",{children:["sales",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,56356)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,75497)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\page.tsx"],u="/sales/orders/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/sales/orders/page",pathname:"/sales/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},98911:(e,t,r)=>{Promise.resolve().then(r.bind(r,41085))},94505:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var a=r(65651),s=r(3729);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};var i=r(49809);let n=s.forwardRef(function(e,t){return s.createElement(i.Z,(0,a.Z)({},e,{ref:t,icon:l}))})},41085:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eC});var a=r(95344),s=r(3729),l=r(22254),i=r(97854),n=r(284),o=r(97557),d=r(30977),c=r(32979),u=r(7618),m=r(90377),h=r(11157),p=r(51410),x=r(39470),g=r(10707),f=r(52788),y=r(6025),j=r(63724),w=r(27976),v=r(83984),b=r(43896),Z=r(36527),P=r(16408),C=r(14223),S=r(67383),k=r(94505),D=r(33537),A=r(2778),N=r(70469),I=r(89645),M=r(58535),O=r(97147),q=r(32066),$=r(18608),R=r(37637),E=r(51221),T=r(48869),_=r.n(T),F=r(16728),V=r(44670),z=r(85287);let L={orderStatusTransition:{sales:{pending:["confirmed","cancelled"],confirmed:["completed","cancelled"],completed:[],cancelled:[]},production:{not_started:["in_progress","cancelled"],in_progress:["completed","paused"],paused:["in_progress","cancelled"],completed:[],cancelled:[]}},orderChange:{allowedTypes:["quantity","delivery_date","cancel"],restrictions:{completed:[],cancelled:[],confirmed:["quantity","delivery_date","cancel"],pending:["quantity","delivery_date","cancel"]},quantityChangeLimit:{maxIncrease:2,maxDecrease:.5,warningThreshold:.2},deliveryDateChangeLimit:{maxDaysForward:90,maxDaysBackward:7,warningThreshold:30}},creditLimit:{warningThreshold:.8,criticalThreshold:1,checkRequired:!0},inventory:{checkRequired:!0,allowNegative:!1,warningThreshold:10,criticalThreshold:0}},Y={required:{orderNumber:"请输入订单号",productCode:"请输入产品编码",productName:"请输入产品名称",customerName:"请输入客户名称",quantity:"请输入数量",unitPrice:"请输入单价",deliveryDate:"请选择交货日期",workstationCode:"请输入工位编码",moldNumber:"请输入模具编号"},format:{orderNumber:"订单号格式不正确",productCode:"产品编码格式不正确",customerCode:"客户编码格式不正确",phone:"手机号格式不正确",email:"邮箱格式不正确"},range:{quantity:"数量超出允许范围",price:"价格超出允许范围",deliveryDate:"交货日期超出允许范围"},business:{orderExists:"订单号已存在",productNotFound:"产品不存在",customerNotFound:"客户不存在",insufficientCredit:"客户信用额度不足",insufficientInventory:"库存不足",invalidStatusTransition:"订单状态转换无效",changeNotAllowed:"当前状态不允许此变更"}};class Q{static async validateSalesOrder(e,t={}){let r=[],a=[],s={};if(e.orderNumber){let a=this.validateOrderNumber(e.orderNumber,"sales",t);a.isValid||(s.orderNumber=a.errors,r.push(...a.errors))}else t.skipOptionalFields||a.push("未提供订单号，将自动生成");if(!e.customerId){let e=["客户ID不能为空"];s.customerId=e,r.push(...e)}if(!e.customerName){let e=["客户名称不能为空"];s.customerName=e,r.push(...e)}if(e.items&&0!==e.items.length)for(let t=0;t<e.items.length;t++){let a=e.items[t],l=this.validateSalesOrderItem(a,t);l.isValid||(s[`items[${t}]`]=l.errors,r.push(...l.errors.map(e=>`项目${t+1}: ${e}`)))}else{let e=["订单项目不能为空"];s.items=e,r.push(...e)}if(void 0!==e.totalAmount&&e.totalAmount<0){let e=["订单总金额不能为负数"];s.totalAmount=e,r.push(...e)}if(e.deliveryDate){let t=new Date(e.deliveryDate),r=new Date;r.setHours(0,0,0,0),t<r&&a.push("交期早于当前日期，请确认是否正确")}if(t.checkUniqueness&&e.orderNumber){let a=await this.checkOrderNumberUniqueness(e.orderNumber,"sales",t.excludeId);a.isValid||(s.orderNumber=[...s.orderNumber||[],...a.errors],r.push(...a.errors))}return{isValid:0===r.length,errors:r,warnings:a,fieldErrors:s}}static async validateProductionOrder(e,t={}){let r=[],a={};if(e.orderNumber){let s=this.validateOrderNumber(e.orderNumber,"production",t);s.isValid||(a.orderNumber=s.errors,r.push(...s.errors))}else{let e=["生产订单号不能为空"];a.orderNumber=e,r.push(...e)}if(!e.productCode){let e=["产品编码不能为空"];a.productCode=e,r.push(...e)}if(!e.productName){let e=["产品名称不能为空"];a.productName=e,r.push(...e)}if(!e.plannedQuantity||e.plannedQuantity<=0){let e=["计划数量必须大于0"];a.plannedQuantity=e,r.push(...e)}if(!e.sourceOrderIds||0===e.sourceOrderIds.length){let e=["源销售订单ID不能为空"];a.sourceOrderIds=e,r.push(...e)}if(t.checkUniqueness&&e.orderNumber){let s=await this.checkOrderNumberUniqueness(e.orderNumber,"production",t.excludeId);s.isValid||(a.orderNumber=[...a.orderNumber||[],...s.errors],r.push(...s.errors))}return{isValid:0===r.length,errors:r,warnings:[],fieldErrors:a}}static validateWorkOrder(e,t={}){let r=[],a={};if(!e.batchNumber){let e=["批次号不能为空"];a.batchNumber=e,r.push(...e)}if(!e.productCode){let e=["产品编码不能为空"];a.productCode=e,r.push(...e)}if(!e.productName){let e=["产品名称不能为空"];a.productName=e,r.push(...e)}if(!e.plannedMoldCount||e.plannedMoldCount<=0){let e=["计划模数必须大于0"];a.plannedMoldCount=e,r.push(...e)}if(!e.sourceOrderId){let e=["源订单ID不能为空"];a.sourceOrderId=e,r.push(...e)}if(!e.hourlyCapacity||e.hourlyCapacity<=0){let e=["小时产能必须大于0"];a.hourlyCapacity=e,r.push(...e)}return{isValid:0===r.length,errors:r,warnings:[],fieldErrors:a}}static validateOrderNumber(e,t,r={}){let a=[],s=[];if(!e||""===e.trim())return a.push("订单号不能为空"),{isValid:!1,errors:a,warnings:s};switch(t){case"sales":z.s.validateSalesOrderNumber(e)||a.push("销售订单号格式不正确，应为：XSDD + YYYYMMDD + 4位序号");break;case"production":z.s.validateProductionOrderNumber(e)||a.push("生产订单号格式不正确，应为：PO- + 销售订单号")}return{isValid:0===a.length,errors:a,warnings:s}}static validateSalesOrderItem(e,t){let r=[];return e.productCode||r.push("产品编码不能为空"),e.productName||r.push("产品名称不能为空"),(!e.quantity||e.quantity<=0)&&r.push("数量必须大于0"),(!e.unitPrice||e.unitPrice<0)&&r.push("单价不能为负数"),{isValid:0===r.length,errors:r}}static async checkOrderNumberUniqueness(e,t,r){let a=[];try{let s=null;if("sales"===t){let t=await R.dataAccessManager.orders.getByNumber(e);"success"===t.status&&t.data&&(s=t.data)}else if("production"===t){let t=await R.dataAccessManager.productionOrders.getByOrderNumber(e);"success"===t.status&&t.data&&(s=t.data)}s&&s.id!==r&&a.push(`${"sales"===t?"销售":"生产"}订单号已存在`)}catch(e){console.error("检查订单号唯一性时发生错误:",e),a.push("无法验证订单号唯一性")}return{isValid:0===a.length,errors:a}}static async validateCustomerCredit(e,t){let r=[],a=[];try{let s=await R.dataAccessManager.customers.getById(e);if("success"!==s.status||!s.data)return r.push("客户不存在"),{isValid:!1,errors:r,warnings:a};let l=s.data,i=(l.creditLimit||0)-(l.usedCredit||0);return t>i?r.push(`订单金额超出客户信用额度。可用额度：\xa5${i.toFixed(2)}，订单金额：\xa5${t.toFixed(2)}`):t>.8*i&&a.push(`订单金额接近客户信用额度上限。可用额度：\xa5${i.toFixed(2)}，订单金额：\xa5${t.toFixed(2)}`),{isValid:0===r.length,errors:r,warnings:a}}catch(e){return console.error("验证客户信用额度时发生错误:",e),r.push("验证客户信用额度时发生错误"),{isValid:!1,errors:r,warnings:a}}}static validateOrderChange(e,t,r,a){let s=[],l=[];if(!function(e,t){let r=L.orderChange.restrictions[e];return!!r&&r.includes(t)}(e.status,t))return s.push(function(e,t){let r=Y[e];return r&&r[t]||`${t}验证失败`}("business","changeNotAllowed")),{isValid:!1,errors:s,warnings:l};let i=function(e,t){let r=L[e];return r?t&&"object"==typeof r&&r[t]?r[t]:r:null}("orderChange");switch(t){case"quantity":let n=parseInt(a);if(isNaN(n)||n<=0)s.push("新数量必须是大于0的整数");else{let e=n/parseInt(r);if(i&&"quantityChangeLimit"in i&&i.quantityChangeLimit){let{maxIncrease:t,maxDecrease:r,warningThreshold:a}=i.quantityChangeLimit;e>t?s.push(`数量增加不能超过${(t-1)*100}%`):e<r?s.push(`数量减少不能超过${(1-r)*100}%`):Math.abs(e-1)>a&&l.push(`数量变更超过${100*a}%，请确认是否正确`)}}break;case"delivery_date":let o=new Date(a),d=new Date(r),c=new Date;if(c.setHours(0,0,0,0),o<c)s.push("新交期不能早于当前日期");else{let e=Math.ceil((o.getTime()-d.getTime())/864e5);if(i&&"deliveryDateChangeLimit"in i&&i.deliveryDateChangeLimit){let{maxDaysForward:t,maxDaysBackward:r,warningThreshold:a}=i.deliveryDateChangeLimit;e>t?s.push(`交期延后不能超过${t}天`):e<-r?s.push(`交期提前不能超过${r}天`):Math.abs(e)>a&&l.push(`交期变更超过${a}天，请确认是否正确`)}}break;case"cancel":"in_progress"===e.productionStatus&&s.push("生产中的订单不能取消，请先停止生产")}return{isValid:0===s.length,errors:s,warnings:l}}static async validateSalesOrderBatch(e,t={}){let r=[],a=0,s=0,l=0;for(let i of e){let e=await this.validateSalesOrder(i,t);r.push(e),e.isValid?a++:s++,e.warnings&&e.warnings.length>0&&l++}return{results:r,summary:{valid:a,invalid:s,warnings:l}}}static createFormValidationRules(e,t={}){let r=[];switch(!1!==t.required&&r.push({required:!0,message:`请输入${this.getFieldDisplayName(e)}`}),e){case"orderNumber":t.orderType&&r.push({validator:(e,r)=>{if(!r)return Promise.resolve();let a=this.validateOrderNumber(r,t.orderType);return a.isValid?Promise.resolve():Promise.reject(Error(a.errors[0]))}});break;case"positiveNumber":r.push({validator:(r,a)=>null==a?Promise.resolve():a<=0?Promise.reject(Error(`${this.getFieldDisplayName(e)}必须大于0`)):void 0!==t.min&&a<t.min?Promise.reject(Error(`${this.getFieldDisplayName(e)}不能小于${t.min}`)):void 0!==t.max&&a>t.max?Promise.reject(Error(`${this.getFieldDisplayName(e)}不能大于${t.max}`)):Promise.resolve()});break;case"email":r.push({type:"email",message:"请输入有效的邮箱地址"});break;case"phone":r.push({pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号码"})}return t.checkUniqueness&&r.push({validator:async(r,a)=>a?await t.checkUniqueness(a)?Promise.resolve():Promise.reject(Error(`${this.getFieldDisplayName(e)}已存在`)):Promise.resolve()}),t.customValidator&&r.push({validator:(e,r)=>{if(!r&&!t.required)return Promise.resolve();let a=t.customValidator(r);return a.isValid?Promise.resolve():Promise.reject(Error(a.errors[0]||"验证失败"))}}),r}static getFieldDisplayName(e){return({orderNumber:"订单号",productCode:"产品编码",customerCode:"客户编码",workstationCode:"工位编码",moldNumber:"模具编号",batchNumber:"批次号",email:"邮箱",phone:"电话",positiveNumber:"数值",text:"文本"})[e]||e}}var X=r(92561),W=r(56870);let{Option:U}=i.default,B=({open:e,onCancel:t,onSelect:r})=>{let{message:l}=c.Z.useApp(),[o,d]=(0,s.useState)(!1),[u,p]=(0,s.useState)([]),[x,g]=(0,s.useState)([]),[f,y]=(0,s.useState)(""),[C,S]=(0,s.useState)(""),[k,D]=(0,s.useState)([]),[A,I]=(0,s.useState)([]),M=async()=>{d(!0);try{let e=await R.dataAccessManager.products.getActive();if("success"===e.status&&e.data){let t=[];t=Array.isArray(e.data)?e.data:e.data&&"object"==typeof e.data&&"items"in e.data?e.data.items:[],p(t),g(t)}else l.error("获取产品数据失败")}catch(e){l.error("加载产品数据失败")}finally{d(!1)}},O=()=>{let e=[...u];if(f){let t=f.toLowerCase();e=e.filter(e=>e.modelCode.toLowerCase().includes(t)||e.modelName.toLowerCase().includes(t))}C&&(e=e.filter(e=>e.status===C)),g(e)},q=()=>{y(""),S(""),D([]),I([]),t()};return(0,s.useEffect)(()=>{e&&M()},[e]),(0,s.useEffect)(()=>{O()},[f,C,u]),(0,a.jsxs)(P.Z,{title:"选择产品",open:e,onCancel:q,width:1e3,footer:[a.jsx(h.ZP,{onClick:q,children:"取消"},"cancel"),a.jsx(h.ZP,{onClick:()=>{D([]),I([])},disabled:0===A.length,children:"清空选择"},"clear"),(0,a.jsxs)(h.ZP,{type:"primary",icon:a.jsx(X.Z,{}),onClick:()=>{A.length>0?(r(A),q()):l.warning("请至少选择一个产品")},disabled:0===A.length,children:["确认添加 (",A.length,"个产品)"]},"select")],destroyOnHidden:!0,children:[(0,a.jsxs)(j.Z,{gutter:16,className:"mb-4",children:[a.jsx(w.Z,{span:8,children:a.jsx(v.Z,{size:"small",children:a.jsx(b.Z,{title:"可用产品",value:u.filter(e=>"active"===e.status).length,suffix:"个"})})}),a.jsx(w.Z,{span:8,children:a.jsx(v.Z,{size:"small",children:a.jsx(b.Z,{title:"筛选结果",value:x.length,suffix:"个"})})}),a.jsx(w.Z,{span:8,children:a.jsx(v.Z,{size:"small",children:a.jsx(b.Z,{title:"平均价格",value:x.length>0?x.reduce((e,t)=>e+t.productPrice,0)/x.length:0,precision:3,prefix:"\xa5"})})})]}),(0,a.jsxs)(j.Z,{gutter:16,className:"mb-4",children:[a.jsx(w.Z,{span:12,children:a.jsx(n.default,{placeholder:"搜索产品编码或名称",prefix:a.jsx(N.Z,{}),value:f,onChange:e=>y(e.target.value),allowClear:!0})}),a.jsx(w.Z,{span:6,children:(0,a.jsxs)(i.default,{placeholder:"状态筛选",value:C,onChange:S,style:{width:"100%"},allowClear:!0,children:[a.jsx(U,{value:"",children:"全部状态"}),a.jsx(U,{value:"active",children:"启用"}),a.jsx(U,{value:"inactive",children:"停用"})]})}),a.jsx(w.Z,{span:6,children:a.jsx(h.ZP,{onClick:M,loading:o,children:"刷新数据"})})]}),a.jsx(Z.Z,{columns:[{title:"产品编码",dataIndex:"modelCode",key:"modelCode",width:120,fixed:"left"},{title:"产品名称",dataIndex:"modelName",key:"modelName",width:200,ellipsis:!0},{title:"产品价格",dataIndex:"productPrice",key:"productPrice",width:120,render:e=>e?`\xa5${e.toFixed(3)}`:"\xa50.000",sorter:(e,t)=>(e.productPrice||0)-(t.productPrice||0)},{title:"产品重量",dataIndex:"productWeight",key:"productWeight",width:120,render:e=>e?`${e.toFixed(2)}g`:"0.00g",sorter:(e,t)=>(e.productWeight||0)-(t.productWeight||0)},{title:"成型模具",dataIndex:"formingMold",key:"formingMold",width:120,ellipsis:!0},{title:"单模数量",dataIndex:"formingMoldQuantity",key:"formingMoldQuantity",width:100,render:e=>`${e||0}个`},{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>a.jsx(m.Z,{color:"active"===e?"green":"red",children:"active"===e?"启用":"停用"})}],dataSource:x,rowKey:"id",rowSelection:{type:"checkbox",selectedRowKeys:k,onChange:(e,t)=>{D(e),I(t)},onSelectAll:(e,t,r)=>{if(e){let e=[...A];r.forEach(t=>{e.find(e=>e.id===t.id)||e.push(t)}),I(e)}else{let e=r.map(e=>e.id);I(A.filter(t=>!e.includes(t.id)))}},onSelect:(e,t)=>{t?A.find(t=>t.id===e.id)||I([...A,e]):I(A.filter(t=>t.id!==e.id))}},loading:o,pagination:{total:x.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`},scroll:{x:800,y:400},size:"small"}),A.length>0&&a.jsx(v.Z,{title:`已选择产品 (${A.length}个)`,size:"small",className:"mt-4",children:a.jsx("div",{style:{maxHeight:"200px",overflowY:"auto"},children:A.map((e,t)=>(0,a.jsxs)(j.Z,{gutter:16,className:t>0?"mt-2 pt-2":"",style:t>0?{borderTop:"1px solid #f0f0f0"}:{},children:[(0,a.jsxs)(w.Z,{span:6,children:[(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"编码:"})," ",e.modelCode]}),(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"名称:"})," ",e.modelName]})]}),(0,a.jsxs)(w.Z,{span:6,children:[(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"价格:"})," \xa5",e.productPrice?e.productPrice.toFixed(3):"0.000"]}),(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"重量:"})," ",e.productWeight?e.productWeight.toFixed(2):"0.00","g"]})]}),(0,a.jsxs)(w.Z,{span:6,children:[(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"成型模具:"})," ",e.formingMold||"-"]}),(0,a.jsxs)("div",{children:[a.jsx("strong",{children:"单模数量:"})," ",e.formingMoldQuantity||0,"个"]})]}),a.jsx(w.Z,{span:6,children:a.jsx(h.ZP,{type:"link",size:"small",danger:!0,onClick:()=>{let t=A.filter(t=>t.id!==e.id),r=k.filter(t=>t!==e.id);I(t),D(r)},children:"移除"})})]},e.id))})})]})};var H=r(53869),K=r(21754),G=r(75397);let{Option:J}=i.default,ee=({open:e,onCancel:t,onSuccess:r})=>{let{message:l}=c.Z.useApp(),[o]=u.Z.useForm(),[d,m]=(0,s.useState)(!1),[h,p]=(0,s.useState)([]),[x,g]=(0,s.useState)(""),f=async()=>{try{let e=await R.dataAccessManager.products.getAll();if("success"===e.status&&e.data){let t=[];Array.isArray(e.data)?t=e.data:"items"in e.data&&Array.isArray(e.data.items)&&(t=e.data.items),p(t)}}catch(e){}},v=async()=>{try{let e=await R.dataAccessManager.products.getAll();if("success"===e.status&&e.data){let t=[];if(Array.isArray(e.data))t=e.data;else{if(!("items"in e.data&&Array.isArray(e.data.items)))return"P00001";t=e.data.items}let r=t.map(e=>e.modelCode).filter(e=>/^P\d{5}$/.test(e)).map(e=>parseInt(e.substring(1),10)).sort((e,t)=>t-e),a=r.length>0?r[0]+1:1;return`P${a.toString().padStart(5,"0")}`}}catch(e){}return"P00001"},b=e=>!h.some(t=>t.modelCode===e),Z=e=>/^M-[A-Z]{2}-\d{2}$/.test(e),C=(e,t)=>"forming"===t?h.filter(t=>t.formingMold===e):h.filter(t=>t.hotPressMold===e),S=async()=>{try{let e=await o.validateFields();if(m(!0),!b(e.modelCode)){l.error("产品编码已存在，请使用其他编码"),m(!1);return}let t={modelCode:e.modelCode,modelName:e.modelName,formingMold:e.formingMold||"",formingMoldQuantity:e.formingMoldQuantity||1,hotPressMold:e.hotPressMold||"",hotPressMoldQuantity:e.hotPressMoldQuantity||1,formingPiecePrice:e.formingPiecePrice||0,hotPressPiecePrice:e.hotPressPiecePrice||0,productPrice:e.productPrice,productWeight:e.productWeight,boxSpecification:e.boxSpecification||"",packingQuantity:e.packingQuantity||100,piecesPerMold:e.piecesPerMold||1,moldId:e.formingMold||"",status:e.status||"active"},a=await R.dataAccessManager.products.create(t);"success"===a.status&&a.data?(l.success("产品创建成功，已自动添加到订单中"),r(a.data),k()):l.error(a.message||"产品创建失败")}catch(e){l.error("创建产品失败")}finally{m(!1)}},k=()=>{o.resetFields(),g(""),t()},D=async()=>{if(e){await f();let e=await v();o.setFieldsValue({modelCode:e,status:"active",formingMoldQuantity:1,hotPressMoldQuantity:1,packingQuantity:100,piecesPerMold:1,formingPiecePrice:0,hotPressPiecePrice:0})}};return(0,s.useEffect)(()=>{D()},[e]),a.jsx(P.Z,{title:"新增产品",open:e,onOk:S,onCancel:k,width:900,confirmLoading:d,okText:"确认创建",cancelText:"取消",destroyOnHidden:!0,styles:{body:{maxHeight:"70vh",overflowY:"auto"}},children:(0,a.jsxs)(u.Z,{form:o,layout:"vertical",initialValues:{status:"active",formingMoldQuantity:1,hotPressMoldQuantity:1,packingQuantity:100,piecesPerMold:1,formingPiecePrice:0,hotPressPiecePrice:0},children:[(0,a.jsxs)(j.Z,{gutter:16,children:[a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"产品编码",name:"modelCode",rules:[{required:!0,message:"请输入产品编码"},{pattern:/^P\d{5}$/,message:"格式：P + 5位数字（如：P00001）"},{validator:(e,t)=>t?b(t)?Promise.resolve():Promise.reject(Error("产品编码已存在，请使用其他编码")):Promise.resolve()}],children:a.jsx(n.default,{placeholder:"如：P00001（自动生成）"})})}),a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"产品名称",name:"modelName",rules:[{required:!0,message:"请输入产品名称"}],children:a.jsx(n.default,{placeholder:"请输入产品名称"})})})]}),a.jsx(H.default,{defaultActiveKey:"mold",items:[{key:"mold",label:(0,a.jsxs)("span",{children:[a.jsx(K.Z,{}),"模具信息"]}),children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(j.Z,{gutter:16,children:[a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"成型模具编号",name:"formingMold",rules:[{required:!0,message:"请输入成型模具编号"},{validator:(e,t)=>!t||Z(t)?Promise.resolve():Promise.reject(Error("模具编号格式错误，请使用格式：M-XX-XX（如：M-JX-05）"))}],extra:x&&C(x,"forming").length>0&&(0,a.jsxs)("div",{style:{marginTop:"4px"},children:[(0,a.jsxs)("span",{style:{color:"#1890ff",fontSize:"12px"},children:["\uD83D\uDCA1 此模具已被 ",C(x,"forming").length," 个产品使用："]}),a.jsx("div",{style:{fontSize:"11px",color:"#666",marginTop:"2px"},children:C(x,"forming").map(e=>e.modelName).join("、")})]}),children:a.jsx(n.default,{placeholder:"如：M-JX-05（支持多产品共享）",onChange:e=>{g(e.target.value)}})})}),a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"成型模具单模数量",name:"formingMoldQuantity",rules:[{required:!0,message:"请输入单模数量"}],children:a.jsx(y.Z,{className:"w-full",placeholder:"请输入数量",suffix:"个/模",min:1,style:{width:"100%"}})})})]}),(0,a.jsxs)(j.Z,{gutter:16,children:[a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"热压模具编号",name:"hotPressMold",rules:[{required:!0,message:"请输入热压模具编号"},{validator:(e,t)=>t?Z(t)?h.some(e=>e.hotPressMold===t)?Promise.reject(Error("热压模具编号已存在，请使用其他编号")):Promise.resolve():Promise.reject(Error("模具编号格式错误，请使用格式：M-XX-XX（如：M-RY-12）")):Promise.resolve()}],children:a.jsx(n.default,{placeholder:"如：M-RY-12"})})}),a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"热压模具单模数量",name:"hotPressMoldQuantity",rules:[{required:!0,message:"请输入单模数量"}],children:a.jsx(y.Z,{className:"w-full",placeholder:"请输入数量",suffix:"个/模",min:1,style:{width:"100%"}})})})]}),a.jsx(u.Z.Item,{label:"单模出数",name:"piecesPerMold",rules:[{required:!0,message:"请输入单模出数"},{type:"number",min:1,message:"单模出数必须大于0"}],children:a.jsx(y.Z,{className:"w-full",placeholder:"用于智能排产模数计算",suffix:"个/模",min:1,precision:0,step:1,style:{width:"100%"}})})]})},{key:"price",label:(0,a.jsxs)("span",{children:[a.jsx(G.Z,{}),"计件单价"]}),children:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(j.Z,{gutter:16,children:[a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"成型计件单价",name:"formingPiecePrice",rules:[{required:!0,message:"请输入成型计件单价"}],children:a.jsx(y.Z,{className:"w-full",placeholder:"请输入单价",prefix:"\xa5",suffix:"/模",min:0,precision:2,style:{width:"100%"}})})}),a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"热压计件单价",name:"hotPressPiecePrice",rules:[{required:!0,message:"请输入热压计件单价"}],children:a.jsx(y.Z,{className:"w-full",placeholder:"请输入单价",prefix:"\xa5",suffix:"/模",min:0,precision:2,style:{width:"100%"}})})})]}),(0,a.jsxs)(j.Z,{gutter:16,children:[a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"产品价格",name:"productPrice",rules:[{required:!0,message:"请输入产品价格"},{type:"number",min:.01,message:"产品价格必须大于0"}],children:a.jsx(y.Z,{className:"w-full",placeholder:"请输入产品价格",prefix:"\xa5",min:0,precision:3,step:.001,style:{width:"100%"}})})}),a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"产品重量",name:"productWeight",rules:[{required:!0,message:"请输入产品重量"},{type:"number",min:.01,message:"产品重量必须大于0"}],children:a.jsx(y.Z,{className:"w-full",placeholder:"请输入产品重量",suffix:"克",min:0,precision:2,step:.01,style:{width:"100%"}})})})]}),(0,a.jsxs)(j.Z,{gutter:16,children:[a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"箱规",name:"boxSpecification",rules:[{required:!0,message:"请输入箱规"}],children:a.jsx(n.default,{placeholder:"如：30\xd720\xd715 cm"})})}),a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"装箱数",name:"packingQuantity",rules:[{required:!0,message:"请输入装箱数"},{type:"number",min:1,message:"装箱数必须大于0"}],children:a.jsx(y.Z,{className:"w-full",placeholder:"请输入装箱数",suffix:"个/箱",min:1,precision:0,step:1,style:{width:"100%"}})})})]}),a.jsx(u.Z.Item,{label:"状态",name:"status",rules:[{required:!0,message:"请选择状态"}],children:(0,a.jsxs)(i.default,{placeholder:"请选择状态",children:[a.jsx(J,{value:"active",children:"启用"}),a.jsx(J,{value:"inactive",children:"停用"})]})})]})}]})]})})};class et{static calculateItemAmount(e,t,r={}){let{precision:a=2,roundingMode:s="round",discountRate:l=0,taxRate:i=0}=r;this.validateItemInput(e,t,l,i);let n=e*t,o=n*l,d=n-o,c=d*i;return{itemSubtotal:this.roundAmount(n,a,s),itemDiscountAmount:this.roundAmount(o,a,s),itemTaxAmount:this.roundAmount(c,a,s),itemTotalAmount:this.roundAmount(d+c,a,s)}}static calculateOrderAmount(e,t={}){let{precision:r=2,roundingMode:a="round",discountRate:s=0,taxRate:l=0}=t;this.validateOrderInput(e);let i=e.map(e=>this.calculateItemAmount(e.price,e.quantity,{precision:r,roundingMode:a,discountRate:s,taxRate:l})),n=i.reduce((e,t)=>e+t.itemSubtotal,0),o=i.reduce((e,t)=>e+t.itemDiscountAmount,0),d=i.reduce((e,t)=>e+t.itemTaxAmount,0),c=i.reduce((e,t)=>e+t.itemTotalAmount,0);return{subtotal:this.roundAmount(n,r,a),discountAmount:this.roundAmount(o,r,a),taxAmount:this.roundAmount(d,r,a),totalAmount:this.roundAmount(c,r,a)}}static calculateCostAmount(e,t,r,a={}){let{precision:s=2,roundingMode:l="round"}=a;this.validateCostInput(e,t,r);let i=e+t+r;return{totalCost:this.roundAmount(i,s,l),unitCost:this.roundAmount(i,s,l),formattedTotalCost:this.formatCurrency(i,s),formattedUnitCost:this.formatCurrency(i,s)}}static calculateProfitAmount(e,t,r={}){let{precision:a=2,roundingMode:s="round"}=r;if(e<0||t<0)throw Error("收入和成本不能为负数");let l=e-t,i=e>0?l/e:0;return{totalProfit:this.roundAmount(l,a,s),profitMargin:this.roundAmount(i,4,s),formattedProfit:this.formatCurrency(l,a),formattedMargin:this.formatPercentage(i,2)}}static formatCurrency(e,t=2){return"number"!=typeof e||isNaN(e)?`\xa50.${"0".repeat(t)}`:`\xa5${e.toFixed(t).replace(/\B(?=(\d{3})+(?!\d))/g,",")}`}static formatPercentage(e,t=1){return"number"!=typeof e||isNaN(e)?"0%":`${(100*e).toFixed(t)}%`}static roundAmount(e,t,r){let a=Math.pow(10,t);switch(r){case"up":return Math.ceil(e*a)/a;case"down":return Math.floor(e*a)/a;default:return Math.round(e*a)/a}}static validateItemInput(e,t,r,a){if("number"!=typeof e||e<0)throw Error("价格必须是非负数");if("number"!=typeof t||t<=0)throw Error("数量必须大于0");if("number"!=typeof r||r<0||r>1)throw Error("折扣率必须在0-1之间");if("number"!=typeof a||a<0||a>1)throw Error("税率必须在0-1之间")}static validateOrderInput(e){if(!Array.isArray(e)||0===e.length)throw Error("订单项目不能为空");e.forEach((e,t)=>{if(!e||"object"!=typeof e)throw Error(`订单项目${t+1}格式错误`);if("number"!=typeof e.price||e.price<0)throw Error(`订单项目${t+1}的价格必须是非负数`);if("number"!=typeof e.quantity||e.quantity<=0)throw Error(`订单项目${t+1}的数量必须大于0`)})}static validateCostInput(e,t,r){if("number"!=typeof e||e<0)throw Error("成型成本必须是非负数");if("number"!=typeof t||t<0)throw Error("热压成本必须是非负数");if("number"!=typeof r||r<0)throw Error("材料成本必须是非负数")}}let er=({value:e=[],onChange:t})=>{let{message:r}=c.Z.useApp(),[l,i]=(0,s.useState)(e),[d,u]=(0,s.useState)(!1),[m,p]=(0,s.useState)(!1),{productModels:x,productModelsLoading:P}=(0,W.rm)(),C=(e,t,r)=>{let a=et.calculateItemAmount(e,t,{taxRate:r/100,precision:2});return{totalPrice:a.itemSubtotal,taxAmount:a.itemTaxAmount}},S=(e,r)=>{let a=[...l],s=a[r];s.quantity=e;let{totalPrice:n,taxAmount:o}=C(s.productPrice,e,s.taxRate);s.totalPrice=n,s.taxAmount=o,i(a),t?.(a)},k=(e,r)=>{if(!e)return;let a=[...l];a[r].deliveryDate=e.format("YYYY-MM-DD"),i(a),t?.(a)},A=(e,r)=>{let a=[...l];a[r].remark=e,i(a),t?.(a)},N=e=>{let a=Array.isArray(e)?e:[e],s=[...l,...a.map(e=>({id:`item_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,productId:e.id,productCode:e.modelCode,productName:e.modelName,productPrice:e.productPrice,quantity:1,productWeight:e.productWeight,deliveryDate:_()().add(7,"day").format("YYYY-MM-DD"),taxRate:13,totalPrice:e.productPrice,taxAmount:.13*e.productPrice,remark:""}))];i(s),t?.(s),1===a.length?r.success("产品添加成功"):r.success(`成功添加 ${a.length} 个产品`)},I=e=>{let a=l.filter((t,r)=>r!==e);i(a),t?.(a),r.success("产品删除成功")},O={totalQuantity:l.reduce((e,t)=>e+t.quantity,0),totalAmount:l.reduce((e,t)=>e+t.totalPrice,0),totalTax:l.reduce((e,t)=>e+t.taxAmount,0),totalWeight:l.reduce((e,t)=>e+t.productWeight*t.quantity,0)/1e6};return(0,s.useEffect)(()=>{i(e)},[e]),(0,a.jsxs)("div",{children:[(0,a.jsxs)(v.Z,{title:"产品信息",extra:(0,a.jsxs)(g.Z,{children:[a.jsx(h.ZP,{icon:a.jsx(X.Z,{}),onClick:()=>u(!0),loading:P,children:"选择产品"}),a.jsx(h.ZP,{type:"primary",icon:a.jsx(M.Z,{}),onClick:()=>{p(!0)},children:"新增产品"})]}),children:[l.length>0&&(0,a.jsxs)(j.Z,{gutter:16,className:"mb-4",children:[a.jsx(w.Z,{span:6,children:a.jsx(b.Z,{title:"产品种类",value:l.length,suffix:"种"})}),a.jsx(w.Z,{span:6,children:a.jsx(b.Z,{title:"总数量",value:O.totalQuantity,suffix:"个"})}),a.jsx(w.Z,{span:6,children:a.jsx(b.Z,{title:"总重量",value:(O.totalWeight||0).toFixed(3),suffix:"吨"})}),a.jsx(w.Z,{span:6,children:a.jsx(b.Z,{title:"总金额",value:(O.totalAmount||0).toFixed(2),prefix:"\xa5"})})]}),a.jsx(Z.Z,{columns:[{title:"产品编码",dataIndex:"productCode",key:"productCode",width:54,fixed:"left"},{title:"产品名称",dataIndex:"productName",key:"productName",width:84,ellipsis:!0},{title:"单价(元)",dataIndex:"productPrice",key:"productPrice",width:44,render:e=>`\xa5${e?e.toFixed(3):"0.000"}`},{title:"数量",dataIndex:"quantity",key:"quantity",width:50,render:(e,t,r)=>a.jsx(y.Z,{value:e,min:1,precision:0,size:"small",style:{width:"100%"},onChange:e=>S(e||1,r)})},{title:"重量(g)",dataIndex:"productWeight",key:"productWeight",width:45,render:e=>`${e?e.toFixed(2):"0.00"}g`},{title:"交货日期",dataIndex:"deliveryDate",key:"deliveryDate",width:74,render:(e,t,r)=>a.jsx(o.default,{value:e?_()(e):null,size:"small",format:"YYYY-MM-DD",style:{width:"100%"},disabledDate:e=>e&&e<_()().startOf("day"),onChange:e=>k(e,r)})},{title:"税率(%)",dataIndex:"taxRate",key:"taxRate",width:50,render:e=>`${e}%`},{title:"总价(元)",dataIndex:"totalPrice",key:"totalPrice",width:58,render:e=>`\xa5${e?e.toFixed(2):"0.00"}`},{title:"税额(元)",dataIndex:"taxAmount",key:"taxAmount",width:53,render:e=>`\xa5${e?e.toFixed(2):"0.00"}`},{title:"备注",dataIndex:"remark",key:"remark",width:160,ellipsis:!0,render:(e,t,r)=>a.jsx(n.default,{value:e||"",size:"small",placeholder:"备注信息",maxLength:100,onChange:e=>A(e.target.value,r)})},{title:"操作",key:"action",width:46,fixed:"right",render:(e,t,r)=>a.jsx(f.Z,{title:"确定删除这个产品吗？",onConfirm:()=>I(r),okText:"确定",cancelText:"取消",children:a.jsx(h.ZP,{type:"link",size:"small",danger:!0,icon:a.jsx(D.Z,{}),title:"删除"})})}],dataSource:l,rowKey:"id",pagination:!1,scroll:{x:1400},size:"small",locale:{emptyText:"暂无产品，请点击上方按钮添加产品"}})]}),a.jsx(B,{open:d,onCancel:()=>u(!1),onSelect:N}),a.jsx(ee,{open:m,onCancel:()=>p(!1),onSuccess:e=>{r.success("产品创建成功，可以继续选择该产品"),N(e)}})]})};var ea=r(53200);let{Option:es}=i.default,el=({open:e,onCancel:t,onSuccess:r})=>{let{message:l}=c.Z.useApp(),[d]=u.Z.useForm(),[m,p]=(0,s.useState)(!1),[x,g]=(0,s.useState)(!1),[f,y]=(0,s.useState)([]),[v,b]=(0,s.useState)([]),[Z,C]=(0,s.useState)(!1),[S,k]=(0,s.useState)(!1),[D,A]=(0,s.useState)([]),N=async()=>{try{let e=await (0,E.Ro)(()=>R.dataAccessManager.customers.getAll(),"获取客户数据");e&&e.items&&y(e.items)}catch(e){console.error("加载客户数据失败:",e),l.error("加载客户数据失败")}},I=async()=>{try{let e=await (0,E.Ro)(()=>R.dataAccessManager.employees.getAll(),"获取员工数据");e&&e.items&&b(e.items)}catch(e){console.error("加载员工数据失败:",e),l.error("加载员工数据失败")}},M=async()=>{C(!0);try{await Promise.all([N(),I()])}finally{C(!1)}},O=async()=>{k(!0);try{let e=z.s.generateSalesOrderId();d.setFieldsValue({orderNumber:e}),l.success("销售订单号生成成功")}catch(e){console.error("生成销售订单号失败:",e),l.error("生成销售订单号失败")}finally{k(!1)}},q=()=>Array.isArray(f)?f.filter(e=>"active"===e.status):[],$=async()=>{try{let e=await d.validateFields();g(!0);let t=D.map(e=>({price:e.productPrice,quantity:e.quantity})),a=et.calculateOrderAmount(t,{precision:2}),s=a.subtotal;a.taxAmount;let i=a.totalAmount,n=D.map(e=>e.deliveryDate).sort()[0]||"",o=ea.IZ.transformSalesOrder({orderNumber:e.orderNumber,customerId:e.customerId,customerName:e.customerName,customerContact:e.customerContact,orderDate:_()(e.orderDate).isValid()?_()(e.orderDate).format("YYYY-MM-DD"):e.orderDate,deliveryDate:n,promisedDeliveryDate:n,salesRepresentative:e.salesRepresentative,totalAmount:s,discountAmount:0,finalAmount:i,status:"pending",productionStatus:"not_started",paymentStatus:"unpaid",paymentTerms:"月结30天",items:D.map(t=>({id:t.id,orderNumber:e.orderNumber,productModelCode:t.productCode,productName:t.productName,productCode:t.productCode,quantity:t.quantity,unit:"个",unitPrice:t.productPrice,totalPrice:t.totalPrice,deliveryQuantity:0,remainingQuantity:t.quantity,deliveryDate:t.deliveryDate,remark:t.remark||""})),changes:[],mrpStatus:"not_started",remark:e.remark||""},{generateId:!0,generateTimestamps:!0,validateData:!0});if(!o.success){let e=ea.N5.handleInternal(Error(o.error),"订单数据转换","salesOrder");l.error(e.message);return}o.warnings&&o.warnings.length>0&&(console.warn("订单数据转换警告:",o.warnings),o.warnings.forEach(e=>{l.warning(e)}));let c=o.data,u=await (0,E.Ro)(()=>R.dataAccessManager.orders.create(c),"创建订单");u?(l.success("订单创建成功"),r(u),T()):l.error("创建订单失败")}catch(e){l.error(`创建订单失败: ${e instanceof Error?e.message:"未知错误"}`)}finally{g(!1)}},T=()=>{d.resetFields(),A([]),t()};return(0,s.useEffect)(()=>{if(e){p(!0),Promise.all([M(),O()]).finally(()=>{p(!1)});let e=_()();d.setFieldsValue({orderDate:e}),A([])}},[e]),a.jsx(P.Z,{title:"新增订单",open:e,onCancel:T,width:1200,footer:[a.jsx(h.ZP,{onClick:T,children:"取消"},"cancel"),a.jsx(h.ZP,{type:"primary",loading:x,onClick:$,children:"确认创建"},"submit")],destroyOnHidden:!0,children:a.jsx(F.Z,{spinning:m||Z,tip:"加载数据中...",children:(0,a.jsxs)(u.Z,{form:d,layout:"vertical",requiredMark:!1,children:[(0,a.jsxs)(j.Z,{gutter:16,children:[a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"销售订单号",name:"orderNumber",rules:Q.createFormValidationRules("orderNumber",{required:!1,orderType:"sales",checkUniqueness:async e=>{if(!(await Q.validateOrderNumber(e,"sales")).isValid)return!1;let t=await R.dataAccessManager.orders.getByNumber(e);return"success"!==t.status||!t.data}}),children:a.jsx(n.default,{placeholder:"XSDD202507040001",addonAfter:a.jsx(h.ZP,{type:"text",icon:a.jsx(V.Z,{}),loading:S,onClick:O,size:"small",children:"重新生成"})})})}),a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"销售日期",name:"orderDate",rules:[{required:!0,message:"请选择销售日期"}],children:a.jsx(o.default,{style:{width:"100%"},format:"YYYY-MM-DD",placeholder:"请选择销售日期"})})})]}),(0,a.jsxs)(j.Z,{gutter:16,children:[a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"客户",name:"customerId",rules:[{required:!0,message:"请选择客户"}],children:a.jsx(i.default,{placeholder:"请选择客户",showSearch:!0,filterOption:(e,t)=>{let r=q().find(e=>e.id===t?.value);return!!r&&(r.customerName.toLowerCase().includes(e.toLowerCase())||r.customerCode.toLowerCase().includes(e.toLowerCase()))},onChange:e=>{let t=f.find(t=>t.id===e);t&&d.setFieldsValue({customerName:t.customerName,customerContact:t.contactPerson||t.contactPhone||""})},notFoundContent:"暂无数据",children:q().map(e=>(0,a.jsxs)(es,{value:e.id,children:[e.customerCode," - ",e.customerName]},e.id))})})}),a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{label:"所属业务员",name:"salesRepresentative",rules:[{required:!0,message:"请选择业务员"}],children:a.jsx(i.default,{placeholder:"请选择业务员",notFoundContent:"暂无数据",children:(Array.isArray(v)?v.filter(e=>"销售部"===e.department||"sales"===e.role):[]).map(e=>(0,a.jsxs)(es,{value:e.id,children:[e.employeeCode," - ",e.name]},e.id))})})})]}),(0,a.jsxs)(j.Z,{gutter:16,children:[a.jsx(w.Z,{xs:24,sm:24,md:7,lg:7,xl:7,children:a.jsx(u.Z.Item,{label:"客户名称",name:"customerName",children:a.jsx(n.default,{placeholder:"选择客户后自动填充",disabled:!0})})}),a.jsx(w.Z,{xs:24,sm:24,md:5,lg:5,xl:5,children:a.jsx(u.Z.Item,{label:"客户联系方式",name:"customerContact",children:a.jsx(n.default,{placeholder:"选择客户后自动填充",disabled:!0,style:{backgroundColor:"#f5f5f5"}})})}),a.jsx(w.Z,{xs:24,sm:24,md:12,lg:12,xl:12,children:a.jsx(u.Z.Item,{label:"备注",name:"remark",children:a.jsx(n.default,{placeholder:"请输入备注信息（可选）",showCount:!0,maxLength:500})})})]}),a.jsx(er,{value:D,onChange:A})]})})})},ei=(e={})=>{let{salesOrderNumber:t,enableRealTimeSync:r=!0,cacheTimeout:a=3e5,autoRefresh:l=!1,refreshInterval:i=3e4}=e,[n,o]=(0,s.useState)([]),[d,c]=(0,s.useState)(!1),[u,m]=(0,s.useState)(null),[h,p]=(0,s.useState)(null),x=(0,s.useCallback)(async()=>{c(!0),m(null);try{let e=await R.dataAccessManager.productionOrders.getAll({filters:t?{salesOrderNumber:t}:void 0});if("success"===e.status&&e.data&&e.data.items){let r=e.data.items;t&&(r=e.data.items.filter(e=>e.salesOrderNumber===t)),o(r),p(new Date)}else m(e.message||"获取生产订单数据失败")}catch(e){m("系统错误，请稍后重试")}c(!1)},[t]),g=(0,s.useCallback)(async()=>{await x()},[x]);(0,s.useEffect)(()=>{x()},[x]),(0,s.useEffect)(()=>{if(!r)return;let e=null;return l&&(e=setInterval(()=>{x()},i)),()=>{e&&clearInterval(e)}},[r,l,i,x]),(0,s.useEffect)(()=>{if(!l)return;let e=setInterval(()=>{x()},i);return()=>clearInterval(e)},[l,i,x]);let f=(0,s.useMemo)(()=>{let e={total:n.length,planned:0,inProgress:0,completed:0,cancelled:0};return n.forEach(t=>{switch(t.status){case"planned":e.planned++;break;case"in_progress":e.inProgress++;break;case"completed":e.completed++;break;case"cancelled":e.cancelled++}}),e},[n]);return{productionOrders:n,loading:d,error:u,refresh:g,statistics:f,lastUpdated:h}},en=e=>ei({salesOrderNumber:e,enableRealTimeSync:!0,autoRefresh:!1});r(49221);var eo=r(6487),ed=r(81037),ec=r(82366);let eu=e=>{let t={autoLoad:!0,pollingInterval:0,enableCache:!0,...e},[r,a]=(0,s.useState)({orders:[],loading:!1,error:null,lastUpdateTime:null}),l=(0,s.useCallback)(async()=>{a(e=>({...e,loading:!0,error:null}));try{let e=await (0,E.Ro)(()=>R.dataAccessManager.orders.getAll(),"加载销售订单");if(e&&e.items)return a(t=>({...t,orders:e.items,loading:!1,lastUpdateTime:Date.now()})),console.log(`✅ [useOrdersData] 加载销售订单成功: ${e.items.length} 个订单`),e.items;throw Error("订单数据格式错误")}catch(t){let e=t instanceof Error?t.message:"加载订单失败";return a(t=>({...t,loading:!1,error:e})),console.error("❌ [useOrdersData] 加载订单失败:",t),[]}},[]),i=(0,s.useCallback)(async()=>{try{return t.enableCache&&(R.dataAccessManager.clearDataTypeCache("orders"),console.log("\uD83D\uDD04 [useOrdersData] 已清理订单缓存")),await l()}catch(e){return console.error("❌ [useOrdersData] 刷新订单失败:",e),[]}},[l,t.enableCache]),n=(0,s.useCallback)(async e=>{try{let t=await R.dataAccessManager.orders.getById(e);if("success"===t.status&&t.data)return t.data;return console.error("❌ [useOrdersData] 获取订单失败:",t.message),null}catch(e){return console.error("❌ [useOrdersData] 获取订单异常:",e),null}},[]),o=(0,s.useCallback)(async e=>{try{let t=await R.dataAccessManager.orders.getByNumber(e);if("success"===t.status&&t.data)return t.data;return console.error("❌ [useOrdersData] 获取订单失败:",t.message),null}catch(e){return console.error("❌ [useOrdersData] 获取订单异常:",e),null}},[]),d=(0,s.useCallback)(async e=>{try{let t=await (0,E.Ro)(()=>R.dataAccessManager.orders.create(e),"创建销售订单");if(t)return await i(),console.log("✅ [useOrdersData] 创建订单成功:",t.orderNumber),t;throw Error("创建订单失败")}catch(e){throw console.error("❌ [useOrdersData] 创建订单失败:",e),e}},[i]),c=(0,s.useCallback)(async(e,t)=>{try{let r=await (0,E.Ro)(()=>R.dataAccessManager.orders.update(e,t),"更新销售订单");if(r)return await i(),console.log("✅ [useOrdersData] 更新订单成功:",e),r;throw Error("更新订单失败")}catch(e){throw console.error("❌ [useOrdersData] 更新订单失败:",e),e}},[i]),u=(0,s.useCallback)(async e=>{try{if(await (0,E.Ro)(()=>R.dataAccessManager.orders.delete(e),"删除销售订单"))return await i(),console.log("✅ [useOrdersData] 删除订单成功:",e),!0;throw Error("删除订单失败")}catch(e){throw console.error("❌ [useOrdersData] 删除订单失败:",e),e}},[i]);return(0,s.useEffect)(()=>{t.autoLoad&&l()},[t.autoLoad,l]),(0,s.useEffect)(()=>{if(t.pollingInterval>0){let e=setInterval(()=>{l()},t.pollingInterval);return()=>clearInterval(e)}},[t.pollingInterval,l]),{...r,loadOrders:l,refreshOrders:i,getOrderById:n,getOrderByNumber:o,createOrder:d,updateOrder:c,deleteOrder:u,hasOrders:r.orders.length>0,isEmpty:0===r.orders.length&&!r.loading,isStale:!r.lastUpdateTime||Date.now()-r.lastUpdateTime>3e5}};var em=r(51360),eh=r(87049),ep=r(39426);let ex={pending:{color:"orange",text:"待审核"},confirmed:{color:"blue",text:"已确认"},completed:{color:"green",text:"已完成"},cancelled:{color:"red",text:"已取消"}},eg={not_started:{color:"default",text:"未开始"},in_progress:{color:"processing",text:"执行中"},completed:{color:"success",text:"已完成"},failed:{color:"error",text:"执行失败"}},ef={unpaid:{color:"red",text:"未付款"},partial:{color:"orange",text:"部分付款"},paid:{color:"green",text:"已付款"}},ey={not_started:{color:"default",text:"未开始"},in_progress:{color:"processing",text:"生产中"},completed:{color:"success",text:"已完成"},paused:{color:"warning",text:"暂停"}},ej={title:"订单详情",statusConfig:ex,getStatus:e=>e.status,width:1200,sections:[{title:"基本信息",columns:3,bordered:!0,size:"small",fields:[{label:"销售订单号",key:"orderNumber"},{label:"客户名称",key:"customerName"},{label:"联系人",key:"customerContact"},{label:"订单日期",key:"orderDate",render:e=>ep.Xe.date(e)},{label:"交货日期",key:"deliveryDate",render:e=>ep.Xe.date(e)},{label:"承诺交期",key:"promisedDeliveryDate",render:e=>ep.Xe.date(e)}]},{title:"状态信息",columns:3,bordered:!0,size:"small",fields:[{label:"订单状态",key:"status",render:e=>ep.Xe.status(e,ex)},{label:"生产状态",key:"productionStatus",render:e=>ep.Xe.status(e,ey)},{label:"MRP状态",key:"mrpStatus",render:e=>ep.Xe.status(e||"not_started",eg)},{label:"付款状态",key:"paymentStatus",render:e=>ep.Xe.status(e,ef)},{label:"付款条件",key:"paymentTerms"},{label:"销售代表",key:"salesRepresentative"}]},{title:"执行信息",columns:3,bordered:!0,size:"small",fields:[{label:"MRP执行时间",key:"mrpExecutedAt",render:e=>e?ep.Xe.datetime(e):ep.Xe.empty("-")},{label:"MRP执行人",key:"mrpExecutedBy",render:e=>e||ep.Xe.empty("-")},{label:"",key:"placeholder",render:()=>ep.Xe.empty(""),span:1}]},{title:"金额信息",columns:3,bordered:!0,size:"small",fields:[{label:"订单总额",key:"totalAmount",render:e=>ep.Xe.currency(e||0)},{label:"折扣金额",key:"discountAmount",render:e=>ep.Xe.currency(e||0)},{label:"最终金额",key:"finalAmount",render:e=>(0,a.jsxs)("span",{style:{fontSize:"16px",fontWeight:"bold",color:"#3f8600"},children:["\xa5",(e||0).toLocaleString()]})}]},{title:"时间信息",columns:2,bordered:!0,size:"small",fields:[{label:"创建时间",key:"createdAt",render:e=>ep.Xe.datetime(e)},{label:"更新时间",key:"updatedAt",render:e=>ep.Xe.datetime(e)}]},{title:"备注信息",columns:1,bordered:!0,size:"small",fields:[{label:"备注",key:"remark",span:3,render:e=>e||ep.Xe.empty("无")}]},{title:"订单明细",columns:1,bordered:!1,fields:[],customContent:e=>a.jsx(Z.Z,{dataSource:e.items,rowKey:"id",pagination:!1,size:"small",columns:[{title:"产品型号",dataIndex:"productModelCode",key:"productModelCode",width:120},{title:"产品名称",dataIndex:"productName",key:"productName",width:150},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,render:(e,t)=>`${(e||0).toLocaleString()} ${t.unit||""}`},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:100,render:e=>`\xa5${e.toFixed(3)}`},{title:"小计",dataIndex:"totalPrice",key:"totalPrice",width:120,render:e=>(0,a.jsxs)("span",{style:{fontWeight:"bold"},children:["\xa5",(e||0).toLocaleString()]})},{title:"已交货",dataIndex:"deliveryQuantity",key:"deliveryQuantity",width:100,render:(e,t)=>`${(e||0).toLocaleString()} ${t.unit||""}`},{title:"剩余数量",dataIndex:"remainingQuantity",key:"remainingQuantity",width:100,render:(e,t)=>`${(e||0).toLocaleString()} ${t.unit||""}`},{title:"交货日期",dataIndex:"deliveryDate",key:"deliveryDate",width:120,render:(t,r)=>{let s=t||e.deliveryDate,l=!!t;return(0,a.jsxs)("span",{style:{color:l?"#1890ff":"#666",fontWeight:l?"bold":"normal"},children:[s,l&&a.jsx("span",{style:{fontSize:"12px",marginLeft:"4px"},children:"(项目级)"})]})}},{title:"生产进度",key:"progress",width:120,render:(e,t)=>{let r=(t.quantity-t.remainingQuantity)/t.quantity*100;return a.jsx(eh.Z,{percent:r,size:"small",status:100===r?"success":"active"})}},{title:"工位",dataIndex:"productionWorkstation",key:"productionWorkstation",width:80},{title:"批次号",dataIndex:"batchNumber",key:"batchNumber",width:120}]})}],actions:[{key:"mrp",text:e=>"completed"===e.mrpStatus?"MRP已完成":"in_progress"===e.mrpStatus?"MRP执行中":"failed"===e.mrpStatus?"重新启动MRP":"启动MRP",type:"primary",icon:a.jsx($.Z,{}),onClick:e=>{console.log("启动MRP:",e.orderNumber)},hidden:e=>"confirmed"!==e.status,disabled:e=>"completed"===e.mrpStatus||"in_progress"===e.mrpStatus}]},{Option:ew}=i.default,{TextArea:ev}=n.default,{RangePicker:eb}=o.default,{Step:eZ}=d.default,eP=()=>{let{message:e,modal:t}=c.Z.useApp(),[d,T]=(0,s.useState)(!1),[F,V]=(0,s.useState)(!1),[z,L]=(0,s.useState)(!1),[Y,X]=(0,s.useState)(!1),[W,U]=(0,s.useState)(!1),[B,H]=(0,s.useState)(null),[K,G]=(0,s.useState)(""),[J]=u.Z.useForm(),{orders:ee,loading:et,error:er,refreshOrders:ea,loadOrders:es,hasOrders:ei,isEmpty:eh}=eu({autoLoad:!0,enableCache:!0}),[ep,ex]=(0,s.useState)([]);(0,s.useEffect)(()=>{ex(ee)},[ee]),(0,s.useEffect)(()=>{er&&e.error(er)},[er,e]),(0,ed.el)("sales-orders-page",{onOrderCreated:()=>{console.log("\uD83D\uDCDD 检测到销售订单创建，自动刷新数据"),ea()},onOrderUpdated:()=>{console.log("\uD83D\uDCDD 检测到销售订单更新，自动刷新数据"),ea()},onOrderDeleted:()=>{console.log("\uD83D\uDCDD 检测到销售订单删除，自动刷新数据"),ea()}});let[eg,ef]=(0,s.useState)(""),[ey,eb]=(0,s.useState)(""),[eZ,eP]=(0,s.useState)(void 0),[eC,eS]=(0,s.useState)(void 0),ek=(0,eo.Yy)(e=>{ef(e)},300,[]),{metrics:eD,cacheStats:eA,isMonitoring:eN,clearCache:eI,getPerformanceAlerts:eM,formatMemorySize:eO,formatPercentage:eq,isHealthy:e$,needsOptimization:eR}=(0,ec.e)({interval:6e4,enabled:!0,showDetails:!1}),[eE,eT]=(0,s.useState)([]),[e_,eF]=(0,s.useState)([]),[eV,ez]=(0,s.useState)(!1),[eL,eY]=(0,s.useState)(null),[eQ,eX]=(0,s.useState)(!1),[eW,eU]=(0,s.useState)(0),[eB]=(0,s.useState)(["启动MRP","MRP分析","生成生产订单","完成"]),[eH]=(0,s.useState)({}),{productionOrders:eK,loading:eG}=en(B?.orderNumber||""),eJ=e=>{let t={pending:{color:"red",text:"未审核"},confirmed:{color:"green",text:"已审核"},completed:{color:"gray",text:"完成"},cancelled:{color:"orange",text:"已取消"}}[e]||{color:"default",text:"未知"};return a.jsx(m.Z,{color:t.color,children:t.text})},e0=(e,t)=>{if("pending"===e||"cancelled"===e)return a.jsx("span",{style:{color:"#999"},children:"-"});let r={not_started:{color:"orange",text:"未开始"},pending:{color:"blue",text:"待生产"},in_progress:{color:"green",text:"生产中"},completed:{color:"cyan",text:"已完成"}}["confirmed"!==e||t?t:"not_started"]||{color:"orange",text:"未开始"};return a.jsx(m.Z,{color:r.color,children:r.text})},e1=e=>{let t={unpaid:{color:"red",text:"未付款"},partial:{color:"orange",text:"部分付款"},paid:{color:"green",text:"已付款"}}[e]||{color:"default",text:"未知"};return a.jsx(m.Z,{color:t.color,children:t.text})},e2=ep.filter(e=>{let t=!eg||e.orderNumber.toLowerCase().includes(eg.toLowerCase())||e.customerName.toLowerCase().includes(eg.toLowerCase())||e.customerContact.toLowerCase().includes(eg.toLowerCase()),r=!eZ||e.status===eZ,a=!eC||e.productionStatus===eC;return t&&r&&a}),e6={total:ep.length,pending:ep.filter(e=>"pending"===e.status).length,confirmed:ep.filter(e=>"confirmed"===e.status).length,completed:ep.filter(e=>"completed"===e.status).length,cancelled:ep.filter(e=>"cancelled"===e.status).length,totalAmount:ep.reduce((e,t)=>e+t.finalAmount,0),delayedOrders:ep.filter(e=>new Date(e.deliveryDate)<new Date&&"completed"!==e.status&&"cancelled"!==e.status).length};(0,l.useRouter)();let e8=e=>{H(e),L(!0)},e4=e=>{H(e),G(""),X(!0),J.resetFields()},e5=(e,t)=>e&&({quantity:{originalValue:"当前订单总数量",newValue:"请输入新的数量"},delivery_date:{originalValue:"当前交期日期",newValue:"请选择新的交期日期"},cancel:{originalValue:"当前订单状态",newValue:"取消原因"}})[e]?.[t]||("originalValue"===t?"请输入原始值":"请输入新值"),e3=(e,t)=>{let r=e5(e,t),s="originalValue"===t&&!!K;switch(e){case"quantity":return a.jsx(y.Z,{placeholder:r,min:0,style:{width:"100%"},readOnly:s});case"delivery_date":if("originalValue"===t)return a.jsx(n.default,{placeholder:r,readOnly:!0});return a.jsx(o.default,{placeholder:r,style:{width:"100%"}});default:return a.jsx(n.default,{placeholder:r,readOnly:s})}},e9=async t=>{null!==await (0,E.Ro)(()=>R.dataAccessManager.orders.delete(t),"删除订单")?(await ea(),e.success("订单删除成功")):e.error("删除订单失败")},e7=async t=>{await ea(),e.success("订单创建成功"),U(!1)},te=async t=>{try{if("confirmed"!==t.status){e.error("只有已审核的订单才能启动MRP");return}if("completed"===t.mrpStatus){e.warning("该订单的MRP已经执行完成");return}if("in_progress"===t.mrpStatus){e.warning("该订单的MRP正在执行中，请等待完成");return}T(!0),eU(1),eX(!1),eY(null);try{await R.dataAccessManager.orders.update(t.id,{mrpStatus:"in_progress",updatedAt:new Date().toISOString()}),B&&B.id===t.id&&H({...B,mrpStatus:"in_progress",updatedAt:new Date().toISOString()}),await ea()}catch(e){console.error("更新订单MRP状态失败:",e)}let{mrpService:a}=await r.e(1428).then(r.bind(r,1428));e.info("正在启动MRP..."),await new Promise(e=>setTimeout(e,1e3)),eU(2),e.info("正在进行MRP分析..."),await new Promise(e=>setTimeout(e,1500)),eU(3),e.info("正在生成生产订单...");let s=await a.executeMRP({salesOrder:t,executedBy:"当前用户",executionDate:new Date().toISOString()});eU(4),await new Promise(e=>setTimeout(e,500)),T(!1),eY(s),eX(!0);try{await R.dataAccessManager.orders.update(t.id,{mrpStatus:"completed",mrpExecutedAt:new Date().toISOString(),mrpExecutedBy:"当前用户",mrpResultId:s.id,updatedAt:new Date().toISOString()}),B&&B.id===t.id&&H({...B,mrpStatus:"completed",mrpExecutedAt:new Date().toISOString(),mrpExecutedBy:"当前用户",mrpResultId:s.id,updatedAt:new Date().toISOString()})}catch(e){console.error("更新订单MRP完成状态失败:",e)}await ea(),s.generatedProductionOrders&&s.generatedProductionOrders.length>0?e.success(`MRP执行完成！生成了 ${s.totalProductionOrders} 个生产订单，请前往生产管理模块查看`):e.success(`MRP执行完成！未生成新的生产订单（可能库存充足）`)}catch(r){T(!1),await (0,E.Ro)(()=>R.dataAccessManager.orders.update(t.id,{mrpStatus:"failed",updatedAt:new Date().toISOString()}),"更新订单MRP状态")&&(B&&B.id===t.id&&H({...B,mrpStatus:"failed",updatedAt:new Date().toISOString()}),await ea()),e.error(`MRP执行失败: ${r instanceof Error?r.message:"未知错误"}`)}};return(0,a.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:[(0,a.jsxs)(j.Z,{gutter:[16,16],children:[a.jsx(w.Z,{xs:24,sm:6,children:a.jsx(v.Z,{children:a.jsx(b.Z,{title:"订单总数",value:e6.total,suffix:"个",prefix:a.jsx(A.Z,{})})})}),a.jsx(w.Z,{xs:24,sm:6,children:a.jsx(v.Z,{children:a.jsx(b.Z,{title:"订单总额",value:e6.totalAmount,precision:0,prefix:"\xa5",valueStyle:{color:"#3f8600"}})})}),a.jsx(w.Z,{xs:24,sm:6,children:a.jsx(v.Z,{children:a.jsx(b.Z,{title:"生产中订单",value:ep.filter(e=>"in_progress"===e.productionStatus).length,suffix:"个",valueStyle:{color:"#1890ff"}})})}),a.jsx(w.Z,{xs:24,sm:6,children:a.jsx(v.Z,{children:a.jsx(b.Z,{title:"已取消订单",value:e6.cancelled,suffix:"个",valueStyle:{color:"#fa8c16"}})})})]}),eD&&(0,a.jsxs)(j.Z,{gutter:[16,16],children:[a.jsx(w.Z,{xs:24,sm:8,children:a.jsx(v.Z,{size:"small",children:a.jsx(b.Z,{title:"平均响应时间",value:`${eD.averageResponseTime}ms`,valueStyle:{color:eD.averageResponseTime>1e3?"#ff4d4f":eD.averageResponseTime>500?"#fa8c16":"#3f8600"}})})}),a.jsx(w.Z,{xs:24,sm:8,children:a.jsx(v.Z,{size:"small",children:a.jsx(b.Z,{title:"缓存命中率",value:eq(eA?.hitRate||0),valueStyle:{color:.3>(eA?.hitRate||0)?"#ff4d4f":.6>(eA?.hitRate||0)?"#fa8c16":"#3f8600"}})})}),a.jsx(w.Z,{xs:24,sm:8,children:a.jsx(v.Z,{size:"small",children:(0,a.jsxs)("div",{style:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,a.jsxs)("div",{children:[a.jsx("div",{style:{fontSize:"14px",color:"#6b7280"},children:"监控状态"}),a.jsx("div",{style:{fontSize:"18px",fontWeight:"500"},children:a.jsx(x.Z,{status:eN?"processing":"default",text:eN?"运行中":"已停止"})})]}),!e$&&a.jsx(p.Z,{title:"系统性能需要优化",children:a.jsx(h.ZP,{size:"small",type:"text",danger:!0,onClick:()=>eI(),children:"⚠️ 优化"})})]})})})]}),a.jsx(v.Z,{size:"small",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[a.jsx(n.default,{placeholder:"搜索订单号、客户名称或联系人",prefix:a.jsx(N.Z,{}),value:ey,onChange:e=>{let t=e.target.value;eb(t),ek(t)},className:"w-full sm:w-64"}),(0,a.jsxs)(i.default,{placeholder:"订单状态",value:eZ,onChange:eP,className:"w-full sm:w-32",allowClear:!0,children:[a.jsx(ew,{value:"pending",children:"未审核"}),a.jsx(ew,{value:"confirmed",children:"已审核"}),a.jsx(ew,{value:"completed",children:"完成"}),a.jsx(ew,{value:"cancelled",children:"已取消"})]}),(0,a.jsxs)(i.default,{placeholder:"生产状态",value:eC,onChange:eS,className:"w-full sm:w-32",allowClear:!0,children:[a.jsx(ew,{value:"not_started",children:"未开始"}),a.jsx(ew,{value:"pending",children:"待生产"}),a.jsx(ew,{value:"in_progress",children:"生产中"}),a.jsx(ew,{value:"completed",children:"已完成"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[!1,a.jsx(h.ZP,{icon:a.jsx(I.Z,{}),children:"导出"}),a.jsx(h.ZP,{type:"primary",icon:a.jsx(M.Z,{}),onClick:()=>{U(!0)},children:"新增订单"})]})]})}),(0,a.jsxs)(v.Z,{title:"订单列表",size:"small",children:[(0,a.jsxs)("div",{className:"mb-3 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0",children:[(0,a.jsxs)(g.Z,{wrap:!0,children:[(0,a.jsxs)(h.ZP,{type:"primary",icon:a.jsx(O.Z,{}),onClick:()=>{if(0===eE.length){e.warning("请先选择要审核的订单");return}let r=e_.filter(e=>"pending"===e.status);if(0===r.length){e.warning("所选订单中没有未审核的订单");return}t.confirm({title:"批量审核确认",content:`确定要审核 ${r.length} 个订单吗？`,onOk:async()=>{ez(!0);try{let t=r.map(e=>(0,E.Ro)(()=>R.dataAccessManager.orders.update(e.id,{status:"confirmed",productionStatus:"not_started",updatedAt:new Date().toISOString()}),`审核订单 ${e.orderNumber}`)),a=(await Promise.all(t)).filter(e=>null!==e).length;a>0?(await ea(),eT([]),eF([]),e.success(`成功审核 ${a} 个订单`)):e.error("批量审核失败，没有订单被成功审核")}catch(t){e.error(`批量审核失败: ${t instanceof Error?t.message:"未知错误"}`),console.error("批量审核异常:",t)}finally{ez(!1)}}})},disabled:0===eE.length||!e_.some(e=>"pending"===e.status),loading:eV,children:["审核 (",e_.filter(e=>"pending"===e.status).length,")"]}),(0,a.jsxs)(h.ZP,{icon:a.jsx(q.Z,{}),onClick:()=>{if(0===eE.length){e.warning("请先选择要反审核的订单");return}let r=e_.filter(e=>"confirmed"===e.status);if(0===r.length){e.warning("所选订单中没有已审核的订单");return}t.confirm({title:"批量反审核确认",content:`确定要反审核 ${r.length} 个订单吗？`,onOk:async()=>{ez(!0);try{let t=r.map(e=>(0,E.Ro)(()=>R.dataAccessManager.orders.update(e.id,{status:"pending",productionStatus:"not_started",updatedAt:new Date().toISOString()}),`反审核订单 ${e.orderNumber}`)),a=(await Promise.all(t)).filter(e=>null!==e).length;a>0?(await ea(),eT([]),eF([]),e.success(`成功反审核 ${a} 个订单`)):e.error("批量反审核失败，没有订单被成功反审核")}catch(t){e.error(`批量反审核失败: ${t instanceof Error?t.message:"未知错误"}`),console.error("批量反审核异常:",t)}finally{ez(!1)}}})},disabled:0===eE.length||!e_.some(e=>"confirmed"===e.status),loading:eV,children:["反审核 (",e_.filter(e=>"confirmed"===e.status).length,")"]})]}),a.jsx("div",{className:"text-sm text-gray-500",children:eE.length>0?`已选择 ${eE.length} 个订单`:`共 ${e2.length} 个订单`})]}),a.jsx(Z.Z,{columns:[{title:"销售订单号",dataIndex:"orderNumber",key:"orderNumber",width:140,fixed:"left",render:(e,t)=>a.jsx(h.ZP,{type:"link",onClick:()=>e8(t),style:{padding:0,height:"auto",fontWeight:"bold"},children:e})},{title:"订单状态",dataIndex:"status",key:"status",width:100,render:e=>eJ(e)},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:160,sorter:(e,t)=>new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime(),defaultSortOrder:"descend",render:e=>e?_()(e).format("YYYY-MM-DD HH:mm:ss"):"-"},{title:"客户名称",dataIndex:"customerName",key:"customerName",width:180,ellipsis:!0},{title:"订单日期",dataIndex:"orderDate",key:"orderDate",width:120,sorter:(e,t)=>new Date(e.orderDate).getTime()-new Date(t.orderDate).getTime()},{title:"订单金额",dataIndex:"finalAmount",key:"finalAmount",width:150,sorter:(e,t)=>e.finalAmount-t.finalAmount,render:(e,t)=>(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{style:{fontWeight:"bold",color:"#1890ff"},children:["\xa5",(e||0).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})]}),(t.discountAmount||0)>0&&(0,a.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["折扣: \xa5",(t.discountAmount||0).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2})]})]})},{title:"生产状态",dataIndex:"productionStatus",key:"productionStatus",width:120,render:(e,t)=>e0(t.status,e)},{title:"付款状态",dataIndex:"paymentStatus",key:"paymentStatus",width:100,render:e=>e1(e)},{title:"变更次数",key:"changeCount",width:100,render:(e,t)=>a.jsx("div",{children:t.changes.length>0?a.jsx(p.Z,{title:"点击查看变更历史",children:a.jsx(x.Z,{count:t.changes.length,size:"small",children:(0,a.jsxs)(h.ZP,{type:"link",size:"small",children:[t.changes.length,"次"]})})}):a.jsx("span",{style:{color:"#666"},children:"无"})})},{title:"操作",key:"action",width:180,fixed:"right",render:(e,t)=>(0,a.jsxs)(g.Z,{size:"small",children:[a.jsx(h.ZP,{type:"link",icon:a.jsx(k.Z,{}),onClick:()=>e4(t),children:"变更"}),a.jsx(f.Z,{title:"确定要删除这个订单吗？",onConfirm:()=>e9(t.id),okText:"确定",cancelText:"取消",children:a.jsx(h.ZP,{type:"link",danger:!0,icon:a.jsx(D.Z,{}),children:"删除"})})]})}],dataSource:e2,rowKey:"id",loading:et||eV,rowSelection:{selectedRowKeys:eE,onChange:(e,t)=>{eT(e),eF(t)},getCheckboxProps:e=>({disabled:!1})},pagination:{total:e2.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`,pageSizeOptions:["10","20","50","100"],defaultPageSize:10},scroll:{x:1600}})]}),(0,a.jsxs)(P.Z,{title:"订单变更申请",open:Y,onOk:()=>{J.validateFields().then(async t=>{if(!B)return;let r=Q.validateOrderChange(B,t.changeType,t.originalValue,t.newValue);if(!r.isValid){e.error(`变更验证失败：${r.errors[0]}`);return}r.warnings&&r.warnings.length>0&&r.warnings.forEach(t=>{e.warning(t)});let a=new Date().toISOString(),s={id:Date.now().toString(),orderNumber:B.orderNumber,...t,changeStatus:"pending",applicant:"当前用户",customerConfirmed:!1,productionStatus:B.productionStatus,createdAt:a},l=ep.find(e=>e.id===B.id);if(l){let t=[...l.changes||[],s];await (0,E.Ro)(()=>R.dataAccessManager.orders.update(B.id,{changes:t,updatedAt:a}),"提交变更申请")?(await ea(),X(!1),J.resetFields(),e.success("订单变更申请提交成功")):e.error("提交变更申请失败")}})},onCancel:()=>{X(!1),G(""),J.resetFields()},width:600,okText:"提交申请",cancelText:"取消",destroyOnHidden:!0,children:[a.jsx(C.Z,{message:"变更控制流程",description:"系统将根据生产状态自动判断变更可行性",type:"warning",showIcon:!0,style:{marginBottom:16}}),(0,a.jsxs)(u.Z,{form:J,layout:"vertical",children:[a.jsx(u.Z.Item,{name:"changeType",label:"变更类型",rules:[{required:!0,message:"请选择变更类型"}],children:(0,a.jsxs)(i.default,{placeholder:"请选择变更类型",onChange:e=>{if(!B)return;G(e);let t="";switch(e){case"quantity":t=(B.items?.reduce((e,t)=>e+t.quantity,0)||0).toString();break;case"delivery_date":try{t=new Date(B.deliveryDate).toISOString().split("T")[0]}catch(e){t=B.deliveryDate.split("T")[0]}break;case"cancel":t=({pending:"未审核",confirmed:"已审核",completed:"完成",cancelled:"已取消"})[B.status]||B.status;break;default:t=""}J.setFieldsValue({originalValue:t,newValue:""})},children:[a.jsx(ew,{value:"quantity",children:"数量变更"}),a.jsx(ew,{value:"delivery_date",children:"交期变更"}),a.jsx(ew,{value:"cancel",children:"订单取消"})]})}),K&&a.jsx(C.Z,{message:`${"quantity"===K?"数量变更":"delivery_date"===K?"交期变更":"订单取消"}提示`,description:"quantity"===K?"系统已自动填入当前订单总数量，请在新值中输入变更后的数量。":"delivery_date"===K?"系统已自动填入当前交期，请选择新的交期日期。":"系统已自动填入当前订单状态，请在新值中说明取消原因。",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,a.jsxs)(j.Z,{gutter:16,children:[a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{name:"originalValue",label:"原始值",rules:[{required:!0,message:"请输入原始值"}],children:e3(K,"originalValue")})}),a.jsx(w.Z,{span:12,children:a.jsx(u.Z.Item,{name:"newValue",label:"新值",rules:[{required:!0,message:"请输入新值"}],children:e3(K,"newValue")})})]}),a.jsx(u.Z.Item,{name:"changeReason",label:"变更原因",rules:[{required:!0,message:"请输入变更原因"}],children:a.jsx(ev,{rows:3,placeholder:"请详细说明变更原因"})}),B&&(0,a.jsxs)("div",{style:{marginTop:16},children:[a.jsx("h4",{children:"当前订单状态"}),(0,a.jsxs)(S.Z,{size:"small",column:2,children:[a.jsx(S.Z.Item,{label:"销售订单号",children:B.orderNumber}),a.jsx(S.Z.Item,{label:"生产状态",children:e0(B.status,B.productionStatus)}),a.jsx(S.Z.Item,{label:"订单状态",children:eJ(B.status)}),(0,a.jsxs)(S.Z.Item,{label:"变更次数",children:[B.changes.length,"次"]})]})]})]})]}),a.jsx(em.ZP,{open:z,order:B,onClose:()=>{L(!1),eX(!1),eY(null),T(!1),eU(0)},config:{...ej,actions:[{key:"mrp",text:e=>d?"执行中...":"completed"===e.mrpStatus?"MRP已完成":"in_progress"===e.mrpStatus?"MRP执行中":"failed"===e.mrpStatus?"重新启动MRP":eQ?"MRP已完成":"启动MRP",type:"primary",icon:a.jsx($.Z,{}),onClick:e=>te(e),loading:d,hidden:e=>"confirmed"!==e.status,disabled:e=>d||"completed"===e.mrpStatus||"in_progress"===e.mrpStatus||eQ,style:{marginRight:8}}]}}),a.jsx(el,{open:W,onCancel:()=>{U(!1)},onSuccess:e7})]})};function eC(){return a.jsx(c.Z,{children:a.jsx(eP,{})})}},75497:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(25036),s=r(38834);function l({children:e}){return a.jsx(s.Z,{children:e})}},56356:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>l,__esModule:()=>s,default:()=>i});let a=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\sales\orders\page.tsx`),{__esModule:s,$$typeof:l}=a,i=a.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,2345,7049,4441,7557,7383,152,934,5236,6274,996,6133,9094],()=>r(2868));module.exports=a})();