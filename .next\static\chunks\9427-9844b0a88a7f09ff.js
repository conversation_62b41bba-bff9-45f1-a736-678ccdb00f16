"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9427],{9427:function(t,o,n){n.d(o,{Z:function(){return B}});var e=n(2265),a=n(42744),r=n.n(a),i=n(32467),c=n(29810),l=n(65823),s=n(57499),u=n(58489),d=n(11303),m=n(82303),b=n(12711),g=n(78387);let p=new u.E4("antStatusProcessing",{"0%":{transform:"scale(0.8)",opacity:.5},"100%":{transform:"scale(2.4)",opacity:0}}),f=new u.E4("antZoomBadgeIn",{"0%":{transform:"scale(0) translate(50%, -50%)",opacity:0},"100%":{transform:"scale(1) translate(50%, -50%)"}}),v=new u.E4("antZoomBadgeOut",{"0%":{transform:"scale(1) translate(50%, -50%)"},"100%":{transform:"scale(0) translate(50%, -50%)",opacity:0}}),h=new u.E4("antNoWrapperZoomBadgeIn",{"0%":{transform:"scale(0)",opacity:0},"100%":{transform:"scale(1)"}}),y=new u.E4("antNoWrapperZoomBadgeOut",{"0%":{transform:"scale(1)"},"100%":{transform:"scale(0)",opacity:0}}),O=new u.E4("antBadgeLoadingCircle",{"0%":{transformOrigin:"50%"},"100%":{transform:"translate(50%, -50%) rotate(360deg)",transformOrigin:"50%"}}),w=t=>{let{componentCls:o,iconCls:n,antCls:e,badgeShadowSize:a,textFontSize:r,textFontSizeSM:i,statusSize:c,dotSize:l,textFontWeight:s,indicatorHeight:b,indicatorHeightSM:g,marginXS:w,calc:E}=t,C="".concat(e,"-scroll-number"),N=(0,m.Z)(t,(t,n)=>{let{darkColor:e}=n;return{["&".concat(o," ").concat(o,"-color-").concat(t)]:{background:e,["&:not(".concat(o,"-count)")]:{color:e},"a:hover &":{background:e}}}});return{[o]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,d.Wf)(t)),{position:"relative",display:"inline-block",width:"fit-content",lineHeight:1,["".concat(o,"-count")]:{display:"inline-flex",justifyContent:"center",zIndex:t.indicatorZIndex,minWidth:b,height:b,color:t.badgeTextColor,fontWeight:s,fontSize:r,lineHeight:(0,u.bf)(b),whiteSpace:"nowrap",textAlign:"center",background:t.badgeColor,borderRadius:E(b).div(2).equal(),boxShadow:"0 0 0 ".concat((0,u.bf)(a)," ").concat(t.badgeShadowColor),transition:"background ".concat(t.motionDurationMid),a:{color:t.badgeTextColor},"a:hover":{color:t.badgeTextColor},"a:hover &":{background:t.badgeColorHover}},["".concat(o,"-count-sm")]:{minWidth:g,height:g,fontSize:i,lineHeight:(0,u.bf)(g),borderRadius:E(g).div(2).equal()},["".concat(o,"-multiple-words")]:{padding:"0 ".concat((0,u.bf)(t.paddingXS)),bdi:{unicodeBidi:"plaintext"}},["".concat(o,"-dot")]:{zIndex:t.indicatorZIndex,width:l,minWidth:l,height:l,background:t.badgeColor,borderRadius:"100%",boxShadow:"0 0 0 ".concat((0,u.bf)(a)," ").concat(t.badgeShadowColor)},["".concat(o,"-count, ").concat(o,"-dot, ").concat(C,"-custom-component")]:{position:"absolute",top:0,insetInlineEnd:0,transform:"translate(50%, -50%)",transformOrigin:"100% 0%",["&".concat(n,"-spin")]:{animationName:O,animationDuration:"1s",animationIterationCount:"infinite",animationTimingFunction:"linear"}},["&".concat(o,"-status")]:{lineHeight:"inherit",verticalAlign:"baseline",["".concat(o,"-status-dot")]:{position:"relative",top:-1,display:"inline-block",width:c,height:c,verticalAlign:"middle",borderRadius:"50%"},["".concat(o,"-status-success")]:{backgroundColor:t.colorSuccess},["".concat(o,"-status-processing")]:{overflow:"visible",color:t.colorInfo,backgroundColor:t.colorInfo,borderColor:"currentcolor","&::after":{position:"absolute",top:0,insetInlineStart:0,width:"100%",height:"100%",borderWidth:a,borderStyle:"solid",borderColor:"inherit",borderRadius:"50%",animationName:p,animationDuration:t.badgeProcessingDuration,animationIterationCount:"infinite",animationTimingFunction:"ease-in-out",content:'""'}},["".concat(o,"-status-default")]:{backgroundColor:t.colorTextPlaceholder},["".concat(o,"-status-error")]:{backgroundColor:t.colorError},["".concat(o,"-status-warning")]:{backgroundColor:t.colorWarning},["".concat(o,"-status-text")]:{marginInlineStart:w,color:t.colorText,fontSize:t.fontSize}}}),N),{["".concat(o,"-zoom-appear, ").concat(o,"-zoom-enter")]:{animationName:f,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},["".concat(o,"-zoom-leave")]:{animationName:v,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack,animationFillMode:"both"},["&".concat(o,"-not-a-wrapper")]:{["".concat(o,"-zoom-appear, ").concat(o,"-zoom-enter")]:{animationName:h,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},["".concat(o,"-zoom-leave")]:{animationName:y,animationDuration:t.motionDurationSlow,animationTimingFunction:t.motionEaseOutBack},["&:not(".concat(o,"-status)")]:{verticalAlign:"middle"},["".concat(C,"-custom-component, ").concat(o,"-count")]:{transform:"none"},["".concat(C,"-custom-component, ").concat(C)]:{position:"relative",top:"auto",display:"block",transformOrigin:"50% 50%"}},[C]:{overflow:"hidden",transition:"all ".concat(t.motionDurationMid," ").concat(t.motionEaseOutBack),["".concat(C,"-only")]:{position:"relative",display:"inline-block",height:b,transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseOutBack),WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden",["> p".concat(C,"-only-unit")]:{height:b,margin:0,WebkitTransformStyle:"preserve-3d",WebkitBackfaceVisibility:"hidden"}},["".concat(C,"-symbol")]:{verticalAlign:"top"}},"&-rtl":{direction:"rtl",["".concat(o,"-count, ").concat(o,"-dot, ").concat(C,"-custom-component")]:{transform:"translate(-50%, -50%)"}}})}},E=t=>{let{fontHeight:o,lineWidth:n,marginXS:e,colorBorderBg:a}=t,r=t.colorTextLightSolid,i=t.colorError,c=t.colorErrorHover;return(0,b.IX)(t,{badgeFontHeight:o,badgeShadowSize:n,badgeTextColor:r,badgeColor:i,badgeColorHover:c,badgeShadowColor:a,badgeProcessingDuration:"1.2s",badgeRibbonOffset:e,badgeRibbonCornerTransform:"scaleY(0.75)",badgeRibbonCornerFilter:"brightness(75%)"})},C=t=>{let{fontSize:o,lineHeight:n,fontSizeSM:e,lineWidth:a}=t;return{indicatorZIndex:"auto",indicatorHeight:Math.round(o*n)-2*a,indicatorHeightSM:o,dotSize:e/2,textFontSize:e,textFontSizeSM:e,textFontWeight:"normal",statusSize:e/2}};var N=(0,g.I$)("Badge",t=>w(E(t)),C);let S=t=>{let{antCls:o,badgeFontHeight:n,marginXS:e,badgeRibbonOffset:a,calc:r}=t,i="".concat(o,"-ribbon"),c=(0,m.Z)(t,(t,o)=>{let{darkColor:n}=o;return{["&".concat(i,"-color-").concat(t)]:{background:n,color:n}}});return{["".concat(o,"-ribbon-wrapper")]:{position:"relative"},[i]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,d.Wf)(t)),{position:"absolute",top:e,padding:"0 ".concat((0,u.bf)(t.paddingXS)),color:t.colorPrimary,lineHeight:(0,u.bf)(n),whiteSpace:"nowrap",backgroundColor:t.colorPrimary,borderRadius:t.borderRadiusSM,["".concat(i,"-text")]:{color:t.badgeTextColor},["".concat(i,"-corner")]:{position:"absolute",top:"100%",width:a,height:a,color:"currentcolor",border:"".concat((0,u.bf)(r(a).div(2).equal())," solid"),transform:t.badgeRibbonCornerTransform,transformOrigin:"top",filter:t.badgeRibbonCornerFilter}}),c),{["&".concat(i,"-placement-end")]:{insetInlineEnd:r(a).mul(-1).equal(),borderEndEndRadius:0,["".concat(i,"-corner")]:{insetInlineEnd:0,borderInlineEndColor:"transparent",borderBlockEndColor:"transparent"}},["&".concat(i,"-placement-start")]:{insetInlineStart:r(a).mul(-1).equal(),borderEndStartRadius:0,["".concat(i,"-corner")]:{insetInlineStart:0,borderBlockEndColor:"transparent",borderInlineStartColor:"transparent"}},"&-rtl":{direction:"rtl"}})}};var j=(0,g.I$)(["Badge","Ribbon"],t=>S(E(t)),C);let x=t=>{let o;let{prefixCls:n,value:a,current:i,offset:c=0}=t;return c&&(o={position:"absolute",top:"".concat(c,"00%"),left:0}),e.createElement("span",{style:o,className:r()("".concat(n,"-only-unit"),{current:i})},a)};var k=t=>{let o,n;let{prefixCls:a,count:r,value:i}=t,c=Number(i),l=Math.abs(r),[s,u]=e.useState(c),[d,m]=e.useState(l),b=()=>{u(c),m(l)};if(e.useEffect(()=>{let t=setTimeout(b,1e3);return()=>clearTimeout(t)},[c]),s===c||Number.isNaN(c)||Number.isNaN(s))o=[e.createElement(x,Object.assign({},t,{key:c,current:!0}))],n={transition:"none"};else{o=[];let a=c+10,r=[];for(let t=c;t<=a;t+=1)r.push(t);let i=d<l?1:-1,u=r.findIndex(t=>t%10===s);o=(i<0?r.slice(0,u+1):r.slice(u)).map((o,n)=>e.createElement(x,Object.assign({},t,{key:o,value:o%10,offset:i<0?n-u:n,current:n===u}))),n={transform:"translateY(".concat(-function(t,o,n){let e=t,a=0;for(;(e+10)%10!==o;)e+=n,a+=n;return a}(s,c,i),"00%)")}}return e.createElement("span",{className:"".concat(a,"-only"),style:n,onTransitionEnd:b},o)},I=function(t,o){var n={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&0>o.indexOf(e)&&(n[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,e=Object.getOwnPropertySymbols(t);a<e.length;a++)0>o.indexOf(e[a])&&Object.prototype.propertyIsEnumerable.call(t,e[a])&&(n[e[a]]=t[e[a]]);return n};let T=e.forwardRef((t,o)=>{let{prefixCls:n,count:a,className:i,motionClassName:c,style:u,title:d,show:m,component:b="sup",children:g}=t,p=I(t,["prefixCls","count","className","motionClassName","style","title","show","component","children"]),{getPrefixCls:f}=e.useContext(s.E_),v=f("scroll-number",n),h=Object.assign(Object.assign({},p),{"data-show":m,style:u,className:r()(v,i,c),title:d}),y=a;if(a&&Number(a)%1==0){let t=String(a).split("");y=e.createElement("bdi",null,t.map((o,n)=>e.createElement(k,{prefixCls:v,count:Number(a),value:o,key:t.length-n})))}return((null==u?void 0:u.borderColor)&&(h.style=Object.assign(Object.assign({},u),{boxShadow:"0 0 0 1px ".concat(u.borderColor," inset")})),g)?(0,l.Tm)(g,t=>({className:r()("".concat(v,"-custom-component"),null==t?void 0:t.className,c)})):e.createElement(b,Object.assign({},h,{ref:o}),y)});var R=function(t,o){var n={};for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&0>o.indexOf(e)&&(n[e]=t[e]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,e=Object.getOwnPropertySymbols(t);a<e.length;a++)0>o.indexOf(e[a])&&Object.prototype.propertyIsEnumerable.call(t,e[a])&&(n[e[a]]=t[e[a]]);return n};let z=e.forwardRef((t,o)=>{var n,a,u,d,m;let{prefixCls:b,scrollNumberPrefixCls:g,children:p,status:f,text:v,color:h,count:y=null,overflowCount:O=99,dot:w=!1,size:E="default",title:C,offset:S,style:j,className:x,rootClassName:k,classNames:I,styles:z,showZero:B=!1}=t,D=R(t,["prefixCls","scrollNumberPrefixCls","children","status","text","color","count","overflowCount","dot","size","title","offset","style","className","rootClassName","classNames","styles","showZero"]),{getPrefixCls:P,direction:W,badge:F}=e.useContext(s.E_),Z=P("badge",b),[M,H,_]=N(Z),A=y>O?"".concat(O,"+"):y,q="0"===A||0===A,X=null===y||q&&!B,L=(null!=f||null!=h)&&X,V=null!=f||!q,Y=w&&!q,$=Y?"":A,G=(0,e.useMemo)(()=>(null==$||""===$||q&&!B)&&!Y,[$,q,B,Y]),J=(0,e.useRef)(y);G||(J.current=y);let K=J.current,Q=(0,e.useRef)($);G||(Q.current=$);let U=Q.current,tt=(0,e.useRef)(Y);G||(tt.current=Y);let to=(0,e.useMemo)(()=>{if(!S)return Object.assign(Object.assign({},null==F?void 0:F.style),j);let t={marginTop:S[1]};return"rtl"===W?t.left=parseInt(S[0],10):t.right=-parseInt(S[0],10),Object.assign(Object.assign(Object.assign({},t),null==F?void 0:F.style),j)},[W,S,j,null==F?void 0:F.style]),tn=null!=C?C:"string"==typeof K||"number"==typeof K?K:void 0,te=G||!v?null:e.createElement("span",{className:"".concat(Z,"-status-text")},v),ta=K&&"object"==typeof K?(0,l.Tm)(K,t=>({style:Object.assign(Object.assign({},to),t.style)})):void 0,tr=(0,c.o2)(h,!1),ti=r()(null==I?void 0:I.indicator,null===(n=null==F?void 0:F.classNames)||void 0===n?void 0:n.indicator,{["".concat(Z,"-status-dot")]:L,["".concat(Z,"-status-").concat(f)]:!!f,["".concat(Z,"-color-").concat(h)]:tr}),tc={};h&&!tr&&(tc.color=h,tc.background=h);let tl=r()(Z,{["".concat(Z,"-status")]:L,["".concat(Z,"-not-a-wrapper")]:!p,["".concat(Z,"-rtl")]:"rtl"===W},x,k,null==F?void 0:F.className,null===(a=null==F?void 0:F.classNames)||void 0===a?void 0:a.root,null==I?void 0:I.root,H,_);if(!p&&L&&(v||V||!X)){let t=to.color;return M(e.createElement("span",Object.assign({},D,{className:tl,style:Object.assign(Object.assign(Object.assign({},null==z?void 0:z.root),null===(u=null==F?void 0:F.styles)||void 0===u?void 0:u.root),to)}),e.createElement("span",{className:ti,style:Object.assign(Object.assign(Object.assign({},null==z?void 0:z.indicator),null===(d=null==F?void 0:F.styles)||void 0===d?void 0:d.indicator),tc)}),v&&e.createElement("span",{style:{color:t},className:"".concat(Z,"-status-text")},v)))}return M(e.createElement("span",Object.assign({ref:o},D,{className:tl,style:Object.assign(Object.assign({},null===(m=null==F?void 0:F.styles)||void 0===m?void 0:m.root),null==z?void 0:z.root)}),p,e.createElement(i.ZP,{visible:!G,motionName:"".concat(Z,"-zoom"),motionAppear:!1,motionDeadline:1e3},t=>{var o,n;let{className:a}=t,i=P("scroll-number",g),c=tt.current,l=r()(null==I?void 0:I.indicator,null===(o=null==F?void 0:F.classNames)||void 0===o?void 0:o.indicator,{["".concat(Z,"-dot")]:c,["".concat(Z,"-count")]:!c,["".concat(Z,"-count-sm")]:"small"===E,["".concat(Z,"-multiple-words")]:!c&&U&&U.toString().length>1,["".concat(Z,"-status-").concat(f)]:!!f,["".concat(Z,"-color-").concat(h)]:tr}),s=Object.assign(Object.assign(Object.assign({},null==z?void 0:z.indicator),null===(n=null==F?void 0:F.styles)||void 0===n?void 0:n.indicator),to);return h&&!tr&&((s=s||{}).background=h),e.createElement(T,{prefixCls:i,show:!G,motionClassName:a,className:l,count:U,title:tn,style:s,key:"scrollNumber"},ta)}),te))});z.Ribbon=t=>{let{className:o,prefixCls:n,style:a,color:i,children:l,text:u,placement:d="end",rootClassName:m}=t,{getPrefixCls:b,direction:g}=e.useContext(s.E_),p=b("ribbon",n),f="".concat(p,"-wrapper"),[v,h,y]=j(p,f),O=(0,c.o2)(i,!1),w=r()(p,"".concat(p,"-placement-").concat(d),{["".concat(p,"-rtl")]:"rtl"===g,["".concat(p,"-color-").concat(i)]:O},o),E={},C={};return i&&!O&&(E.background=i,C.color=i),v(e.createElement("div",{className:r()(f,m,h,y)},l,e.createElement("div",{className:r()(w,h),style:Object.assign(Object.assign({},E),a)},e.createElement("span",{className:"".concat(p,"-text")},u),e.createElement("div",{className:"".concat(p,"-corner"),style:C}))))};var B=z}}]);