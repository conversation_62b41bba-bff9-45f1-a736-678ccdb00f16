(()=>{var e={};e.id=5137,e.ids=[5137],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},1203:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>i});var o=r(50482),l=r(69108),a=r(62563),s=r.n(a),n=r(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(t,c);let i=["",{children:["warehouse",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,59683)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,76485)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\page.tsx"],u="/warehouse/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new o.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/warehouse/page",pathname:"/warehouse",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},10179:(e,t,r)=>{Promise.resolve().then(r.bind(r,21552))},54649:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var o=r(65651),l=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var s=r(49809);let n=l.forwardRef(function(e,t){return l.createElement(s.Z,(0,o.Z)({},e,{ref:t,icon:a}))})},89645:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var o=r(65651),l=r(3729);let a={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var s=r(49809);let n=l.forwardRef(function(e,t){return l.createElement(s.Z,(0,o.Z)({},e,{ref:t,icon:a}))})},94505:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var o=r(65651),l=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};var s=r(49809);let n=l.forwardRef(function(e,t){return l.createElement(s.Z,(0,o.Z)({},e,{ref:t,icon:a}))})},37372:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var o=r(65651),l=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var s=r(49809);let n=l.forwardRef(function(e,t){return l.createElement(s.Z,(0,o.Z)({},e,{ref:t,icon:a}))})},90377:(e,t,r)=>{"use strict";r.d(t,{Z:()=>O});var o=r(3729),l=r(34132),a=r.n(l),s=r(24773),n=r(22624),c=r(46164),i=r(29545),d=r(30605),u=r(84893),p=r(92959),x=r(55002),h=r(22989),m=r(96373),g=r(13165);let f=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:o,componentCls:l,calc:a}=e,s=a(o).sub(r).equal(),n=a(t).sub(r).equal();return{[l]:Object.assign(Object.assign({},(0,h.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:s,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${l}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${l}-close-icon`]:{marginInlineStart:n,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${l}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${l}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:s}}),[`${l}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},v=e=>{let{lineWidth:t,fontSizeIcon:r,calc:o}=e,l=e.fontSizeSM;return(0,m.IX)(e,{tagFontSize:l,tagLineHeight:(0,p.bf)(o(e.lineHeightSM).mul(l).equal()),tagIconSize:o(r).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new x.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),j=(0,g.I$)("Tag",e=>f(v(e)),y);var b=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,o=Object.getOwnPropertySymbols(e);l<o.length;l++)0>t.indexOf(o[l])&&Object.prototype.propertyIsEnumerable.call(e,o[l])&&(r[o[l]]=e[o[l]]);return r};let k=o.forwardRef((e,t)=>{let{prefixCls:r,style:l,className:s,checked:n,onChange:c,onClick:i}=e,d=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:x}=o.useContext(u.E_),h=p("tag",r),[m,g,f]=j(h),v=a()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:n},null==x?void 0:x.className,s,g,f);return m(o.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},l),null==x?void 0:x.style),className:v,onClick:e=>{null==c||c(!n),null==i||i(e)}})))});var w=r(78701);let C=e=>(0,w.Z)(e,(t,{textColor:r,lightBorderColor:o,lightColor:l,darkColor:a})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:l,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),S=(0,g.bk)(["Tag","preset"],e=>C(v(e)),y),Z=(e,t,r)=>{let o=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},$=(0,g.bk)(["Tag","status"],e=>{let t=v(e);return[Z(t,"success","Success"),Z(t,"processing","Info"),Z(t,"error","Error"),Z(t,"warning","Warning")]},y);var P=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,o=Object.getOwnPropertySymbols(e);l<o.length;l++)0>t.indexOf(o[l])&&Object.prototype.propertyIsEnumerable.call(e,o[l])&&(r[o[l]]=e[o[l]]);return r};let N=o.forwardRef((e,t)=>{let{prefixCls:r,className:l,rootClassName:p,style:x,children:h,icon:m,color:g,onClose:f,bordered:v=!0,visible:y}=e,b=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:k,direction:w,tag:C}=o.useContext(u.E_),[Z,N]=o.useState(!0),O=(0,s.Z)(b,["closeIcon","closable"]);o.useEffect(()=>{void 0!==y&&N(y)},[y]);let z=(0,n.o2)(g),I=(0,n.yT)(g),q=z||I,E=Object.assign(Object.assign({backgroundColor:g&&!q?g:void 0},null==C?void 0:C.style),x),L=k("tag",r),[_,B,M]=j(L),T=a()(L,null==C?void 0:C.className,{[`${L}-${g}`]:q,[`${L}-has-color`]:g&&!q,[`${L}-hidden`]:!Z,[`${L}-rtl`]:"rtl"===w,[`${L}-borderless`]:!v},l,p,B,M),H=e=>{e.stopPropagation(),null==f||f(e),e.defaultPrevented||N(!1)},[,R]=(0,c.Z)((0,c.w)(e),(0,c.w)(C),{closable:!1,closeIconRender:e=>{let t=o.createElement("span",{className:`${L}-close-icon`,onClick:H},e);return(0,i.wm)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),H(t)},className:a()(null==e?void 0:e.className,`${L}-close-icon`)}))}}),A="function"==typeof b.onClick||h&&"a"===h.type,U=m||null,D=U?o.createElement(o.Fragment,null,U,h&&o.createElement("span",null,h)):h,F=o.createElement("span",Object.assign({},O,{ref:t,className:T,style:E}),D,R,z&&o.createElement(S,{key:"preset",prefixCls:L}),I&&o.createElement($,{key:"status",prefixCls:L}));return _(A?o.createElement(d.Z,{component:"Tag"},F):F)});N.CheckableTag=k;let O=N},21552:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>Z});var o=r(95344),l=r(3729),a=r(97854),s=r(7618),n=r(90377),c=r(87049),i=r(10707),d=r(11157),u=r(63724),p=r(27976),x=r(83984),h=r(43896),m=r(53869),g=r(284),f=r(36527),v=r(94505),y=r(54649),j=r(2778),b=r(37372),k=r(70469),w=r(89645),C=r(58535);let{Option:S}=a.default,Z=()=>{let[e,t]=(0,l.useState)(!1),[r,Z]=(0,l.useState)("inventory"),[$]=s.Z.useForm(),P=[{id:"1",productCode:"P001",productName:"高强度钢材",category:"原材料",currentStock:1250,minStock:500,maxStock:2e3,unitPrice:45.5,location:"A区-01",lastUpdated:"2024-01-15"},{id:"2",productCode:"P002",productName:"精密轴承",category:"零部件",currentStock:320,minStock:200,maxStock:800,unitPrice:125,location:"B区-05",lastUpdated:"2024-01-14"},{id:"3",productCode:"P003",productName:"电机组件",category:"成品",currentStock:85,minStock:100,maxStock:300,unitPrice:850,location:"C区-12",lastUpdated:"2024-01-13"}],N=[{id:"1",productCode:"P001",productName:"高强度钢材",type:"in",quantity:500,toLocation:"A区-01",date:"2024-01-15",operator:"张三",reason:"采购入库"},{id:"2",productCode:"P002",productName:"精密轴承",type:"out",quantity:50,fromLocation:"B区-05",date:"2024-01-14",operator:"李四",reason:"生产领料"}],O=(e,t,r)=>e<t?{status:"exception",text:"库存不足",color:"red"}:e>r?{status:"active",text:"库存过多",color:"orange"}:{status:"success",text:"正常",color:"green"};return(0,o.jsxs)("div",{className:"space-y-6",children:[o.jsx("div",{className:"page-header",children:(0,o.jsxs)("div",{className:"flex items-center",children:[o.jsx(j.Z,{className:"text-2xl text-green-600 mr-3"}),(0,o.jsxs)("div",{children:[o.jsx("h1",{className:"page-title",children:"仓库管理"}),o.jsx("p",{className:"page-description",children:"管理库存、库位和出入库操作"})]})]})}),(0,o.jsxs)(u.Z,{gutter:[16,16],children:[o.jsx(p.Z,{xs:24,sm:6,children:o.jsx(x.Z,{children:o.jsx(h.Z,{title:"库存总值",value:2456789,precision:2,prefix:"\xa5",valueStyle:{color:"#1890ff"}})})}),o.jsx(p.Z,{xs:24,sm:6,children:o.jsx(x.Z,{children:o.jsx(h.Z,{title:"库存品种",value:1256,suffix:"种",valueStyle:{color:"#52c41a"}})})}),o.jsx(p.Z,{xs:24,sm:6,children:o.jsx(x.Z,{children:o.jsx(h.Z,{title:"库存不足",value:23,suffix:"种",valueStyle:{color:"#ff4d4f"},prefix:o.jsx(b.Z,{})})})}),o.jsx(p.Z,{xs:24,sm:6,children:o.jsx(x.Z,{children:o.jsx(h.Z,{title:"今日出入库",value:156,suffix:"次",valueStyle:{color:"#722ed1"}})})})]}),o.jsx(x.Z,{children:o.jsx(m.default,{activeKey:r,onChange:Z,items:[{key:"inventory",label:"库存管理",children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[o.jsx(g.default,{placeholder:"搜索产品编码或名称",prefix:o.jsx(k.Z,{}),className:"w-full sm:w-64"}),(0,o.jsxs)(a.default,{placeholder:"产品分类",className:"w-full sm:w-32",children:[o.jsx(S,{value:"",children:"全部"}),o.jsx(S,{value:"原材料",children:"原材料"}),o.jsx(S,{value:"零部件",children:"零部件"}),o.jsx(S,{value:"成品",children:"成品"})]}),(0,o.jsxs)(a.default,{placeholder:"库存状态",className:"w-full sm:w-32",children:[o.jsx(S,{value:"",children:"全部"}),o.jsx(S,{value:"normal",children:"正常"}),o.jsx(S,{value:"low",children:"库存不足"}),o.jsx(S,{value:"high",children:"库存过多"})]})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[o.jsx(d.ZP,{icon:o.jsx(w.Z,{}),children:"导出"}),o.jsx(d.ZP,{type:"primary",icon:o.jsx(C.Z,{}),children:"新增产品"})]})]}),o.jsx(f.Z,{columns:[{title:"产品编码",dataIndex:"productCode",key:"productCode",width:100},{title:"产品名称",dataIndex:"productName",key:"productName",width:150},{title:"分类",dataIndex:"category",key:"category",width:100},{title:"当前库存",dataIndex:"currentStock",key:"currentStock",width:100,render:(e,t)=>{let r=O(t.currentStock,t.minStock,t.maxStock);return(0,o.jsxs)("div",{children:[o.jsx("div",{children:e}),o.jsx(n.Z,{color:r.color,children:r.text})]})}},{title:"库存水位",key:"stockLevel",width:150,render:(e,t)=>{let r=t.currentStock/t.maxStock*100,l=O(t.currentStock,t.minStock,t.maxStock);return o.jsx(c.Z,{percent:r,size:"small",status:l.status,format:()=>`${t.currentStock}/${t.maxStock}`})}},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:100,render:e=>`\xa5${e.toFixed(2)}`},{title:"库位",dataIndex:"location",key:"location",width:100},{title:"操作",key:"action",width:150,render:(e,t)=>(0,o.jsxs)(i.Z,{size:"small",children:[o.jsx(d.ZP,{type:"text",icon:o.jsx(v.Z,{}),size:"small",children:"调拨"}),o.jsx(d.ZP,{type:"text",icon:o.jsx(y.Z,{}),size:"small",children:"编辑"})]})}],dataSource:P,rowKey:"id",pagination:{total:P.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`},scroll:{x:1e3}})]})},{key:"movement",label:"出入库记录",children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[o.jsx(g.default,{placeholder:"搜索产品编码或名称",prefix:o.jsx(k.Z,{}),className:"w-full sm:w-64"}),(0,o.jsxs)(a.default,{placeholder:"变动类型",className:"w-full sm:w-32",children:[o.jsx(S,{value:"",children:"全部"}),o.jsx(S,{value:"in",children:"入库"}),o.jsx(S,{value:"out",children:"出库"}),o.jsx(S,{value:"transfer",children:"调拨"})]})]}),(0,o.jsxs)("div",{className:"flex space-x-2",children:[o.jsx(d.ZP,{icon:o.jsx(w.Z,{}),children:"导出"}),o.jsx(d.ZP,{type:"primary",icon:o.jsx(C.Z,{}),children:"新增记录"})]})]}),o.jsx(f.Z,{columns:[{title:"产品编码",dataIndex:"productCode",key:"productCode",width:100},{title:"产品名称",dataIndex:"productName",key:"productName",width:150},{title:"变动类型",dataIndex:"type",key:"type",width:100,render:e=>{let t={in:{color:"green",text:"入库"},out:{color:"red",text:"出库"},transfer:{color:"blue",text:"调拨"}}[e];return o.jsx(n.Z,{color:t.color,children:t.text})}},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"库位",key:"location",width:120,render:(e,t)=>"in"===t.type?t.toLocation:"out"===t.type?t.fromLocation:`${t.fromLocation} → ${t.toLocation}`},{title:"操作人",dataIndex:"operator",key:"operator",width:100},{title:"日期",dataIndex:"date",key:"date",width:100},{title:"原因",dataIndex:"reason",key:"reason",width:120}],dataSource:N,rowKey:"id",pagination:{total:N.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`},scroll:{x:1e3}})]})}]})})]})}},76485:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var o=r(25036),l=r(38834);function a({children:e}){return o.jsx(l.Z,{children:e})}},59683:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>l,default:()=>s});let o=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\warehouse\page.tsx`),{__esModule:l,$$typeof:a}=o,s=o.default}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,7049,6274,996,6133],()=>r(1203));module.exports=o})();