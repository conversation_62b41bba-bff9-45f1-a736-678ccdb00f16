(()=>{var e={};e.id=5993,e.ids=[5993],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},68279:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(50482),n=r(69108),a=r(62563),i=r.n(a),o=r(68300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["production",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8524)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,18223)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\page.tsx"],u="/production/orders/page",h={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/production/orders/page",pathname:"/production/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},14476:(e,t,r)=>{Promise.resolve().then(r.bind(r,13179))},1426:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(65651),n=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M304 280h56c4.4 0 8-3.6 8-8 0-28.3 5.9-53.2 17.1-73.5 10.6-19.4 26-34.8 45.4-45.4C450.9 142 475.7 136 504 136h16c28.3 0 53.2 5.9 73.5 17.1 19.4 10.6 34.8 26 45.4 45.4C650 218.9 656 243.7 656 272c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-40-8.8-76.7-25.9-108.1a184.31 184.31 0 00-74-74C596.7 72.8 560 64 520 64h-16c-40 0-76.7 8.8-108.1 25.9a184.31 184.31 0 00-74 74C304.8 195.3 296 232 296 272c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M940 512H792V412c76.8 0 139-62.2 139-139 0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8a63 63 0 01-63 63H232a63 63 0 01-63-63c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 76.8 62.2 139 139 139v100H84c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h148v96c0 6.5.2 13 .7 19.3C164.1 728.6 116 796.7 116 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-44.2 23.9-82.9 59.6-103.7a273 273 0 0022.7 49c24.3 41.5 59 76.2 100.5 100.5S460.5 960 512 960s99.8-13.9 141.3-38.2a281.38 281.38 0 00123.2-149.5A120 120 0 01836 876c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8 0-79.3-48.1-147.4-116.7-176.7.4-6.4.7-12.8.7-19.3v-96h148c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM716 680c0 36.8-9.7 72-27.8 102.9-17.7 30.3-43 55.6-73.3 73.3C584 874.3 548.8 884 512 884s-72-9.7-102.9-27.8c-30.3-17.7-55.6-43-73.3-73.3A202.75 202.75 0 01308 680V412h408v268z"}}]},name:"bug",theme:"outlined"};var i=r(49809);let o=n.forwardRef(function(e,t){return n.createElement(i.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},70414:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(65651),n=r(3729);let a={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"};var i=r(49809);let o=n.forwardRef(function(e,t){return n.createElement(i.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},98021:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(65651),n=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};var i=r(49809);let o=n.forwardRef(function(e,t){return n.createElement(i.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},14921:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(65651),n=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"}}]},name:"info-circle",theme:"outlined"};var i=r(49809);let o=n.forwardRef(function(e,t){return n.createElement(i.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},39593:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(65651),n=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm-88-532h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8zm224 0h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8z"}}]},name:"pause-circle",theme:"outlined"};var i=r(49809);let o=n.forwardRef(function(e,t){return n.createElement(i.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},98507:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(65651),n=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"};var i=r(49809);let o=n.forwardRef(function(e,t){return n.createElement(i.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},16043:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(65651),n=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M931.4 498.9L94.9 79.5c-3.4-1.7-7.3-2.1-11-1.2a15.99 15.99 0 00-11.7 19.3l86.2 352.2c1.3 5.3 5.2 9.6 10.4 11.3l147.7 50.7-147.6 50.7c-5.2 1.8-9.1 6-10.3 11.3L72.2 926.5c-.9 3.7-.5 7.6 1.2 10.9 3.9 7.9 13.5 11.1 21.5 7.2l836.5-417c3.1-1.5 5.6-4.1 7.2-7.1 3.9-8 .7-17.6-7.2-21.6zM170.8 826.3l50.3-205.6 295.2-101.3c2.3-.8 4.2-2.6 5-5 1.4-4.2-.8-8.7-5-10.2L221.1 403 171 198.2l628 314.9-628.2 313.2z"}}]},name:"send",theme:"outlined"};var i=r(49809);let o=n.forwardRef(function(e,t){return n.createElement(i.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},96291:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(65651),n=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var i=r(49809);let o=n.forwardRef(function(e,t){return n.createElement(i.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},37372:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(65651),n=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var i=r(49809);let o=n.forwardRef(function(e,t){return n.createElement(i.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},13179:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>rE});var s=r(95344),n=r(3729),a=r.n(n),i=r(32979),o=r(16728),l=r(83984),c=r(63724),d=r(27976),u=r(10707),h=r(90377),m=r(11157),g=r(83512),p=r(43896),x=r(53869),f=r(39470),y=r(96291),j=r(46472),w=r(65651);let v={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"unordered-list",theme:"outlined"};var k=r(49809),S=n.forwardRef(function(e,t){return n.createElement(k.Z,(0,w.Z)({},e,{ref:t,icon:v}))}),Z=r(44670),b=r(2014),C=r(96266),T=r(35329),D=r(55741),M=r(21754),$=r(65566),I=r(51410),E=r(97854),O=r(97557),N=r(16262),_=r(48869),z=r.n(_),W=r(48606),A=r(37637);class R{constructor(){this.abortController=new AbortController,this.listeners=[]}addEventListener(e,t,r,s){let n={...s,signal:this.abortController.signal};e.addEventListener(t,r,n),this.listeners.push({target:e,type:t,listener:r})}removeEventListener(e,t,r){e.removeEventListener(t,r),this.listeners=this.listeners.filter(s=>!(s.target===e&&s.type===t&&s.listener===r))}cleanup(){this.abortController.abort(),this.listeners=[]}getActiveListenersCount(){return this.listeners.length}isCleanedUp(){return this.abortController.signal.aborted}}let P={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"};function B(e){return null==e?"":"string"!=typeof e?String(e):e.replace(/[&<>"'`=\/]/g,e=>P[e]||e)}function L(e){return e?B(e.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"").replace(/javascript:/gi,"").replace(/on\w+\s*=/gi,"").trim()):"未知产品"}function H(e,t=200){return e?B((e.length>t?e.substring(0,t)+"...":e).trim()):""}let V=[{label:"7天",value:7},{label:"14天",value:14},{label:"30天",value:30}],q=e=>{if(!e||"string"!=typeof e)throw Error("Invalid time string: must be a non-empty string");let t=e.split(":");if(2!==t.length)throw Error("Invalid time format: must be HH:MM");let r=parseInt(t[0],10),s=parseInt(t[1],10);if(isNaN(r)||isNaN(s)||r<0||r>23||s<0||s>59)throw Error(`Invalid time values: hour=${r}, minute=${s}`);return 60*r+s},F=e=>`${Math.floor(e/60).toString().padStart(2,"0")}:${(e%60).toString().padStart(2,"0")}`,Q=e=>{let t=new Map;return e.forEach(e=>{t.get(e.time)&&"work"!==e.type||t.set(e.time,e)}),Array.from(t.values()).sort((e,t)=>q(e.time)-q(t.time))},U=(e,t,r)=>{let s=q(e),{startMinutes:n,endMinutes:a}=t,i=s;return s<n&&(i+=1440),20+(i-n)/(a-n)*(r-40)},Y=e=>{let t=q(e.startTime),r=q(e.endTime)-t,s=[];if(r>=180){let n=t+Math.floor(r/2);s.push({time:F(n),type:"work",label:F(n),isMiddle:!0,slotName:e.name})}return s},X=e=>{if(!e)return[];let t=e.workTimeSlots.filter(e=>e.isActive);if(0===t.length)return[];let r=[];return t.forEach(e=>{r.push({time:e.startTime,type:"work",label:e.startTime,isStart:!0,slotName:e.name}),r.push({time:e.endTime,type:"work",label:e.endTime,isEnd:!0,slotName:e.name});let t=Y(e);r.push(...t)}),Q(r)},G=e=>{if(!e)return{startMinutes:480,endMinutes:1020};let t=e.workTimeSlots.filter(e=>e.isActive);if(0===t.length)return{startMinutes:480,endMinutes:1020};let r=1/0,s=-1/0;return t.forEach(e=>{let t=q(e.startTime),n=q(e.endTime);r=Math.min(r,t),s=Math.max(s,n)}),s<r&&(s+=1440),{startMinutes:r,endMinutes:s}},K=({task:e,startDate:t,dayWidth:r,timeRange:n,actualDays:a,onTaskClick:i})=>{let o=new Date(e.plannedStartTime),l=new Date(e.plannedEndTime),c=(e,s=!1)=>{let i=new Date(e);i.setHours(0,0,0,0);let o=new Date(t);o.setHours(0,0,0,0);let l=(i.getTime()-o.getTime())/864e5,c=a-1;if(l<0){if(s){let e=U(F(n.startMinutes),n,r);return 0*r+e}{let e=U(F(n.startMinutes),n,r);return 0*r+e}}if(l>c){if(s){let e=U(F(n.endMinutes),n,r);return c*r+e}{let e=U(F(n.endMinutes),n,r);return c*r+e}}{let t=U(`${e.getHours().toString().padStart(2,"0")}:${e.getMinutes().toString().padStart(2,"0")}`,n,r);return Math.floor(l)*r+t}},d=c(o,!0),u=Math.max(c(l,!1)-d,20),h=e.plannedMoldCount>0?e.completedMoldCount/e.plannedMoldCount*100:0,m=h/100*u,g="completed"===e.status?{completed:"#52c41a",remaining:"#52c41a"}:"in_progress"===e.status?{completed:"#52c41a",remaining:"#f0f0f0"}:{completed:"#1890ff",remaining:"#e6f7ff"};return s.jsx(I.Z,{title:(0,s.jsxs)("div",{children:[s.jsx("div",{children:s.jsx("strong",{children:L(e.productName)})}),(0,s.jsxs)("div",{children:["批次号: ",H(e.batchNumber)]}),(0,s.jsxs)("div",{children:["开始: ",z()(e.plannedStartTime).format("MM-DD HH:mm")]}),(0,s.jsxs)("div",{children:["结束: ",z()(e.plannedEndTime).format("MM-DD HH:mm")]}),(0,s.jsxs)("div",{children:["计划模数: ",e.plannedMoldCount,"模"]}),(0,s.jsxs)("div",{children:["完成模数: ",e.completedMoldCount,"模"]}),(0,s.jsxs)("div",{children:["完成率: ",h.toFixed(1),"%"]}),(0,s.jsxs)("div",{children:["状态: ",W.ux[e.status]?.text||e.status]})]}),children:(0,s.jsxs)("div",{style:{position:"absolute",left:`${d}px`,width:`${u}px`,height:"28px",top:"6px",borderRadius:"4px",cursor:"pointer",border:"1px solid rgba(0,0,0,0.1)",overflow:"hidden",background:g.remaining},onClick:()=>i?.(e),children:[s.jsx("div",{style:{position:"absolute",left:0,top:0,width:`${m}px`,height:"100%",background:g.completed,transition:"width 0.3s ease"}}),(0,s.jsxs)("div",{style:{position:"absolute",left:"8px",top:"50%",transform:"translateY(-50%)",color:m>.5*u?"white":"#333",fontSize:"12px",fontWeight:500,whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:`${u-16}px`,zIndex:10},children:[L(e.productName)," (",h.toFixed(0),"%)"]})]})})},J=({tasks:e,startDate:t,dayWidth:r,timeRange:n,actualDays:a,onTaskClick:i})=>s.jsx("div",{style:{minHeight:"50px",borderBottom:"1px solid #f0f0f0",position:"relative"},children:e.map(e=>s.jsx(K,{task:e,startDate:t,dayWidth:r,timeRange:n,actualDays:a,onTaskClick:i},e.id))}),ee=({startDate:e,days:t,dayWidth:r,workTimeConfig:n})=>{let a=Array.from({length:t},(t,r)=>{let s=new Date(e);return s.setDate(s.getDate()+r),s}),i=X(n||null),o=G(n||null);return s.jsx("div",{style:{height:"100px",display:"flex",borderBottom:"2px solid #e8e8e8",background:"#fafafa"},children:a.map((e,t)=>(0,s.jsxs)("div",{style:{width:`${r}px`,borderRight:t<a.length-1?"1px solid #f0f0f0":"none",display:"flex",flexDirection:"column"},children:[(0,s.jsxs)("div",{style:{height:"40px",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",borderBottom:"1px solid #f0f0f0",padding:"4px"},children:[s.jsx("div",{style:{fontWeight:600,fontSize:"14px"},children:z()(e).format("MM-DD")}),s.jsx("div",{style:{color:"#666",fontSize:"12px"},children:z()(e).format("ddd")})]}),s.jsx("div",{style:{height:"60px",position:"relative",background:"#fafafa",overflow:"visible"},children:i.map((e,t)=>{let n=U(e.time,o,r);return(0,s.jsxs)("div",{style:{position:"absolute",left:`${n}px`,top:"0px",height:"50px",display:"flex",flexDirection:"column",alignItems:"center"},children:[s.jsx("div",{style:{width:"2px",height:"25px",background:"#1890ff",position:"absolute",top:"5px",left:"50%",transform:"translateX(-50%)"}}),s.jsx("div",{style:{fontSize:"12px",color:"#666",fontWeight:500,position:"absolute",top:"35px",left:"50%",transform:"translateX(-50%)",whiteSpace:"nowrap"},children:e.label})]},t)})})]},t))})},et=({tasks:e=[],workstations:t=[],onTaskClick:r,onRefresh:i,loading:c=!1})=>{let[d,g]=(0,n.useState)(7),[p,x]=(0,n.useState)([null,null]),[f,y]=(0,n.useState)(null),j=(0,n.useMemo)(()=>G(f),[f]),[w,v]=(0,n.useState)(!1),k=(0,n.useCallback)(async()=>{try{let e=await A.dataAccessManager.workTime.getDefault();"success"===e.status&&e.data&&y(e.data)}catch(e){console.error("加载工作时间配置失败:",e)}},[]),S=(0,n.useCallback)(async()=>{try{v(!0),A.dataAccessManager.clearDataTypeCache("workstations"),A.dataAccessManager.clearDataTypeCache("productionWorkOrders"),await k(),"function"==typeof i&&await i()}catch(e){console.error("数据刷新失败:",e)}finally{v(!1)}},[k,i]);(0,n.useEffect)(()=>{k()},[k]),function(e,t){a().useEffect(()=>{let r=new R;return r.addEventListener(window,"storage",r=>{r.key===e&&t(r.newValue,r.oldValue)}),()=>{r.cleanup()}},[e,t])}("work-time-configurations",(0,n.useCallback)(()=>{k()},[k]));let{startDate:b,endDate:C}=(0,n.useMemo)(()=>{if(p[0]&&p[1]){let e=new Date(p[0]);e.setHours(0,0,0,0);let t=new Date(p[1]);return t.setHours(23,59,59,999),{startDate:e,endDate:t}}let e=new Date;e.setHours(0,0,0,0);let t=new Date(e);return t.setDate(t.getDate()+d),{startDate:e,endDate:t}},[d,p]),T=(0,n.useMemo)(()=>Math.ceil((C.getTime()-b.getTime())/864e5),[b,C]),D=(0,n.useMemo)(()=>e.filter(e=>e.plannedStartTime&&e.plannedEndTime&&("scheduled"===e.status||"in_progress"===e.status||"completed"===e.status)&&new Date(e.plannedStartTime)<C&&new Date(e.plannedEndTime)>b),[e,b,C]),M=(0,n.useMemo)(()=>{let e=new Map;return D.forEach(t=>{t.workstationCode&&(e.has(t.workstationCode)||e.set(t.workstationCode,[]),e.get(t.workstationCode).push(t))}),e.forEach(e=>{e.sort((e,t)=>new Date(e.plannedStartTime).getTime()-new Date(t.plannedStartTime).getTime())}),e},[D]),I=(0,n.useMemo)(()=>{let e=[],r=[];return t.forEach(t=>{M.has(t.code)?e.push(t):r.push(t.code)}),e},[t,M]);return c?(0,s.jsxs)("div",{style:{padding:"24px",textAlign:"center"},children:[s.jsx(o.Z,{size:"large"}),s.jsx("div",{style:{marginTop:"16px",color:"#666"},children:"正在加载排产看板数据..."})]}):0===D.length?s.jsx("div",{style:{padding:"24px"},children:s.jsx(l.Z,{title:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[s.jsx($.Z,{}),s.jsx("span",{children:"排产看板"})]}),extra:(0,s.jsxs)(u.Z,{children:[s.jsx(E.default,{value:d,onChange:e=>{g(e),x([null,null])},options:V,style:{width:80}}),s.jsx(O.default.RangePicker,{value:p[0]&&p[1]?[z()(p[0]),z()(p[1])]:[null,null],onChange:e=>{e&&e[0]&&e[1]?x([e[0].toDate(),e[1].toDate()]):x([null,null])},placeholder:["开始日期","结束日期"],style:{width:240}}),s.jsx(m.ZP,{icon:s.jsx(Z.Z,{}),onClick:S,loading:w,children:"刷新"})]}),children:s.jsx(N.Z,{description:(0,s.jsxs)("div",{children:[s.jsx("div",{children:"暂无排产数据"}),s.jsx("div",{style:{color:"#999",fontSize:"12px",marginTop:"4px"},children:"当前时间范围内没有已排程的工单"})]}),image:N.Z.PRESENTED_IMAGE_SIMPLE})})}):s.jsx("div",{style:{padding:"24px"},children:(0,s.jsxs)(l.Z,{title:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[s.jsx($.Z,{}),s.jsx("span",{children:"排产看板"}),(0,s.jsxs)(h.Z,{color:"blue",children:[D.length," 个工单"]})]}),extra:(0,s.jsxs)(u.Z,{children:[s.jsx(E.default,{value:d,onChange:e=>{g(e),x([null,null])},options:V,style:{width:80}}),s.jsx(O.default.RangePicker,{value:p[0]&&p[1]?[z()(p[0]),z()(p[1])]:[null,null],onChange:e=>{e&&e[0]&&e[1]?x([e[0].toDate(),e[1].toDate()]):x([null,null])},placeholder:["开始日期","结束日期"],style:{width:240}}),s.jsx(m.ZP,{icon:s.jsx(Z.Z,{}),onClick:S,loading:w,children:"刷新"})]}),children:[s.jsx("div",{style:{border:"1px solid #f0f0f0",borderRadius:"6px",overflow:"hidden",display:"flex",flexDirection:"column"},children:(0,s.jsxs)("div",{style:{display:"flex",height:"600px"},children:[(0,s.jsxs)("div",{style:{width:"50px",flexShrink:0,display:"flex",flexDirection:"column",background:"#fafafa",borderRight:"2px solid #e8e8e8",zIndex:10},children:[s.jsx("div",{style:{height:"100px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:600,fontSize:"10px",color:"#666",borderBottom:"2px solid #e8e8e8",padding:"4px 2px",boxSizing:"border-box",background:"#fafafa",writingMode:"vertical-rl",textOrientation:"mixed"},children:"工位"}),s.jsx("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:I.map(e=>s.jsx("div",{title:`${e.name} (${e.code})`,style:{minHeight:"50px",padding:"2px",borderBottom:"1px solid #f0f0f0",display:"flex",alignItems:"center",justifyContent:"center",background:"#fafafa",cursor:"help"},children:s.jsx("div",{style:{fontWeight:600,fontSize:"9px",color:"#333",textAlign:"center",lineHeight:"10px",display:"flex",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.name})},e.id))})]}),s.jsx("div",{style:{flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},children:s.jsx("div",{style:{width:"100%",height:"100%",overflowX:"auto",overflowY:"hidden"},children:(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",minWidth:`${320*T}px`,height:"100%"},children:[s.jsx("div",{style:{position:"sticky",top:0,zIndex:5,flexShrink:0},children:s.jsx(ee,{startDate:b,days:T,dayWidth:320,workTimeConfig:f})}),s.jsx("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:I.map(e=>s.jsx(J,{tasks:M.get(e.code)||[],startDate:b,dayWidth:320,timeRange:j,actualDays:T,onTaskClick:r},e.id))})]})})})]})}),(0,s.jsxs)("div",{style:{marginTop:"16px",display:"flex",gap:"24px",flexWrap:"wrap",alignItems:"center"},children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[s.jsx("span",{style:{fontSize:"12px",color:"#666"},children:"状态说明："}),s.jsx(h.Z,{color:"blue",children:"已排程"}),s.jsx(h.Z,{color:"green",children:"进行中"}),s.jsx(h.Z,{color:"cyan",children:"已完成"})]}),(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[s.jsx("span",{style:{fontSize:"12px",color:"#666"},children:"进度说明："}),(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[s.jsx("div",{style:{width:"20px",height:"8px",background:"#52c41a",borderRadius:"2px"}}),s.jsx("span",{style:{fontSize:"12px"},children:"已完成"})]}),(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[s.jsx("div",{style:{width:"20px",height:"8px",background:"#f0f0f0",borderRadius:"2px"}}),s.jsx("span",{style:{fontSize:"12px"},children:"未完成"})]})]})]})]})})};var er=r(90152),es=r(7618),en=r(16408),ea=r(6025),ei=r(36527),eo=r(46116);class el{static canEditOrder(e){return!!e&&"in_plan"===e.status}static canDeleteOrder(e){return!!e&&["in_plan","cancelled"].includes(e.status)}static canCreateWorkOrder(e){return!!e&&"in_plan"===e.status&&(e.quantity||0)>0}static canCancelOrder(e){return!!e&&["in_plan","in_progress"].includes(e.status)}static canCompleteOrder(e){return!!e&&"in_progress"===e.status}static validateOrderStatus(e){if(!e)return{isValid:!1,canEdit:!1,canDelete:!1,canCreateWorkOrder:!1,canCancel:!1,canComplete:!1,message:"订单数据无效"};let t=this.canEditOrder(e),r=this.canDeleteOrder(e);return{isValid:!0,canEdit:t,canDelete:r,canCreateWorkOrder:this.canCreateWorkOrder(e),canCancel:this.canCancelOrder(e),canComplete:this.canCompleteOrder(e),message:this.getStatusMessage(e.status)}}static getStatusMessage(e){return({in_plan:"订单在计划中，可以编辑、删除或创建工单",planned:"订单已规划，准备进入生产",in_progress:"订单正在生产中，可以取消或完成",completed:"订单已完成，无法修改",cancelled:"订单已取消，只能删除"})[e]||"未知状态"}static batchValidateOrderStatus(e){let t=new Map;return e.forEach(e=>{e.id&&t.set(e.id,this.validateOrderStatus(e))}),t}static getOperableOrders(e,t){return e.filter(e=>{switch(t){case"edit":return this.canEditOrder(e);case"delete":return this.canDeleteOrder(e);case"createWorkOrder":return this.canCreateWorkOrder(e);case"cancel":return this.canCancelOrder(e);case"complete":return this.canCompleteOrder(e);default:return!1}})}}var ec=r(3745),ed=r(98507),eu=r(70414),eh=r(39593);let em={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372 0-89 31.3-170.8 83.5-234.8l523.3 523.3C682.8 852.7 601 884 512 884zm288.5-137.2L277.2 223.5C341.2 171.3 423 140 512 140c205.4 0 372 166.6 372 372 0 89-31.3 170.8-83.5 234.8z"}}]},name:"stop",theme:"outlined"};var eg=n.forwardRef(function(e,t){return n.createElement(k.Z,(0,w.Z)({},e,{ref:t,icon:em}))}),ep=r(15595),ex=r(56074);let ef={production_order:{in_plan:{color:"blue",icon:s.jsx(T.Z,{}),text:"计划中",antdColor:"blue"},planned:{color:"cyan",icon:s.jsx(ec.Z,{}),text:"已计划",antdColor:"cyan"},in_production:{color:"orange",icon:s.jsx(ed.Z,{}),text:"生产中",antdColor:"orange"},completed:{color:"green",icon:s.jsx(ec.Z,{}),text:"已完成",antdColor:"green"},cancelled:{color:"red",icon:s.jsx(eu.Z,{}),text:"已取消",antdColor:"red"}},work_order:{pending:{color:"default",icon:s.jsx(T.Z,{}),text:"待开始",antdColor:"default"},in_progress:{color:"processing",icon:s.jsx(ed.Z,{}),text:"进行中",antdColor:"processing"},paused:{color:"warning",icon:s.jsx(eh.Z,{}),text:"已暂停",antdColor:"warning"},completed:{color:"success",icon:s.jsx(ec.Z,{}),text:"已完成",antdColor:"success"},cancelled:{color:"error",icon:s.jsx(eu.Z,{}),text:"已取消",antdColor:"error"}},workstation:{active:{color:"success",icon:s.jsx(ec.Z,{}),text:"启用",antdColor:"success"},inactive:{color:"default",icon:s.jsx(eg,{}),text:"停用",antdColor:"default"},maintenance:{color:"warning",icon:s.jsx(M.Z,{}),text:"维护中",antdColor:"warning"},error:{color:"error",icon:s.jsx(ep.Z,{}),text:"故障",antdColor:"error"}},credit_level:{A:{color:"green",text:"A级",antdColor:"green"},B:{color:"blue",text:"B级",antdColor:"blue"},C:{color:"orange",text:"C级",antdColor:"orange"},D:{color:"red",text:"D级",antdColor:"red"},E:{color:"volcano",text:"E级",antdColor:"volcano"}},priority:{low:{color:"default",text:"低",antdColor:"default"},medium:{color:"blue",text:"中",antdColor:"blue"},high:{color:"orange",text:"高",antdColor:"orange"},urgent:{color:"red",text:"紧急",antdColor:"red"}},work_time:{active:{color:"success",icon:s.jsx(ec.Z,{}),text:"启用",antdColor:"success"},inactive:{color:"default",icon:s.jsx(eg,{}),text:"停用",antdColor:"default"},configured:{color:"processing",icon:s.jsx(T.Z,{}),text:"已配置",antdColor:"processing"},pending:{color:"warning",icon:s.jsx(ep.Z,{}),text:"待配置",antdColor:"warning"},updating:{color:"blue",icon:s.jsx(T.Z,{}),text:"更新中",antdColor:"blue"}}},ey=a().memo(({type:e,status:t,showIcon:r=!0,style:n,className:a,onClick:i})=>{let o=((e,t)=>{let r=ef[e];return r&&r[t]||{color:"default",icon:s.jsx(ex.Z,{}),text:t,antdColor:"default"}})(e,t);return s.jsx(h.Z,{color:o.antdColor||o.color,icon:r?o.icon:void 0,style:n,className:a,onClick:i,children:o.text})});ey.displayName="UnifiedTagRenderer";let ej=a().memo(({status:e,showIcon:t=!0,onClick:r})=>s.jsx(ey,{type:"production_order",status:e,showIcon:t,onClick:r}));ej.displayName="ProductionOrderStatusTag";let ew=a().memo(({status:e,showIcon:t=!0,onClick:r})=>s.jsx(ey,{type:"work_order",status:e,showIcon:t,onClick:r}));ew.displayName="WorkOrderStatusTag",a().memo(({status:e,showIcon:t=!0,onClick:r})=>s.jsx(ey,{type:"workstation",status:e,showIcon:t,onClick:r})).displayName="WorkstationStatusTag";let ev=a().memo(({level:e,onClick:t})=>s.jsx(ey,{type:"credit_level",status:e,showIcon:!1,onClick:t}));ev.displayName="CreditLevelTag",a().memo(({priority:e,onClick:t})=>s.jsx(ey,{type:"priority",status:e,showIcon:!1,onClick:t})).displayName="PriorityTag";let ek=a().memo(({status:e,showIcon:t=!0,onClick:r,style:n,className:a})=>s.jsx(ey,{type:"work_time",status:e,showIcon:t,onClick:r,style:n,className:a}));ek.displayName="WorkTimeStatusTag",a().memo(({tags:e,direction:t="horizontal",size:r="small"})=>s.jsx(u.Z,{direction:t,size:r,children:e.map((e,t)=>s.jsx(ey,{type:e.type,status:e.status,showIcon:e.showIcon},`${e.type}-${e.status}-${t}`))})).displayName="CombinedTags";let eS=e=>{let t=ef[e];return t?Object.entries(t).map(([e,t])=>({text:t.text,value:e})):[]},{Text:eZ}=er.default,eb=a().memo(({open:e,loading:t,selectedOrders:r,onConfirm:a,onCancel:i})=>{let[o]=es.Z.useForm();(0,n.useEffect)(()=>{e&&o.setFieldsValue({hourlyCapacity:130})},[e,o]);let l=async()=>{try{let e=await o.validateFields();await a(e),o.resetFields()}catch(e){}};return(0,s.jsxs)(en.Z,{title:"创建生产工单",open:e,onOk:l,onCancel:()=>{o.resetFields(),i()},confirmLoading:t,width:600,destroyOnHidden:!0,children:[(0,s.jsxs)("div",{style:{marginBottom:16},children:[s.jsx(eZ,{strong:!0,children:"已选择的生产订单："}),s.jsx("div",{style:{marginTop:8,maxHeight:200,overflowY:"auto"},children:r.map(e=>s.jsx("div",{style:{padding:"8px 12px",border:"1px solid #d9d9d9",borderRadius:6,marginBottom:8,backgroundColor:"#fafafa"},children:(0,s.jsxs)(u.Z,{direction:"vertical",size:4,children:[(0,s.jsxs)("div",{children:[s.jsx(eZ,{strong:!0,children:"订单号："}),s.jsx(eZ,{children:e.orderNumber})]}),(0,s.jsxs)("div",{children:[s.jsx(eZ,{strong:!0,children:"产品名称："}),s.jsx(eZ,{children:e.productName})]}),(0,s.jsxs)("div",{children:[s.jsx(eZ,{strong:!0,children:"计划数量："}),s.jsx(eZ,{children:e.plannedQuantity})]}),(0,s.jsxs)("div",{children:[s.jsx(eZ,{strong:!0,children:"客户："}),s.jsx(eZ,{children:e.customerName}),e.customerCreditLevel&&s.jsx("div",{style:{marginLeft:8},children:s.jsx(ev,{level:e.customerCreditLevel})})]}),(0,s.jsxs)("div",{children:[s.jsx(eZ,{strong:!0,children:"信用等级："}),e.customerCreditLevel?s.jsx(ev,{level:e.customerCreditLevel}):s.jsx(h.Z,{color:"default",children:"未设置"})]})]})},e.id))})]}),s.jsx(es.Z,{form:o,layout:"vertical",children:s.jsx(es.Z.Item,{name:"hourlyCapacity",label:"小时产能",rules:[{required:!0,message:"请输入小时产能"},{type:"number",min:1,message:"产能必须大于0"}],children:s.jsx(ea.Z,{style:{width:"100%"},placeholder:"请输入小时产能",min:1,max:1e3,precision:0,addonAfter:"模/小时"})})})]})}),eC=a().memo(({orders:e,loading:t=!1,onRefresh:r,onOrderDetail:a,onStatusChange:o,onOrderSelectionChange:c,onCreateWorkOrders:d,onTabChange:g,workstations:p=[],currentSplitConfig:x,selectedOrdersCount:f=0})=>{let{message:y}=i.Z.useApp(),[j,w]=(0,n.useState)([]),[v,k]=(0,n.useState)([]),[S,b]=(0,n.useState)(!1),[C,T]=(0,n.useState)(!1),D=(0,n.useCallback)(()=>{r?.()},[r]),$=(0,n.useCallback)(e=>{c?.(e)},[c]);(0,n.useCallback)(()=>{b(!0)},[]);let I=(0,n.useCallback)(()=>{b(!1),T(!1)},[]),E=(0,n.useCallback)(async e=>{try{T(!0);let t=v.map(e=>e.id).filter(Boolean);await d?.(t,e.hourlyCapacity),b(!1),w([]),k([]),D()}catch(e){console.error("创建工单失败:",e)}finally{T(!1)}},[v,d,D]),O=(0,n.useMemo)(()=>[{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>s.jsx(ej,{status:e})},{title:"生产订单号",dataIndex:"orderNumber",key:"orderNumber",width:150},{title:"产品名称",dataIndex:"productName",key:"productName",width:200,render:(e,t)=>t.isSharedMold&&t.productItems&&t.productItems.length>0?s.jsx("div",{style:{lineHeight:"1.4"},children:t.productItems.map((e,r)=>s.jsx("div",{style:{marginBottom:r<t.productItems.length-1?"2px":"0"},children:s.jsx(eZ,{style:{fontSize:"13px",display:"block",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"180px"},title:L(e.productName),children:L(e.productName)})},r))}):s.jsx(eZ,{children:L(e)})},{title:"产品编码",dataIndex:"productCode",key:"productCode",width:120,render:(e,t)=>t.isSharedMold&&t.productItems&&t.productItems.length>0?s.jsx("div",{style:{lineHeight:"1.4"},children:t.productItems.map((e,r)=>s.jsx("div",{style:{marginBottom:r<t.productItems.length-1?"2px":"0"},children:s.jsx(eZ,{style:{fontSize:"13px"},children:e.productCode})},r))}):s.jsx(eZ,{children:e})},{title:"计划数量",dataIndex:"plannedQuantity",key:"plannedQuantity",width:100,render:e=>s.jsx(eZ,{strong:!0,children:e})},{title:"客户信息",key:"customerInfo",width:180,render:(e,t)=>(0,s.jsxs)(u.Z,{direction:"vertical",size:2,children:[s.jsx(eZ,{strong:!0,children:t.customerName}),t.customerCreditLevel&&s.jsx(ev,{level:t.customerCreditLevel})]})},{title:"信用等级",dataIndex:"customerCreditLevel",key:"customerCreditLevel",width:120,render:(e,t)=>e?(0,s.jsxs)(u.Z,{direction:"vertical",size:2,children:[s.jsx(ev,{level:e}),"manual"===t.prioritySource&&s.jsx(h.Z,{color:"blue",style:{fontSize:"12px"},children:"手动"})]}):s.jsx(h.Z,{color:"default",children:"未设置"})},{title:"交付日期",dataIndex:"deliveryDate",key:"deliveryDate",width:120,render:e=>z()(e).format("YYYY-MM-DD"),sorter:(e,t)=>z()(e.deliveryDate).unix()-z()(t.deliveryDate).unix()},{title:"成型模具编号",dataIndex:"formingMoldNumber",key:"formingMoldNumber",width:140,render:e=>e||"-"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:140,render:e=>z()(e).format("YYYY-MM-DD HH:mm"),sorter:(e,t)=>z()(e.createdAt).unix()-z()(t.createdAt).unix(),defaultSortOrder:"descend"},{title:"操作",key:"action",width:150,render:(e,t)=>s.jsx(u.Z,{size:"small",children:s.jsx(m.ZP,{type:"link",size:"small",icon:s.jsx(eo.Z,{}),onClick:()=>a?.(t),children:"详情"})})}],[a,o]),_=(0,n.useMemo)(()=>({selectedRowKeys:j,onChange:(e,t)=>{w(e),k(t),$(t)},getCheckboxProps:e=>({disabled:!el.canCreateWorkOrder(e)})}),[j,$]),W=(0,n.useCallback)(()=>0===v.length?(y.warning("请选择要创建工单的生产订单"),!1):!(v.filter(e=>!el.canCreateWorkOrder(e)).length>0)||(y.error('只能为"计划中"状态的订单创建工单'),!1),[v,y]),A=(0,n.useCallback)(()=>{W()&&b(!0)},[W]);return(0,s.jsxs)(l.Z,{title:(0,s.jsxs)(u.Z,{children:[s.jsx("span",{children:"生产订单列表"}),(0,s.jsxs)(eZ,{type:"secondary",children:["(",e.length," 个订单)"]})]}),extra:(0,s.jsxs)(u.Z,{children:[s.jsx(m.ZP,{type:"primary",icon:s.jsx(M.Z,{}),onClick:A,disabled:0===v.length,children:"创建工单"}),s.jsx(m.ZP,{icon:s.jsx(Z.Z,{}),onClick:D,loading:t,children:"刷新"})]}),children:[0===e.length?s.jsx(N.Z,{description:(0,s.jsxs)("div",{children:[s.jsx("div",{children:"暂无生产订单"}),s.jsx("div",{style:{fontSize:"12px",color:"#666",marginTop:"8px"},children:"生产订单只能通过MRP流程创建，请前往销售订单管理页面启动MRP"})]}),image:N.Z.PRESENTED_IMAGE_SIMPLE}):s.jsx(ei.Z,{rowSelection:_,columns:O,dataSource:e,rowKey:"id",loading:t,pagination:{total:e.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`},scroll:{x:1140}}),s.jsx(eb,{open:S,loading:C,selectedOrders:v,onConfirm:E,onCancel:I})]})});var eT=r(51360),eD=r(67383),eM=r(39426);let{Text:e$,Title:eI}=er.default,eE={title:"生产订单详情",statusConfig:W.Fi,getStatus:e=>e.status,width:800,sections:[{title:"基本信息",columns:2,bordered:!0,size:"small",fields:[{label:"生产订单号",key:"orderNumber"},{label:"状态",key:"status",render:e=>eM.Xe.status(e,W.Fi)},{label:"订单类型",key:"isSharedMold",render:(e,t)=>e&&t.productItems&&t.productItems.length>1?s.jsx(h.Z,{color:"blue",children:"\uD83D\uDD27 共享模具生产"}):s.jsx(h.Z,{color:"default",children:"单产品生产"})},{label:"成型模具",key:"formingMoldNumber",render:e=>e||eM.Xe.empty("未指定")},{label:"产品名称",key:"productName",hidden:e=>e.isSharedMold&&e.productItems&&e.productItems.length>1},{label:"产品编码",key:"productCode"},{label:"客户信用等级",key:"customerCreditLevel",render:e=>e?s.jsx(h.Z,{color:{A:"red",B:"orange",C:"green"}[e]||"default",children:e}):eM.Xe.empty("未设置")}]},{title:"产品明细",columns:1,bordered:!1,hidden:e=>!(e.isSharedMold&&e.productItems&&e.productItems.length>1),fields:[],customContent:e=>e.isSharedMold&&e.productItems&&!(e.productItems.length<=1)?(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{style:{marginBottom:16},children:(0,s.jsxs)(e$,{type:"secondary",children:["此生产订单包含 ",e.productItems.length," 个产品，使用共享模具 ",e.formingMoldNumber," 同时生产"]})}),e.productItems.map((e,t)=>s.jsx("div",{style:{marginBottom:12,padding:12,border:"1px solid #f0f0f0",borderRadius:4},children:(0,s.jsxs)(eD.Z,{column:3,size:"small",children:[s.jsx(eD.Z.Item,{label:"产品编码",children:e.productCode}),s.jsx(eD.Z.Item,{label:"产品名称",children:e.productName}),s.jsx(eD.Z.Item,{label:"需求数量",children:s.jsx(e$,{type:"secondary",children:e.requiredQuantity})}),s.jsx(eD.Z.Item,{label:"生产数量",children:s.jsx(e$,{strong:!0,children:e.plannedQuantity})}),s.jsx(eD.Z.Item,{label:"超产数量",children:s.jsx(e$,{type:e.plannedQuantity>e.requiredQuantity?"warning":"secondary",children:e.plannedQuantity-e.requiredQuantity})})]})},e.id))]}):null},{title:"生产信息",columns:2,bordered:!0,size:"small",fields:[{label:"计划数量",key:"plannedQuantity",render:e=>s.jsx(e$,{strong:!0,children:e})},{label:"分配工位",key:"workstation",render:e=>e||eM.Xe.empty("未分配")},{label:"交付日期",key:"deliveryDate",render:e=>eM.Xe.date(e)}]},{title:"关联信息",columns:2,bordered:!0,size:"small",fields:[{label:"销售订单号",key:"salesOrderNumber",render:(e,t)=>e||(t.sourceOrderNumbers&&t.sourceOrderNumbers.length>0?t.sourceOrderNumbers[0]:eM.Xe.empty("无"))},{label:"客户名称",key:"customerName",render:e=>e||eM.Xe.empty("无")},{label:"创建时间",key:"createdAt",render:e=>eM.Xe.datetime(e)},{label:"更新时间",key:"updatedAt",render:e=>eM.Xe.datetime(e)}]}]},eO=({open:e,order:t,onClose:r,onStatusChange:n})=>s.jsx(eT.ZP,{open:e,order:t,onClose:r,config:eE});var eN=r(284),e_=r(31529),ez=r(34132),eW=r.n(ez),eA=r(22363),eR=r(93727),eP=r(12403),eB=r(80595),eL=r(21029),eH=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],eV=n.forwardRef(function(e,t){var r,s=e.prefixCls,a=void 0===s?"rc-switch":s,i=e.className,o=e.checked,l=e.defaultChecked,c=e.disabled,d=e.loadingIcon,u=e.checkedChildren,h=e.unCheckedChildren,m=e.onClick,g=e.onChange,p=e.onKeyDown,x=(0,eP.Z)(e,eH),f=(0,eB.Z)(!1,{value:o,defaultValue:l}),y=(0,eR.Z)(f,2),j=y[0],v=y[1];function k(e,t){var r=j;return c||(v(r=e),null==g||g(r,t)),r}var S=eW()(a,i,(r={},(0,eA.Z)(r,"".concat(a,"-checked"),j),(0,eA.Z)(r,"".concat(a,"-disabled"),c),r));return n.createElement("button",(0,w.Z)({},x,{type:"button",role:"switch","aria-checked":j,disabled:c,className:S,ref:t,onKeyDown:function(e){e.which===eL.Z.LEFT?k(!1,e):e.which===eL.Z.RIGHT&&k(!0,e),null==p||p(e)},onClick:function(e){var t=k(!j,e);null==m||m(t,e)}}),d,n.createElement("span",{className:"".concat(a,"-inner")},n.createElement("span",{className:"".concat(a,"-inner-checked")},u),n.createElement("span",{className:"".concat(a,"-inner-unchecked")},h)))});eV.displayName="Switch";var eq=r(30605),eF=r(84893),eQ=r(30681),eU=r(54527),eY=r(92959),eX=r(55002),eG=r(22989),eK=r(13165),eJ=r(96373);let e0=e=>{let{componentCls:t,trackHeightSM:r,trackPadding:s,trackMinWidthSM:n,innerMinMarginSM:a,innerMaxMarginSM:i,handleSizeSM:o,calc:l}=e,c=`${t}-inner`,d=(0,eY.bf)(l(o).add(l(s).mul(2)).equal()),u=(0,eY.bf)(l(i).mul(2).equal());return{[t]:{[`&${t}-small`]:{minWidth:n,height:r,lineHeight:(0,eY.bf)(r),[`${t}-inner`]:{paddingInlineStart:i,paddingInlineEnd:a,[`${c}-checked, ${c}-unchecked`]:{minHeight:r},[`${c}-checked`]:{marginInlineStart:`calc(-100% + ${d} - ${u})`,marginInlineEnd:`calc(100% - ${d} + ${u})`},[`${c}-unchecked`]:{marginTop:l(r).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${t}-handle`]:{width:o,height:o},[`${t}-loading-icon`]:{top:l(l(o).sub(e.switchLoadingIconSize)).div(2).equal(),fontSize:e.switchLoadingIconSize},[`&${t}-checked`]:{[`${t}-inner`]:{paddingInlineStart:a,paddingInlineEnd:i,[`${c}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${c}-unchecked`]:{marginInlineStart:`calc(100% - ${d} + ${u})`,marginInlineEnd:`calc(-100% + ${d} - ${u})`}},[`${t}-handle`]:{insetInlineStart:`calc(100% - ${(0,eY.bf)(l(o).add(s).equal())})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${c}`]:{[`${c}-unchecked`]:{marginInlineStart:l(e.marginXXS).div(2).equal(),marginInlineEnd:l(e.marginXXS).mul(-1).div(2).equal()}},[`&${t}-checked ${c}`]:{[`${c}-checked`]:{marginInlineStart:l(e.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:l(e.marginXXS).div(2).equal()}}}}}}},e1=e=>{let{componentCls:t,handleSize:r,calc:s}=e;return{[t]:{[`${t}-loading-icon${e.iconCls}`]:{position:"relative",top:s(s(r).sub(e.fontSize)).div(2).equal(),color:e.switchLoadingIconColor,verticalAlign:"top"},[`&${t}-checked ${t}-loading-icon`]:{color:e.switchColor}}}},e2=e=>{let{componentCls:t,trackPadding:r,handleBg:s,handleShadow:n,handleSize:a,calc:i}=e,o=`${t}-handle`;return{[t]:{[o]:{position:"absolute",top:r,insetInlineStart:r,width:a,height:a,transition:`all ${e.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:s,borderRadius:i(a).div(2).equal(),boxShadow:n,transition:`all ${e.switchDuration} ease-in-out`,content:'""'}},[`&${t}-checked ${o}`]:{insetInlineStart:`calc(100% - ${(0,eY.bf)(i(a).add(r).equal())})`},[`&:not(${t}-disabled):active`]:{[`${o}::before`]:{insetInlineEnd:e.switchHandleActiveInset,insetInlineStart:0},[`&${t}-checked ${o}::before`]:{insetInlineEnd:0,insetInlineStart:e.switchHandleActiveInset}}}}},e4=e=>{let{componentCls:t,trackHeight:r,trackPadding:s,innerMinMargin:n,innerMaxMargin:a,handleSize:i,calc:o}=e,l=`${t}-inner`,c=(0,eY.bf)(o(i).add(o(s).mul(2)).equal()),d=(0,eY.bf)(o(a).mul(2).equal());return{[t]:{[l]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:a,paddingInlineEnd:n,transition:`padding-inline-start ${e.switchDuration} ease-in-out, padding-inline-end ${e.switchDuration} ease-in-out`,[`${l}-checked, ${l}-unchecked`]:{display:"block",color:e.colorTextLightSolid,fontSize:e.fontSizeSM,transition:`margin-inline-start ${e.switchDuration} ease-in-out, margin-inline-end ${e.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:r},[`${l}-checked`]:{marginInlineStart:`calc(-100% + ${c} - ${d})`,marginInlineEnd:`calc(100% - ${c} + ${d})`},[`${l}-unchecked`]:{marginTop:o(r).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${t}-checked ${l}`]:{paddingInlineStart:n,paddingInlineEnd:a,[`${l}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${l}-unchecked`]:{marginInlineStart:`calc(100% - ${c} + ${d})`,marginInlineEnd:`calc(-100% + ${c} - ${d})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${l}`]:{[`${l}-unchecked`]:{marginInlineStart:o(s).mul(2).equal(),marginInlineEnd:o(s).mul(-1).mul(2).equal()}},[`&${t}-checked ${l}`]:{[`${l}-checked`]:{marginInlineStart:o(s).mul(-1).mul(2).equal(),marginInlineEnd:o(s).mul(2).equal()}}}}}},e6=e=>{let{componentCls:t,trackHeight:r,trackMinWidth:s}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,eG.Wf)(e)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:s,height:r,lineHeight:(0,eY.bf)(r),verticalAlign:"middle",background:e.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${e.motionDurationMid}`,userSelect:"none",[`&:hover:not(${t}-disabled)`]:{background:e.colorTextTertiary}}),(0,eG.Qy)(e)),{[`&${t}-checked`]:{background:e.switchColor,[`&:hover:not(${t}-disabled)`]:{background:e.colorPrimaryHover}},[`&${t}-loading, &${t}-disabled`]:{cursor:"not-allowed",opacity:e.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${t}-rtl`]:{direction:"rtl"}})}},e8=(0,eK.I$)("Switch",e=>{let t=(0,eJ.IX)(e,{switchDuration:e.motionDurationMid,switchColor:e.colorPrimary,switchDisabledOpacity:e.opacityLoading,switchLoadingIconSize:e.calc(e.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${e.opacityLoading})`,switchHandleActiveInset:"-30%"});return[e6(t),e4(t),e2(t),e1(t),e0(t)]},e=>{let{fontSize:t,lineHeight:r,controlHeight:s,colorWhite:n}=e,a=t*r,i=s/2,o=a-4,l=i-4;return{trackHeight:a,trackHeightSM:i,trackMinWidth:2*o+8,trackMinWidthSM:2*l+4,trackPadding:2,handleBg:n,handleSize:o,handleSizeSM:l,handleShadow:`0 2px 4px 0 ${new eX.t("#00230b").setA(.2).toRgbString()}`,innerMinMargin:o/2,innerMaxMargin:o+2+4,innerMinMarginSM:l/2,innerMaxMarginSM:l+2+4}});var e3=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,s=Object.getOwnPropertySymbols(e);n<s.length;n++)0>t.indexOf(s[n])&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]]);return r};let e5=n.forwardRef((e,t)=>{let{prefixCls:r,size:s,disabled:a,loading:i,className:o,rootClassName:l,style:c,checked:d,value:u,defaultChecked:h,defaultValue:m,onChange:g}=e,p=e3(e,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[x,f]=(0,eB.Z)(!1,{value:null!=d?d:u,defaultValue:null!=h?h:m}),{getPrefixCls:y,direction:j,switch:w}=n.useContext(eF.E_),v=n.useContext(eQ.Z),k=(null!=a?a:v)||i,S=y("switch",r),Z=n.createElement("div",{className:`${S}-handle`},i&&n.createElement(e_.Z,{className:`${S}-loading-icon`})),[b,C,T]=e8(S),D=(0,eU.Z)(s),M=eW()(null==w?void 0:w.className,{[`${S}-small`]:"small"===D,[`${S}-loading`]:i,[`${S}-rtl`]:"rtl"===j},o,l,C,T),$=Object.assign(Object.assign({},null==w?void 0:w.style),c);return b(n.createElement(eq.Z,{component:"Switch"},n.createElement(eV,Object.assign({},p,{checked:x,onChange:(...e)=>{f(e[0]),null==g||g.apply(void 0,e)},prefixCls:S,className:M,style:$,disabled:k,ref:t,loadingIcon:Z}))))});e5.__ANT_SWITCH=!0;var e7=r(52788),e9=r(14223),te=r(54649),tt=r(33537),tr=r(98021),ts=r(58535);let tn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 208H676V232h212v136zm0 224H676V432h212v160zM412 432h200v160H412V432zm200-64H412V232h200v136zm-476 64h212v160H136V432zm0-200h212v136H136V232zm0 424h212v136H136V656zm276 0h200v136H412V656zm476 136H676V656h212v136z"}}]},name:"table",theme:"outlined"};var ta=n.forwardRef(function(e,t){return n.createElement(k.Z,(0,w.Z)({},e,{ref:t,icon:tn}))}),ti=r(77746),to=r(19101),tl=r(97147);class tc{static{this.ORDER_STATUS_MAP={pending:{color:"orange",icon:s.jsx(T.Z,{}),text:"未审核"},confirmed:{color:"green",icon:s.jsx(ec.Z,{}),text:"已审核"},completed:{color:"default",icon:s.jsx(tl.Z,{}),text:"完成"},cancelled:{color:"red",icon:s.jsx(eu.Z,{}),text:"已取消"}}}static{this.WORKSTATION_STATUS_MAP={active:{color:"success",icon:s.jsx(ec.Z,{}),text:"启用"},inactive:{color:"default",icon:s.jsx(eg,{}),text:"停用"}}}static{this.PRODUCTION_ORDER_STATUS_MAP={planned:{color:"blue",icon:s.jsx(T.Z,{}),text:"计划中"},in_progress:{color:"processing",icon:s.jsx(y.Z,{spin:!0}),text:"生产中"},completed:{color:"success",icon:s.jsx(ec.Z,{}),text:"已完成"},cancelled:{color:"error",icon:s.jsx(eu.Z,{}),text:"已取消"}}}static{this.WORK_ORDER_STATUS_MAP={pending:{color:"default",icon:s.jsx(T.Z,{}),text:"待开始"},in_progress:{color:"processing",icon:s.jsx(y.Z,{spin:!0}),text:"进行中"},completed:{color:"success",icon:s.jsx(ec.Z,{}),text:"已完成"},paused:{color:"warning",icon:s.jsx(ep.Z,{}),text:"已暂停"}}}static getOrderStatusConfig(e){return this.ORDER_STATUS_MAP[e]||{color:"default",icon:s.jsx(ex.Z,{}),text:"未知状态"}}static getWorkstationStatusConfig(e){return this.WORKSTATION_STATUS_MAP[e]||{color:"default",icon:s.jsx(ex.Z,{}),text:"未知状态"}}static getProductionOrderStatusConfig(e){return this.PRODUCTION_ORDER_STATUS_MAP[e]||{color:"default",icon:s.jsx(ex.Z,{}),text:"未知状态"}}static getWorkOrderStatusConfig(e){return this.WORK_ORDER_STATUS_MAP[e]||{color:"default",icon:s.jsx(ex.Z,{}),text:"未知状态"}}static renderOrderStatusTag(e){let t=this.getOrderStatusConfig(e);return s.jsx(h.Z,{color:t.color,icon:t.icon,children:t.text})}static renderWorkstationStatusTag(e){let t=this.getWorkstationStatusConfig(e);return s.jsx(h.Z,{color:t.color,icon:t.icon,children:t.text})}static renderProductionOrderStatusTag(e){let t=this.getProductionOrderStatusConfig(e);return s.jsx(h.Z,{color:t.color,icon:t.icon,children:t.text})}static renderWorkOrderStatusTag(e){let t=this.getWorkOrderStatusConfig(e);return s.jsx(h.Z,{color:t.color,icon:t.icon,children:t.text})}static renderStatusTag(e,t="order"){switch(t){case"workstation":return this.renderWorkstationStatusTag(e);case"production_order":return this.renderProductionOrderStatusTag(e);case"work_order":return this.renderWorkOrderStatusTag(e);default:return this.renderOrderStatusTag(e)}}static getStatusColor(e,t="order"){switch(t){case"workstation":return this.getWorkstationStatusConfig(e).color;case"production_order":return this.getProductionOrderStatusConfig(e).color;case"work_order":return this.getWorkOrderStatusConfig(e).color;default:return this.getOrderStatusConfig(e).color}}static getStatusIcon(e,t="order"){switch(t){case"workstation":return this.getWorkstationStatusConfig(e).icon;case"production_order":return this.getProductionOrderStatusConfig(e).icon;case"work_order":return this.getWorkOrderStatusConfig(e).icon;default:return this.getOrderStatusConfig(e).icon}}static getStatusText(e,t="order"){switch(t){case"workstation":return this.getWorkstationStatusConfig(e).text;case"production_order":return this.getProductionOrderStatusConfig(e).text;case"work_order":return this.getWorkOrderStatusConfig(e).text;default:return this.getOrderStatusConfig(e).text}}}let{renderOrderStatusTag:td,renderWorkstationStatusTag:tu,renderProductionOrderStatusTag:th,renderWorkOrderStatusTag:tm,renderStatusTag:tg,getStatusColor:tp,getStatusIcon:tx,getStatusText:tf,getOrderStatusConfig:ty,getWorkstationStatusConfig:tj,getProductionOrderStatusConfig:tw,getWorkOrderStatusConfig:tv}=tc;var tk=r(98033);let tS=({workstation:e,onEdit:t,onDelete:r,onStatusToggle:n})=>s.jsx(l.Z,{style:{height:"100%",transition:tk.ZB.transitions.normal,borderColor:"active"===e.status?"#bbf7d0":tk.ZB.colors.gray[200],...tk.ZB.createShadow("md")},styles:{body:{padding:tk.ZB.spacing.md},header:{borderBottom:`1px solid ${tk.ZB.colors.gray[200]}`}},onMouseEnter:e=>{e.currentTarget.style.boxShadow=tk.ZB.shadows.lg},onMouseLeave:e=>{e.currentTarget.style.boxShadow=tk.ZB.shadows.md},actions:[s.jsx(I.Z,{title:"编辑",children:s.jsx(m.ZP,{type:"text",icon:s.jsx(te.Z,{}),onClick:()=>t(e)})},"edit"),s.jsx(I.Z,{title:"active"===e.status?"停用":"启用",children:s.jsx(m.ZP,{type:"text",icon:tc.getStatusIcon(e.status,"workstation"),onClick:()=>n(e)})},"toggle"),s.jsx(e7.Z,{title:"确定要删除这个工位吗？",description:"删除后将无法恢复，请谨慎操作。",onConfirm:()=>r(e.id),okText:"确定",cancelText:"取消",children:s.jsx(I.Z,{title:"删除",children:s.jsx(m.ZP,{type:"text",danger:!0,icon:s.jsx(tt.Z,{})})})},"delete")],children:(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:tk.ZB.spacing.md},children:[s.jsx("div",{style:{display:"flex",alignItems:"flex-start",justifyContent:"space-between"},children:(0,s.jsxs)("div",{style:{flex:1},children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:tk.ZB.spacing.sm},children:[s.jsx("span",{style:{fontFamily:"monospace",fontSize:"18px",fontWeight:"bold",color:tk.ZB.colors.primary[600]},children:H(e.code)}),tc.renderWorkstationStatusTag(e.status)]}),s.jsx("h3",{style:{fontSize:"16px",fontWeight:"600",marginTop:tk.ZB.spacing.xs,color:tk.ZB.colors.gray[800],margin:`${tk.ZB.spacing.xs}px 0 0 0`},children:H(e.name)}),e.description&&s.jsx("p",{style:{fontSize:"14px",color:tk.ZB.colors.gray[500],marginTop:tk.ZB.spacing.xs,display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden",margin:`${tk.ZB.spacing.xs}px 0 0 0`},children:H(e.description)})]})}),s.jsx("div",{style:{display:"flex",flexDirection:"column",gap:tk.ZB.spacing.sm},children:(0,s.jsxs)("div",{style:{background:tk.ZB.colors.primary[50],borderRadius:tk.ZB.borderRadius.sm,padding:tk.ZB.spacing.sm,marginTop:tk.ZB.spacing.sm},children:[(0,s.jsxs)("div",{style:{fontSize:"12px",color:tk.ZB.colors.primary[600],marginBottom:tk.ZB.spacing.xs},children:[s.jsx(j.Z,{style:{marginRight:tk.ZB.spacing.xs}}),"高级功能"]}),s.jsx("div",{style:{fontSize:"12px",color:tk.ZB.colors.primary[500]},children:"工位状态监控、产能统计等功能正在开发中"})]})}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:tk.ZB.colors.gray[400],borderTop:`1px solid ${tk.ZB.colors.gray[200]}`,paddingTop:tk.ZB.spacing.sm},children:[(0,s.jsxs)("div",{children:["创建: ",new Date(e.createdAt).toLocaleDateString()]}),(0,s.jsxs)("div",{children:["更新: ",new Date(e.updatedAt).toLocaleDateString()]})]})]})});var tZ=r(51221);class tb{static getInstance(){return tb.instance||(tb.instance=new tb),tb.instance}async validateWorkstation(e){let t=[],r=[];try{let s=await A.dataAccessManager.productionWorkOrders.getAll();if("success"!==s.status||!s.data)return t.push({type:"missing_work_order",severity:"error",description:"无法获取生产工单数据进行验证"}),{isValid:!1,workstationId:e.id,workstationCode:e.code,issues:t,recommendations:["请检查生产工单数据访问服务"]};let n=s.data.items||[];if(!(e.currentMoldNumber||e.currentBatchNumber||e.batchNumberQueue&&e.batchNumberQueue.length>0))return{isValid:!0,workstationId:e.id,workstationCode:e.code,issues:[],recommendations:[]};if(e.currentBatchNumber){let s=n.find(t=>t.batchNumber===e.currentBatchNumber&&t.workstationCode===e.code);s?e.currentMoldNumber&&s.formingMoldNumber!==e.currentMoldNumber&&(t.push({type:"mold_mismatch",severity:"warning",description:`工位模具编号与工单不匹配`,currentValue:e.currentMoldNumber,expectedValue:s.formingMoldNumber}),r.push("同步工位模具编号与工单数据")):(t.push({type:"missing_work_order",severity:"error",description:`工位当前批次号 ${e.currentBatchNumber} 在生产工单中不存在`,currentValue:e.currentBatchNumber}),r.push("检查生产工单数据或重置工位状态"))}if(e.batchNumberQueue&&e.batchNumberQueue.length>0)for(let s of e.batchNumberQueue)n.find(t=>t.batchNumber===s&&(t.workstationCode===e.code||"pending"===t.status))||(t.push({type:"queue_inconsistency",severity:"warning",description:`队列中的批次号 ${s} 在生产工单中不存在`,currentValue:s}),r.push("清理工位队列中的无效批次号"));return e.currentMoldNumber&&!e.currentBatchNumber&&(t.push({type:"orphaned_production_data",severity:"warning",description:"工位有模具编号但无对应批次号",currentValue:e.currentMoldNumber}),r.push("重置工位状态或补充批次号信息")),{isValid:0===t.filter(e=>"error"===e.severity).length,workstationId:e.id,workstationCode:e.code,issues:t,recommendations:r}}catch(t){return console.error(`验证工位 ${e.code} 数据一致性时发生错误:`,t),{isValid:!1,workstationId:e.id,workstationCode:e.code,issues:[{type:"missing_work_order",severity:"error",description:"验证过程中发生系统错误"}],recommendations:["请联系系统管理员检查"]}}}async validateAllWorkstations(){try{console.log("\uD83D\uDD0D 开始批量验证工位数据一致性");let e=await A.dataAccessManager.workstations.getAll();if("success"!==e.status||!e.data)throw Error("无法获取工位数据");let t=e.data.items||[],r=[];for(let e of t){let t=await this.validateWorkstation(e);r.push(t)}let s=r.filter(e=>e.isValid).length,n=r.length-s,a=r.filter(e=>!e.isValid),i={missingWorkOrders:0,batchMismatches:0,moldMismatches:0,orphanedData:0,queueInconsistencies:0};a.forEach(e=>{e.issues.forEach(e=>{switch(e.type){case"missing_work_order":i.missingWorkOrders++;break;case"batch_mismatch":i.batchMismatches++;break;case"mold_mismatch":i.moldMismatches++;break;case"orphaned_production_data":i.orphanedData++;break;case"queue_inconsistency":i.queueInconsistencies++}})});let o={totalWorkstations:t.length,validWorkstations:s,invalidWorkstations:n,issues:a,summary:i,generatedAt:new Date().toISOString()};return console.log(`✅ 工位数据一致性验证完成:`),console.log(`   总工位数: ${o.totalWorkstations}`),console.log(`   有效工位: ${o.validWorkstations}`),console.log(`   问题工位: ${o.invalidWorkstations}`),o}catch(e){throw console.error("批量验证工位数据一致性失败:",e),e}}async autoFixInconsistencies(e){let t=[],r=0;for(let s of(console.log(`🔧 开始自动修复 ${e.length} 个工位的数据一致性问题`),e))try{s.issues.some(e=>"orphaned_production_data"===e.type||"missing_work_order"===e.type)?t.push({workstationId:s.workstationId,success:!1,message:`工位 ${s.workstationCode} 有孤立数据，需要手动处理（单个工位重置功能暂时不可用）`}):t.push({workstationId:s.workstationId,success:!1,message:`工位 ${s.workstationCode} 的问题需要手动处理`}),r++}catch(e){r++,t.push({workstationId:s.workstationId,success:!1,message:`修复工位 ${s.workstationCode} 时发生错误: ${e}`})}return console.log(`✅ 自动修复完成: 成功 0 个，失败 ${r} 个`),{fixed:0,failed:r,results:t}}}let tC=tb.getInstance();var tT=r(27392);class tD{constructor(){this.eventSource=null,this.listeners=new Map,this.statusListeners=new Set,this.connectionStatus="disconnected",this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.maxReconnectDelay=3e4,this.reconnectTimer=null}static getInstance(){return tD.instance||(tD.instance=new tD),tD.instance}connect(e="/api/workstation/events"){this.eventSource&&(console.warn("[WorkstationRealtimeService] 已存在连接，先断开现有连接"),this.disconnect()),console.log(`🔌 [WorkstationRealtimeService] 连接到实时事件流: ${e}`),this.setConnectionStatus("connecting");try{this.eventSource=new EventSource(e),this.setupEventHandlers()}catch(t){console.error("[WorkstationRealtimeService] 连接失败:",t),this.setConnectionStatus("error",t),this.scheduleReconnect(e)}}disconnect(){console.log("\uD83D\uDD0C [WorkstationRealtimeService] 断开实时事件流连接"),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.setConnectionStatus("disconnected"),this.reconnectAttempts=0}addEventListener(e,t){this.listeners.has(e)||this.listeners.set(e,new Set),this.listeners.get(e).add(t),console.log(`📝 [WorkstationRealtimeService] 添加事件监听器: ${e}`)}removeEventListener(e,t){let r=this.listeners.get(e);r&&(r.delete(t),0===r.size&&this.listeners.delete(e)),console.log(`🗑️ [WorkstationRealtimeService] 移除事件监听器: ${e}`)}addConnectionStatusListener(e){this.statusListeners.add(e)}removeConnectionStatusListener(e){this.statusListeners.delete(e)}getConnectionStatus(){return this.connectionStatus}reconnect(){if(this.eventSource){let e=this.eventSource.url;this.disconnect(),this.connect(e)}}setupEventHandlers(){this.eventSource&&(this.eventSource.onopen=()=>{console.log("✅ [WorkstationRealtimeService] 实时事件流连接成功"),this.setConnectionStatus("connected"),this.reconnectAttempts=0,this.reconnectDelay=1e3},this.eventSource.onerror=e=>{console.error("❌ [WorkstationRealtimeService] 实时事件流连接错误:",e),this.setConnectionStatus("error",Error("EventSource connection error")),this.eventSource?.readyState===EventSource.CLOSED&&this.scheduleReconnect(this.eventSource.url)},this.eventSource.onmessage=e=>{try{let t=JSON.parse(e.data);this.handleWorkstationEvent(t)}catch(t){console.error("[WorkstationRealtimeService] 解析事件数据失败:",t,e.data)}},["workstation_updated","workstation_created","workstation_deleted","workstation_status_changed","workstation_production_assigned","workstation_reset","batch_update_completed"].forEach(e=>{this.eventSource.addEventListener(e,t=>{try{let e=JSON.parse(t.data);this.handleWorkstationEvent(e)}catch(t){console.error(`[WorkstationRealtimeService] 解析 ${e} 事件失败:`,t)}})}))}handleWorkstationEvent(e){console.log(`📡 [WorkstationRealtimeService] 收到工位事件:`,{type:e.type,workstationId:e.workstationId,timestamp:e.timestamp});let t=this.listeners.get(e.type);t&&t.forEach(t=>{try{t(e)}catch(e){console.error(`[WorkstationRealtimeService] 事件监听器执行失败:`,e)}}),this.logEventHandling(e)}setConnectionStatus(e,t){this.connectionStatus!==e&&(this.connectionStatus=e,console.log(`🔄 [WorkstationRealtimeService] 连接状态变更: ${e}`),this.statusListeners.forEach(r=>{try{r(e,t)}catch(e){console.error("[WorkstationRealtimeService] 状态监听器执行失败:",e)}}))}scheduleReconnect(e){if(this.reconnectAttempts>=this.maxReconnectAttempts){console.error(`[WorkstationRealtimeService] 达到最大重连次数 (${this.maxReconnectAttempts})，停止重连`);return}this.reconnectAttempts++;let t=Math.min(this.reconnectDelay*Math.pow(2,this.reconnectAttempts-1),this.maxReconnectDelay);console.log(`🔄 [WorkstationRealtimeService] 计划在 ${t}ms 后进行第 ${this.reconnectAttempts} 次重连`),this.reconnectTimer=setTimeout(()=>{console.log(`🔄 [WorkstationRealtimeService] 执行第 ${this.reconnectAttempts} 次重连`),this.connect(e)},t)}logEventHandling(e){console.log(`📝 [WorkstationRealtimeService] 事件处理日志:`,{timestamp:new Date().toISOString(),eventType:e.type,workstationId:e.workstationId,source:e.metadata?.source,userId:e.metadata?.userId,reason:e.metadata?.reason})}}let tM=tD.getInstance(),{Search:t$}=eN.default,{Option:tI}=E.default,{Text:tE}=er.default,tO=({loading:e=!1})=>{let{message:t}=i.Z.useApp(),[r,a]=(0,n.useState)(!1),[o,g]=(0,n.useState)(null),[f]=es.Z.useForm(),[y,w]=(0,n.useState)(""),[v,k]=(0,n.useState)(""),[S,b]=(0,n.useState)("table"),[C,T]=(0,n.useState)("list"),[$,O]=(0,n.useState)([]),[N,_]=(0,n.useState)(!1),[W,A]=(0,n.useState)(!1),[R,P]=(0,n.useState)(null),[B,L]=(0,n.useState)(!1),{connectionStatus:H,isConnected:V}=function(e,t,r={}){let{onAnyWorkstationEvent:s,...a}=function(e={}){let{autoConnect:t=!0,endpoint:r="/api/workstation/events",disconnectOnUnmount:s=!0,onConnectionError:a,onConnectionStatusChange:i}=e,[o,l]=(0,n.useState)(tM.getConnectionStatus()),c=(0,n.useRef)(new Map),d=(0,n.useRef)(null),u=(0,n.useCallback)((e,t)=>{l(e),i&&i(e),t&&a&&a(t)},[a,i]),h=(0,n.useCallback)(()=>{tM.connect(r)},[r]),m=(0,n.useCallback)(()=>{tM.disconnect()},[]),g=(0,n.useCallback)(()=>{tM.reconnect()},[]),p=(0,n.useCallback)(e=>t=>(tM.addEventListener(e,t),c.current.has(e)||c.current.set(e,new Set),c.current.get(e).add(t),()=>{tM.removeEventListener(e,t);let r=c.current.get(e);r&&(r.delete(t),0===r.size&&c.current.delete(e))}),[]),x=(0,n.useCallback)(e=>{let t=["workstation_updated","workstation_created","workstation_deleted","workstation_status_changed","workstation_production_assigned","workstation_reset","batch_update_completed"].map(t=>p(t)(e));return()=>{t.forEach(e=>e())}},[p]);(0,n.useEffect)(()=>(d.current=u,tM.addConnectionStatusListener(u),t&&"disconnected"===o&&h(),()=>{d.current&&(tM.removeConnectionStatusListener(d.current),d.current=null),c.current.forEach((e,t)=>{e.forEach(e=>{tM.removeEventListener(t,e)})}),c.current.clear(),s&&m()}),[t,h,m,s,u,o]);let f="connected"===o,y="connecting"===o,j="error"===o;return{connectionStatus:o,isConnected:f,isConnecting:y,hasError:j,connect:h,disconnect:m,reconnect:g,onWorkstationUpdate:p("workstation_updated"),onWorkstationCreate:p("workstation_created"),onWorkstationDelete:p("workstation_deleted"),onWorkstationStatusChange:p("workstation_status_changed"),onWorkstationProductionAssign:p("workstation_production_assigned"),onWorkstationReset:p("workstation_reset"),onBatchUpdateComplete:p("batch_update_completed"),onAnyWorkstationEvent:x}}(r);return(0,n.useEffect)(()=>s(r=>{switch(console.log(`🔄 [useWorkstationSync] 处理工位事件: ${r.type}`,r.workstationId),r.type){case"workstation_updated":case"workstation_status_changed":case"workstation_production_assigned":case"workstation_reset":r.workstation&&t(e.map(e=>e.id===r.workstationId?r.workstation:e));break;case"workstation_created":r.workstation&&t([...e,r.workstation]);break;case"workstation_deleted":t(e.filter(e=>e.id!==r.workstationId));break;case"batch_update_completed":console.log("\uD83D\uDD04 [useWorkstationSync] 批量更新完成，建议重新加载数据")}}),[s,t,e]),a}($,O,{autoConnect:!0,onConnectionError:e=>{console.error("工位实时同步连接错误:",e),t.warning("实时同步连接异常，数据可能不是最新的")},onConnectionStatusChange:e=>{console.log(`工位实时同步状态: ${e}`),"connected"===e?t.success("实时同步已连接",2):"disconnected"===e&&t.warning("实时同步已断开",2)}}),q=(0,n.useCallback)(async()=>{_(!0);try{let e=await dataAccessManager.workstations.getAll();"success"===e.status&&e.data&&e.data.items?(O(e.data.items),console.log({count:e.data.items.length,workstations:e.data.items.map(e=>({id:e.id,code:e.code,name:e.name}))})):t.error(e.message||"加载工位数据失败")}catch(e){t.error("系统错误，请稍后重试")}finally{_(!1)}},[t]);(0,n.useEffect)(()=>{q()},[q]);let F=()=>{let e=$.map(e=>e.code);for(let t of["A","B","C","D","E","F","G","H"])for(let r=1;r<=99;r++){let s=`${t}${r}`;if(!e.includes(s))return s}return`A${e.length+1}`},Q=e=>{g(e),f.setFieldsValue({...e}),a(!0)},U=async e=>{await (0,tZ.Ro)(()=>dataAccessManager.workstations.delete(e),"删除工位")&&(t.success("工位删除成功"),dataAccessManager.clearServiceCache("WorkstationService"),await q())},Y=async e=>{try{let r="active"===e.status?"inactive":"active";console.log(`🔄 开始切换工位 ${e.code} 状态: ${e.status} → ${r}`);let s={source:"user",operation:"status_change",userId:"current_user",reason:`用户手动${"active"===r?"启用":"停用"}工位`},n=await tT.r.updateWorkstation(e.id,{status:r},s,e.version);n.success?(t.success(`工位已${"active"===r?"启用":"停用"}`),dataAccessManager.clearServiceCache("WorkstationService"),await q(),console.log(`✅ 工位 ${e.code} 状态切换成功`)):(n.conflictInfo?(t.error(`状态切换失败：数据已被其他用户修改，请刷新后重试`),dataAccessManager.clearServiceCache("WorkstationService"),await q()):t.error(`状态切换失败：${n.error}`),console.error(`❌ 工位 ${e.code} 状态切换失败:`,n.error)),n.warnings&&n.warnings.length>0&&n.warnings.forEach(e=>{t.warning(e)})}catch(e){console.error(`❌ 工位状态切换异常:`,e),t.error("系统错误，请稍后重试")}},X=async e=>{try{console.log(`🔄 开始重置工位 ${e.code} 状态`);let r=await tT.r.updateWorkstation(e.id,{currentMoldNumber:null,currentBatchNumber:null,batchNumberQueue:[],lastEndTime:null},{source:"user",operation:"reset",userId:"current_user",reason:"用户手动重置工位为空闲状态"},e.version);r.success?(t.success(`工位 ${e.code} 已重置为空闲状态`),dataAccessManager.clearServiceCache("WorkstationService"),await q(),console.log(`✅ 工位 ${e.code} 重置完成`)):(r.conflictInfo?(t.error(`重置失败：数据已被其他用户修改，请刷新后重试`),dataAccessManager.clearServiceCache("WorkstationService"),await q()):t.error(`重置失败：${r.error}`),console.error(`❌ 工位 ${e.code} 重置失败:`,r.error)),r.warnings&&r.warnings.length>0&&r.warnings.forEach(e=>{t.warning(e)})}catch(r){console.error(`❌ 重置工位 ${e.code} 状态异常:`,r),t.error("系统错误，请稍后重试")}},G=async()=>{try{A(!0),console.log("\uD83D\uDD0D 开始工位数据一致性验证");let e=await tC.validateAllWorkstations();P(e),L(!0),0===e.invalidWorkstations?t.success("所有工位数据一致性验证通过"):t.warning(`发现 ${e.invalidWorkstations} 个工位存在数据一致性问题`),console.log("✅ 数据一致性验证完成:",e)}catch(e){console.error("❌ 数据一致性验证失败:",e),t.error("数据一致性验证失败，请稍后重试")}finally{A(!1)}},K=async()=>{if(!R||0===R.issues.length){t.info("没有需要修复的问题");return}try{console.log("\uD83D\uDD27 开始自动修复数据一致性问题");let e=await tC.autoFixInconsistencies(R.issues);if(e.fixed>0){t.success(`成功修复 ${e.fixed} 个工位的数据问题`),await q();let r=await tC.validateAllWorkstations();P(r)}e.failed>0&&t.warning(`${e.failed} 个工位的问题需要手动处理`),console.log("✅ 自动修复完成:",e)}catch(e){console.error("❌ 自动修复失败:",e),t.error("自动修复失败，请稍后重试")}},J=async()=>{try{let e=await f.validateFields();if(o){console.log(`🔄 开始更新工位 ${o.code}`);let r=await tT.r.updateWorkstation(o.id,e,{source:"user",operation:"general",userId:"current_user",reason:"用户编辑工位信息"},o.version);if(r.success)t.success("工位更新成功"),dataAccessManager.clearServiceCache("WorkstationService"),await q(),console.log(`✅ 工位 ${o.code} 更新成功`);else{r.conflictInfo?(t.error(`更新失败：数据已被其他用户修改，请刷新后重试`),dataAccessManager.clearServiceCache("WorkstationService"),await q()):t.error(`更新失败：${r.error}`),console.error(`❌ 工位 ${o.code} 更新失败:`,r.error);return}r.warnings&&r.warnings.length>0&&r.warnings.forEach(e=>{t.warning(e)})}else{let r={id:`ws_${Date.now()}`,...e,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};if(!await (0,tZ.Ro)(()=>dataAccessManager.workstations.create(r),"创建工位"))return;t.success("工位创建成功"),dataAccessManager.clearServiceCache("WorkstationService"),await q(),console.log(`✅ 工位 ${e.code} 创建成功`)}a(!1),f.resetFields(),g(null)}catch(e){console.error("工位操作失败:",e),t.error("操作失败，请重试")}},ee=$.filter(e=>{let t=!y||e.name.toLowerCase().includes(y.toLowerCase())||e.code.toLowerCase().includes(y.toLowerCase())||e.description&&e.description.toLowerCase().includes(y.toLowerCase()),r=!v||e.status===v;return t&&r}),et={total:$.length,active:$.filter(e=>"active"===e.status).length,inactive:$.filter(e=>"inactive"===e.status).length},er=[{title:"工位编码",dataIndex:"code",key:"code",width:100,fixed:"left",sorter:(e,t)=>e.code.localeCompare(t.code)},{title:"工位名称",dataIndex:"name",key:"name",width:80,ellipsis:!0},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>s.jsx(h.Z,{color:"active"===e?"success":"default",children:"active"===e?"启用":"停用"}),filters:[{text:"启用",value:"active"},{text:"停用",value:"inactive"}],onFilter:(e,t)=>t.status===e},{title:"当前模具",dataIndex:"currentMoldNumber",key:"currentMoldNumber",width:120,render:(e,t)=>e?s.jsx(I.Z,{title:`当前使用模具: ${e}`,children:s.jsx(h.Z,{color:"blue",children:e})}):s.jsx(I.Z,{title:"工位空闲，无当前模具",children:s.jsx(h.Z,{color:"default",style:{color:"#999"},children:"空闲"})})},{title:"当前批次",dataIndex:"currentBatchNumber",key:"currentBatchNumber",width:140,render:(e,t)=>e?s.jsx(I.Z,{title:`当前生产批次: ${e}`,children:s.jsx(h.Z,{color:"green",children:e})}):s.jsx(I.Z,{title:"工位空闲，无生产批次",children:s.jsx(h.Z,{color:"default",style:{color:"#999"},children:"无任务"})})},{title:"队列任务",dataIndex:"batchNumberQueue",key:"batchNumberQueue",width:120,render:(e=[],t)=>{let r=e&&e.length>0;return t.currentMoldNumber||t.currentBatchNumber||r?s.jsx(I.Z,{title:r?`排队任务: ${e.join(", ")}`:"无排队任务",children:(0,s.jsxs)(h.Z,{color:r?"orange":"default",children:[e.length," 个任务"]})}):s.jsx(I.Z,{title:"工位空闲，无排队任务",children:s.jsx(h.Z,{color:"default",style:{color:"#999"},children:"空闲中"})})}},{title:"预计结束",dataIndex:"lastEndTime",key:"lastEndTime",width:140,render:(e,t)=>{if(!e){let e=!t.currentMoldNumber&&!t.currentBatchNumber;return s.jsx(I.Z,{title:e?"工位空闲，无预计结束时间":"暂无预计结束时间",children:s.jsx(tE,{type:"secondary",style:{color:"#ccc"},children:e?"空闲中":"-"})})}return s.jsx(I.Z,{title:`预计结束时间: ${e}`,children:s.jsx(tE,{type:"secondary",children:z()(e).format("MM-DD HH:mm")})})}},{title:"操作",key:"action",width:200,fixed:"right",render:(e,t)=>(0,s.jsxs)(u.Z,{size:"small",children:[s.jsx(I.Z,{title:"编辑",children:s.jsx(m.ZP,{type:"text",icon:s.jsx(te.Z,{}),onClick:()=>Q(t),size:"small"})}),s.jsx(I.Z,{title:"active"===t.status?"停用":"启用",children:s.jsx(e5,{size:"small",checked:"active"===t.status,onChange:()=>Y(t)})}),(t.currentMoldNumber||t.currentBatchNumber||t.batchNumberQueue&&t.batchNumberQueue.length>0)&&s.jsx(e7.Z,{title:"重置工位状态",description:"确定要将此工位重置为空闲状态吗？这将清空当前模具、批次和队列信息。",onConfirm:()=>X(t),okText:"确定重置",cancelText:"取消",children:s.jsx(I.Z,{title:"重置为空闲状态",children:s.jsx(m.ZP,{type:"text",icon:s.jsx(Z.Z,{}),size:"small",style:{color:"#faad14"}})})}),s.jsx(e7.Z,{title:"确定要删除这个工位吗？",description:"删除后将无法恢复，请谨慎操作。",onConfirm:()=>U(t.id),okText:"确定",cancelText:"取消",children:s.jsx(I.Z,{title:"删除",children:s.jsx(m.ZP,{type:"text",icon:s.jsx(tt.Z,{}),danger:!0,size:"small"})})})]})}];return(0,s.jsxs)("div",{style:{padding:0},children:[s.jsx(l.Z,{size:"small",style:{marginBottom:"16px"},children:(0,s.jsxs)(c.Z,{justify:"space-between",align:"middle",children:[s.jsx(d.Z,{children:(0,s.jsxs)(u.Z,{children:[s.jsx(t$,{placeholder:"搜索工位编码、名称或描述",value:y,onChange:e=>w(e.target.value),style:{width:250},allowClear:!0}),(0,s.jsxs)(E.default,{placeholder:"筛选状态",value:v,onChange:k,style:{width:120},allowClear:!0,children:[s.jsx(tI,{value:"active",children:"启用"}),s.jsx(tI,{value:"inactive",children:"停用"})]}),s.jsx(I.Z,{title:V?"实时同步已连接，数据将自动更新":`实时同步状态: ${H}`,children:s.jsx(h.Z,{color:V?"green":"connecting"===H?"orange":"red",style:{cursor:"help"},children:V?"\uD83D\uDFE2 实时同步":"connecting"===H?"\uD83D\uDFE1 连接中":"\uD83D\uDD34 离线"})})]})}),s.jsx(d.Z,{children:(0,s.jsxs)(u.Z,{children:[s.jsx(m.ZP,{icon:s.jsx(Z.Z,{}),onClick:q,loading:N,children:"刷新"}),s.jsx(m.ZP,{icon:s.jsx(tr.Z,{}),onClick:()=>{try{let e=["工位编码,工位名称,描述,状态,创建时间,更新时间",...ee.map(e=>[e.code,e.name,e.description||"","active"===e.status?"启用":"停用",new Date(e.createdAt).toLocaleDateString(),new Date(e.updatedAt).toLocaleDateString()].join(","))].join("\n"),r=new Blob([e],{type:"text/csv;charset=utf-8;"}),s=document.createElement("a"),n=URL.createObjectURL(r);s.setAttribute("href",n),s.setAttribute("download",`工位数据_${new Date().toISOString().split("T")[0]}.csv`),s.style.visibility="hidden",document.body.appendChild(s),s.click(),document.body.removeChild(s),t.success("工位数据导出成功")}catch(e){t.error("导出失败，请稍后重试")}},children:"导出"}),s.jsx(m.ZP,{icon:s.jsx(D.Z,{}),onClick:G,loading:W,children:"数据验证"}),s.jsx(m.ZP,{type:"primary",icon:s.jsx(ts.Z,{}),onClick:()=>{g(null),f.resetFields(),f.setFieldsValue({code:F(),status:"active"}),a(!0)},children:"新增工位"})]})})]})}),(0,s.jsxs)(c.Z,{gutter:16,style:{marginBottom:"16px"},children:[s.jsx(d.Z,{xs:24,sm:12,md:8,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"工位总数",value:et.total,prefix:s.jsx(j.Z,{}),valueStyle:{color:"#1890ff"}})})}),s.jsx(d.Z,{xs:24,sm:12,md:8,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"启用工位",value:et.active,prefix:s.jsx(M.Z,{}),valueStyle:{color:"#52c41a"}})})}),s.jsx(d.Z,{xs:24,sm:12,md:8,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"停用工位",value:et.inactive,prefix:s.jsx(M.Z,{}),valueStyle:{color:"#ff4d4f"}})})})]}),s.jsx(l.Z,{children:s.jsx(x.default,{activeKey:C,onChange:T,tabBarExtraContent:"list"===C&&(0,s.jsxs)(u.Z.Compact,{size:"small",children:[s.jsx(m.ZP,{type:"table"===S?"primary":"default",icon:s.jsx(ta,{}),onClick:()=>b("table"),size:"small",children:"表格"}),s.jsx(m.ZP,{type:"card"===S?"primary":"default",icon:s.jsx(ti.Z,{}),onClick:()=>b("card"),size:"small",children:"卡片"})]}),items:[{key:"list",label:(0,s.jsxs)("span",{children:[s.jsx(j.Z,{}),"工位列表 (",ee.length,")"]}),children:"table"===S?s.jsx(ei.Z,{columns:er,dataSource:ee,rowKey:"id",loading:N,pagination:{total:ee.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`},scroll:{x:1200}}):s.jsx(c.Z,{gutter:[16,16],children:ee.map(e=>s.jsx(d.Z,{xs:24,sm:12,lg:8,xl:6,children:s.jsx(tS,{workstation:e,onEdit:Q,onDelete:U,onStatusToggle:Y})},e.id))})},{key:"report",label:"统计报告",icon:s.jsx(D.Z,{}),children:(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[s.jsx(l.Z,{title:"总体统计",size:"small",children:(0,s.jsxs)(c.Z,{gutter:[16,16],children:[s.jsx(d.Z,{xs:24,sm:12,md:8,children:s.jsx(p.Z,{title:"工位利用率",value:et.total>0?Math.round(et.active/et.total*100):0,suffix:"%",valueStyle:{color:"#52c41a"}})}),s.jsx(d.Z,{xs:24,sm:12,md:8,children:s.jsx(e9.Z,{message:"功能开发中",description:"产能统计功能正在开发中，敬请期待",type:"info",showIcon:!0,style:{height:"100%"}})})]})}),s.jsx(l.Z,{title:"改进建议",size:"small",children:s.jsx(e9.Z,{message:"系统建议",description:(0,s.jsxs)("ul",{style:{listStyleType:"disc",listStylePosition:"inside",display:"flex",flexDirection:"column",gap:"4px",margin:0,padding:0},children:[et.inactive>0&&(0,s.jsxs)("li",{children:["有 ",et.inactive," 个工位处于停用状态，建议检查设备状况"]}),et.total<5&&s.jsx("li",{children:"工位数量偏少，建议适当增加工位配置"}),et.active===et.total&&et.total>0&&s.jsx("li",{children:"所有工位均已启用，工位状态管理良好"})]}),type:"info",showIcon:!0})})]})}]})}),s.jsx(en.Z,{title:o?"编辑工位":"新增工位",open:r,onOk:J,onCancel:()=>{a(!1),f.resetFields()},width:600,okText:"保存",cancelText:"取消",children:(0,s.jsxs)(es.Z,{form:f,layout:"vertical",initialValues:{status:"active"},children:[(0,s.jsxs)(c.Z,{gutter:16,children:[s.jsx(d.Z,{span:12,children:s.jsx(es.Z.Item,{label:"工位编码",name:"code",rules:(0,to.zX)("code"),children:s.jsx(eN.default,{placeholder:"如：A1, B2",disabled:!!o})})}),s.jsx(d.Z,{span:12,children:s.jsx(es.Z.Item,{label:"工位名称",name:"name",rules:(0,to.zX)("name"),children:s.jsx(eN.default,{placeholder:"请输入工位名称"})})})]}),s.jsx(es.Z.Item,{label:"工位描述",name:"description",rules:(0,to.zX)("description"),children:s.jsx(eN.default,{placeholder:"请输入工位描述"})}),s.jsx(es.Z.Item,{label:"状态",name:"status",rules:[{required:!0,message:"请选择状态"}],children:(0,s.jsxs)(E.default,{children:[s.jsx(tI,{value:"active",children:"启用"}),s.jsx(tI,{value:"inactive",children:"停用"})]})})]})}),s.jsx(en.Z,{title:"工位数据一致性验证报告",open:B,onCancel:()=>L(!1),width:800,footer:[s.jsx(m.ZP,{onClick:()=>L(!1),children:"关闭"},"close"),R&&R.invalidWorkstations>0&&s.jsx(m.ZP,{type:"primary",onClick:K,children:"自动修复"},"autofix")],children:R&&(0,s.jsxs)("div",{children:[(0,s.jsxs)(c.Z,{gutter:16,style:{marginBottom:16},children:[s.jsx(d.Z,{span:6,children:s.jsx(p.Z,{title:"总工位数",value:R.totalWorkstations,valueStyle:{color:"#1890ff"}})}),s.jsx(d.Z,{span:6,children:s.jsx(p.Z,{title:"验证通过",value:R.validWorkstations,valueStyle:{color:"#52c41a"}})}),s.jsx(d.Z,{span:6,children:s.jsx(p.Z,{title:"存在问题",value:R.invalidWorkstations,valueStyle:{color:"#ff4d4f"}})}),s.jsx(d.Z,{span:6,children:s.jsx(p.Z,{title:"验证时间",value:z()(R.generatedAt).format("HH:mm:ss"),valueStyle:{fontSize:14}})})]}),R.invalidWorkstations>0&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(e9.Z,{message:"发现数据一致性问题",description:`共发现 ${R.invalidWorkstations} 个工位存在数据一致性问题，建议及时处理。`,type:"warning",showIcon:!0,style:{marginBottom:16}}),s.jsx("div",{style:{maxHeight:400,overflowY:"auto"},children:R.issues.map((e,t)=>s.jsx(l.Z,{size:"small",style:{marginBottom:8},children:(0,s.jsxs)("div",{children:[(0,s.jsxs)(tE,{strong:!0,children:["工位 ",e.workstationCode]}),s.jsx("div",{style:{marginTop:8},children:e.issues.map((e,t)=>(0,s.jsxs)("div",{style:{marginBottom:4},children:[s.jsx(h.Z,{color:"error"===e.severity?"red":"warning"===e.severity?"orange":"blue",children:"error"===e.severity?"错误":"warning"===e.severity?"警告":"信息"}),s.jsx(tE,{children:e.description})]},t))}),e.recommendations.length>0&&(0,s.jsxs)("div",{style:{marginTop:8},children:[s.jsx(tE,{type:"secondary",children:"建议："}),s.jsx("ul",{style:{margin:"4px 0",paddingLeft:20},children:e.recommendations.map((e,t)=>s.jsx("li",{children:s.jsx(tE,{type:"secondary",style:{fontSize:12},children:e})},t))})]})]})},t))})]}),0===R.invalidWorkstations&&s.jsx(e9.Z,{message:"数据一致性验证通过",description:"所有工位的数据都与生产工单保持一致，无需处理。",type:"success",showIcon:!0})]})})]})};var tN=r(9286),t_=r(69109),tz=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,s=Object.getOwnPropertySymbols(e);n<s.length;n++)0>t.indexOf(s[n])&&Object.prototype.propertyIsEnumerable.call(e,s[n])&&(r[s[n]]=e[s[n]]);return r};let{TimePicker:tW,RangePicker:tA}=O.default,tR=n.forwardRef((e,t)=>n.createElement(tA,Object.assign({},e,{picker:"time",mode:void 0,ref:t}))),tP=n.forwardRef((e,t)=>{var{addon:r,renderExtraFooter:s,variant:a,bordered:i}=e,o=tz(e,["addon","renderExtraFooter","variant","bordered"]);let[l]=(0,t_.Z)("timePicker",a,i),c=n.useMemo(()=>s||r||void 0,[r,s]);return n.createElement(tW,Object.assign({},o,{mode:void 0,ref:t,renderExtraFooter:c,variant:l}))}),tB=(0,tN.Z)(tP,"popupAlign",void 0,"picker");tP._InternalPanelDoNotUseOrYouWillBeFired=tB,tP.RangePicker=tR,tP._InternalPanelDoNotUseOrYouWillBeFired=tB;var tL=r(13113),tH=r(14921);let tV={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"};var tq=n.forwardRef(function(e,t){return n.createElement(k.Z,(0,w.Z)({},e,{ref:t,icon:tV}))}),tF=r(43471);let{Title:tQ,Text:tU}=er.default,tY=({loading:e=!1})=>{let{message:t,modal:r}=i.Z.useApp(),[a]=es.Z.useForm(),[o,h]=(0,n.useState)([]),[g,x]=(0,n.useState)(null),[f,y]=(0,n.useState)(!1),j=(0,n.useCallback)(async()=>{y(!0);try{let e=await A.dataAccessManager.workTime.getConfigurations();if("success"===e.status&&e.data){if(h(e.data),!g&&e.data.length>0){let t=e.data.find(e=>e.isDefault)||e.data[0];x(t)}}else t.error(e.message||"获取工作时间配置失败")}catch(e){t.error("获取工作时间配置失败")}finally{y(!1)}},[g,t]);(0,n.useEffect)(()=>{j()},[j]),(0,n.useEffect)(()=>{if(g){let e={...(g.workTimeSlots||[]).reduce((e,t)=>({...e,[`work_start_${t.id}`]:z()(t.startTime,"HH:mm"),[`work_end_${t.id}`]:z()(t.endTime,"HH:mm"),[`work_active_${t.id}`]:t.isActive}),{})};a.setFieldsValue(e)}},[g,a]);let w=async e=>{if(g)try{let r=(g.workTimeSlots||[]).map(t=>({...t,startTime:e[`work_start_${t.id}`]?.format("HH:mm")||t.startTime,endTime:e[`work_end_${t.id}`]?.format("HH:mm")||t.endTime,isActive:e[`work_active_${t.id}`]??t.isActive})),s=await A.dataAccessManager.workTime.calculateWorkingMinutes(r),n={...g,workTimeSlots:r,breakTimeSlots:[],...s,updatedAt:new Date().toISOString()},a=await A.dataAccessManager.workTime.update(g.id,{workTimeSlots:r,breakTimeSlots:[],...s});"success"===a.status?(x(n),tF.dataChangeNotifier.notifyDataChange({type:"work_time_configuration",action:"update",data:n,affectedIds:[g.id]}),t.success("工作时间配置已保存")):t.error(a.message||"保存工作时间配置失败")}catch(e){t.error("保存工作时间配置失败")}},v=async()=>{if(!g)return;let e=g.workTimeSlots||[],r="工作时间段",s="09:00",n="17:00",a="";0===e.length?(r="上午工作时间段",s="06:30",n="11:00",a="上午工作时间"):1===e.length?(r="下午工作时间段",s="11:30",n="17:00",a="下午工作时间"):(r=`工作时间段${e.length+1}`,a=`第${e.length+1}个工作时间段`);let i=[...e,{id:`work_slot_${Date.now()}`,name:r,startTime:s,endTime:n,isActive:!0,description:a}],o=await A.dataAccessManager.workTime.calculateWorkingMinutes(i);await (0,tZ.Ro)(()=>A.dataAccessManager.workTime.update(g.id,{workTimeSlots:i,...o}),"添加工作时间段")&&(x({...g,workTimeSlots:i,...o}),t.success(`已添加${r}`))},k=async e=>{if(!g)return;let r=g.workTimeSlots.filter(t=>t.id!==e),s=await A.dataAccessManager.workTime.calculateWorkingMinutes(r);await (0,tZ.Ro)(()=>A.dataAccessManager.workTime.update(g.id,{workTimeSlots:r,...s}),"删除工作时间段")&&(x({...g,workTimeSlots:r,...s}),t.success("工作时间段已删除"))};return(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[s.jsx("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:(0,s.jsxs)("div",{children:[(0,s.jsxs)(tQ,{level:4,style:{marginBottom:"8px"},children:[s.jsx(T.Z,{style:{marginRight:"8px"}}),"工作时间配置"]}),s.jsx(tU,{type:"secondary",children:"配置生产工位的工作时间安排"})]})}),g&&s.jsx(e9.Z,{description:(0,s.jsxs)("div",{style:{fontSize:"14px",color:"#666"},children:[g.workTimeSlots&&g.workTimeSlots.length>0&&(0,s.jsxs)("span",{children:["工作时间: ",g.workTimeSlots.filter(e=>e.isActive).map(e=>`${e.startTime}-${e.endTime}`).join(", ")]}),g.effectiveWorkingMinutes&&(0,s.jsxs)("span",{style:{marginLeft:"16px"},children:["总工作时间: ",Math.floor(g.effectiveWorkingMinutes/60),"小时",g.effectiveWorkingMinutes%60,"分钟"]})]}),type:"info",showIcon:!0,icon:s.jsx(tH.Z,{})}),s.jsx(l.Z,{children:s.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:g&&s.jsx(es.Z,{form:a,layout:"vertical",onFinish:w,initialValues:{...(g?.workTimeSlots||[]).reduce((e,t)=>({...e,[`work_start_${t.id}`]:z()(t.startTime,"HH:mm"),[`work_end_${t.id}`]:z()(t.endTime,"HH:mm"),[`work_active_${t.id}`]:t.isActive}),{})},children:s.jsx(l.Z,{size:"small",title:"工作时间配置",children:(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"},children:[(0,s.jsxs)("div",{children:[s.jsx(tQ,{level:5,children:"工作时间段（上午/下午）"}),s.jsx(tU,{type:"secondary",style:{fontSize:"12px"},children:"建议设置上午和下午两个工作时间段"})]}),s.jsx(m.ZP,{type:"dashed",size:"small",icon:s.jsx(ts.Z,{}),onClick:v,children:"添加时间段"})]}),(g.workTimeSlots||[]).map(e=>s.jsx(l.Z,{size:"small",style:{marginBottom:"8px"},children:(0,s.jsxs)(c.Z,{gutter:16,align:"middle",children:[s.jsx(d.Z,{span:4,children:s.jsx(es.Z.Item,{label:"开始时间",name:`work_start_${e.id}`,rules:[{required:!0,message:"请选择开始时间"},{validator:(t,r)=>{if(!r)return Promise.resolve();let s=a.getFieldValue(`work_end_${e.id}`);if(s&&r.isAfter(s))return Promise.reject(Error("开始时间不能晚于结束时间"));let n=r.hour();return n<6||n>23?Promise.reject(Error("工作时间应在6:00-23:59之间")):Promise.resolve()}}],children:s.jsx(tP,{format:"HH:mm",placeholder:"开始时间",size:"small",onChange:()=>{a.validateFields([`work_end_${e.id}`])}})})}),s.jsx(d.Z,{span:4,children:s.jsx(es.Z.Item,{label:"结束时间",name:`work_end_${e.id}`,rules:[{required:!0,message:"请选择结束时间"},{validator:(t,r)=>{if(!r)return Promise.resolve();let s=a.getFieldValue(`work_start_${e.id}`);if(s&&r.isBefore(s))return Promise.reject(Error("结束时间不能早于开始时间"));let n=r.hour();return n<6||n>23?Promise.reject(Error("工作时间应在6:00-23:59之间")):s&&30>r.diff(s,"minutes")?Promise.reject(Error("工作时长不能少于30分钟")):Promise.resolve()}}],children:s.jsx(tP,{format:"HH:mm",placeholder:"结束时间",size:"small",onChange:()=>{a.validateFields([`work_start_${e.id}`])}})})}),s.jsx(d.Z,{span:3,children:s.jsx(es.Z.Item,{label:"启用",name:`work_active_${e.id}`,valuePropName:"checked",children:s.jsx(e5,{size:"small"})})}),s.jsx(d.Z,{span:8,children:(0,s.jsxs)(u.Z,{children:[s.jsx(tU,{type:"secondary",children:e.name}),s.jsx(ek,{status:e.isActive?"configured":"inactive",showIcon:!0})]})}),s.jsx(d.Z,{span:5,children:s.jsx(u.Z,{children:s.jsx(I.Z,{title:"删除时间段",children:s.jsx(e7.Z,{title:"确定删除此工作时间段吗？",onConfirm:()=>k(e.id),okText:"确定",cancelText:"取消",children:s.jsx(m.ZP,{type:"text",danger:!0,size:"small",icon:s.jsx(tt.Z,{})})})})})})]})},e.id))]}),s.jsx(tL.Z,{}),(0,s.jsxs)(c.Z,{gutter:16,children:[s.jsx(d.Z,{span:12,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"总工作时间",value:Math.round((g?.totalWorkingMinutes||0)/60*10)/10,suffix:"小时",precision:1,valueStyle:{color:"#1890ff"}})})}),s.jsx(d.Z,{span:12,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"工作时间段数量",value:g?.workTimeSlots?.filter(e=>e.isActive).length||0,suffix:"个",valueStyle:{color:"#52c41a"}})})})]}),s.jsx("div",{style:{textAlign:"center"},children:(0,s.jsxs)(u.Z,{children:[s.jsx(m.ZP,{type:"primary",htmlType:"submit",icon:s.jsx(tq,{}),loading:e,children:"保存配置"}),s.jsx(m.ZP,{onClick:()=>{if(!g){t.warning("没有可重置的配置");return}let e={...(g.workTimeSlots||[]).reduce((e,t)=>({...e,[`work_start_${t.id}`]:z()(t.startTime,"HH:mm"),[`work_end_${t.id}`]:z()(t.endTime,"HH:mm"),[`work_active_${t.id}`]:t.isActive}),{})};a.setFieldsValue(e),t.success("表单已重置到当前配置")},icon:s.jsx(Z.Z,{}),children:"重置"})]})})]})})})})})]})};var tX=r(87049);let tG={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M456 231a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0zm0 280a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"more",theme:"outlined"};var tK=n.forwardRef(function(e,t){return n.createElement(k.Z,(0,w.Z)({},e,{ref:t,icon:tG}))});let tJ={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"};var t0=n.forwardRef(function(e,t){return n.createElement(k.Z,(0,w.Z)({},e,{ref:t,icon:tJ}))}),t1=r(89645);let t2=a().memo(({productCode:e,format:t="simple",style:r,className:a,loadingText:i="加载中...",emptyText:l="暂无重量",unit:c="g"})=>{let[d,u]=(0,n.useState)(null),[h,m]=(0,n.useState)(!1),[g,p]=(0,n.useState)(null);return(0,n.useEffect)(()=>{let t=!1;return(async()=>{if(!e){u(null);return}m(!0),p(null);try{let r=await (0,tZ.Ro)(()=>A.dataAccessManager.products.getByCode(e),"获取产品重量");if(t)return;if("success"===r.status&&r.data){let t=function(e,t={}){let{min:r=0,max:s=1e6,allowDecimal:n=!0,maxDecimals:a=3}=t,i=[];if(null==e||""===e)return{isValid:!0,errors:[],sanitizedValue:null};let o=Number(e);return isNaN(o)?(i.push("产品重量必须是有效数字"),{isValid:!1,errors:i}):(o<0&&i.push("产品重量不能为负数"),o<r&&i.push(`产品重量不能小于${r}`),o>s&&i.push(`产品重量不能大于${s}`),n||o%1==0?n&&a>0&&(o.toString().split(".")[1]||"").length>a&&i.push(`产品重量小数位数不能超过${a}位`):i.push("产品重量必须是整数"),{isValid:0===i.length,errors:i,sanitizedValue:0===i.length?o:null})}(r.data.productWeight,{min:0,max:1e6,allowDecimal:!0,maxDecimals:3});t.isValid&&null!==t.sanitizedValue?u(t.sanitizedValue):(u(null),t.errors.length>0&&(console.warn(`产品重量验证失败 (${e}):`,t.errors),p("重量数据异常")))}else u(null)}catch(r){if(t)return;console.error(`获取产品重量失败 (${e}):`,r),u(null),p("获取重量失败")}finally{t||m(!1)}})(),()=>{t=!0}},[e]),h?(0,s.jsxs)("span",{className:a,style:r,children:[s.jsx(o.Z,{size:"small"})," ",i]}):g?s.jsx("span",{className:a,style:{color:"#ff4d4f",...r},title:g,children:l}):null===d?s.jsx("span",{className:a,style:{color:"#999",...r},children:l}):s.jsx("span",{className:a,style:r,title:`产品编码: ${e}, 重量: ${d} ${c}`,children:"detailed"===t?`重量: ${d.toLocaleString()} ${c}`:`${d.toLocaleString()} ${c}`})});t2.displayName="ProductWeightDisplay";let t4=a().memo(({productCode:e,style:t,className:r})=>s.jsx(t2,{productCode:e,format:"simple",style:t,className:r,emptyText:"-",loadingText:"..."}));t4.displayName="SimpleProductWeight",a().memo(({productCode:e,style:t,className:r})=>s.jsx(t2,{productCode:e,format:"detailed",style:t,className:r})).displayName="DetailedProductWeight",a().memo(({productCodes:e,direction:t="vertical",separator:r=", ",maxDisplay:n=5})=>{let i=e.slice(0,n),o=e.length>n;return"horizontal"===t?(0,s.jsxs)("span",{children:[i.map((e,t)=>(0,s.jsxs)(a().Fragment,{children:[t>0&&r,s.jsx(t4,{productCode:e})]},e)),o&&(0,s.jsxs)("span",{style:{color:"#999"},children:[r,"等",e.length,"个产品"]})]}):(0,s.jsxs)("div",{children:[i.map(e=>s.jsx("div",{style:{marginBottom:4},children:s.jsx(t4,{productCode:e})},e)),o&&(0,s.jsxs)("div",{style:{color:"#999",fontSize:"12px"},children:["等",e.length,"个产品..."]})]})}).displayName="BatchProductWeightDisplay";let{Search:t6}=eN.default,{Option:t8}=E.default,{RangePicker:t3}=O.default,{Text:t5}=er.default,t7=({workOrders:e=[],loading:t=!1,onRefresh:r,onWorkOrderDetail:a,onStatusChange:o,onEdit:x,onDelete:y,onExport:j,onStartScheduling:w})=>{let{modal:v}=i.Z.useApp(),[k,S]=(0,n.useState)(""),[b,C]=(0,n.useState)(""),[T,D]=(0,n.useState)(""),[M,$]=(0,n.useState)(""),[I,O]=(0,n.useState)(null),[N,_]=(0,n.useState)([]),W=(0,n.useCallback)(e=>{S(H(e.target.value,100))},[]),A=(0,n.useCallback)(e=>{$(function(e){return e?B(e.replace(/[^A-Za-z0-9\-]/g,"")):""}(e.target.value))},[]),R=(0,n.useMemo)(()=>{let t=[...e];if(k){let e=k.toLowerCase();t=t.filter(t=>t.batchNumber.toLowerCase().includes(e)||t.productName.toLowerCase().includes(e)||t.productCode.toLowerCase().includes(e)||t.customerName.toLowerCase().includes(e))}if(b&&(t=t.filter(e=>e.status===b)),T&&(t=t.filter(e=>e.customerCreditLevel===T)),M&&(t=t.filter(e=>e.workstation.includes(M))),I){let[e,r]=I;t=t.filter(t=>{let s=z()(t.plannedStartTime);return s.isAfter(e.startOf("day"))&&s.isBefore(r.endOf("day"))})}return t},[e,k,b,T,M,I]),P=(0,n.useMemo)(()=>({total:R.length,inProgress:R.filter(e=>"in_progress"===e.status).length,completed:R.filter(e=>"completed"===e.status).length,exception:R.filter(e=>"exception"===e.status||e.exceptionCount>0).length}),[R]),V=e=>({items:[{key:"detail",icon:s.jsx(eo.Z,{}),label:"查看详情",onClick:()=>a?.(e)},{key:"edit",icon:s.jsx(te.Z,{}),label:"编辑",onClick:()=>x?.(e)},{type:"divider"},{key:"delete",icon:s.jsx(tt.Z,{}),label:"删除",danger:!0,onClick:()=>{v.confirm({title:"确认删除",content:`确定要删除工单 ${e.batchNumber} 吗？`,onOk:()=>y?.(e.id)})}}]}),q=[{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>s.jsx(ew,{status:e}),filters:eS("work_order"),onFilter:(e,t)=>t.status===e},{title:"生产工单ID",dataIndex:"id",key:"id",width:160,render:e=>s.jsx(t5,{code:!0,style:{fontSize:"12px"},children:e})},{title:"批次号",dataIndex:"batchNumber",key:"batchNumber",width:120,render:(e,t)=>s.jsx(m.ZP,{type:"link",onClick:()=>a?.(t),style:{padding:0,height:"auto"},children:e})},{title:"产品名称",dataIndex:"productName",key:"productName",width:200,render:(e,t)=>t.isSharedMold&&t.productItems&&t.productItems.length>0?s.jsx("div",{style:{lineHeight:"1.4"},children:t.productItems.map((e,r)=>s.jsx("div",{style:{marginBottom:r<t.productItems.length-1?"2px":"0"},children:s.jsx(t5,{style:{fontSize:"13px",display:"block",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"180px"},title:L(e.productName),children:L(e.productName)})},r))}):s.jsx(t5,{ellipsis:{tooltip:L(e)},children:L(e)})},{title:"产品编码",dataIndex:"productCode",key:"productCode",width:120,render:(e,t)=>t.isSharedMold&&t.productItems&&t.productItems.length>0?s.jsx("div",{style:{lineHeight:"1.4"},children:t.productItems.map((e,r)=>s.jsx("div",{style:{marginBottom:r<t.productItems.length-1?"2px":"0"},children:s.jsx(t5,{style:{fontSize:"13px"},children:L(e.productCode)})},r))}):s.jsx(t5,{children:L(e)})},{title:"产品重量(g)",dataIndex:"productCode",key:"weight",width:120,align:"right",render:(e,t)=>t.isSharedMold&&t.productItems&&t.productItems.length>0?s.jsx("div",{style:{lineHeight:"1.4"},children:t.productItems.map((e,r)=>s.jsx("div",{style:{marginBottom:r<t.productItems.length-1?"2px":"0"},children:s.jsx(t5,{style:{fontSize:"13px"},children:s.jsx(t4,{productCode:e.productCode})})},r))}):s.jsx(t4,{productCode:e}),sorter:!1},{title:"信用等级",dataIndex:"customerCreditLevel",key:"customerCreditLevel",width:100,render:e=>e?s.jsx(ev,{level:e}):s.jsx(h.Z,{color:"default",children:"未设置"}),filters:[{text:"A级",value:"A"},{text:"B级",value:"B"},{text:"C级",value:"C"},{text:"D级",value:"D"},{text:"E级",value:"E"}],onFilter:(e,t)=>t.customerCreditLevel===e},{title:"计划模数",dataIndex:"plannedMoldCount",key:"plannedMoldCount",width:100,align:"right",sorter:(e,t)=>e.plannedMoldCount-t.plannedMoldCount},{title:"工位",dataIndex:"workstation",key:"workstation",width:100},{title:"计划开始",dataIndex:"plannedStartTime",key:"plannedStartTime",width:140,render:e=>e?z()(e).format("YYYY-MM-DD HH:mm"):s.jsx("span",{style:{color:"#999"},children:"未设置"}),sorter:(e,t)=>e.plannedStartTime||t.plannedStartTime?e.plannedStartTime?t.plannedStartTime?z()(e.plannedStartTime).unix()-z()(t.plannedStartTime).unix():-1:1:0},{title:"计划结束",dataIndex:"plannedEndTime",key:"plannedEndTime",width:140,render:e=>e?z()(e).format("YYYY-MM-DD HH:mm"):s.jsx("span",{style:{color:"#999"},children:"未设置"}),sorter:(e,t)=>e.plannedEndTime||t.plannedEndTime?e.plannedEndTime?t.plannedEndTime?z()(e.plannedEndTime).unix()-z()(t.plannedEndTime).unix():-1:1:0},{title:"交货日期",dataIndex:"deliveryDate",key:"deliveryDate",width:110,render:e=>z()(e).format("YYYY-MM-DD"),sorter:(e,t)=>z()(e.deliveryDate).unix()-z()(t.deliveryDate).unix()},{title:"小时产能",dataIndex:"hourlyCapacity",key:"hourlyCapacity",width:120,align:"right",render:e=>`${e}模/小时`,sorter:(e,t)=>e.hourlyCapacity-t.hourlyCapacity},{title:"完成模数",dataIndex:"completedMoldCount",key:"completedMoldCount",width:100,align:"right",sorter:(e,t)=>e.completedMoldCount-t.completedMoldCount},{title:"执行比(%)",dataIndex:"executionRate",key:"executionRate",width:120,render:e=>{let t="normal";return e>=100?t="success":e<50&&(t="exception"),s.jsx(tX.Z,{percent:e,size:"small",status:t,format:e=>`${e?.toFixed(1)}%`})},sorter:(e,t)=>e.executionRate-t.executionRate},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:140,render:e=>z()(e).format("YYYY-MM-DD HH:mm"),sorter:(e,t)=>z()(e.createdAt).unix()-z()(t.createdAt).unix(),defaultSortOrder:"descend"},{title:"成型模具编号",dataIndex:"formingMoldNumber",key:"formingMoldNumber",width:140,render:e=>e||"-"},{title:"异常数量",dataIndex:"exceptionCount",key:"exceptionCount",width:100,align:"right",render:e=>e>0?s.jsx("span",{style:{color:"#ff4d4f",fontWeight:"bold"},children:e}):s.jsx("span",{children:e}),sorter:(e,t)=>e.exceptionCount-t.exceptionCount},{title:"操作",key:"action",width:80,fixed:"right",render:(e,t)=>s.jsx(g.Z,{menu:V(t),trigger:["click"],children:s.jsx(m.ZP,{type:"text",icon:s.jsx(tK,{})})})}],F={selectedRowKeys:N,onChange:_,getCheckboxProps:e=>({disabled:"pending"!==e.status,name:e.batchNumber}),selections:[{key:"select-pending",text:"选择待开始工单",onSelect:e=>{_(e.filter(e=>{let t=R.find(t=>t.id===e);return t?.status==="pending"}))}},ei.Z.SELECTION_NONE]};return(0,s.jsxs)("div",{className:"production-work-orders-list",children:[s.jsx(l.Z,{size:"small",style:{marginBottom:"16px"},children:(0,s.jsxs)(c.Z,{gutter:16,align:"middle",children:[s.jsx(d.Z,{flex:"auto",children:(0,s.jsxs)(u.Z,{wrap:!0,children:[s.jsx(t6,{placeholder:"搜索批次号、产品名称、产品编码、客户名称",value:k,onChange:W,style:{width:300},allowClear:!0}),(0,s.jsxs)(E.default,{placeholder:"状态",value:b,onChange:C,style:{width:120},allowClear:!0,children:[s.jsx(t8,{value:"pending",children:"待开始"}),s.jsx(t8,{value:"in_progress",children:"进行中"}),s.jsx(t8,{value:"completed",children:"已完成"}),s.jsx(t8,{value:"paused",children:"暂停"}),s.jsx(t8,{value:"cancelled",children:"已取消"}),s.jsx(t8,{value:"exception",children:"异常"})]}),(0,s.jsxs)(E.default,{placeholder:"信用等级",value:T,onChange:D,style:{width:100},allowClear:!0,children:[s.jsx(t8,{value:"A",children:"A级"}),s.jsx(t8,{value:"B",children:"B级"}),s.jsx(t8,{value:"C",children:"C级"}),s.jsx(t8,{value:"D",children:"D级"}),s.jsx(t8,{value:"E",children:"E级"})]}),s.jsx(eN.default,{placeholder:"工位",value:M,onChange:A,style:{width:120},allowClear:!0}),s.jsx(t3,{value:I,onChange:e=>O(e),placeholder:["开始日期","结束日期"]}),s.jsx(m.ZP,{icon:s.jsx(t0,{}),onClick:()=>{S(""),C(""),D(""),$(""),O(null)},children:"清空筛选"})]})}),s.jsx(d.Z,{children:(0,s.jsxs)(u.Z,{children:[(0,s.jsxs)(m.ZP,{type:"primary",onClick:()=>w?.(N),disabled:0===N.length,children:["开始排单 ",N.length>0&&`(${N.length})`]}),s.jsx(m.ZP,{icon:s.jsx(t1.Z,{}),onClick:j,children:"导出"}),s.jsx(m.ZP,{icon:s.jsx(Z.Z,{}),onClick:r,loading:t,children:"刷新"})]})})]})}),(0,s.jsxs)(c.Z,{gutter:16,style:{marginBottom:"16px"},children:[s.jsx(d.Z,{span:6,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"总工单数",value:P.total,prefix:s.jsx(f.Z,{status:"default"})})})}),s.jsx(d.Z,{span:6,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"进行中",value:P.inProgress,prefix:s.jsx(f.Z,{status:"processing"}),valueStyle:{color:"#1890ff"}})})}),s.jsx(d.Z,{span:6,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"已完成",value:P.completed,prefix:s.jsx(f.Z,{status:"success"}),valueStyle:{color:"#52c41a"}})})}),s.jsx(d.Z,{span:6,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"异常工单",value:P.exception,prefix:s.jsx(f.Z,{status:"error"}),valueStyle:{color:"#ff4d4f"}})})})]}),s.jsx(l.Z,{children:s.jsx(ei.Z,{columns:q,dataSource:R,rowKey:"id",loading:t,rowSelection:F,scroll:{x:1900},pagination:{total:R.length,pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条，共 ${e} 条`},size:"small"})})]})};var t9=r(16407),re=r(95905),rt=r(37372);let{Title:rr,Text:rs,Paragraph:rn}=er.default,ra=({open:e,onCancel:t,onConfirm:r,selectedWorkOrders:i,loading:o=!1})=>{let[h]=es.Z.useForm(),[m,g]=(0,n.useState)("same_mold_priority"),x=a().useMemo(()=>{let e=i.length;return{totalWorkOrders:e,totalMoldCount:i.reduce((e,t)=>e+t.plannedMoldCount,0),uniqueMolds:new Set(i.map(e=>e.formingMoldNumber)).size,avgHourlyCapacity:i.length>0?Math.round(i.reduce((e,t)=>e+t.hourlyCapacity,0)/i.length):0}},[i]),f=async()=>{try{let e=await h.validateFields(),t={mode:m,changeoverTime:20,allowOvertime:e.allowOvertime||!1,maxOvertimeHours:e.maxOvertimeHours||4,sameMoldDeliveryToleranceDays:e.sameMoldDeliveryToleranceDays||30};r(t)}catch(e){t9.ZP.error("请完善排程配置信息")}},y=(e=>{switch(e){case"same_mold_priority":return{title:"相同模具优先",description:"优先将工单分配到已装载相同模具的工位，减少换模时间，提高生产效率",advantages:["减少换模次数，节省换模时间","提高工位利用率","降低生产成本","适合批量生产场景"],icon:s.jsx(M.Z,{style:{color:"#1890ff"}})};case"delivery_priority":return{title:"交期优先",description:"严格按照交货期排序，优先安排紧急订单，确保按时交付",advantages:["确保交货期准时性","提高客户满意度","降低延期风险","适合多品种小批量场景"],icon:s.jsx($.Z,{style:{color:"#52c41a"}})};default:return{title:"",description:"",advantages:[],icon:null}}})(m);return s.jsx(en.Z,{title:(0,s.jsxs)(u.Z,{children:[s.jsx(T.Z,{}),s.jsx("span",{children:"生产排程配置"})]}),open:e,onCancel:t,onOk:f,confirmLoading:o,width:800,okText:"开始排程",cancelText:"取消",destroyOnHidden:!0,children:(0,s.jsxs)(es.Z,{form:h,layout:"vertical",initialValues:{mode:"same_mold_priority",allowOvertime:!1,maxOvertimeHours:4,sameMoldDeliveryToleranceDays:30},children:[(0,s.jsxs)(l.Z,{size:"small",style:{marginBottom:16},children:[s.jsx(rr,{level:5,style:{margin:0,marginBottom:12},children:"待排程工单统计"}),(0,s.jsxs)(c.Z,{gutter:16,children:[s.jsx(d.Z,{span:6,children:s.jsx(p.Z,{title:"工单数量",value:x.totalWorkOrders,suffix:"个",valueStyle:{color:"#1890ff"}})}),s.jsx(d.Z,{span:6,children:s.jsx(p.Z,{title:"总计划模数",value:x.totalMoldCount,suffix:"模",valueStyle:{color:"#52c41a"}})}),s.jsx(d.Z,{span:6,children:s.jsx(p.Z,{title:"涉及模具",value:x.uniqueMolds,suffix:"套",valueStyle:{color:"#faad14"}})}),s.jsx(d.Z,{span:6,children:s.jsx(p.Z,{title:"平均产能",value:x.avgHourlyCapacity,suffix:"模/时",valueStyle:{color:"#722ed1"}})})]})]}),s.jsx(es.Z.Item,{name:"mode",label:(0,s.jsxs)(u.Z,{children:[s.jsx(M.Z,{}),s.jsx("span",{children:"排程模式"})]}),rules:[{required:!0,message:"请选择排程模式"}],children:s.jsx(re.ZP.Group,{value:m,onChange:e=>g(e.target.value),style:{width:"100%"},children:(0,s.jsxs)(u.Z,{direction:"vertical",style:{width:"100%"},children:[s.jsx(re.ZP,{value:"same_mold_priority",children:s.jsx(l.Z,{size:"small",hoverable:!0,style:{width:"100%",border:"same_mold_priority"===m?"2px solid #1890ff":"1px solid #d9d9d9"},children:(0,s.jsxs)(u.Z,{children:[s.jsx(M.Z,{style:{color:"#1890ff",fontSize:20}}),(0,s.jsxs)("div",{children:[s.jsx(rs,{strong:!0,children:"相同模具优先"}),s.jsx("br",{}),s.jsx(rs,{type:"secondary",style:{fontSize:12},children:"优先分配到相同模具工位，减少换模时间"})]})]})})}),s.jsx(re.ZP,{value:"delivery_priority",children:s.jsx(l.Z,{size:"small",hoverable:!0,style:{width:"100%",border:"delivery_priority"===m?"2px solid #1890ff":"1px solid #d9d9d9"},children:(0,s.jsxs)(u.Z,{children:[s.jsx($.Z,{style:{color:"#52c41a",fontSize:20}}),(0,s.jsxs)("div",{children:[s.jsx(rs,{strong:!0,children:"交期优先"}),s.jsx("br",{}),s.jsx(rs,{type:"secondary",style:{fontSize:12},children:"严格按照交货期排序，确保按时交付"})]})]})})})]})})}),(0,s.jsxs)(l.Z,{size:"small",style:{marginBottom:16},children:[(0,s.jsxs)(u.Z,{children:[y.icon,s.jsx(rr,{level:5,style:{margin:0},children:y.title})]}),s.jsx(rn,{style:{marginTop:8,marginBottom:12},children:y.description}),s.jsx(rs,{strong:!0,children:"主要优势："}),s.jsx("ul",{style:{marginTop:4,marginBottom:0},children:y.advantages.map((e,t)=>s.jsx("li",{children:s.jsx(rs,{children:e})},t))})]}),s.jsx(l.Z,{size:"small",title:"排程参数",children:(0,s.jsxs)(c.Z,{gutter:16,children:[s.jsx(d.Z,{span:8,children:s.jsx(es.Z.Item,{label:"换模时间",children:s.jsx(rs,{children:"20 分钟（固定）"})})}),s.jsx(d.Z,{span:8,children:s.jsx(es.Z.Item,{label:"工作时间配置",children:s.jsx(rs,{children:"使用系统默认配置"})})}),s.jsx(d.Z,{span:8,children:s.jsx(es.Z.Item,{label:"相同模具交货期容忍天数",name:"sameMoldDeliveryToleranceDays",tooltip:"交货期相差在此天数内的相同模具工单将被分配到同一工位，减少换模次数",rules:[{required:!0,message:"请输入容忍天数"},{type:"number",min:1,max:90,message:"容忍天数必须在1-90天之间"}],children:s.jsx(ea.Z,{min:1,max:90,suffix:"天",placeholder:"请输入天数",style:{width:"100%"}})})})]})}),s.jsx(e9.Z,{message:"排程注意事项",description:(0,s.jsxs)("ul",{style:{margin:0,paddingLeft:16},children:[s.jsx("li",{children:"排程将自动分配工位和时间，请确认工单信息准确"}),s.jsx("li",{children:'排程完成后，工单状态将变更为"已排程"'}),s.jsx("li",{children:"如有冲突或风险，系统将提供详细的警告信息"}),s.jsx("li",{children:"排程结果可在排程完成后进行调整"})]}),type:"info",showIcon:!0,icon:s.jsx(rt.Z,{}),style:{marginTop:16}})]})})},{Title:ri,Text:ro}=er.default,{TabPane:rl}=x.default,rc=({open:e,onCancel:t,onConfirm:r,result:a,loading:i=!1})=>{let[o,m]=(0,n.useState)("results");if(!a)return null;let g=[{title:"工单ID",dataIndex:"workOrderId",key:"workOrderId",width:120,render:e=>s.jsx(ro,{code:!0,children:e.slice(-8)})},{title:"分配工位",dataIndex:"workstation",key:"workstation",width:150,render:(e,t)=>(0,s.jsxs)(u.Z,{children:[s.jsx(h.Z,{color:t.isSameMold?"green":"blue",children:t.workstationCode}),s.jsx(ro,{children:e})]})},{title:"模具匹配",dataIndex:"isSameMold",key:"isSameMold",width:100,render:e=>s.jsx(h.Z,{color:e?"green":"orange",icon:s.jsx(M.Z,{}),children:e?"相同模具":"换模"})},{title:"预计开始时间",dataIndex:"plannedStartTime",key:"plannedStartTime",width:150,render:e=>(0,s.jsxs)(u.Z,{direction:"vertical",size:0,children:[s.jsx(ro,{children:new Date(e).toLocaleDateString()}),s.jsx(ro,{type:"secondary",style:{fontSize:12},children:new Date(e).toLocaleTimeString()})]})},{title:"预计结束时间",dataIndex:"plannedEndTime",key:"plannedEndTime",width:150,render:e=>(0,s.jsxs)(u.Z,{direction:"vertical",size:0,children:[s.jsx(ro,{children:new Date(e).toLocaleDateString()}),s.jsx(ro,{type:"secondary",style:{fontSize:12},children:new Date(e).toLocaleTimeString()})]})},{title:"生产时长",dataIndex:"productionTimeMinutes",key:"productionTimeMinutes",width:100,render:e=>(0,s.jsxs)(ro,{children:[Math.round(e/60*10)/10," 小时"]})},{title:"换模时间",dataIndex:"changeoverTimeMinutes",key:"changeoverTimeMinutes",width:100,render:e=>(0,s.jsxs)(ro,{type:e>0?"warning":"secondary",children:[e," 分钟"]})}],y=[{title:"工单ID",dataIndex:"workOrderId",key:"workOrderId",width:120,render:e=>s.jsx(ro,{code:!0,children:e.slice(-8)})},{title:"风险等级",dataIndex:"riskLevel",key:"riskLevel",width:100,render:e=>{let t={normal:{text:"正常",color:"green",icon:s.jsx(ec.Z,{})},medium:{text:"中风险",color:"orange",icon:s.jsx(rt.Z,{})},high:{text:"高风险",color:"red",icon:s.jsx(ep.Z,{})}}[e]||{text:e,color:"default",icon:null};return s.jsx(h.Z,{color:t.color,icon:t.icon,children:t.text})}},{title:"延误/提前",key:"timeDiff",width:120,render:(e,t)=>t.delayDays?(0,s.jsxs)(ro,{type:"danger",children:["延误 ",t.delayDays," 天"]}):t.advanceDays?(0,s.jsxs)(ro,{type:"success",children:["提前 ",t.advanceDays," 天"]}):s.jsx(ro,{children:"按时"})},{title:"预计完成日期",dataIndex:"expectedCompletionDate",key:"expectedCompletionDate",width:120,render:e=>new Date(e).toLocaleDateString()},{title:"建议",dataIndex:"suggestion",key:"suggestion",ellipsis:!0,render:e=>e||"-"}],j={normal:a.riskAssessments.filter(e=>"normal"===e.riskLevel).length,medium:a.riskAssessments.filter(e=>"medium"===e.riskLevel).length,high:a.riskAssessments.filter(e=>"high"===e.riskLevel).length};return(0,s.jsxs)(en.Z,{title:(0,s.jsxs)(u.Z,{children:[s.jsx(T.Z,{style:{color:"#1890ff"}}),s.jsx("span",{children:"排程结果"}),s.jsx(h.Z,{color:"blue",icon:s.jsx(T.Z,{}),children:"预览模式"})]}),open:e,onCancel:t,onOk:r,confirmLoading:i,width:1200,okText:"确认应用排程结果",cancelText:"取消",style:{top:20},children:[s.jsx(e9.Z,{message:"\uD83D\uDCCB 排程预览模式",description:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:["当前显示的是排程计算结果预览，",s.jsx("strong",{children:"工位状态和工单状态尚未实际更新"}),"。"]}),s.jsx("p",{children:"• 工位队列任务：未添加"}),s.jsx("p",{children:'• 工单状态：仍为"待开始"'}),s.jsx("p",{children:"• 工位状态：保持原有状态"}),s.jsx("p",{style:{marginBottom:0,marginTop:8},children:s.jsx("strong",{children:'点击"确认应用排程结果"后才会正式应用所有更改。'})})]}),type:"info",showIcon:!0,style:{marginBottom:16}}),s.jsx(l.Z,{size:"small",style:{marginBottom:16},children:(0,s.jsxs)(c.Z,{gutter:16,children:[s.jsx(d.Z,{span:4,children:s.jsx(p.Z,{title:"总工单数",value:a.statistics.totalWorkOrders,suffix:"个",valueStyle:{color:"#1890ff"}})}),s.jsx(d.Z,{span:4,children:s.jsx(p.Z,{title:"成功排程",value:a.statistics.successfullyScheduled,suffix:"个",valueStyle:{color:"#52c41a"}})}),s.jsx(d.Z,{span:4,children:s.jsx(p.Z,{title:"相同模具",value:a.statistics.sameMoldAssignments,suffix:"个",valueStyle:{color:"#722ed1"}})}),s.jsx(d.Z,{span:4,children:s.jsx(p.Z,{title:"涉及工位",value:a.statistics.involvedWorkstations,suffix:"个",valueStyle:{color:"#faad14"}})}),s.jsx(d.Z,{span:4,children:s.jsx(p.Z,{title:"正常风险",value:j.normal,suffix:"个",valueStyle:{color:"#52c41a"}})}),s.jsx(d.Z,{span:4,children:s.jsx(p.Z,{title:"风险工单",value:j.medium+j.high,suffix:"个",valueStyle:{color:"#ff4d4f"}})})]})}),(0,s.jsxs)(x.default,{activeKey:o,onChange:m,children:[s.jsx(rl,{tab:(0,s.jsxs)(u.Z,{children:[s.jsx(T.Z,{}),s.jsx("span",{children:"排程结果"}),s.jsx(f.Z,{count:a.schedulingResults.length,showZero:!0})]}),children:s.jsx(ei.Z,{columns:g,dataSource:a.schedulingResults,rowKey:"workOrderId",size:"small",pagination:{pageSize:10},scroll:{y:400}})},"results"),s.jsx(rl,{tab:(0,s.jsxs)(u.Z,{children:[s.jsx(rt.Z,{}),s.jsx("span",{children:"冲突警告"}),s.jsx(f.Z,{count:a.conflicts.length,showZero:!0})]}),children:a.conflicts.length>0?s.jsx(ei.Z,{columns:[{title:"冲突类型",dataIndex:"type",key:"type",width:120,render:e=>{let t={time_overlap:{text:"时间重叠",color:"red"},workstation_conflict:{text:"工位冲突",color:"orange"},mold_conflict:{text:"模具冲突",color:"yellow"}}[e]||{text:e,color:"default"};return s.jsx(h.Z,{color:t.color,children:t.text})}},{title:"涉及工位",dataIndex:"workstation",key:"workstation",width:150},{title:"冲突工单",dataIndex:"conflictingTasks",key:"conflictingTasks",width:200,render:e=>s.jsx(u.Z,{wrap:!0,children:e.map(e=>s.jsx(h.Z,{color:"red",children:e.slice(-8)},e))})},{title:"重叠时间",dataIndex:"overlapTime",key:"overlapTime",width:100,render:e=>e?`${e} 分钟`:"-"},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0}],dataSource:a.conflicts,rowKey:(e,t)=>`conflict-${t}`,size:"small",pagination:{pageSize:10},scroll:{y:400}}):s.jsx(e9.Z,{message:"无冲突",description:"排程结果无时间冲突或工位冲突",type:"success",showIcon:!0})},"conflicts"),s.jsx(rl,{tab:(0,s.jsxs)(u.Z,{children:[s.jsx($.Z,{}),s.jsx("span",{children:"风险评估"}),s.jsx(f.Z,{count:j.medium+j.high,showZero:!0})]}),children:s.jsx(ei.Z,{columns:y,dataSource:a.riskAssessments,rowKey:"workOrderId",size:"small",pagination:{pageSize:10},scroll:{y:400}})},"risks")]}),a.conflicts.length>0&&s.jsx(e9.Z,{message:"发现排程冲突",description:"系统检测到时间冲突，建议检查冲突详情并考虑调整排程参数",type:"warning",showIcon:!0,style:{marginTop:16}}),s.jsx(e9.Z,{message:"\uD83D\uDCA1 温馨提示",description:(0,s.jsxs)("div",{children:[s.jsx("p",{children:"这是排程计算的预览结果，您可以："}),(0,s.jsxs)("ul",{style:{marginBottom:0,paddingLeft:20},children:[(0,s.jsxs)("li",{children:[s.jsx("strong",{children:"确认应用"}),"：将排程结果应用到实际系统，更新工单和工位状态"]}),(0,s.jsxs)("li",{children:[s.jsx("strong",{children:"取消"}),"：放弃此次排程，不做任何更改"]}),(0,s.jsxs)("li",{children:[s.jsx("strong",{children:"查看详情"}),"：检查排程结果、冲突和风险评估"]})]})]}),type:"success",showIcon:!0,style:{marginTop:16}})]})};var rd=r(1426),ru=r(16043);let rh=()=>{let{message:e}=i.Z.useApp(),[t,r]=(0,n.useState)({queueExists:!1,handlersRegistered:!1,messagesInQueue:0,pendingOrdersCount:0,errors:[]}),[a,o]=(0,n.useState)(!1),[c,d]=(0,n.useState)(!1),g=async()=>{o(!0);let e=[];try{throw Error("消息队列服务已删除")}catch(t){e.push(`调试信息收集失败: ${t instanceof Error?t.message:"未知错误"}`),r(t=>({...t,errors:e}))}finally{o(!1)}},p=async()=>{try{throw Error("消息队列服务已删除")}catch(t){e.error(`发送测试消息失败: ${t instanceof Error?t.message:"未知错误"}`)}},x=async()=>{try{throw Error("消息队列服务已删除")}catch(t){e.error(`清空队列失败: ${t instanceof Error?t.message:"未知错误"}`)}},f=()=>t.errors.length>0?"red":t.queueExists&&t.handlersRegistered?"green":"orange";return(0,n.useEffect)(()=>{c&&g()},[c,0]),(0,s.jsxs)(s.Fragment,{children:[s.jsx(m.ZP,{icon:s.jsx(rd.Z,{}),onClick:()=>d(!0),title:"消息队列调试器",children:"调试器"}),s.jsx(en.Z,{title:"消息队列调试器",open:c,onCancel:()=>d(!1),width:800,footer:[s.jsx(m.ZP,{icon:s.jsx(Z.Z,{}),onClick:g,loading:a,children:"刷新"},"refresh"),s.jsx(m.ZP,{icon:s.jsx(ru.Z,{}),onClick:p,type:"primary",children:"发送测试消息"},"test"),s.jsx(m.ZP,{onClick:x,danger:!0,children:"清空队列"},"clear"),s.jsx(m.ZP,{onClick:()=>d(!1),children:"关闭"},"close")],children:(0,s.jsxs)(u.Z,{direction:"vertical",style:{width:"100%"},size:"large",children:[s.jsx(e9.Z,{message:`消息队列状态: ${t.errors.length>0?"有错误":t.queueExists&&t.handlersRegistered?"正常":"配置不完整"}`,type:"green"===f()?"success":"orange"===f()?"warning":"error",showIcon:!0}),s.jsx(l.Z,{title:"系统状态",size:"small",children:(0,s.jsxs)(eD.Z,{column:2,size:"small",children:[s.jsx(eD.Z.Item,{label:"生产队列",children:s.jsx(h.Z,{color:t.queueExists?"green":"red",children:t.queueExists?"已创建":"未创建"})}),s.jsx(eD.Z.Item,{label:"消息处理器",children:s.jsx(h.Z,{color:t.handlersRegistered?"green":"red",children:t.handlersRegistered?"已注册":"未注册"})}),s.jsx(eD.Z.Item,{label:"队列中消息数",children:s.jsx(h.Z,{color:t.messagesInQueue>0?"blue":"default",children:t.messagesInQueue})}),s.jsx(eD.Z.Item,{label:"待排产订单数",children:s.jsx(h.Z,{color:t.pendingOrdersCount>0?"green":"default",children:t.pendingOrdersCount})}),t.lastMessageTime&&s.jsx(eD.Z.Item,{label:"最后消息时间",span:2,children:t.lastMessageTime})]})}),t.errors.length>0&&s.jsx(l.Z,{title:"错误信息",size:"small",children:s.jsx("ul",{style:{margin:0,paddingLeft:"20px"},children:t.errors.map((e,t)=>s.jsx("li",{style:{color:"#ff4d4f",marginBottom:"4px"},children:e},t))})}),s.jsx(l.Z,{title:"诊断建议",size:"small",children:(0,s.jsxs)("div",{style:{fontSize:"14px",lineHeight:"1.6"},children:[s.jsx("p",{children:s.jsx("strong",{children:"如果消息无法传输，请检查："})}),(0,s.jsxs)("ol",{style:{paddingLeft:"20px"},children:[s.jsx("li",{children:"确保销售订单页面和自动排单页面都已访问过（初始化消息队列）"}),s.jsx("li",{children:"检查浏览器控制台是否有错误信息"}),s.jsx("li",{children:"确认MRP执行成功并生成了待排产订单"}),s.jsx("li",{children:"检查消息处理器是否正确注册"}),s.jsx("li",{children:"尝试发送测试消息验证队列功能"})]}),s.jsx("p",{children:s.jsx("strong",{children:"预期流程："})}),s.jsx("p",{children:"MRP执行 → 发送消息到队列 → 消息处理器接收 → 添加到自动排单模块 → 在待排产订单列表中显示"})]})})]})})]})};class rm{static{this.workstationUpdateService=tT.J.getInstance()}static{this.currentSchedulingConfig={sameMoldDeliveryToleranceDays:30}}static async calculateSchedulingResults(e,t){try{try{let e=A.dataAccessManager.clearServiceCache("WorkstationService","pre_calculation"),t=A.dataAccessManager.clearDataTypeCache("workstations");console.log(`🧹 [排程计算前缓存清理] 清除工位服务缓存: ${e} 个缓存项`),console.log(`🧹 [排程计算前缓存清理] 清除工位数据缓存: ${t} 个缓存项`)}catch(e){console.warn("⚠️ [排程计算前缓存清理] 清除缓存时发生错误:",e)}t?.sameMoldDeliveryToleranceDays!==void 0&&(this.currentSchedulingConfig.sameMoldDeliveryToleranceDays=t.sameMoldDeliveryToleranceDays);let r=await this.getPendingWorkOrders(e),s=await this.getAvailableWorkstations(),n=await this.getWorkTimeConfiguration();console.log("\uD83D\uDD0D [排程调试] 获取到的工位状态:"),s.forEach(e=>{console.log(`   工位 ${e.code}: 当前模具=${e.currentMoldNumber||"无"}, 最后结束时间=${e.lastEndTime||"无"}, 当前批次=${e.currentBatchNumber||"无"}`)});let a=this.createVirtualWorkstationStates(s);return await this.performSchedulingCalculation(r,a,n)}catch(e){throw Error(`排程计算失败: ${e instanceof Error?e.message:"未知错误"}`)}}static async executeSameMoldPriorityScheduling(e){try{return await this.calculateSchedulingResults(e)}catch(e){throw Error(`排程执行失败: ${e instanceof Error?e.message:"未知错误"}`)}}static async performSchedulingCalculation(e,t,r){try{let s=[],n=[],a=[];for(let n of e){console.log(`🔍 [相同模具检测] 工单 ${n.batchNumber} 模具 ${n.formingMoldNumber}`);let e=this.findSameMoldWorkstation(n.formingMoldNumber,t);if(e){console.log(`✅ [相同模具匹配] 工单 ${n.batchNumber} 找到相同模具工位: ${e.code}`);let t=await this.assignWorkOrderToWorkstation(n,e,!0,r);s.push(t),this.updateVirtualWorkstationStatus(e,t)}else console.log(`❌ [相同模具检测] 工单 ${n.batchNumber} 未找到相同模具工位`),a.push(n)}let i=await this.processRemainingWorkOrdersWithMoldGrouping(a,t,r);n.push(...i);let o=[...s,...n],l=this.detectSchedulingConflicts(o),c=this.assessDeliveryRisks(o,e),d=this.calculateStatistics(e,o,s.length,n.length,t.map(e=>e.originalState)),u=o.reduce((e,t)=>{let r=t.workstationCode;return e[r]=(e[r]||0)+1,e},{});return Object.entries(u).forEach(([e,t])=>{}),{schedulingResults:o,conflicts:l,riskAssessments:c,statistics:d}}catch(e){throw Error(`排程执行失败: ${e instanceof Error?e.message:"未知错误"}`)}}static async getPendingWorkOrders(e){try{let t=await A.dataAccessManager.productionWorkOrders.getAll();if("success"!==t.status||!t.data||!t.data.items||0===t.data.items.length)throw Error("获取生产工单失败：数据为空");let r=t.data.items,s=r.filter(t=>e.includes(t.id)&&"pending"===t.status);if(0===s.length)throw r.forEach(t=>{e.includes(t.id)}),Error("没有找到待开始状态的工单");let n=[];s.forEach(e=>{let t=[];e.formingMoldNumber||t.push("formingMoldNumber"),(!e.plannedMoldCount||e.plannedMoldCount<=0)&&t.push("plannedMoldCount"),(!e.hourlyCapacity||e.hourlyCapacity<=0)&&t.push("hourlyCapacity"),e.deliveryDate||t.push("deliveryDate"),n.push({workOrder:e,isValid:0===t.length,missingFields:t})}),n.forEach(e=>{e.isValid});let a=n.filter(e=>e.isValid).map(e=>e.workOrder);if(0===a.length)throw n.filter(e=>!e.isValid).length,n.forEach(e=>{e.isValid}),Error("没有找到有效的待排程工单：所有工单都存在数据完整性问题");return a}catch(e){throw e}}static async getAvailableWorkstations(){try{let e=await A.dataAccessManager.workstations.getWorkstations();if("success"!==e.status||!e.data||!e.data.items)throw Error("获取工位信息失败");let t=e.data.items.filter(e=>"active"===e.status);if(0===t.length)throw Error("没有可用的工位");return t}catch(e){throw e}}static async getWorkTimeConfiguration(){try{let e=await (0,tZ.Ro)(()=>A.dataAccessManager.workTime.getDefault(),"获取工作时间配置");if(!e)throw Error("获取工作时间配置失败");return e}catch(e){throw e}}static createVirtualWorkstationStates(e){return e.map(e=>({...e,isVirtual:!0,originalState:{...e},code:e.code,name:e.name,lastEndTime:e.lastEndTime||null,currentMoldNumber:e.currentMoldNumber||null,currentBatchNumber:e.currentBatchNumber||null,batchNumberQueue:e.batchNumberQueue||[]}))}static updateVirtualWorkstationStatus(e,t){e.lastEndTime=t.plannedEndTime,e.currentMoldNumber=t.formingMoldNumber,e.currentBatchNumber=t.batchNumber,e.batchNumberQueue||(e.batchNumberQueue=[]),e.batchNumberQueue.includes(t.batchNumber)||e.batchNumberQueue.push(t.batchNumber)}static hasTimeOverlap(e,t){let r=new Date(e.plannedStartTime).getTime(),s=new Date(e.plannedEndTime).getTime(),n=new Date(t.plannedStartTime).getTime();return r<new Date(t.plannedEndTime).getTime()&&n<s}static async calculateWorkstationUpdates(e){try{let t=await A.dataAccessManager.workstations.getWorkstations();if("success"!==t.status||!t.data||!t.data.items)return[];let r=t.data.items,s=new Map,n=e.reduce((e,t)=>{let r=t.workstationCode;return e[r]||(e[r]=[]),e[r].push(t),e},{});return Object.entries(n).forEach(([e,t])=>{let n=t.sort((e,t)=>new Date(e.plannedEndTime).getTime()-new Date(t.plannedEndTime).getTime()),a=n[n.length-1],i=r.find(t=>t.code===e);i&&s.set(i.id,{workstationId:i.id,updates:{lastEndTime:a.plannedEndTime,currentMoldNumber:a.formingMoldNumber,currentBatchNumber:a.batchNumber},batchNumber:a.batchNumber})}),Array.from(s.values())}catch(e){return[]}}static findSameMoldWorkstation(e,t){let r=t.filter(t=>t.currentMoldNumber===e);return 0===r.length?null:r.reduce((e,t)=>{let r=e.lastEndTime?new Date(e.lastEndTime):new Date;return(t.lastEndTime?new Date(t.lastEndTime):new Date)<r?t:e})}static sortByDeliveryPriority(e){return e.sort((e,t)=>{let r=new Date(e.deliveryDate),s=new Date(t.deliveryDate);if(r.getTime()!==s.getTime())return r.getTime()-s.getTime();let n={A:5,B:4,C:3,D:2,E:1},a=n[e.customerCreditLevel]||0,i=n[t.customerCreditLevel]||0;return a!==i?i-a:t.plannedMoldCount-e.plannedMoldCount})}static selectOptimalWorkstation(e){return 0===e.length?null:e.reduce((e,t)=>{let r=e.lastEndTime?new Date(e.lastEndTime):new Date;return(t.lastEndTime?new Date(t.lastEndTime):new Date)<r?t:e})}static async assignWorkOrderToWorkstation(e,t,r,s){try{let n;let a=r?0:20;if(t.lastEndTime){n=new Date(t.lastEndTime);let e=new Date;n<e?n=this.calculateNextValidWorkTime(s):r||n.setMinutes(n.getMinutes()+a)}else n=this.calculateNextValidWorkTime(s);let i=Math.ceil(e.plannedMoldCount/e.hourlyCapacity*60),o=this.calculateContinuousWorkEndTime(n,i,s);return{workOrderId:e.id,batchNumber:e.batchNumber,formingMoldNumber:e.formingMoldNumber||"",workstation:t.name,workstationCode:t.code,plannedStartTime:n.toISOString(),plannedEndTime:o.toISOString(),productionTimeMinutes:i,isSameMold:r,changeoverTimeMinutes:a}}catch(e){throw Error(`分配工单到工位失败: ${e instanceof Error?e.message:"未知错误"}`)}}static calculateNextValidWorkTime(e){let t=new Date,r=t.toTimeString().substring(0,5),s=e.workTimeSlots.filter(e=>e.isActive).sort((e,t)=>e.startTime.localeCompare(t.startTime));for(let e of s)if(r>=e.startTime&&r<e.endTime)return t;for(let e of s)if(r<e.startTime){let r=new Date(t),[s,n]=e.startTime.split(":").map(Number);return r.setHours(s,n,0,0),r}let n=new Date(t);if(n.setDate(n.getDate()+1),s.length>0){let[e,t]=s[0].startTime.split(":").map(Number);return n.setHours(e,t,0,0),n}return t}static calculateContinuousWorkEndTime(e,t,r){let s=new Date(e),n=t,a=r.workTimeSlots.filter(e=>e.isActive).sort((e,t)=>e.startTime.localeCompare(t.startTime));for(;n>0;){s.toISOString().split("T")[0];let e=s.toTimeString().substring(0,5),t=this.calculateRemainingWorkTimeToday(e,a);if(t>=n)return this.advanceTimeWithinWorkDay(s,n,a);{n-=t;let e=new Date(s);e.setDate(e.getDate()+1),s=new Date(`${e.toISOString().split("T")[0]}T${a[0].startTime}:00`)}}return s}static calculateRemainingWorkTimeToday(e,t){let r=0;for(let s of t)if(e<s.endTime){let t=e>s.startTime?e:s.startTime;r+=this.calculateTimeDifferenceMinutes(t,s.endTime)}return r}static advanceTimeWithinWorkDay(e,t,r){let s=new Date(e),n=t,a=s.toTimeString().substring(0,5);for(let e of r){if(a>=e.endTime)continue;let t=a>e.startTime?a:e.startTime,i=this.calculateTimeDifferenceMinutes(t,e.endTime);if(n<=i){let e=this.addMinutesToTime(t,n),r=new Date(s),[a,i]=e.split(":").map(Number);return r.setHours(a,i,0,0),r}if(n-=i,r.indexOf(e)<r.length-1){let[t,n]=r[r.indexOf(e)+1].startTime.split(":").map(Number);s.setHours(t,n,0,0)}}return s}static calculateTimeDifferenceMinutes(e,t){let[r,s]=e.split(":").map(Number),[n,a]=t.split(":").map(Number);return Math.max(0,60*n+a-(60*r+s))}static addMinutesToTime(e,t){let[r,s]=e.split(":").map(Number),n=60*r+s+t;return`${(Math.floor(n/60)%24).toString().padStart(2,"0")}:${(n%60).toString().padStart(2,"0")}`}static async updateWorkstationStatus(e,t){try{if("active"!==e.status){console.warn(`工位 ${e.code} 状态为 ${e.status}，无法分配任务`);return}if(!t.formingMoldNumber||!t.batchNumber){console.error(`排程结果缺少必要信息：模具编号=${t.formingMoldNumber}, 批次号=${t.batchNumber}`);return}console.log(`🔄 工位状态转换: ${e.code} 从空闲状态激活为生产状态`),console.log(`   模具: ${t.formingMoldNumber}`),console.log(`   批次: ${t.batchNumber}`),console.log(`   计划结束时间: ${t.plannedEndTime}`);let r={source:"scheduling",operation:"production_assignment",userId:"scheduling-system",reason:`自动排程分配任务 - 模具:${t.formingMoldNumber}, 批次:${t.batchNumber}`},s=await this.workstationUpdateService.updateWorkstation(e.id,{lastEndTime:t.plannedEndTime,currentMoldNumber:t.formingMoldNumber,currentBatchNumber:t.batchNumber},r,e.version);if(!s.success){s.conflictInfo?console.error(`❌ 工位 ${e.code} 状态更新失败 - 版本冲突:`,s.conflictInfo):console.error(`❌ 工位 ${e.code} 状态更新失败:`,s.error);return}console.log(`✅ 工位 ${e.code} 状态更新成功，版本: ${e.version} → ${s.workstation?.version}`),e.lastEndTime=t.plannedEndTime,e.currentMoldNumber=t.formingMoldNumber,e.currentBatchNumber=t.batchNumber,await (0,tZ.Ro)(()=>A.dataAccessManager.workstations.addToQueue(e.id,t.batchNumber),`更新工位 ${e.code} 队列`)||console.warn(`⚠️ 工位 ${e.code} 队列更新失败，但状态更新成功`),e.batchNumberQueue||(e.batchNumberQueue=[]),e.batchNumberQueue.includes(t.batchNumber)||e.batchNumberQueue.push(t.batchNumber),console.log(`✅ 工位 ${e.code} 状态更新完成`)}catch(t){console.error(`❌ 更新工位 ${e.code} 状态时发生错误:`,t)}}static detectSchedulingConflicts(e){let t=[],r=new Map;for(let t of e)r.has(t.workstation)||r.set(t.workstation,[]),r.get(t.workstation).push(t);for(let[e,s]of Array.from(r.entries())){let r=s.sort((e,t)=>new Date(e.plannedStartTime).getTime()-new Date(t.plannedStartTime).getTime());for(let s=0;s<r.length-1;s++){let n=r[s],a=r[s+1],i=new Date(n.plannedEndTime),o=new Date(a.plannedStartTime);if(i>o){let r=Math.ceil((i.getTime()-o.getTime())/6e4);t.push({type:"time_overlap",workstation:e,conflictingTasks:[n.workOrderId,a.workOrderId],overlapTime:r,description:`工位 ${e} 存在时间重叠，重叠时间 ${r} 分钟`})}}}return t}static assessDeliveryRisks(e,t){let r=[];for(let s of e){let e=t.find(e=>e.id===s.workOrderId);if(!e)continue;let n=new Date(e.deliveryDate),a=new Date(s.plannedEndTime),i=Math.ceil((a.getTime()-n.getTime())/864e5);if(i>0){let e=i>3?"high":"medium";r.push({workOrderId:s.workOrderId,riskLevel:e,delayDays:i,suggestion:"考虑调整优先级或增加产能",expectedCompletionDate:a.toISOString().split("T")[0]})}else r.push({workOrderId:s.workOrderId,riskLevel:"normal",advanceDays:Math.abs(i),expectedCompletionDate:a.toISOString().split("T")[0]})}return r}static calculateStatistics(e,t,r,s,n){let a=new Set(t.map(e=>e.workstation)).size,i=t.reduce((e,t)=>new Date(t.plannedEndTime)>new Date(e)?t.plannedEndTime:e,t[0]?.plannedEndTime||new Date().toISOString());return{totalWorkOrders:e.length,successfullyScheduled:t.length,sameMoldAssignments:r,deliveryDateAssignments:s,involvedWorkstations:a,latestCompletionTime:i}}static async applySchedulingResults(e){try{let t=e.map(e=>(0,tZ.Ro)(()=>A.dataAccessManager.productionWorkOrders.update(e.workOrderId,{workstation:e.workstation,workstationCode:e.workstationCode,plannedStartTime:e.plannedStartTime,plannedEndTime:e.plannedEndTime,status:"scheduled",updatedAt:new Date().toISOString()}),`更新工单 ${e.workOrderId} 状态`)),s=await Promise.allSettled(t);s.filter(e=>"fulfilled"===e.status&&null!==e.value).length,s.filter(e=>"rejected"===e.status||null===e.value).length>0&&s.forEach((e,t)=>{"rejected"===e.status||"fulfilled"===e.status&&e.value});let n=await this.calculateWorkstationUpdates(e);for(let t of n){let r=await A.dataAccessManager.workstations.getById(t.workstationId);if(!r||"success"!==r.status||!r.data){console.warn(`⚠️ 无法获取工位 ${t.workstationId} 信息，跳过更新`);continue}let s={source:"scheduling",operation:"production_assignment",userId:"scheduling-system",reason:"批量排程结果应用"},n=await this.workstationUpdateService.updateWorkstation(t.workstationId,t.updates,s,r.data.version);if(!n.success){console.error(`❌ 工位 ${t.workstationId} 状态更新失败:`,n.error);continue}let a=n.workstation;if(!a){console.error(`❌ 工位 ${t.workstationId} 更新响应为空`);continue}for(let r of e.filter(e=>e.workstationCode===a.code))await (0,tZ.Ro)(()=>A.dataAccessManager.workstations.addToQueue(t.workstationId,r.batchNumber),`更新工位 ${t.workstationId} 队列`)}console.log({操作类型:"自动排程",排程模式:"相同模具优先",影响工单数:e.length,影响工位数:n.length,操作时间:new Date().toISOString(),工位更新详情:n.map(e=>({工位ID:e.workstationId,最后结束时间:e.updates.lastEndTime,当前模具:e.updates.currentMoldNumber}))});try{let{dataSyncService:t}=await Promise.resolve().then(r.bind(r,51178));t.addChangeEvent({type:"update",module:"production",entityType:"production_work_order",entityId:"batch_update",newData:{count:e.length,workOrderIds:e.map(e=>e.workOrderId),workstationCodes:Array.from(new Set(e.map(e=>e.workstationCode))),timestamp:new Date().toISOString()}}),t.addChangeEvent({type:"create",module:"scheduling",entityType:"scheduling_result",entityId:"batch_update",newData:{count:e.length,timestamp:new Date().toISOString()}})}catch(e){}}catch(e){throw Error(`应用排程结果失败: ${e instanceof Error?e.message:"未知错误"}`)}}static async processRemainingWorkOrdersWithMoldGrouping(e,t,r){try{let s=[],n=this.groupWorkOrdersByMold(e),a=this.filterEligibleMoldGroups(n),i=new Set;for(let e of a){let n=await this.assignMoldGroupToSameWorkstation(e,t,r);s.push(...n),e.workOrders.forEach(e=>i.add(e.id))}let o=e.filter(e=>!i.has(e.id));for(let e of this.sortByDeliveryPriority(o)){let n=this.selectOptimalWorkstation(t);if(n){let t=await this.assignWorkOrderToWorkstation(e,n,!1,r);s.push(t),this.updateVirtualWorkstationStatus(n,t)}}return s}catch(e){throw console.error("处理剩余工单失败:",e),Error(`处理剩余工单失败: ${e instanceof Error?e.message:"未知错误"}`)}}static groupWorkOrdersByMold(e){let t=new Map;for(let r of e){let e=r.formingMoldNumber;t.has(e)||t.set(e,[]),t.get(e).push(r)}return t}static filterEligibleMoldGroups(e){let t=[];for(let[r,s]of e)if(s.length>=2){let e=s.map(e=>new Date(e.deliveryDate).getTime()),n=Math.min(...e),a=(Math.max(...e)-n)/864e5;if(a<=this.getSameMoldDeliveryToleranceDays()){let e=s.sort((e,t)=>new Date(e.deliveryDate).getTime()-new Date(t.deliveryDate).getTime());t.push({moldNumber:r,workOrders:e,daysDiff:a})}}return t.sort((e,t)=>new Date(e.workOrders[0].deliveryDate).getTime()-new Date(t.workOrders[0].deliveryDate).getTime())}static async assignMoldGroupToSameWorkstation(e,t,r){let s=[],n=this.selectOptimalWorkstation(t);if(!n)return s;console.log(`🔧 相同模具聚合: 模具 ${e.moldNumber} 的 ${e.workOrders.length} 个工单分配到工位 ${n.code}`);for(let t=0;t<e.workOrders.length;t++){let a=e.workOrders[t],i=0===t,o=await this.assignWorkOrderToWorkstation(a,n,!i,r);s.push(o),this.updateVirtualWorkstationStatus(n,o)}return s}static getSameMoldDeliveryToleranceDays(){return this.currentSchedulingConfig.sameMoldDeliveryToleranceDays}}var rg=r(6487),rp=r(81037),rx=r(82366);let rf=async(e,t)=>{try{let r=await A.dataAccessManager.products.getByCode(t);if("success"!==r.status||!r.data)return console.warn(`⚠️ 未找到产品编码 ${t} 的产品数据，使用默认模数计算`),Math.ceil(e/100);let s=r.data;if(!s.formingMoldQuantity||s.formingMoldQuantity<=0)return console.warn(`⚠️ 产品 ${t} 的成型模具单模数量无效 (${s.formingMoldQuantity})，使用默认值`),Math.ceil(e/100);let n=Math.ceil(e/s.formingMoldQuantity);return console.log(`✅ 模数计算: 产品=${t}, 计划数量=${e}, 单模数量=${s.formingMoldQuantity}, 生产模数=${n}`),n}catch(t){return console.error("❌ 模数计算失败:",t),Math.ceil(e/100)}},ry=0,rj=()=>{let e=new Date,t=e.getFullYear(),r=(e.getMonth()+1).toString().padStart(2,"0"),s=e.getDate().toString().padStart(2,"0"),n=`${t}${r}${s}`;ry++;let a=(Date.now().toString().slice(-3)+ry.toString().padStart(1,"0")).slice(-4);return`PC${n}${a}`},rw=async(e,t=130)=>{if(!e)throw Error("生产订单数据不能为空");if(!e.productCode||!e.productName)throw Error(`生产订单 ${e.orderNumber} 缺少必要的产品信息：productCode=${e.productCode}, productName=${e.productName}`);if(!e.plannedQuantity||e.plannedQuantity<=0)throw Error(`生产订单 ${e.orderNumber} 计划数量无效：${e.plannedQuantity}`);if(!e.orderNumber)throw Error("生产订单号不能为空");console.log(`🔍 工单生成验证: 订单=${e.orderNumber}, 产品=${e.productCode}-${e.productName}, 数量=${e.plannedQuantity}`);let r=rj(),s=await rf(e.plannedQuantity,e.productCode),n="UNASSIGNED",a="未分配";if(e.workstation){let t=e.workstation.match(/(\d+)/);if(t){let r=t[1].padStart(3,"0");n=`WS${r}`,a=e.workstation;try{let e=await A.dataAccessManager.workstations.getAll();"success"===e.status&&e.data?.items&&(e.data.items.some(e=>e.code===n)?console.log(`✅ 工位编码验证通过: ${n}`):(console.warn(`⚠️ 工位编码 ${n} 不存在，将使用UNASSIGNED`),n="UNASSIGNED",a="未分配"))}catch(e){console.warn(`⚠️ 工位编码验证失败，使用默认值: ${e}`),n="UNASSIGNED",a="未分配"}}}let i={batchNumber:r,productCode:e.productCode,productName:e.productName,productModelCode:e.productCode,formingMoldNumber:e.formingMoldNumber,status:"pending",customerCode:e.customerId||"",customerCreditLevel:e.customerCreditLevel,prioritySource:e.prioritySource||"auto",workstation:a,workstationCode:n,createdBy:"system",plannedStartTime:void 0,plannedEndTime:void 0,deliveryDate:e.deliveryDate,plannedMoldCount:s,completedMoldCount:0,hourlyCapacity:t,executionRate:0,exceptionCount:0,sourceOrderId:e.id,sourceOrderNumber:e.orderNumber,customerName:e.customerName||"",isSharedMold:e.isSharedMold||!1};if(i.productCode!==e.productCode||i.productName!==e.productName)throw Error(`工单数据映射错误：期望产品 ${e.productCode}-${e.productName}，实际得到 ${i.productCode}-${i.productName}`);return console.log(`✅ 工单生成成功: 批次=${r}, 产品=${i.productCode}-${i.productName}`),i};var rv=r(69915),rk=r(58357);class rS{static async handleWorkOrderStatusChange(e,t={}){let{showSuccessMessage:r=!0,showErrorMessage:s=!0,successMessage:n="工单状态更新成功",errorMessagePrefix:a="工单状态更新失败",silent:i=!1}=t;try{let t=await rk.Fc.handleWorkOrderStatusChange({workOrderId:e.workOrderId,oldStatus:e.oldStatus,newStatus:e.newStatus,sourceOrderId:e.sourceOrderId,operatorId:e.operatorId,reason:e.reason},{enableErrorLogging:!0,throwOnError:!1});return!i&&t.success&&r&&(t.orderStatusChanged?t9.ZP.success(`${n}，订单状态已自动更新为：${t.newOrderStatus}`):t9.ZP.success(n)),{success:t.success,orderStatusChanged:t.orderStatusChanged,newOrderStatus:t.newOrderStatus,error:t.error,duration:t.duration}}catch(t){let e=t instanceof Error?t.message:"未知错误";return!i&&s&&t9.ZP.error(`${a}：${e}`),{success:!1,orderStatusChanged:!1,error:e}}}static async handleWorkOrderCreation(e,t,r={}){let{showSuccessMessage:s=!1,showErrorMessage:n=!0,errorMessagePrefix:a="工单创建状态转换失败",silent:i=!1}=r;try{let r=await rk.Fc.handleWorkOrderCreation(e,t,{enableErrorLogging:!0,throwOnError:!1});return!i&&r.success&&r.orderStatusChanged&&s&&t9.ZP.success(`订单状态已自动更新为：${r.newOrderStatus}`),{success:r.success,orderStatusChanged:r.orderStatusChanged,newOrderStatus:r.newOrderStatus,error:r.error,duration:r.duration}}catch(t){let e=t instanceof Error?t.message:"未知错误";return!i&&n&&t9.ZP.error(`${a}：${e}`),{success:!1,orderStatusChanged:!1,error:e}}}static async handleBatchWorkOrderCreation(e,t={}){let{silent:r=!0}=t,s=[];for(let n of e){let e=await this.handleWorkOrderCreation(n.id,n.status,{...t,silent:r});s.push({orderId:n.id,orderNumber:n.orderNumber,result:e})}return s}static getTransitionStatistics(e){let t=e.length,r=e.filter(e=>e.success).length,s=e.filter(e=>e.orderStatusChanged).length,n=e.filter(e=>e.duration).map(e=>e.duration);return{total:t,successful:r,failed:t-r,orderStatusChanged:s,successRate:Math.round(100*(t>0?r/t*100:0))/100,averageDuration:Math.round(n.length>0?n.reduce((e,t)=>e+t,0)/n.length:0)}}}let rZ={handleStatusChange:rS.handleWorkOrderStatusChange,handleCreation:rS.handleWorkOrderCreation,handleBatch:rS.handleBatchWorkOrderCreation,getStatistics:rS.getTransitionStatistics};var rb=r(55362),rC=r(76717),rT=r(18608);let rD={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zm-600 72h560v208H232V136zm560 480H232V408h560v208zm0 272H232V680h560v208zM496 208H312c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM312 544h184c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H312c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8zm328 244a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"hdd",theme:"outlined"};var rM=n.forwardRef(function(e,t){return n.createElement(k.Z,(0,w.Z)({},e,{ref:t,icon:rD}))});let r$=({metrics:e,cacheStats:t,isMonitoring:r,lastUpdateTime:n,error:a,onClearCache:i,formatMemorySize:o,formatPercentage:h})=>{let g=()=>t&&t.enabled?t.hitRate>.7?"success":t.hitRate>.3?"warning":"error":"default";return(0,s.jsxs)(l.Z,{size:"small",title:(0,s.jsxs)(u.Z,{children:[s.jsx(rb.Z,{}),"DataAccessManager监控",s.jsx(f.Z,{status:r?"processing":"default",text:r?"运行中":"已停止"})]}),extra:s.jsx(u.Z,{children:s.jsx(I.Z,{title:"清理缓存",children:s.jsx(m.ZP,{type:"text",size:"small",icon:s.jsx(rC.Z,{}),onClick:i,disabled:!t?.enabled})})}),style:{marginBottom:16},children:[a&&s.jsx(e9.Z,{message:"监控错误",description:a,type:"error",style:{marginBottom:12}}),e?(0,s.jsxs)(c.Z,{gutter:16,children:[s.jsx(d.Z,{span:12,children:s.jsx(p.Z,{title:"平均响应时间",value:`${e.averageResponseTime.toFixed(1)}ms`,prefix:s.jsx(rT.Z,{}),valueStyle:{fontSize:"14px",color:e.averageResponseTime>1e3?"#ff4d4f":"#52c41a"}})}),s.jsx(d.Z,{span:12,children:s.jsx(p.Z,{title:"错误率",value:h(e.errorRate),valueStyle:{fontSize:"14px",color:e.errorRate>.05?"#ff4d4f":"#52c41a"}})})]}):s.jsx("div",{style:{textAlign:"center",color:"#999"},children:"性能指标不可用"}),t?(0,s.jsxs)("div",{style:{marginTop:16},children:[(0,s.jsxs)(c.Z,{gutter:16,children:[s.jsx(d.Z,{span:12,children:s.jsx(p.Z,{title:"缓存命中率",value:h(t.hitRate),prefix:s.jsx(rM,{}),valueStyle:{fontSize:"14px",color:t.hitRate>.5?"#52c41a":"#ff4d4f"}})}),s.jsx(d.Z,{span:12,children:s.jsx(p.Z,{title:"缓存大小",value:t.size,valueStyle:{fontSize:"14px"}})})]}),s.jsx("div",{style:{marginTop:12},children:s.jsx(tX.Z,{percent:Math.round(100*t.hitRate),status:"success"===g()?"success":"warning"===g()?"active":"exception",size:"small",format:e=>`命中率 ${e}%`})}),(0,s.jsxs)(c.Z,{gutter:16,style:{marginTop:12},children:[s.jsx(d.Z,{span:8,children:(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["命中: ",s.jsx("strong",{children:t.hits})]})}),s.jsx(d.Z,{span:8,children:(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["未命中: ",s.jsx("strong",{children:t.misses})]})}),s.jsx(d.Z,{span:8,children:(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["状态: ",s.jsx(f.Z,{status:t.enabled?"success":"default",text:t.enabled?"启用":"禁用"})]})})]})]}):s.jsx("div",{style:{textAlign:"center",color:"#999",marginTop:16},children:"缓存统计不可用"}),e&&(0,s.jsxs)(c.Z,{gutter:16,style:{marginTop:16},children:[s.jsx(d.Z,{span:12,children:(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["总请求: ",s.jsx("strong",{children:e.totalRequests})]})}),s.jsx(d.Z,{span:12,children:(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["成功请求: ",s.jsx("strong",{children:e.successfulRequests})]})})]}),n&&(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666",marginTop:12,textAlign:"center"},children:["最后更新: ",new Date(n).toLocaleTimeString()]}),s.jsx("div",{style:{fontSize:"11px",color:"#52c41a",marginTop:8,textAlign:"center",fontStyle:"italic"},children:"✅ 使用DataAccessManager统一监控"})]})},rI=()=>{let{message:e}=i.Z.useApp(),{metrics:t,cacheStats:a,isMonitoring:w,lastUpdateTime:v,error:k,clearCache:I,getPerformanceAlerts:E,formatMemorySize:O,formatPercentage:N,isHealthy:_,needsOptimization:z}=(0,rx.e)({interval:6e4,enabled:!0,showDetails:!1}),[W,R]=(0,n.useState)(!1),[P,B]=(0,n.useState)(null),[L,H]=(0,n.useState)(!1),[V,q]=(0,n.useState)(!1),[F,Q]=(0,n.useState)(!1),[U,Y]=(0,n.useState)([]),[X,G]=(0,n.useState)(null),[K,J]=(0,n.useState)(!1),[ee,er]=(0,n.useState)("pending-orders"),[es,en]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("tab"),t=window.location.hash.replace("#",""),r=e||t;r&&["pending-orders","production-work-orders","scheduling-board","workstation-management"].includes(r)&&er(r)},[]);let ea=e=>{er(e);let t=new URL(window.location.href);t.searchParams.set("tab",e),window.history.replaceState({},"",t.toString())},[ei,eo]=(0,n.useState)([]),[ec,ed]=(0,n.useState)([]),[eu,eh]=(0,n.useState)([]),em=ei.filter(e=>"in_plan"===e.status),eg=ei.filter(e=>"planned"===e.status),ep=ei.filter(e=>"in_progress"===e.status),ex=ei.filter(e=>"completed"===e.status),[ef,ey]=(0,n.useState)({totalOrders:0,inPlanOrders:0,plannedOrders:0,inProgressOrders:0,completedOrders:0}),ej=(0,n.useMemo)(()=>({totalOrders:ei.length,inPlanOrders:em.length,plannedOrders:eg.length,inProgressOrders:ep.length,completedOrders:ex.length}),[ei.length,em.length,eg.length,ep.length,ex.length]);(0,n.useEffect)(()=>{ey(ej)},[ej]);let ew=async(t=!1)=>{try{t&&A.dataAccessManager.clearAllCache();let r=await A.dataAccessManager.productionOrders.getAll();"success"===r.status&&r.data&&r.data.items?(eo(r.data.items),console.log(`📊 加载生产订单: ${r.data.items.length} 个订单`)):(e.error(r.message||"加载生产订单失败"),console.error("加载生产订单失败:",r))}catch(t){e.error("系统错误，请稍后重试"),console.error("加载生产订单异常:",t)}},ev=async(t=!1)=>{try{let r;t&&(console.log("\uD83D\uDD27 开始彻底清除工单缓存..."),A.dataAccessManager.clearAllCache(),console.log("✅ 缓存已清除"),await new Promise(e=>setTimeout(e,100)),console.log("\uD83D\uDD27 所有工单相关缓存已彻底清除")),console.log("\uD83D\uDCCA 开始获取工单数据...");try{r=await A.dataAccessManager.productionWorkOrders.getAll(),console.log("\uD83D\uDCCA 工单数据响应 (方式1-缓存):",{status:r.status,hasData:!!r.data,itemsLength:r.data?.items?.length||0})}catch(e){throw console.error("获取工单数据失败:",e),e}"success"===r.status&&r.data&&r.data.items?(ed(r.data.items),console.log(`📊 加载生产工单: ${r.data.items.length} 个工单`),r.data.items.length>0&&console.log("\uD83D\uDCCB 工单详情:",r.data.items.map(e=>({id:e.id,batchNumber:e.batchNumber,productName:e.productName,status:e.status})))):(e.error(r.message||"加载生产工单失败"),console.error("加载生产工单失败:",r))}catch(t){e.error("系统错误，请稍后重试"),console.error("加载生产工单异常:",t)}},ek=async(t=!1)=>{try{if(t){console.log("\uD83D\uDD04 重置所有工位状态到初始空闲状态...");let e=await A.dataAccessManager.workstations.resetAllWorkstationsToIdle();e?.status==="success"?(console.log(`✅ 工位状态重置成功，成功重置了 ${e.data?.resetCount||0} 个工位`),e.data?.details?.length>0&&console.log("\uD83D\uDCCB 重置详情:",e.data.details.map(e=>({工位ID:e.workstationId,状态:"成功",消息:`工位 ${e.workstationCode} 已重置为空闲状态`})))):console.warn("⚠️ 重置工位状态失败:",e?.message)}let r=await A.dataAccessManager.workstations.getAll();"success"===r.status&&r.data&&r.data.items?(eh(r.data.items),console.log({count:r.data.items.length,workstations:r.data.items.map(e=>({id:e.id,code:e.code,name:e.name}))})):e.error(r.message||"加载工位数据失败")}catch(t){e.error("系统错误，请稍后重试")}},eS=async(e=!0,t=!1)=>{console.log("\uD83D\uDD04 开始刷新数据...",{forceRefresh:e,isInitialLoad:t}),await Promise.all([ew(e),ev(e),ek(t)]),console.log("✅ 数据刷新完成")},eZ=(0,n.useCallback)(()=>{let e=ei.length,t=ei.filter(e=>"in_plan"===e.status).length;ey({totalOrders:e,inPlanOrders:t,plannedOrders:ei.filter(e=>"planned"===e.status).length,inProgressOrders:ei.filter(e=>"in_progress"===e.status).length,completedOrders:ei.filter(e=>"completed"===e.status).length})},[ei]);(0,n.useEffect)(()=>{eZ()},[eZ]);let eb=(0,n.useCallback)(async()=>{if(0!==ei.length)try{let{dataConsistencyService:t}=await r.e(9984).then(r.bind(r,59984)),s=(await t.performFullConsistencyCheck()).results.reduce((e,t)=>e+t.inconsistentItems,0);s>0&&(e.warning(`发现 ${s} 个数据一致性问题，正在重新加载...`),await eS(!0))}catch(e){console.error("数据一致性验证失败:",e)}},[ei]);(0,n.useEffect)(()=>{if(ei.length>0){let e=setTimeout(()=>{eb()},2e3);return()=>clearTimeout(e)}},[ei,eb]);let eT=(0,rg.y1)(()=>ew(),300,[ew]),eD=(0,rg.y1)(()=>ev(!1),300,[ev]);(0,rp.el)("production-orders-page",{onProductionOrderCreated:e=>{console.log("生产订单页面收到新订单创建事件:",e),eT()},onProductionOrderUpdated:e=>{console.log("生产订单页面收到订单更新事件:",e),eT()},onProductionWorkOrderCreated:e=>{console.log("生产订单页面收到新工单创建事件:",e),eD()},onProductionWorkOrderUpdated:e=>{console.log("生产订单页面收到工单更新事件:",e),eD()},onProductionWorkOrderDeleted:e=>{console.log("生产订单页面收到工单删除事件:",e),ev(!1)}}),(0,n.useEffect)(()=>{(async()=>{H(!0);try{await eS(!0,!0),e.success("数据初始化完成")}catch(t){e.error("数据初始化失败")}finally{H(!1)}})()},[]);let eM=async()=>{H(!0);try{console.log("\uD83D\uDD04 用户手动刷新数据"),await eS(!0),e.success("数据刷新完成")}catch(t){console.error("数据刷新失败:",t),e.error("数据刷新失败，请稍后重试")}finally{H(!1)}},e$=async(t,r)=>{await (0,tZ.Ro)(()=>A.dataAccessManager.productionOrders.update(t,{status:r}),"更新订单状态")?(e.success("订单状态更新成功"),await eS()):e.error("订单状态更新失败")},eI=async()=>{H(!0);try{await new Promise(e=>setTimeout(e,1e3)),e.success("数据同步完成")}catch(t){e.error("数据同步失败")}finally{H(!1)}},eE=async(t,r)=>{let s=await A.dataAccessManager.productionWorkOrders.getById(t);if("success"!==s.status||!s.data){e.error("获取工单信息失败");return}let n=s.data.status,a=await (0,tZ.Ro)(()=>A.dataAccessManager.productionWorkOrders.update(t,{status:r}),"更新工单状态");a&&a.sourceOrderId?(await rZ.handleStatusChange({workOrderId:t,oldStatus:n,newStatus:r,sourceOrderId:a.sourceOrderId},{showSuccessMessage:!0,successMessage:"工单状态更新成功"}),await eS()):a?(e.success("工单状态更新成功"),await eS()):e.error("工单状态更新失败")},eN=async t=>{await (0,tZ.Ro)(()=>A.dataAccessManager.productionWorkOrders.delete(t),"删除工单")?(e.success("工单删除成功"),await eS()):e.error("工单删除失败")},e_=async t=>{try{J(!0);let r=U.map(e=>e.id),s=await rm.calculateSchedulingResults(r,{sameMoldDeliveryToleranceDays:t.sameMoldDeliveryToleranceDays});G(s),q(!1),Q(!0),e.success(`排程计算完成！生成 ${s.schedulingResults.length} 个工单的排程预览`),e.info('当前为预览模式，工位状态和工单状态尚未实际更新，请确认后点击"确认应用"')}catch(t){e.error(`排程计算失败: ${t instanceof Error?t.message:"未知错误"}`)}finally{J(!1)}},ez=async()=>{if(X)try{J(!0),await rm.applySchedulingResults(X.schedulingResults),e.success("排程结果已完整应用！工单状态和工位状态已同步更新"),e.info('所有工单状态已更新为"已排程"，工位队列任务已同步'),Q(!1),G(null),Y([]),await eS(!0)}catch(t){e.error(`应用排程结果失败: ${t instanceof Error?t.message:"未知错误"}`)}finally{J(!1)}},eW=async(t,r)=>{try{e.loading("正在批量创建工单...",0),e.info("正在验证订单数据...");let s=[],n=[];for(let e of t)try{let t=await (0,tZ.Ro)(()=>A.dataAccessManager.productionOrders.getById(e),`验证生产订单 (${e})`);if(!t){s.push(`订单ID ${e} 不存在`);continue}if(!el.canCreateWorkOrder(t)){let e="planned"===t.status?"已计划":"in_progress"===t.status?"生产中":"completed"===t.status?"已完成":"cancelled"===t.status?"已取消":t.status;s.push(`订单 ${t.orderNumber} 状态为"${e}"，只能为"计划中"状态的订单创建工单`);continue}if(!t.productCode||!t.productName||!t.plannedQuantity){s.push(`订单 ${t.orderNumber} 数据不完整，缺少必要字段`);continue}let r=await A.dataAccessManager.productionWorkOrders.getAll();if("success"===r.status&&r.data){let n=r.data.items.find(t=>t.sourceOrderId===e);if(n){s.push(`订单 ${t.orderNumber} 已存在工单 ${n.batchNumber}`);continue}}n.push(t)}catch(t){s.push(`验证订单 ${e} 时发生错误: ${t instanceof Error?t.message:"未知错误"}`)}if(s.length>0){e.destroy();let t=`数据验证失败，无法创建工单：
${s.join("\n")}`;e.error(t),console.error("工单创建验证失败:",s);return}if(0===n.length){e.destroy(),e.warning("没有有效的订单可以创建工单");return}e.info(`验证通过，开始创建 ${n.length} 个工单...`);let a=n.map(e=>async()=>{let t=await rw(e,r),s=await (0,tZ.Ro)(()=>A.dataAccessManager.productionWorkOrders.create(t),`创建生产工单 (${e.orderNumber})`);if(!s)throw Error(`创建工单失败: ${e.orderNumber}，请检查数据格式`);let n=await rZ.handleCreation(e.id,e.status,{silent:!0});return{workOrder:s,order:{...e,status:n.orderStatusChanged?n.newOrderStatus:e.status},orderNumber:e.orderNumber}}),i=n.map(e=>`创建工单-${e.orderNumber}`),o=await rv.AO.executeBatch(a,i);e.destroy();let{successful:l,failed:c,successRate:d,totalDuration:u}=o;if(l.length>0&&(e.success(`成功创建 ${l.length} 个工单${c.length>0?`，${c.length} 个失败`:""} (耗时: ${u}ms)`),await eS()),c.length>0){let t=c.map(e=>`• ${e.taskName||e.name||"未知任务"}: ${e.error}`).join("\n"),r=`${c.length} 个工单创建失败：
${t}`;e.error({content:r,duration:8}),console.error("批量创建工单失败详情:",c)}if(0===l.length){let t="没有成功创建任何工单，请检查订单数据或联系系统管理员";throw e.error(t),Error(t)}console.log("批量创建工单完成:",{success:l.length,failed:c.length,successRate:d,totalDuration:u})}catch(r){e.destroy();let t=r instanceof Error?r.message:"批量创建工单失败";throw e.error(t),console.error("批量创建工单错误:",r),r}},eA=ec.filter(e=>"scheduled"===e.status||"in_progress"===e.status||"completed"===e.status),eR=ec.filter(e=>"pending"===e.status),eP={items:[{key:"sync",icon:s.jsx(y.Z,{}),label:"同步数据",onClick:eI},{key:"config",icon:s.jsx(j.Z,{}),label:"排单配置",onClick:()=>e.info("排单配置功能开发中")}]};return s.jsx("div",{style:{padding:0},children:(0,s.jsxs)(o.Z,{spinning:L,children:[s.jsx(l.Z,{size:"small",style:{marginBottom:"16px"},children:(0,s.jsxs)(c.Z,{justify:"space-between",align:"middle",children:[s.jsx(d.Z,{children:s.jsx(u.Z,{size:"large",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{style:{margin:0},children:[s.jsx(S,{style:{marginRight:"8px"}}),"生产订单管理"]}),(0,s.jsxs)("div",{style:{marginTop:"4px"},children:[s.jsx(h.Z,{color:"blue",children:"当前策略: 默认智能拆单配置"}),s.jsx(h.Z,{color:"orange",style:{marginLeft:"8px"},children:"生产订单只能通过MRP流程创建"})]})]})})}),s.jsx(d.Z,{children:(0,s.jsxs)(u.Z,{children:[s.jsx(rh,{}),s.jsx(m.ZP,{icon:s.jsx(Z.Z,{}),onClick:eM,children:"刷新"}),s.jsx(g.Z,{menu:eP,trigger:["click"],children:(0,s.jsxs)(m.ZP,{children:["更多操作 ",s.jsx(b.Z,{})]})})]})})]})}),(0,s.jsxs)(c.Z,{gutter:16,style:{marginBottom:"16px"},children:[s.jsx(d.Z,{xs:24,sm:12,md:6,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"生产订单",value:ef?.totalOrders||0,prefix:s.jsx(C.Z,{}),valueStyle:{color:"#1890ff"}})})}),s.jsx(d.Z,{xs:24,sm:12,md:6,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"待分配任务",value:eR.length,prefix:s.jsx(T.Z,{}),valueStyle:{color:"#faad14"}})})}),s.jsx(d.Z,{xs:24,sm:12,md:6,children:s.jsx(l.Z,{size:"small",children:s.jsx(p.Z,{title:"工位利用率",value:75,suffix:"%",prefix:s.jsx(D.Z,{}),valueStyle:{color:"#722ed1"}})})})]}),s.jsx(c.Z,{gutter:16,style:{marginBottom:"16px"},children:s.jsx(d.Z,{xs:24,sm:12,md:8,children:s.jsx(r$,{metrics:t?{averageResponseTime:t.averageResponseTime,totalRequests:t.totalCalls||0,successfulRequests:t.successCalls||0,errorRate:t.totalCalls>0?t.errorCalls/t.totalCalls:0}:null,cacheStats:a,isMonitoring:w,lastUpdateTime:v,error:k,onClearCache:I,formatMemorySize:O,formatPercentage:N})})}),s.jsx(l.Z,{extra:s.jsx(u.Z,{children:s.jsx(m.ZP,{icon:s.jsx(D.Z,{}),onClick:()=>en(!0),type:"text",size:"small",children:"DataAccessManager监控"})}),children:s.jsx(x.default,{defaultActiveKey:"pending-orders",size:"large",onChange:ea,activeKey:ee,items:[{key:"pending-orders",label:(0,s.jsxs)("span",{children:[s.jsx(C.Z,{}),"生产订单",(ef?.totalOrders||0)>0&&s.jsx(f.Z,{count:ef?.totalOrders||0,style:{marginLeft:"8px"},showZero:!0})]}),children:s.jsx(eC,{orders:ei,loading:L,onRefresh:eM,onOrderDetail:e=>{B(e),R(!0)},onStatusChange:e$,onCreateWorkOrders:eW,onTabChange:ea,workstations:eu,currentSplitConfig:null})},{key:"production-work-orders",label:(0,s.jsxs)("span",{children:[s.jsx(M.Z,{}),"生产工单",ec.length>0&&s.jsx(f.Z,{count:ec.length,style:{marginLeft:"8px"},showZero:!0})]}),children:s.jsx(t7,{workOrders:ec,loading:L,onRefresh:eM,onStatusChange:eE,onDelete:eN,onExport:()=>{e.info("导出工单数据")},onStartScheduling:t=>{if(0===t.length){e.warning("请先选择要排程的工单");return}let r=ec.filter(e=>t.includes(e.id)&&"pending"===e.status);if(0===r.length){e.warning('所选工单中没有"待开始"状态的工单');return}r.length!==t.length&&e.warning(`已过滤掉 ${t.length-r.length} 个非"待开始"状态的工单`),q(!0),Y(r)}})},{key:"scheduling-board",label:(0,s.jsxs)("span",{children:[s.jsx($.Z,{}),"排产看板",eA.length>0&&s.jsx(f.Z,{count:eA.length,style:{marginLeft:"8px"},showZero:!0})]}),children:s.jsx(et,{tasks:eA,workstations:eu,onTaskClick:t=>{e.info(`查看任务详情: ${t.id||"未知任务"}`)},loading:L})},{key:"workstation-management",label:(0,s.jsxs)("span",{children:[s.jsx(j.Z,{}),"工位管理",eu.length>0&&s.jsx(f.Z,{count:eu.length,style:{marginLeft:"8px"},showZero:!0})]}),children:s.jsx(tO,{loading:L})},{key:"work-time-management",label:(0,s.jsxs)("span",{children:[s.jsx(T.Z,{}),"工作时间"]}),children:s.jsx(tY,{loading:L})}]})}),s.jsx(eO,{open:W,order:P,onClose:()=>{R(!1),B(null)},onStatusChange:e$}),s.jsx(ra,{open:V,onCancel:()=>{q(!1),Y([])},onConfirm:e_,selectedWorkOrders:U,loading:K}),s.jsx(rc,{open:F,onCancel:()=>{e.info("已取消排程应用，工位状态和工单状态保持不变"),Q(!1),G(null),Y([])},onConfirm:ez,result:X,loading:K}),es&&s.jsx(r$,{metrics:t?{averageResponseTime:t.averageResponseTime,totalRequests:t.totalCalls||0,successfulRequests:t.successCalls||0,errorRate:t.totalCalls>0?t.errorCalls/t.totalCalls:0}:null,cacheStats:a,isMonitoring:w,lastUpdateTime:v,error:k,onClearCache:I,formatMemorySize:O,formatPercentage:N})]})})};function rE(){return s.jsx(i.Z,{children:s.jsx(rI,{})})}},48606:(e,t,r)=>{"use strict";r.d(t,{Fi:()=>s,ux:()=>n});let s={in_plan:{color:"blue",text:"计划中"},planned:{color:"cyan",text:"已计划"},in_progress:{color:"orange",text:"生产中"},completed:{color:"green",text:"已完成"},cancelled:{color:"red",text:"已取消"}},n={pending:{color:"default",text:"待开始",antdColor:"default"},scheduled:{color:"blue",text:"已排程",antdColor:"blue"},in_progress:{color:"processing",text:"进行中",antdColor:"processing"},completed:{color:"success",text:"已完成",antdColor:"success"},paused:{color:"warning",text:"暂停",antdColor:"warning"},cancelled:{color:"error",text:"已取消",antdColor:"error"},exception:{color:"error",text:"异常",antdColor:"error"}}},8524:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>i});let s=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\production\orders\page.tsx`),{__esModule:n,$$typeof:a}=s,i=s.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,2345,7049,4441,7557,7383,152,8243,5236,6274,996,6133,9094,9544],()=>r(68279));module.exports=s})();