"use strict";exports.id=7383,exports.ids=[7383],exports.modules={67383:(e,t,l)=>{l.d(t,{Z:()=>P});var n=l(3729),a=l.n(n),o=l(34132),i=l.n(o),s=l(91782),r=l(84893),c=l(54527),d=l(91735);let b={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},m=a().createContext({});var g=l(89299),p=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let u=e=>(0,g.Z)(e).map(e=>Object.assign(Object.assign({},null==e?void 0:e.props),{key:e.key}));var f=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let O=(e,t)=>{let[l,a]=(0,n.useMemo)(()=>(function(e,t){let l=[],n=[],a=!1,o=0;return e.filter(e=>e).forEach(e=>{let{filled:i}=e,s=f(e,["filled"]);if(i){n.push(s),l.push(n),n=[],o=0;return}let r=t-o;(o+=e.span||1)>=t?(o>t?(a=!0,n.push(Object.assign(Object.assign({},s),{span:r}))):n.push(s),l.push(n),n=[],o=0):n.push(s)}),n.length>0&&l.push(n),[l=l.map(e=>{let l=e.reduce((e,t)=>e+(t.span||1),0);if(l<t){let n=e[e.length-1];n.span=t-(l-(n.span||1))}return e}),a]})(t,e),[t,e]);return l},y=e=>{let{itemPrefixCls:t,component:l,span:a,className:o,style:s,labelStyle:r,contentStyle:c,bordered:d,label:b,content:g,colon:p,type:u,styles:f}=e,{classNames:O}=n.useContext(m);return d?n.createElement(l,{className:i()({[`${t}-item-label`]:"label"===u,[`${t}-item-content`]:"content"===u,[`${null==O?void 0:O.label}`]:"label"===u,[`${null==O?void 0:O.content}`]:"content"===u},o),style:s,colSpan:a},null!=b&&n.createElement("span",{style:Object.assign(Object.assign({},r),null==f?void 0:f.label)},b),null!=g&&n.createElement("span",{style:Object.assign(Object.assign({},r),null==f?void 0:f.content)},g)):n.createElement(l,{className:i()(`${t}-item`,o),style:s,colSpan:a},n.createElement("div",{className:`${t}-item-container`},(b||0===b)&&n.createElement("span",{className:i()(`${t}-item-label`,null==O?void 0:O.label,{[`${t}-item-no-colon`]:!p}),style:Object.assign(Object.assign({},r),null==f?void 0:f.label)},b),(g||0===g)&&n.createElement("span",{className:i()(`${t}-item-content`,null==O?void 0:O.content),style:Object.assign(Object.assign({},c),null==f?void 0:f.content)},g)))};function j(e,{colon:t,prefixCls:l,bordered:a},{component:o,type:i,showLabel:s,showContent:r,labelStyle:c,contentStyle:d,styles:b}){return e.map(({label:e,children:m,prefixCls:g=l,className:p,style:u,labelStyle:f,contentStyle:O,span:j=1,key:$,styles:h},v)=>"string"==typeof o?n.createElement(y,{key:`${i}-${$||v}`,className:p,style:u,styles:{label:Object.assign(Object.assign(Object.assign(Object.assign({},c),null==b?void 0:b.label),f),null==h?void 0:h.label),content:Object.assign(Object.assign(Object.assign(Object.assign({},d),null==b?void 0:b.content),O),null==h?void 0:h.content)},span:j,colon:t,component:o,itemPrefixCls:g,bordered:a,label:s?e:null,content:r?m:null,type:i}):[n.createElement(y,{key:`label-${$||v}`,className:p,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},c),null==b?void 0:b.label),u),f),null==h?void 0:h.label),span:1,colon:t,component:o[0],itemPrefixCls:g,bordered:a,label:e,type:"label"}),n.createElement(y,{key:`content-${$||v}`,className:p,style:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},d),null==b?void 0:b.content),u),O),null==h?void 0:h.content),span:2*j-1,component:o[1],itemPrefixCls:g,bordered:a,content:m,type:"content"})])}let $=e=>{let t=n.useContext(m),{prefixCls:l,vertical:a,row:o,index:i,bordered:s}=e;return a?n.createElement(n.Fragment,null,n.createElement("tr",{key:`label-${i}`,className:`${l}-row`},j(o,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),n.createElement("tr",{key:`content-${i}`,className:`${l}-row`},j(o,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):n.createElement("tr",{key:i,className:`${l}-row`},j(o,e,Object.assign({component:s?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))};var h=l(92959),v=l(22989),x=l(13165),S=l(96373);let E=e=>{let{componentCls:t,labelBg:l}=e;return{[`&${t}-bordered`]:{[`> ${t}-view`]:{border:`${(0,h.bf)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${t}-row`]:{borderBottom:`${(0,h.bf)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:first-child":{"> th:first-child, > td:first-child":{borderStartStartRadius:e.borderRadiusLG}},"&:last-child":{borderBottom:"none","> th:first-child, > td:first-child":{borderEndStartRadius:e.borderRadiusLG}},[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,h.bf)(e.padding)} ${(0,h.bf)(e.paddingLG)}`,borderInlineEnd:`${(0,h.bf)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${t}-item-label`]:{color:e.colorTextSecondary,backgroundColor:l,"&::after":{display:"none"}}}},[`&${t}-middle`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,h.bf)(e.paddingSM)} ${(0,h.bf)(e.paddingLG)}`}}},[`&${t}-small`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,h.bf)(e.paddingXS)} ${(0,h.bf)(e.padding)}`}}}}}},w=e=>{let{componentCls:t,extraColor:l,itemPaddingBottom:n,itemPaddingEnd:a,colonMarginRight:o,colonMarginLeft:i,titleMarginBottom:s}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,v.Wf)(e)),E(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:s},[`${t}-title`]:Object.assign(Object.assign({},v.vS),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:l,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${t}-row`]:{"> th, > td":{paddingBottom:n,paddingInlineEnd:a},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${t}-item-label`]:{color:e.labelColor,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,h.bf)(i)} ${(0,h.bf)(o)}`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},N=(0,x.I$)("Descriptions",e=>w((0,S.IX)(e,{})),e=>({labelBg:e.colorFillAlter,labelColor:e.colorTextTertiary,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText}));var C=function(e,t){var l={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(l[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,n=Object.getOwnPropertySymbols(e);a<n.length;a++)0>t.indexOf(n[a])&&Object.prototype.propertyIsEnumerable.call(e,n[a])&&(l[n[a]]=e[n[a]]);return l};let I=e=>{let{prefixCls:t,title:l,extra:a,column:o,colon:g=!0,bordered:f,layout:y,children:j,className:h,rootClassName:v,style:x,size:S,labelStyle:E,contentStyle:w,styles:I,items:P,classNames:k}=e,B=C(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","styles","items","classNames"]),{getPrefixCls:L,direction:M,className:z,style:T,classNames:W,styles:G}=(0,r.dj)("descriptions"),H=L("descriptions",t),R=(0,d.Z)(),X=n.useMemo(()=>{var e;return"number"==typeof o?o:null!==(e=(0,s.m9)(R,Object.assign(Object.assign({},b),o)))&&void 0!==e?e:3},[R,o]),Z=function(e,t,l){let a=n.useMemo(()=>t||u(l),[t,l]);return n.useMemo(()=>a.map(t=>{var{span:l}=t,n=p(t,["span"]);return"filled"===l?Object.assign(Object.assign({},n),{filled:!0}):Object.assign(Object.assign({},n),{span:"number"==typeof l?l:(0,s.m9)(e,l)})}),[a,e])}(R,P,j),A=(0,c.Z)(S),F=O(X,Z),[D,q,J]=N(H),K=n.useMemo(()=>({labelStyle:E,contentStyle:w,styles:{content:Object.assign(Object.assign({},G.content),null==I?void 0:I.content),label:Object.assign(Object.assign({},G.label),null==I?void 0:I.label)},classNames:{label:i()(W.label,null==k?void 0:k.label),content:i()(W.content,null==k?void 0:k.content)}}),[E,w,I,k,W,G]);return D(n.createElement(m.Provider,{value:K},n.createElement("div",Object.assign({className:i()(H,z,W.root,null==k?void 0:k.root,{[`${H}-${A}`]:A&&"default"!==A,[`${H}-bordered`]:!!f,[`${H}-rtl`]:"rtl"===M},h,v,q,J),style:Object.assign(Object.assign(Object.assign(Object.assign({},T),G.root),null==I?void 0:I.root),x)},B),(l||a)&&n.createElement("div",{className:i()(`${H}-header`,W.header,null==k?void 0:k.header),style:Object.assign(Object.assign({},G.header),null==I?void 0:I.header)},l&&n.createElement("div",{className:i()(`${H}-title`,W.title,null==k?void 0:k.title),style:Object.assign(Object.assign({},G.title),null==I?void 0:I.title)},l),a&&n.createElement("div",{className:i()(`${H}-extra`,W.extra,null==k?void 0:k.extra),style:Object.assign(Object.assign({},G.extra),null==I?void 0:I.extra)},a)),n.createElement("div",{className:`${H}-view`},n.createElement("table",null,n.createElement("tbody",null,F.map((e,t)=>n.createElement($,{key:t,index:t,colon:g,prefixCls:H,vertical:"vertical"===y,bordered:f,row:e}))))))))};I.Item=({children:e})=>e;let P=I}};