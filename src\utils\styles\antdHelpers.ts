/**
 * Ant Design样式增强工具函数
 * 用于Tailwind CSS迁移到Ant Design的过渡期间
 * 提供统一的样式常量和工具函数
 */

export const styleHelpers: {
  spacing: Record<string, number>
  shadows: Record<string, string>
  borderRadius: Record<string, number | string>
  colors: {
    primary: Record<number, string>
    gray: Record<number, string>
  }
  breakpoints: Record<string, number>
  zIndex: Record<string, number>
  transitions: Record<string, string>
  fonts: Record<string, string>
  lineHeights: Record<string, number>
  letterSpacing: Record<string, string>
} = {
  // 间距工具 - 对应Tailwind的spacing scale
  spacing: {
    xs: 4,   // 对应 p-1
    sm: 8,   // 对应 p-2  
    md: 16,  // 对应 p-4
    lg: 24,  // 对应 p-6
    xl: 32,  // 对应 p-8
    xxl: 48, // 对应 p-12
  },
  
  // 阴影工具 - 对应Tailwind的shadow utilities
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',      // shadow-sm
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',    // shadow-md
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',  // shadow-lg
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',  // shadow-xl
  },
  
  // 圆角工具 - 对应Tailwind的border-radius
  borderRadius: {
    sm: 4,   // rounded-sm
    md: 8,   // rounded-md
    lg: 12,  // rounded-lg
    xl: 16,  // rounded-xl
  },
  
  // 颜色工具 - 对应项目中使用的颜色
  colors: {
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe', 
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  
  // 生成内联样式的工具函数
  createSpacing: (size: keyof typeof styleHelpers.spacing) => ({
    padding: styleHelpers.spacing[size]
  }),
  
  createMargin: (size: keyof typeof styleHelpers.spacing) => ({
    margin: styleHelpers.spacing[size]
  }),
  
  createShadow: (size: keyof typeof styleHelpers.shadows) => ({
    boxShadow: styleHelpers.shadows[size]
  }),
  
  createBorderRadius: (size: keyof typeof styleHelpers.borderRadius) => ({
    borderRadius: styleHelpers.borderRadius[size]
  }),
  
  // 常用的组合样式
  cardStyle: {
    borderRadius: 12,
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
    border: '1px solid #f0f0f0',
  },
  
  buttonStyle: {
    borderRadius: 8,
    transition: 'all 0.2s ease-in-out',
  },
  
  layoutStyle: {
    minHeight: '100vh',
    background: '#f9fafb',
  },
  
  sidebarStyle: {
    background: '#ffffff',
    boxShadow: '2px 0 8px 0 rgba(29, 35, 41, 0.05)',
    borderRight: '1px solid #f0f0f0',
  },
  
  headerStyle: {
    background: '#ffffff',
    borderBottom: '1px solid #f0f0f0',
    boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  },
  
  // 响应式断点 - 对应Tailwind的breakpoints
  breakpoints: {
    sm: '640px',
    md: '768px', 
    lg: '1024px',
    xl: '1280px',
    xxl: '1536px',
  },
  
  // 过渡动画
  transitions: {
    fast: 'all 0.15s ease-in-out',
    normal: 'all 0.2s ease-in-out',
    slow: 'all 0.3s ease-in-out',
  },
}

// 类型定义
export type SpacingSize = keyof typeof styleHelpers.spacing
export type ShadowSize = keyof typeof styleHelpers.shadows
export type BorderRadiusSize = keyof typeof styleHelpers.borderRadius
export type ColorShade = keyof typeof styleHelpers.colors.gray

// 工具函数：将Tailwind类名转换为内联样式
export const tailwindToStyle = {
  // 间距转换
  'p-1': { padding: styleHelpers.spacing.xs },
  'p-2': { padding: styleHelpers.spacing.sm },
  'p-3': { padding: 12 },
  'p-4': { padding: styleHelpers.spacing.md },
  'p-6': { padding: styleHelpers.spacing.lg },
  'p-8': { padding: styleHelpers.spacing.xl },
  
  // 边距转换
  'm-1': { margin: styleHelpers.spacing.xs },
  'm-2': { margin: styleHelpers.spacing.sm },
  'm-4': { margin: styleHelpers.spacing.md },
  'm-6': { margin: styleHelpers.spacing.lg },
  
  // 布局转换
  'min-h-screen': { minHeight: '100vh' },
  'h-full': { height: '100%' },
  'w-full': { width: '100%' },
  
  // 背景色转换
  'bg-white': { background: '#ffffff' },
  'bg-gray-50': { background: styleHelpers.colors.gray[50] },
  'bg-gray-100': { background: styleHelpers.colors.gray[100] },
  'bg-gray-200': { background: styleHelpers.colors.gray[200] },
  
  // 边框转换
  'border': { border: '1px solid #e5e7eb' },
  'border-gray-200': { borderColor: styleHelpers.colors.gray[200] },
  'border-b': { borderBottom: '1px solid #e5e7eb' },
  'border-r': { borderRight: '1px solid #e5e7eb' },
  
  // 阴影转换
  'shadow-sm': { boxShadow: styleHelpers.shadows.sm },
  'shadow-md': { boxShadow: styleHelpers.shadows.md },
  'shadow-lg': { boxShadow: styleHelpers.shadows.lg },
  
  // 圆角转换
  'rounded': { borderRadius: styleHelpers.borderRadius.sm },
  'rounded-md': { borderRadius: styleHelpers.borderRadius.md },
  'rounded-lg': { borderRadius: styleHelpers.borderRadius.lg },
  
  // 过渡转换
  'transition-all': { transition: styleHelpers.transitions.normal },
  'duration-200': { transitionDuration: '200ms' },
  'duration-300': { transitionDuration: '300ms' },
}

// 辅助函数：合并多个样式对象
export const mergeStyles = (...styles: Array<React.CSSProperties | undefined>): React.CSSProperties => {
  return Object.assign({}, ...styles.filter(Boolean))
}

// 辅助函数：根据条件应用样式
export const conditionalStyle = (
  condition: boolean, 
  trueStyle: React.CSSProperties, 
  falseStyle?: React.CSSProperties
): React.CSSProperties => {
  return condition ? trueStyle : (falseStyle || {})
}

export default styleHelpers
