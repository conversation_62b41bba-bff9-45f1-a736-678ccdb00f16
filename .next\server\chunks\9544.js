"use strict";exports.id=9544,exports.ids=[9544],exports.modules={55741:(e,t,a)=>{a.d(t,{Z:()=>i});var r=a(65651),s=a(3729);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};var o=a(49809);let i=s.forwardRef(function(e,t){return s.createElement(o.Z,(0,r.Z)({},e,{ref:t,icon:n}))})},76717:(e,t,a)=>{a.d(t,{Z:()=>i});var r=a(65651),s=a(3729);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6a25.95 25.95 0 0025.6 30.4h723c1.5 0 3-.1 4.4-.4a25.88 25.88 0 0021.2-30zM204 390h272V182h72v208h272v104H204V390zm468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z"}}]},name:"clear",theme:"outlined"};var o=a(49809);let i=s.forwardRef(function(e,t){return s.createElement(o.Z,(0,r.Z)({},e,{ref:t,icon:n}))})},15595:(e,t,a)=>{a.d(t,{Z:()=>i});var r=a(65651),s=a(3729);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"};var o=a(49809);let i=s.forwardRef(function(e,t){return s.createElement(o.Z,(0,r.Z)({},e,{ref:t,icon:n}))})},16407:(e,t,a)=>{a.d(t,{ZP:()=>b});var r=a(72375),s=a(3729),n=a.n(s),o=a(13743),i=a(84893),c=a(90263),l=a(73101),u=a(727),d=a(11779),h=a(47190);let g=null,v=e=>e(),f=[],p={};function w(){let{getContainer:e,duration:t,rtl:a,maxCount:r,top:s}=p,n=(null==e?void 0:e())||document.body;return{getContainer:()=>n,duration:t,rtl:a,maxCount:r,top:s}}let m=n().forwardRef((e,t)=>{let{messageConfig:a,sync:r}=e,{getPrefixCls:c}=(0,s.useContext)(i.E_),l=p.prefixCls||c("message"),u=(0,s.useContext)(o.J),[h,g]=(0,d.K)(Object.assign(Object.assign(Object.assign({},a),{prefixCls:l}),u.message));return n().useImperativeHandle(t,()=>{let e=Object.assign({},h);return Object.keys(e).forEach(t=>{e[t]=(...e)=>(r(),h[t].apply(h,e))}),{instance:e,sync:r}}),g}),k=n().forwardRef((e,t)=>{let[a,r]=n().useState(w),s=()=>{r(w)};n().useEffect(s,[]);let o=(0,c.w6)(),i=o.getRootPrefixCls(),l=o.getIconPrefixCls(),u=o.getTheme(),d=n().createElement(m,{ref:t,sync:s,messageConfig:a});return n().createElement(c.ZP,{prefixCls:i,iconPrefixCls:l,theme:u},o.holderRender?o.holderRender(d):d)});function y(){if(!g){let e=document.createDocumentFragment(),t={fragment:e};g=t,v(()=>{(0,l.q)()(n().createElement(k,{ref:e=>{let{instance:a,sync:r}=e||{};Promise.resolve().then(()=>{!t.instance&&a&&(t.instance=a,t.sync=r,y())})}}),e)});return}g.instance&&(f.forEach(e=>{let{type:t,skipped:a}=e;if(!a)switch(t){case"open":v(()=>{let t=g.instance.open(Object.assign(Object.assign({},p),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":v(()=>{null==g||g.instance.destroy(e.key)});break;default:v(()=>{var a;let s=(a=g.instance)[t].apply(a,(0,r.Z)(e.args));null==s||s.then(e.resolve),e.setCloseFn(s)})}}),f=[])}let S={open:function(e){let t=(0,h.J)(t=>{let a;let r={type:"open",config:e,resolve:t,setCloseFn:e=>{a=e}};return f.push(r),()=>{a?v(()=>{a()}):r.skipped=!0}});return y(),t},destroy:e=>{f.push({type:"destroy",key:e}),y()},config:function(e){p=Object.assign(Object.assign({},p),e),v(()=>{var e;null===(e=null==g?void 0:g.sync)||void 0===e||e.call(g)})},useMessage:d.Z,_InternalPanelDoNotUseOrYouWillBeFired:u.ZP};["success","info","warning","error","loading"].forEach(e=>{S[e]=(...t)=>(function(e,t){(0,c.w6)();let a=(0,h.J)(a=>{let r;let s={type:e,args:t,resolve:a,setCloseFn:e=>{r=e}};return f.push(s),()=>{r?v(()=>{r()}):s.skipped=!0}});return y(),a})(e,t)});let b=S},27392:(e,t,a)=>{a.d(t,{J:()=>s,r:()=>n});var r=a(23218);class s{constructor(){this.workstationService=r.D.getInstance()}static getInstance(){return s.instance||(s.instance=new s),s.instance}async updateWorkstation(e,t,a,r){let s=Date.now();try{console.log(`🔄 [WorkstationUpdateService] 开始更新工位 ${e}`,{source:a.source,operation:a.operation,expectedVersion:r,reason:a.reason});let n=await this.validatePermissions(e,a);if(!n.allowed)return{success:!1,error:`权限不足: ${n.reason}`};let o=await this.validateBusinessRules(e,t,a);if(!o.valid)return{success:!1,error:`业务规则验证失败: ${o.reason}`,warnings:o.warnings};let i=this.getModifiedBy(a),c=await this.workstationService.update(e,t,r,i);if("success"!==c.status){if(c.code&&"VERSION_CONFLICT"===c.code.toString()){let e=c.data;return{success:!1,error:c.message||"版本冲突",conflictInfo:{expectedVersion:r||0,currentVersion:e?.currentVersion||0,lastModifiedBy:"unknown",lastModifiedAt:new Date().toISOString()}}}return{success:!1,error:c.message||"更新失败"}}return c.data&&await this.logUpdateOperation(e,t,a,c.data,s),await this.clearWorkstationCaches(e,a.operation),await this.clearDataAccessManagerCaches(),c.data&&await this.triggerPostUpdateActions(e,t,a,c.data),console.log(`✅ [WorkstationUpdateService] 工位 ${e} 更新成功`,{duration:Date.now()-s,newVersion:c.data?.version}),{success:!0,workstation:c.data,warnings:o.warnings}}catch(t){return console.error(`❌ [WorkstationUpdateService] 工位 ${e} 更新失败:`,t),{success:!1,error:t instanceof Error?t.message:"未知错误"}}}async batchUpdateWorkstations(e,t){console.log(`🔄 [WorkstationUpdateService] 开始批量更新 ${e.length} 个工位`);let a=[],r=0,s=0,n=0,o=0,i=0;for(let c of e){let e=await this.updateWorkstation(c.workstationId,c.updates,t,c.expectedVersion);a.push({workstationId:c.workstationId,result:e}),e.success?r++:(s++,e.conflictInfo?n++:o++),e.warnings&&e.warnings.length>0&&i++}let c={totalCount:e.length,successCount:r,failureCount:s,results:a,summary:{conflicts:n,errors:o,warnings:i}};return console.log(`✅ [WorkstationUpdateService] 批量更新完成`,c.summary),c}async validatePermissions(e,t){switch(t.source){case"user":if(!t.userId)return{allowed:!1,reason:"用户操作必须提供用户ID"};break;case"system":case"scheduling":case"batch":break;default:return{allowed:!1,reason:"未知的更新来源"}}return{allowed:!0}}async validateBusinessRules(e,t,a){let r=[],s=await this.workstationService.getWorkstationById(e);if("success"!==s.status)return{valid:!1,reason:"无法获取当前工位状态"};let n=s.data;if(!n)return{valid:!1,reason:"工位数据不存在"};if(t.status&&t.status!==n.status){let e=this.validateStatusTransition(n.status,t.status,a);if(!e.valid)return{valid:!1,reason:e.reason};e.warnings&&r.push(...e.warnings)}if(this.hasProductionStateChanges(t)){let e=this.validateProductionStateChanges(n,t,a);if(!e.valid)return{valid:!1,reason:e.reason};e.warnings&&r.push(...e.warnings)}return{valid:!0,warnings:r.length>0?r:void 0}}validateStatusTransition(e,t,a){return({active:["inactive"],inactive:["active"]})[e]?.includes(t)?{valid:!0}:{valid:!1,reason:`不允许从状态 ${e} 转换到 ${t}`}}hasProductionStateChanges(e){return!(void 0===e.currentMoldNumber&&void 0===e.currentBatchNumber&&void 0===e.batchNumberQueue&&void 0===e.lastEndTime)}validateProductionStateChanges(e,t,a){let r=[];return"scheduling"!==a.source&&"system"!==a.source?{valid:!1,reason:"只有排程系统可以修改工位的生产状态"}:(t.currentMoldNumber&&t.currentBatchNumber&&r.push("请确认模具编号与批次号匹配"),{valid:!0,warnings:r.length>0?r:void 0})}getModifiedBy(e){switch(e.source){case"user":return e.userId||"unknown_user";case"system":return"system";case"scheduling":return"scheduling_service";case"batch":return"batch_operation";default:return"unknown"}}async logUpdateOperation(e,t,a,r,s){console.log(`📝 [WorkstationUpdateService] 审计日志:`,{timestamp:new Date().toISOString(),workstationId:e,operation:a.operation,source:a.source,userId:a.userId,reason:a.reason,updates:Object.keys(t),newVersion:r.version,duration:Date.now()-s,metadata:a.metadata})}async clearWorkstationCaches(e,t){try{let{dataAccessManager:t}=a(37637);t.clearDataTypeCache("workstations",[e]),t.clearServiceCache("WorkstationService")}catch(t){console.error(`[WorkstationUpdateService] 清除工位 ${e} 缓存失败:`,t)}}async clearDataAccessManagerCaches(){try{let{dataAccessManager:e}=await Promise.resolve().then(a.bind(a,37637)),t=0;["WorkstationService:*","WorkstationService.*"].forEach(a=>{let r=e.clearServiceCache("WorkstationService");t+=r});let r=e.clearDataTypeCache("workstations");t+=r,console.log(`[WorkstationUpdateService] 清理DataAccessManager缓存: ${t} 个条目`)}catch(e){console.error("[WorkstationUpdateService] 清理DataAccessManager缓存失败:",e)}}async triggerPostUpdateActions(e,t,a,r){console.log(`🔔 [WorkstationUpdateService] 触发后置处理: ${e}`)}}let n=s.getInstance()},18223:(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});var r=a(25036),s=a(38834);function n({children:e}){return r.jsx(s.Z,{children:e})}}};