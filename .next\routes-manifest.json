{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin/data-management", "regex": "^/admin/data\\-management(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/data\\-management(?:/)?$"}, {"page": "/admin/integration-test", "regex": "^/admin/integration\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/integration\\-test(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/data-status", "regex": "^/data\\-status(?:/)?$", "routeKeys": {}, "namedRegex": "^/data\\-status(?:/)?$"}, {"page": "/finance", "regex": "^/finance(?:/)?$", "routeKeys": {}, "namedRegex": "^/finance(?:/)?$"}, {"page": "/master-data", "regex": "^/master\\-data(?:/)?$", "routeKeys": {}, "namedRegex": "^/master\\-data(?:/)?$"}, {"page": "/master-data/employees", "regex": "^/master\\-data/employees(?:/)?$", "routeKeys": {}, "namedRegex": "^/master\\-data/employees(?:/)?$"}, {"page": "/master-data/materials", "regex": "^/master\\-data/materials(?:/)?$", "routeKeys": {}, "namedRegex": "^/master\\-data/materials(?:/)?$"}, {"page": "/master-data/product-models", "regex": "^/master\\-data/product\\-models(?:/)?$", "routeKeys": {}, "namedRegex": "^/master\\-data/product\\-models(?:/)?$"}, {"page": "/procurement", "regex": "^/procurement(?:/)?$", "routeKeys": {}, "namedRegex": "^/procurement(?:/)?$"}, {"page": "/production", "regex": "^/production(?:/)?$", "routeKeys": {}, "namedRegex": "^/production(?:/)?$"}, {"page": "/production/cost-calculation", "regex": "^/production/cost\\-calculation(?:/)?$", "routeKeys": {}, "namedRegex": "^/production/cost\\-calculation(?:/)?$"}, {"page": "/production/hot-press-board", "regex": "^/production/hot\\-press\\-board(?:/)?$", "routeKeys": {}, "namedRegex": "^/production/hot\\-press\\-board(?:/)?$"}, {"page": "/production/orders", "regex": "^/production/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/production/orders(?:/)?$"}, {"page": "/production/queue-cleaner", "regex": "^/production/queue\\-cleaner(?:/)?$", "routeKeys": {}, "namedRegex": "^/production/queue\\-cleaner(?:/)?$"}, {"page": "/production/work-report", "regex": "^/production/work\\-report(?:/)?$", "routeKeys": {}, "namedRegex": "^/production/work\\-report(?:/)?$"}, {"page": "/sales/after-sales", "regex": "^/sales/after\\-sales(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales/after\\-sales(?:/)?$"}, {"page": "/sales/analytics", "regex": "^/sales/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales/analytics(?:/)?$"}, {"page": "/sales/credit", "regex": "^/sales/credit(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales/credit(?:/)?$"}, {"page": "/sales/customers", "regex": "^/sales/customers(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales/customers(?:/)?$"}, {"page": "/sales/delivery", "regex": "^/sales/delivery(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales/delivery(?:/)?$"}, {"page": "/sales/invoices", "regex": "^/sales/invoices(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales/invoices(?:/)?$"}, {"page": "/sales/orders", "regex": "^/sales/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales/orders(?:/)?$"}, {"page": "/sales/payments", "regex": "^/sales/payments(?:/)?$", "routeKeys": {}, "namedRegex": "^/sales/payments(?:/)?$"}, {"page": "/warehouse", "regex": "^/warehouse(?:/)?$", "routeKeys": {}, "namedRegex": "^/warehouse(?:/)?$"}, {"page": "/warehouse/materials", "regex": "^/warehouse/materials(?:/)?$", "routeKeys": {}, "namedRegex": "^/warehouse/materials(?:/)?$"}, {"page": "/warehouse/products", "regex": "^/warehouse/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/warehouse/products(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}