(()=>{var e={};e.id=1887,e.ids=[1887],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},37981:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var o=r(50482),n=r(69108),a=r(62563),l=r.n(a),s=r(68300),i={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>s[e]);r.d(t,i);let c=["",{children:["production",{children:["queue-cleaner",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,23418)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\queue-cleaner\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,18223)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\queue-cleaner\\page.tsx"],u="/production/queue-cleaner/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/production/queue-cleaner/page",pathname:"/production/queue-cleaner",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},73394:(e,t,r)=>{Promise.resolve().then(r.bind(r,93682))},3745:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var o=r(65651),n=r(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var l=r(49809);let s=n.forwardRef(function(e,t){return n.createElement(l.Z,(0,o.Z)({},e,{ref:t,icon:a}))})},14223:(e,t,r)=>{"use strict";r.d(t,{Z:()=>_});var o=r(3729),n=r(33795),a=r(57629),l=r(32066),s=r(2523),i=r(29513),c=r(34132),d=r.n(c),u=r(27335),p=r(7305),m=r(67862),g=r(29545),h=r(84893),f=r(92959),b=r(22989),v=r(13165);let x=(e,t,r,o,n)=>({background:e,border:`${(0,f.bf)(o.lineWidth)} ${o.lineType} ${t}`,[`${n}-icon`]:{color:r}}),y=e=>{let{componentCls:t,motionDurationSlow:r,marginXS:o,marginSM:n,fontSize:a,fontSizeLG:l,lineHeight:s,borderRadiusLG:i,motionEaseInOutCirc:c,withDescriptionIconSize:d,colorText:u,colorTextHeading:p,withDescriptionPadding:m,defaultPadding:g}=e;return{[t]:Object.assign(Object.assign({},(0,b.Wf)(e)),{position:"relative",display:"flex",alignItems:"center",padding:g,wordWrap:"break-word",borderRadius:i,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:o,lineHeight:0},"&-description":{display:"none",fontSize:a,lineHeight:s},"&-message":{color:p},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${r} ${c}, opacity ${r} ${c},
        padding-top ${r} ${c}, padding-bottom ${r} ${c},
        margin-bottom ${r} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:m,[`${t}-icon`]:{marginInlineEnd:n,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:o,color:p,fontSize:l},[`${t}-description`]:{display:"block",color:u}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},k=e=>{let{componentCls:t,colorSuccess:r,colorSuccessBorder:o,colorSuccessBg:n,colorWarning:a,colorWarningBorder:l,colorWarningBg:s,colorError:i,colorErrorBorder:c,colorErrorBg:d,colorInfo:u,colorInfoBorder:p,colorInfoBg:m}=e;return{[t]:{"&-success":x(n,o,r,e,t),"&-info":x(m,p,u,e,t),"&-warning":x(s,l,a,e,t),"&-error":Object.assign(Object.assign({},x(d,c,i,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},$=e=>{let{componentCls:t,iconCls:r,motionDurationMid:o,marginXS:n,fontSizeIcon:a,colorIcon:l,colorIconHover:s}=e;return{[t]:{"&-action":{marginInlineStart:n},[`${t}-close-icon`]:{marginInlineStart:n,padding:0,overflow:"hidden",fontSize:a,lineHeight:(0,f.bf)(a),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${r}-close`]:{color:l,transition:`color ${o}`,"&:hover":{color:s}}},"&-close-text":{color:l,transition:`color ${o}`,"&:hover":{color:s}}}}},j=(0,v.I$)("Alert",e=>[y(e),k(e),$(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var I=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let w={success:n.Z,info:i.Z,error:a.Z,warning:s.Z},C=e=>{let{icon:t,prefixCls:r,type:n}=e,a=w[n]||null;return t?(0,g.wm)(t,o.createElement("span",{className:`${r}-icon`},t),()=>({className:d()(`${r}-icon`,t.props.className)})):o.createElement(a,{className:`${r}-icon`})},Z=e=>{let{isClosable:t,prefixCls:r,closeIcon:n,handleClose:a,ariaProps:s}=e,i=!0===n||void 0===n?o.createElement(l.Z,null):n;return t?o.createElement("button",Object.assign({type:"button",onClick:a,className:`${r}-close-icon`,tabIndex:0},s),i):null},S=o.forwardRef((e,t)=>{let{description:r,prefixCls:n,message:a,banner:l,className:s,rootClassName:i,style:c,onMouseEnter:g,onMouseLeave:f,onClick:b,afterClose:v,showIcon:x,closable:y,closeText:k,closeIcon:$,action:w,id:S}=e,O=I(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[E,P]=o.useState(!1),N=o.useRef(null);o.useImperativeHandle(t,()=>({nativeElement:N.current}));let{getPrefixCls:W,direction:q,closable:M,closeIcon:_,className:B,style:L}=(0,h.dj)("alert"),z=W("alert",n),[D,H,Q]=j(z),T=t=>{var r;P(!0),null===(r=e.onClose)||void 0===r||r.call(e,t)},A=o.useMemo(()=>void 0!==e.type?e.type:l?"warning":"info",[e.type,l]),R=o.useMemo(()=>"object"==typeof y&&!!y.closeIcon||!!k||("boolean"==typeof y?y:!1!==$&&null!=$||!!M),[k,$,y,M]),G=!!l&&void 0===x||x,U=d()(z,`${z}-${A}`,{[`${z}-with-description`]:!!r,[`${z}-no-icon`]:!G,[`${z}-banner`]:!!l,[`${z}-rtl`]:"rtl"===q},B,s,i,Q,H),F=(0,p.Z)(O,{aria:!0,data:!0}),X=o.useMemo(()=>"object"==typeof y&&y.closeIcon?y.closeIcon:k||(void 0!==$?$:"object"==typeof M&&M.closeIcon?M.closeIcon:_),[$,y,k,_]),K=o.useMemo(()=>{let e=null!=y?y:M;if("object"==typeof e){let{closeIcon:t}=e;return I(e,["closeIcon"])}return{}},[y,M]);return D(o.createElement(u.ZP,{visible:!E,motionName:`${z}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:v},({className:t,style:n},l)=>o.createElement("div",Object.assign({id:S,ref:(0,m.sQ)(N,l),"data-show":!E,className:d()(U,t),style:Object.assign(Object.assign(Object.assign({},L),c),n),onMouseEnter:g,onMouseLeave:f,onClick:b,role:"alert"},F),G?o.createElement(C,{description:r,icon:e.icon,prefixCls:z,type:A}):null,o.createElement("div",{className:`${z}-content`},a?o.createElement("div",{className:`${z}-message`},a):null,r?o.createElement("div",{className:`${z}-description`},r):null),w?o.createElement("div",{className:`${z}-action`},w):null,o.createElement(Z,{isClosable:R,prefixCls:z,closeIcon:X,handleClose:T,ariaProps:K}))))});var O=r(31475),E=r(24142),P=r(61792),N=r(50804),W=r(6392),q=r(94977);let M=function(e){function t(){var e,r,o;return(0,O.Z)(this,t),r=t,o=arguments,r=(0,P.Z)(r),(e=(0,W.Z)(this,(0,N.Z)()?Reflect.construct(r,o||[],(0,P.Z)(this).constructor):r.apply(this,o))).state={error:void 0,info:{componentStack:""}},e}return(0,q.Z)(t,e),(0,E.Z)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:r,children:n}=this.props,{error:a,info:l}=this.state,s=(null==l?void 0:l.componentStack)||null,i=void 0===e?(a||"").toString():e;return a?o.createElement(S,{id:r,type:"error",message:i,description:o.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?s:t)}):n}}])}(o.Component);S.ErrorBoundary=M;let _=S},90377:(e,t,r)=>{"use strict";r.d(t,{Z:()=>E});var o=r(3729),n=r(34132),a=r.n(n),l=r(24773),s=r(22624),i=r(46164),c=r(29545),d=r(30605),u=r(84893),p=r(92959),m=r(55002),g=r(22989),h=r(96373),f=r(13165);let b=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:o,componentCls:n,calc:a}=e,l=a(o).sub(r).equal(),s=a(t).sub(r).equal();return{[n]:Object.assign(Object.assign({},(0,g.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,p.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${n}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${n}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${n}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${n}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${n}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},v=e=>{let{lineWidth:t,fontSizeIcon:r,calc:o}=e,n=e.fontSizeSM;return(0,h.IX)(e,{tagFontSize:n,tagLineHeight:(0,p.bf)(o(e.lineHeightSM).mul(n).equal()),tagIconSize:o(r).sub(o(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},x=e=>({defaultBg:new m.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),y=(0,f.I$)("Tag",e=>b(v(e)),x);var k=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let $=o.forwardRef((e,t)=>{let{prefixCls:r,style:n,className:l,checked:s,onChange:i,onClick:c}=e,d=k(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:p,tag:m}=o.useContext(u.E_),g=p("tag",r),[h,f,b]=y(g),v=a()(g,`${g}-checkable`,{[`${g}-checkable-checked`]:s},null==m?void 0:m.className,l,f,b);return h(o.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},n),null==m?void 0:m.style),className:v,onClick:e=>{null==i||i(!s),null==c||c(e)}})))});var j=r(78701);let I=e=>(0,j.Z)(e,(t,{textColor:r,lightBorderColor:o,lightColor:n,darkColor:a})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:n,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),w=(0,f.bk)(["Tag","preset"],e=>I(v(e)),x),C=(e,t,r)=>{let o=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${o}Bg`],borderColor:e[`color${o}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},Z=(0,f.bk)(["Tag","status"],e=>{let t=v(e);return[C(t,"success","Success"),C(t,"processing","Info"),C(t,"error","Error"),C(t,"warning","Warning")]},x);var S=function(e,t){var r={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(r[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,o=Object.getOwnPropertySymbols(e);n<o.length;n++)0>t.indexOf(o[n])&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(r[o[n]]=e[o[n]]);return r};let O=o.forwardRef((e,t)=>{let{prefixCls:r,className:n,rootClassName:p,style:m,children:g,icon:h,color:f,onClose:b,bordered:v=!0,visible:x}=e,k=S(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:$,direction:j,tag:I}=o.useContext(u.E_),[C,O]=o.useState(!0),E=(0,l.Z)(k,["closeIcon","closable"]);o.useEffect(()=>{void 0!==x&&O(x)},[x]);let P=(0,s.o2)(f),N=(0,s.yT)(f),W=P||N,q=Object.assign(Object.assign({backgroundColor:f&&!W?f:void 0},null==I?void 0:I.style),m),M=$("tag",r),[_,B,L]=y(M),z=a()(M,null==I?void 0:I.className,{[`${M}-${f}`]:W,[`${M}-has-color`]:f&&!W,[`${M}-hidden`]:!C,[`${M}-rtl`]:"rtl"===j,[`${M}-borderless`]:!v},n,p,B,L),D=e=>{e.stopPropagation(),null==b||b(e),e.defaultPrevented||O(!1)},[,H]=(0,i.Z)((0,i.w)(e),(0,i.w)(I),{closable:!1,closeIconRender:e=>{let t=o.createElement("span",{className:`${M}-close-icon`,onClick:D},e);return(0,c.wm)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),D(t)},className:a()(null==e?void 0:e.className,`${M}-close-icon`)}))}}),Q="function"==typeof k.onClick||g&&"a"===g.type,T=h||null,A=T?o.createElement(o.Fragment,null,T,g&&o.createElement("span",null,g)):g,R=o.createElement("span",Object.assign({},E,{ref:t,className:z,style:q}),A,H,P&&o.createElement(w,{key:"preset",prefixCls:M}),N&&o.createElement(Z,{key:"status",prefixCls:M}));return _(Q?o.createElement(d.Z,{component:"Tag"},R):R)});O.CheckableTag=$;let E=O},93682:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var o=r(95344),n=r(3729),a=r(90152),l=r(32979),s=r(16407),i=r(90377),c=r(83984),d=r(10707),u=r(11157),p=r(63724),m=r(27976),g=r(43896),h=r(14223),f=r(36527),b=r(15595),v=r(76717),x=r(55741),y=r(3745),k=r(37637),$=r(27392);function j(e){return/^PC\d{8}\d{4}$/.test(e)&&14===e.length}async function I(){let e=await k.dataAccessManager.workstations.getActiveWorkstations();if("success"!==e.status||!e.data)return{totalWorkstations:0,workstationsWithQueues:0,totalQueueItems:0,validBatchNumbers:0,invalidWorkOrderIds:0,otherInvalidItems:0,details:[]};let t=e.data,r=0,o=0,n=0,a=0,l=t.map(e=>{let t=e.batchNumberQueue||[],l=[],s=[],i=[];return t.forEach(e=>{(r++,j(e))?(l.push(e),o++):/^wo_\d+_[a-z0-9]+$/.test(e)?(s.push(e),n++):(i.push(e),a++)}),{workstationId:e.id,workstationCode:e.code,workstationName:e.name,queueLength:t.length,validItems:l,invalidWorkOrderIds:s,otherInvalidItems:i}});return{totalWorkstations:t.length,workstationsWithQueues:t.filter(e=>(e.batchNumberQueue?.length||0)>0).length,totalQueueItems:r,validBatchNumbers:o,invalidWorkOrderIds:n,otherInvalidItems:a,details:l}}async function w(){console.warn("⚠️ [workstationQueueCleaner] cleanAllWorkstationQueues 函数已废弃，建议使用dataAccessManager重新实现");let e=await k.dataAccessManager.workstations.getWorkstations();if("success"!==e.status||!e.data?.items)throw Error("获取工位数据失败");let t=e.data.items,r=0,o=0,n=[],a=t.map(async e=>{let t=e.batchNumberQueue||[];if(0===t.length)return null;let n=t.filter(e=>j(e)),a=t.filter(e=>!j(e));if(a.length>0)try{let l=await $.r.updateWorkstation(e.id,{batchNumberQueue:n},{source:"system",operation:"queue_management",userId:"system",reason:"清理工位队列中的无效数据"},e.version);if(l.success)return r++,o+=a.length,{workstationId:e.id,workstationCode:e.code,originalQueueLength:t.length,cleanedQueueLength:n.length,removedItems:a};console.error(`❌ 工位 ${e.code} 队列清理失败:`,l.error)}catch(t){console.error(`❌ 工位 ${e.code} 队列清理异常:`,t)}return null});return n.push(...(await Promise.all(a)).filter(e=>null!==e)),{totalWorkstations:t.length,cleanedWorkstations:r,removedItems:o,cleanupDetails:n}}let{Title:C,Text:Z,Paragraph:S}=a.default;function O(){let{modal:e}=l.Z.useApp(),[t,r]=(0,n.useState)(null),[a,k]=(0,n.useState)(null),[$,j]=(0,n.useState)(!1),[Z,O]=(0,n.useState)(!1),E=()=>{j(!0);try{let e=I();r(e),s.ZP.success("队列数据分析完成")}catch(e){s.ZP.error("分析失败: "+e.message)}finally{j(!1)}};return(0,n.useEffect)(()=>{E()},[]),(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsxs)(C,{level:2,children:[o.jsx(v.Z,{})," 工位队列数据清理工具"]}),o.jsx(S,{children:"此工具用于分析和清理工位队列中的无效数据。工位队列应该只包含有效的批次号格式（PC开头的14位格式）， 而不应包含工单ID格式（wo_开头的格式）。"}),o.jsx(c.Z,{className:"mb-6",children:(0,o.jsxs)(d.Z,{children:[o.jsx(u.ZP,{type:"primary",icon:o.jsx(x.Z,{}),onClick:E,loading:$,children:"分析队列数据"}),o.jsx(u.ZP,{type:"primary",danger:!0,icon:o.jsx(v.Z,{}),onClick:()=>{e.confirm({title:"确认清理队列数据",content:(0,o.jsxs)("div",{children:[o.jsx(S,{children:"此操作将清理所有工位队列中的无效数据（工单ID格式），只保留有效的批次号格式数据。"}),(0,o.jsxs)(S,{type:"warning",children:[o.jsx(b.Z,{})," 此操作不可撤销，请确认继续。"]})]}),onOk:async()=>{O(!0);try{let e=await w();k(e),s.ZP.success(`清理完成！共清理 ${e?.removedItems||0} 个无效项目`),E()}catch(e){s.ZP.error("清理失败: "+e.message)}finally{O(!1)}}})},loading:Z,disabled:!t||0===t.invalidWorkOrderIds,children:"清理无效数据"})]})}),t&&(0,o.jsxs)(c.Z,{title:"队列数据分析结果",className:"mb-6",children:[(0,o.jsxs)(p.Z,{gutter:16,children:[o.jsx(m.Z,{span:6,children:o.jsx(g.Z,{title:"总工位数",value:t.totalWorkstations,prefix:o.jsx(y.Z,{})})}),o.jsx(m.Z,{span:6,children:o.jsx(g.Z,{title:"有队列的工位",value:t.workstationsWithQueues,valueStyle:{color:"#1890ff"}})}),o.jsx(m.Z,{span:6,children:o.jsx(g.Z,{title:"有效批次号",value:t.validBatchNumbers,valueStyle:{color:"#52c41a"}})}),o.jsx(m.Z,{span:6,children:o.jsx(g.Z,{title:"无效工单ID",value:t.invalidWorkOrderIds,valueStyle:{color:t.invalidWorkOrderIds>0?"#ff4d4f":"#52c41a"}})})]}),t.invalidWorkOrderIds>0&&o.jsx(h.Z,{className:"mt-4",message:"发现无效数据",description:`检测到 ${t.invalidWorkOrderIds} 个工单ID格式的无效数据，建议立即清理。`,type:"warning",showIcon:!0}),0===t.invalidWorkOrderIds&&o.jsx(h.Z,{className:"mt-4",message:"数据格式正确",description:"所有工位队列数据格式正确，无需清理。",type:"success",showIcon:!0})]}),t&&o.jsx(c.Z,{title:"工位队列详细分析",className:"mb-6",children:o.jsx(f.Z,{columns:[{title:"工位编码",dataIndex:"workstationCode",key:"workstationCode",width:100},{title:"工位名称",dataIndex:"workstationName",key:"workstationName",width:200},{title:"队列长度",dataIndex:"queueLength",key:"queueLength",width:100,render:e=>o.jsx(i.Z,{color:e>0?"blue":"default",children:e})},{title:"有效批次号",dataIndex:"validItems",key:"validItems",width:120,render:e=>o.jsx(i.Z,{color:"green",children:e.length})},{title:"无效工单ID",dataIndex:"invalidWorkOrderIds",key:"invalidWorkOrderIds",width:120,render:e=>o.jsx(i.Z,{color:e.length>0?"red":"default",children:e.length})},{title:"其他无效项",dataIndex:"otherInvalidItems",key:"otherInvalidItems",width:120,render:e=>o.jsx(i.Z,{color:e.length>0?"orange":"default",children:e.length})}],dataSource:t.details,rowKey:"workstationId",pagination:{pageSize:10},scroll:{x:800}})}),a&&(0,o.jsxs)(c.Z,{title:"清理结果",className:"mb-6",children:[(0,o.jsxs)(p.Z,{gutter:16,className:"mb-4",children:[o.jsx(m.Z,{span:8,children:o.jsx(g.Z,{title:"清理工位数",value:a.cleanedWorkstations,valueStyle:{color:"#52c41a"}})}),o.jsx(m.Z,{span:8,children:o.jsx(g.Z,{title:"清理项目数",value:a.removedItems,valueStyle:{color:"#ff4d4f"}})}),o.jsx(m.Z,{span:8,children:o.jsx(g.Z,{title:"清理完成率",value:a.totalWorkstations>0?Math.round(a.cleanedWorkstations/a.totalWorkstations*100):0,suffix:"%",valueStyle:{color:"#1890ff"}})})]}),a.cleanupDetails.length>0&&o.jsx(f.Z,{columns:[{title:"工位编码",dataIndex:"workstationCode",key:"workstationCode",width:100},{title:"原队列长度",dataIndex:"originalQueueLength",key:"originalQueueLength",width:120},{title:"清理后长度",dataIndex:"cleanedQueueLength",key:"cleanedQueueLength",width:120},{title:"清理项目",dataIndex:"removedItems",key:"removedItems",render:e=>o.jsx("div",{style:{maxWidth:300},children:e.map((e,t)=>o.jsx(i.Z,{color:"red",style:{marginBottom:4},children:e.length>20?`${e.substring(0,20)}...`:e},t))})}],dataSource:a.cleanupDetails,rowKey:"workstationId",pagination:{pageSize:10},scroll:{x:800}})]})]})}function E(){return o.jsx(l.Z,{children:o.jsx(O,{})})}},23418:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>l});let o=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\production\queue-cleaner\page.tsx`),{__esModule:n,$$typeof:a}=o,l=o.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,152,6274,996,6133,9544],()=>r(37981));module.exports=o})();