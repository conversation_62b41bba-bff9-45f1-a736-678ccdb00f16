"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[128],{30128:function(e,t,n){n.d(t,{Z:function(){return B}});var a,o=n(2265),r=n(42744),i=n.n(r),l=n(13428),u=n(21076),s=n(10870),c=n(16141),d=n(98961),f=n(82554),p=n(22766),m=n(88202),v=n(90309),g=n(73310),x=n(60075),h=n(11288),b=n(19836),w=n(43197),y=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","font-variant","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing","word-break","white-space"],S={},z=["prefixCls","defaultValue","value","autoSize","onResize","className","style","disabled","onChange","onInternalAutoSize"],C=o.forwardRef(function(e,t){var n=e.prefixCls,r=e.defaultValue,c=e.value,p=e.autoSize,m=e.onResize,v=e.className,C=e.style,Z=e.disabled,E=e.onChange,A=(e.onInternalAutoSize,(0,f.Z)(e,z)),N=(0,g.Z)(r,{value:c,postState:function(e){return null!=e?e:""}}),I=(0,d.Z)(N,2),R=I[0],O=I[1],j=o.useRef();o.useImperativeHandle(t,function(){return{textArea:j.current}});var H=o.useMemo(function(){return p&&"object"===(0,x.Z)(p)?[p.minRows,p.maxRows]:[]},[p]),F=(0,d.Z)(H,2),P=F[0],T=F[1],V=!!p,k=function(){try{if(document.activeElement===j.current){var e=j.current,t=e.selectionStart,n=e.selectionEnd,a=e.scrollTop;j.current.setSelectionRange(t,n),j.current.scrollTop=a}}catch(e){}},D=o.useState(2),M=(0,d.Z)(D,2),L=M[0],W=M[1],B=o.useState(),_=(0,d.Z)(B,2),q=_[0],K=_[1],X=function(){W(0)};(0,b.Z)(function(){V&&X()},[c,P,T,V]),(0,b.Z)(function(){if(0===L)W(1);else if(1===L){var e=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;a||((a=document.createElement("textarea")).setAttribute("tab-index","-1"),a.setAttribute("aria-hidden","true"),a.setAttribute("name","hiddenTextarea"),document.body.appendChild(a)),e.getAttribute("wrap")?a.setAttribute("wrap",e.getAttribute("wrap")):a.removeAttribute("wrap");var i=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=e.getAttribute("id")||e.getAttribute("data-reactid")||e.getAttribute("name");if(t&&S[n])return S[n];var a=window.getComputedStyle(e),o=a.getPropertyValue("box-sizing")||a.getPropertyValue("-moz-box-sizing")||a.getPropertyValue("-webkit-box-sizing"),r=parseFloat(a.getPropertyValue("padding-bottom"))+parseFloat(a.getPropertyValue("padding-top")),i=parseFloat(a.getPropertyValue("border-bottom-width"))+parseFloat(a.getPropertyValue("border-top-width")),l={sizingStyle:y.map(function(e){return"".concat(e,":").concat(a.getPropertyValue(e))}).join(";"),paddingSize:r,borderSize:i,boxSizing:o};return t&&n&&(S[n]=l),l}(e,n),l=i.paddingSize,u=i.borderSize,s=i.boxSizing,c=i.sizingStyle;a.setAttribute("style","".concat(c,";").concat("\n  min-height:0 !important;\n  max-height:none !important;\n  height:0 !important;\n  visibility:hidden !important;\n  overflow:hidden !important;\n  position:absolute !important;\n  z-index:-1000 !important;\n  top:0 !important;\n  right:0 !important;\n  pointer-events: none !important;\n")),a.value=e.value||e.placeholder||"";var d=void 0,f=void 0,p=a.scrollHeight;if("border-box"===s?p+=u:"content-box"===s&&(p-=l),null!==o||null!==r){a.value=" ";var m=a.scrollHeight-l;null!==o&&(d=m*o,"border-box"===s&&(d=d+l+u),p=Math.max(d,p)),null!==r&&(f=m*r,"border-box"===s&&(f=f+l+u),t=p>f?"":"hidden",p=Math.min(f,p))}var v={height:p,overflowY:t,resize:"none"};return d&&(v.minHeight=d),f&&(v.maxHeight=f),v}(j.current,!1,P,T);W(2),K(e)}else k()},[L]);var J=o.useRef(),Y=function(){w.Z.cancel(J.current)};o.useEffect(function(){return Y},[]);var Q=(0,s.Z)((0,s.Z)({},C),V?q:null);return(0===L||1===L)&&(Q.overflowY="hidden",Q.overflowX="hidden"),o.createElement(h.Z,{onResize:function(e){2===L&&(null==m||m(e),p&&(Y(),J.current=(0,w.Z)(function(){X()})))},disabled:!(p||m)},o.createElement("textarea",(0,l.Z)({},A,{ref:j,style:Q,className:i()(n,v,(0,u.Z)({},"".concat(n,"-disabled"),Z)),disabled:Z,value:R,onChange:function(e){O(e.target.value),null==E||E(e)}})))}),Z=["defaultValue","value","onFocus","onBlur","onChange","allowClear","maxLength","onCompositionStart","onCompositionEnd","suffix","prefixCls","showCount","count","className","style","disabled","hidden","classNames","styles","onResize","onClear","onPressEnter","readOnly","autoSize","onKeyDown"],E=o.forwardRef(function(e,t){var n,a,r=e.defaultValue,x=e.value,h=e.onFocus,b=e.onBlur,w=e.onChange,y=e.allowClear,S=e.maxLength,z=e.onCompositionStart,E=e.onCompositionEnd,A=e.suffix,N=e.prefixCls,I=void 0===N?"rc-textarea":N,R=e.showCount,O=e.count,j=e.className,H=e.style,F=e.disabled,P=e.hidden,T=e.classNames,V=e.styles,k=e.onResize,D=e.onClear,M=e.onPressEnter,L=e.readOnly,W=e.autoSize,B=e.onKeyDown,_=(0,f.Z)(e,Z),q=(0,g.Z)(r,{value:x,defaultValue:r}),K=(0,d.Z)(q,2),X=K[0],J=K[1],Y=null==X?"":String(X),Q=o.useState(!1),$=(0,d.Z)(Q,2),G=$[0],U=$[1],ee=o.useRef(!1),et=o.useState(null),en=(0,d.Z)(et,2),ea=en[0],eo=en[1],er=(0,o.useRef)(null),ei=(0,o.useRef)(null),el=function(){var e;return null===(e=ei.current)||void 0===e?void 0:e.textArea},eu=function(){el().focus()};(0,o.useImperativeHandle)(t,function(){var e;return{resizableTextArea:ei.current,focus:eu,blur:function(){el().blur()},nativeElement:(null===(e=er.current)||void 0===e?void 0:e.nativeElement)||el()}}),(0,o.useEffect)(function(){U(function(e){return!F&&e})},[F]);var es=o.useState(null),ec=(0,d.Z)(es,2),ed=ec[0],ef=ec[1];o.useEffect(function(){if(ed){var e;(e=el()).setSelectionRange.apply(e,(0,c.Z)(ed))}},[ed]);var ep=(0,m.Z)(O,R),em=null!==(n=ep.max)&&void 0!==n?n:S,ev=Number(em)>0,eg=ep.strategy(Y),ex=!!em&&eg>em,eh=function(e,t){var n=t;!ee.current&&ep.exceedFormatter&&ep.max&&ep.strategy(t)>ep.max&&(n=ep.exceedFormatter(t,{max:ep.max}),t!==n&&ef([el().selectionStart||0,el().selectionEnd||0])),J(n),(0,v.rJ)(e.currentTarget,e,w,n)},eb=A;ep.show&&(a=ep.showFormatter?ep.showFormatter({value:Y,count:eg,maxLength:em}):"".concat(eg).concat(ev?" / ".concat(em):""),eb=o.createElement(o.Fragment,null,eb,o.createElement("span",{className:i()("".concat(I,"-data-count"),null==T?void 0:T.count),style:null==V?void 0:V.count},a)));var ew=!W&&!R&&!y;return o.createElement(p.Q,{ref:er,value:Y,allowClear:y,handleReset:function(e){J(""),eu(),(0,v.rJ)(el(),e,w)},suffix:eb,prefixCls:I,classNames:(0,s.Z)((0,s.Z)({},T),{},{affixWrapper:i()(null==T?void 0:T.affixWrapper,(0,u.Z)((0,u.Z)({},"".concat(I,"-show-count"),R),"".concat(I,"-textarea-allow-clear"),y))}),disabled:F,focused:G,className:i()(j,ex&&"".concat(I,"-out-of-range")),style:(0,s.Z)((0,s.Z)({},H),ea&&!ew?{height:"auto"}:{}),dataAttrs:{affixWrapper:{"data-count":"string"==typeof a?a:void 0}},hidden:P,readOnly:L,onClear:D},o.createElement(C,(0,l.Z)({},_,{autoSize:W,maxLength:S,onKeyDown:function(e){"Enter"===e.key&&M&&M(e),null==B||B(e)},onChange:function(e){eh(e,e.target.value)},onFocus:function(e){U(!0),null==h||h(e)},onBlur:function(e){U(!1),null==b||b(e)},onCompositionStart:function(e){ee.current=!0,null==z||z(e)},onCompositionEnd:function(e){ee.current=!1,eh(e,e.currentTarget.value),null==E||E(e)},className:i()(null==T?void 0:T.textarea),style:(0,s.Z)((0,s.Z)({},null==V?void 0:V.textarea),{},{resize:null==H?void 0:H.resize}),disabled:F,prefixCls:I,onResize:function(e){var t;null==k||k(e),null!==(t=el())&&void 0!==t&&t.style.height&&eo(!0)},ref:ei,readOnly:L})))}),A=n(37411),N=n(47794),I=n(57499),R=n(17094),O=n(92935),j=n(10693),H=n(47137),F=n(8443),P=n(92801),T=n(94759),V=n(78387),k=n(12711),D=n(85980);let M=e=>{let{componentCls:t,paddingLG:n}=e,a="".concat(t,"-textarea");return{["textarea".concat(t)]:{maxWidth:"100%",height:"auto",minHeight:e.controlHeight,lineHeight:e.lineHeight,verticalAlign:"bottom",transition:"all ".concat(e.motionDurationSlow),resize:"vertical",["&".concat(t,"-mouse-active")]:{transition:"all ".concat(e.motionDurationSlow,", height 0s, width 0s")}},["".concat(t,"-textarea-affix-wrapper-resize-dirty")]:{width:"auto"},[a]:{position:"relative","&-show-count":{["".concat(t,"-data-count")]:{position:"absolute",bottom:e.calc(e.fontSize).mul(e.lineHeight).mul(-1).equal(),insetInlineEnd:0,color:e.colorTextDescription,whiteSpace:"nowrap",pointerEvents:"none"}},["\n        &-allow-clear > ".concat(t,",\n        &-affix-wrapper").concat(a,"-has-feedback ").concat(t,"\n      ")]:{paddingInlineEnd:n},["&-affix-wrapper".concat(t,"-affix-wrapper")]:{padding:0,["> textarea".concat(t)]:{fontSize:"inherit",border:"none",outline:"none",background:"transparent",minHeight:e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),"&:focus":{boxShadow:"none !important"}},["".concat(t,"-suffix")]:{margin:0,"> *:not(:last-child)":{marginInline:0},["".concat(t,"-clear-icon")]:{position:"absolute",insetInlineEnd:e.paddingInline,insetBlockStart:e.paddingXS},["".concat(a,"-suffix")]:{position:"absolute",top:0,insetInlineEnd:e.paddingInline,bottom:0,zIndex:1,display:"inline-flex",alignItems:"center",margin:"auto",pointerEvents:"none"}}},["&-affix-wrapper".concat(t,"-affix-wrapper-rtl")]:{["".concat(t,"-suffix")]:{["".concat(t,"-data-count")]:{direction:"ltr",insetInlineStart:0}}},["&-affix-wrapper".concat(t,"-affix-wrapper-sm")]:{["".concat(t,"-suffix")]:{["".concat(t,"-clear-icon")]:{insetInlineEnd:e.paddingInlineSM}}}}}};var L=(0,V.I$)(["Input","TextArea"],e=>[M((0,k.IX)(e,(0,D.e)(e)))],D.T,{resetFont:!1}),W=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&0>t.indexOf(a)&&(n[a]=e[a]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(e);o<a.length;o++)0>t.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(e,a[o])&&(n[a[o]]=e[a[o]]);return n},B=(0,o.forwardRef)((e,t)=>{var n;let{prefixCls:a,bordered:r=!0,size:l,disabled:u,status:s,allowClear:c,classNames:d,rootClassName:f,className:p,style:m,styles:g,variant:x,showCount:h,onMouseDown:b,onResize:w}=e,y=W(e,["prefixCls","bordered","size","disabled","status","allowClear","classNames","rootClassName","className","style","styles","variant","showCount","onMouseDown","onResize"]),{getPrefixCls:S,direction:z,allowClear:C,autoComplete:Z,className:V,style:k,classNames:D,styles:M}=(0,I.dj)("textArea"),B=o.useContext(R.Z),{status:_,hasFeedback:q,feedbackIcon:K}=o.useContext(H.aM),X=(0,N.F)(_,s),J=o.useRef(null);o.useImperativeHandle(t,()=>{var e;return{resizableTextArea:null===(e=J.current)||void 0===e?void 0:e.resizableTextArea,focus:e=>{var t,n;(0,v.nH)(null===(n=null===(t=J.current)||void 0===t?void 0:t.resizableTextArea)||void 0===n?void 0:n.textArea,e)},blur:()=>{var e;return null===(e=J.current)||void 0===e?void 0:e.blur()}}});let Y=S("input",a),Q=(0,O.Z)(Y),[$,G,U]=(0,T.TI)(Y,f),[ee]=L(Y,Q),{compactSize:et,compactItemClassnames:en}=(0,P.ri)(Y,z),ea=(0,j.Z)(e=>{var t;return null!==(t=null!=l?l:et)&&void 0!==t?t:e}),[eo,er]=(0,F.Z)("textArea",x,r),ei=(0,A.Z)(null!=c?c:C),[el,eu]=o.useState(!1),[es,ec]=o.useState(!1);return $(ee(o.createElement(E,Object.assign({autoComplete:Z},y,{style:Object.assign(Object.assign({},k),m),styles:Object.assign(Object.assign({},M),g),disabled:null!=u?u:B,allowClear:ei,className:i()(U,Q,p,f,en,V,es&&"".concat(Y,"-textarea-affix-wrapper-resize-dirty")),classNames:Object.assign(Object.assign(Object.assign({},d),D),{textarea:i()({["".concat(Y,"-sm")]:"small"===ea,["".concat(Y,"-lg")]:"large"===ea},G,null==d?void 0:d.textarea,D.textarea,el&&"".concat(Y,"-mouse-active")),variant:i()({["".concat(Y,"-").concat(eo)]:er},(0,N.Z)(Y,X)),affixWrapper:i()("".concat(Y,"-textarea-affix-wrapper"),{["".concat(Y,"-affix-wrapper-rtl")]:"rtl"===z,["".concat(Y,"-affix-wrapper-sm")]:"small"===ea,["".concat(Y,"-affix-wrapper-lg")]:"large"===ea,["".concat(Y,"-textarea-show-count")]:h||(null===(n=e.count)||void 0===n?void 0:n.show)},G)}),prefixCls:Y,suffix:q&&o.createElement("span",{className:"".concat(Y,"-textarea-suffix")},K),showCount:h,ref:J,onResize:e=>{var t,n;if(null==w||w(e),el&&"function"==typeof getComputedStyle){let e=null===(n=null===(t=J.current)||void 0===t?void 0:t.nativeElement)||void 0===n?void 0:n.querySelector("textarea");e&&"both"===getComputedStyle(e).resize&&ec(!0)}},onMouseDown:e=>{eu(!0),null==b||b(e);let t=()=>{eu(!1),document.removeEventListener("mouseup",t)};document.addEventListener("mouseup",t)}}))))})}}]);