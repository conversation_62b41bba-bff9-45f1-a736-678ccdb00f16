(()=>{var e={};e.id=2649,e.ids=[2649],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},65776:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d});var t=r(50482),a=r(69108),i=r(62563),l=r.n(i),n=r(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(s,c);let d=["",{children:["master-data",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9026)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,52054)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\page.tsx"],x="/master-data/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/master-data/page",pathname:"/master-data",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19720:(e,s,r)=>{Promise.resolve().then(r.bind(r,66705))},66705:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var t=r(95344);r(3729);var a=r(63724),i=r(27976),l=r(83984),n=r(43896),c=r(11157),d=r(55362),o=r(77746),x=r(35720),m=r(58535),p=r(22254);let u=()=>{let e=(0,p.useRouter)();return(0,t.jsxs)("div",{className:"space-y-6",children:[t.jsx("div",{className:"page-header",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(d.Z,{className:"text-2xl text-blue-600 mr-3"}),(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"page-title",children:"基础数据管理"}),t.jsx("p",{className:"page-description",children:"管理物料主数据和产品型号库等基础信息"})]})]})}),(0,t.jsxs)(a.Z,{gutter:[16,16],children:[t.jsx(i.Z,{xs:24,sm:12,lg:6,children:t.jsx(l.Z,{children:t.jsx(n.Z,{title:"物料总数",value:156,suffix:"种",valueStyle:{color:"#1890ff"},prefix:t.jsx(o.Z,{})})})}),t.jsx(i.Z,{xs:24,sm:12,lg:6,children:t.jsx(l.Z,{children:t.jsx(n.Z,{title:"产品型号",value:89,suffix:"个",valueStyle:{color:"#52c41a"},prefix:t.jsx(x.Z,{})})})}),t.jsx(i.Z,{xs:24,sm:12,lg:6,children:t.jsx(l.Z,{children:t.jsx(n.Z,{title:"活跃物料",value:142,suffix:"种",valueStyle:{color:"#722ed1"}})})}),t.jsx(i.Z,{xs:24,sm:12,lg:6,children:t.jsx(l.Z,{children:t.jsx(n.Z,{title:"活跃型号",value:76,suffix:"个",valueStyle:{color:"#fa8c16"}})})})]}),(0,t.jsxs)(a.Z,{gutter:[16,16],children:[t.jsx(i.Z,{xs:24,lg:12,children:t.jsx(l.Z,{title:"物料主数据管理",className:"h-64",extra:t.jsx(c.ZP,{type:"primary",icon:t.jsx(m.Z,{}),onClick:()=>e.push("/master-data/materials"),children:"管理物料"}),children:(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>e.push("/master-data/materials"),children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(o.Z,{className:"text-xl text-blue-500 mr-3"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:"物料信息管理"}),t.jsx("div",{className:"text-sm text-gray-500",children:"管理物料编码、名称、价格等基础信息"})]})]})}),t.jsx("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(d.Z,{className:"text-xl text-green-500 mr-3"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:"批量导入导出"}),t.jsx("div",{className:"text-sm text-gray-500",children:"支持Excel批量导入导出物料数据"})]})]})})]})})}),t.jsx(i.Z,{xs:24,lg:12,children:t.jsx(l.Z,{title:"产品型号库管理",className:"h-64",extra:t.jsx(c.ZP,{type:"primary",icon:t.jsx(m.Z,{}),onClick:()=>e.push("/master-data/product-models"),children:"管理型号"}),children:(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>e.push("/master-data/product-models"),children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(x.Z,{className:"text-xl text-purple-500 mr-3"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:"型号信息管理"}),t.jsx("div",{className:"text-sm text-gray-500",children:"管理产品型号、模具关联和计件单价"})]})]})}),t.jsx("div",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(d.Z,{className:"text-xl text-orange-500 mr-3"}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:"模具关联管理"}),t.jsx("div",{className:"text-sm text-gray-500",children:"管理成型模具和热压模具关联信息"})]})]})})]})})})]})]})}},52054:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(25036),a=r(38834);function i({children:e}){return t.jsx(a.Z,{children:e})}},9026:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let t=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\master-data\page.tsx`),{__esModule:a,$$typeof:i}=t,l=t.default}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[1638,1880,8811,3984,7883,7779,2079,6274,996,6133],()=>r(65776));module.exports=t})();