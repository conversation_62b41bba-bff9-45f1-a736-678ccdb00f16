"use strict";exports.id=7883,exports.ids=[7883],exports.modules={2014:(e,t,o)=>{o.d(t,{Z:()=>a});var n=o(65651),r=o(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"};var l=o(49809);let a=r.forwardRef(function(e,t){return r.createElement(l.Z,(0,n.Z)({},e,{ref:t,icon:i}))})},17438:(e,t,o)=>{o.d(t,{Z:()=>a});var n=o(65651),r=o(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"};var l=o(49809);let a=r.forwardRef(function(e,t){return r.createElement(l.Z,(0,n.Z)({},e,{ref:t,icon:i}))})},67871:(e,t,o)=>{o.d(t,{Z:()=>a});var n=o(65651),r=o(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"};var l=o(49809);let a=r.forwardRef(function(e,t){return r.createElement(l.Z,(0,n.Z)({},e,{ref:t,icon:i}))})},9286:(e,t,o)=>{o.d(t,{Z:()=>s,i:()=>a});var n=o(3729),r=o(80595),i=o(90263),l=o(84893);function a(e){return t=>n.createElement(i.ZP,{theme:{token:{motion:!1,zIndexPopupBase:0}}},n.createElement(e,Object.assign({},t)))}let s=(e,t,o,i,s)=>a(a=>{let{prefixCls:d,style:c}=a,m=n.useRef(null),[u,p]=n.useState(0),[g,b]=n.useState(0),[$,f]=(0,r.Z)(!1,{value:a.open}),{getPrefixCls:v}=n.useContext(l.E_),h=v(i||"select",d);n.useEffect(()=>{if(f(!0),"undefined"!=typeof ResizeObserver){let e=new ResizeObserver(e=>{let t=e[0].target;p(t.offsetHeight+8),b(t.offsetWidth)}),t=setInterval(()=>{var o;let n=s?`.${s(h)}`:`.${h}-dropdown`,r=null===(o=m.current)||void 0===o?void 0:o.querySelector(n);r&&(clearInterval(t),e.observe(r))},10);return()=>{clearInterval(t),e.disconnect()}}},[]);let y=Object.assign(Object.assign({},a),{style:Object.assign(Object.assign({},c),{margin:0}),open:$,visible:$,getPopupContainer:()=>m.current});return o&&(y=o(y)),t&&Object.assign(y,{[t]:{overflow:{adjustX:!1,adjustY:!1}}}),n.createElement("div",{ref:m,style:{paddingBottom:u,position:"relative",minWidth:g}},n.createElement(e,Object.assign({},y)))})},83512:(e,t,o)=>{o.d(t,{Z:()=>q});var n=o(3729),r=o(17438),i=o(67871),l=o(34132),a=o.n(l),s=o(42952),d=o(67827),c=o(80595),m=o(24773),u=o(43531);let p=e=>"object"!=typeof e&&"function"!=typeof e||null===e;var g=o(91604),b=o(9286),$=o(29545),f=o(55984),v=o(21992),h=o(84893),y=o(13878),I=o(68222),w=o(70930),x=o(10486),C=o(92959),O=o(22989),S=o(19532),B=o(26782),E=o(96461),k=o(66256),j=o(27071),z=o(13165),H=o(96373);let T=e=>{let{componentCls:t,menuCls:o,colorError:n,colorTextLightSolid:r}=e,i=`${o}-item`;return{[`${t}, ${t}-menu-submenu`]:{[`${o} ${i}`]:{[`&${i}-danger:not(${i}-disabled)`]:{color:n,"&:hover":{color:r,backgroundColor:n}}}}}},R=e=>{let{componentCls:t,menuCls:o,zIndexPopup:n,dropdownArrowDistance:r,sizePopupArrow:i,antCls:l,iconCls:a,motionDurationMid:s,paddingBlock:d,fontSize:c,dropdownEdgeChildPadding:m,colorTextDisabled:u,fontSizeIcon:p,controlPaddingHorizontal:g,colorBgElevated:b}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:n,display:"block","&::before":{position:"absolute",insetBlock:e.calc(i).div(2).sub(r).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},[`&-trigger${l}-btn`]:{[`& > ${a}-down, & > ${l}-btn-icon > ${a}-down`]:{fontSize:p}},[`${t}-wrap`]:{position:"relative",[`${l}-btn > ${a}-down`]:{fontSize:p},[`${a}-down::before`]:{transition:`transform ${s}`}},[`${t}-wrap-open`]:{[`${a}-down::before`]:{transform:"rotate(180deg)"}},[`
        &-hidden,
        &-menu-hidden,
        &-menu-submenu-hidden
      `]:{display:"none"},[`&${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottomLeft,
          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottomLeft,
          &${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottom,
          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottom,
          &${l}-slide-down-enter${l}-slide-down-enter-active${t}-placement-bottomRight,
          &${l}-slide-down-appear${l}-slide-down-appear-active${t}-placement-bottomRight`]:{animationName:S.fJ},[`&${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-topLeft,
          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-topLeft,
          &${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-top,
          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-top,
          &${l}-slide-up-enter${l}-slide-up-enter-active${t}-placement-topRight,
          &${l}-slide-up-appear${l}-slide-up-appear-active${t}-placement-topRight`]:{animationName:S.Qt},[`&${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottomLeft,
          &${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottom,
          &${l}-slide-down-leave${l}-slide-down-leave-active${t}-placement-bottomRight`]:{animationName:S.Uw},[`&${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-topLeft,
          &${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-top,
          &${l}-slide-up-leave${l}-slide-up-leave-active${t}-placement-topRight`]:{animationName:S.ly}}},(0,k.ZP)(e,b,{arrowPlacement:{top:!0,bottom:!0}}),{[`${t} ${o}`]:{position:"relative",margin:0},[`${o}-submenu-popup`]:{position:"absolute",zIndex:n,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},[`${t}, ${t}-menu-submenu`]:Object.assign(Object.assign({},(0,O.Wf)(e)),{[o]:Object.assign(Object.assign({padding:m,listStyleType:"none",backgroundColor:b,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,O.Qy)(e)),{"&:empty":{padding:0,boxShadow:"none"},[`${o}-item-group-title`]:{padding:`${(0,C.bf)(d)} ${(0,C.bf)(g)}`,color:e.colorTextDescription,transition:`all ${s}`},[`${o}-item`]:{position:"relative",display:"flex",alignItems:"center"},[`${o}-item-icon`]:{minWidth:c,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},[`${o}-title-content`]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:`all ${s}`,"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},[`${o}-item-extra`]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},[`${o}-item, ${o}-submenu-title`]:Object.assign(Object.assign({display:"flex",margin:0,padding:`${(0,C.bf)(d)} ${(0,C.bf)(g)}`,color:e.colorText,fontWeight:"normal",fontSize:c,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${s}`,borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,O.Qy)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:u,cursor:"not-allowed","&:hover":{color:u,backgroundColor:b,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:`${(0,C.bf)(e.marginXXS)} 0`,overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},[`${t}-menu-submenu-expand-icon`]:{position:"absolute",insetInlineEnd:e.paddingXS,[`${t}-menu-submenu-arrow-icon`]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:p,fontStyle:"normal"}}}),[`${o}-item-group-list`]:{margin:`0 ${(0,C.bf)(e.marginXS)}`,padding:0,listStyle:"none"},[`${o}-submenu-title`]:{paddingInlineEnd:e.calc(g).add(e.fontSizeSM).equal()},[`${o}-submenu-vertical`]:{position:"relative"},[`${o}-submenu${o}-submenu-disabled ${t}-menu-submenu-title`]:{[`&, ${t}-menu-submenu-arrow-icon`]:{color:u,backgroundColor:b,cursor:"not-allowed"}},[`${o}-submenu-selected ${t}-menu-submenu-title`]:{color:e.colorPrimary}})})},[(0,S.oN)(e,"slide-up"),(0,S.oN)(e,"slide-down"),(0,B.Fm)(e,"move-up"),(0,B.Fm)(e,"move-down"),(0,E._y)(e,"zoom-big")]]},N=(0,z.I$)("Dropdown",e=>{let{marginXXS:t,sizePopupArrow:o,paddingXXS:n,componentCls:r}=e,i=(0,H.IX)(e,{menuCls:`${r}-menu`,dropdownArrowDistance:e.calc(o).div(2).add(t).equal(),dropdownEdgeChildPadding:n});return[R(i),T(i)]},e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,k.wZ)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,j.w)(e)),{resetStyle:!1}),P=e=>{var t;let{menu:o,arrow:l,prefixCls:b,children:C,trigger:O,disabled:S,dropdownRender:B,popupRender:E,getPopupContainer:k,overlayClassName:j,rootClassName:z,overlayStyle:H,open:T,onOpenChange:R,visible:P,onVisibleChange:Z,mouseEnterDelay:M=.15,mouseLeaveDelay:D=.1,autoAdjustOverflow:A=!0,placement:W="",overlay:L,transitionName:X,destroyOnHidden:q,destroyPopupOnHide:_}=e,{getPopupContainer:F,getPrefixCls:Y,direction:V,dropdown:G}=n.useContext(h.E_),K=E||B;(0,f.ln)("Dropdown");let U=n.useMemo(()=>{let e=Y();return void 0!==X?X:W.includes("top")?`${e}-slide-down`:`${e}-slide-up`},[Y,W,X]),J=n.useMemo(()=>W?W.includes("Center")?W.slice(0,W.indexOf("Center")):W:"rtl"===V?"bottomRight":"bottomLeft",[W,V]),Q=Y("dropdown",b),ee=(0,y.Z)(Q),[et,eo,en]=N(Q,ee),[,er]=(0,x.ZP)(),ei=n.Children.only(p(C)?n.createElement("span",null,C):C),el=(0,$.Tm)(ei,{className:a()(`${Q}-trigger`,{[`${Q}-rtl`]:"rtl"===V},ei.props.className),disabled:null!==(t=ei.props.disabled)&&void 0!==t?t:S}),ea=S?[]:O,es=!!(null==ea?void 0:ea.includes("contextMenu")),[ed,ec]=(0,c.Z)(!1,{value:null!=T?T:P}),em=(0,d.Z)(e=>{null==R||R(e,{source:"trigger"}),null==Z||Z(e),ec(e)}),eu=a()(j,z,eo,en,ee,null==G?void 0:G.className,{[`${Q}-rtl`]:"rtl"===V}),ep=(0,g.Z)({arrowPointAtCenter:"object"==typeof l&&l.pointAtCenter,autoAdjustOverflow:A,offset:er.marginXXS,arrowWidth:l?er.sizePopupArrow:0,borderRadius:er.borderRadius}),eg=n.useCallback(()=>{null!=o&&o.selectable&&null!=o&&o.multiple||(null==R||R(!1,{source:"menu"}),ec(!1))},[null==o?void 0:o.selectable,null==o?void 0:o.multiple]),[eb,e$]=(0,u.Cn)("Dropdown",null==H?void 0:H.zIndex),ef=n.createElement(s.Z,Object.assign({alignPoint:es},(0,m.Z)(e,["rootClassName"]),{mouseEnterDelay:M,mouseLeaveDelay:D,visible:ed,builtinPlacements:ep,arrow:!!l,overlayClassName:eu,prefixCls:Q,getPopupContainer:k||F,transitionName:U,trigger:ea,overlay:()=>{let e;return e=(null==o?void 0:o.items)?n.createElement(I.Z,Object.assign({},o)):"function"==typeof L?L():L,K&&(e=K(e)),e=n.Children.only("string"==typeof e?n.createElement("span",null,e):e),n.createElement(w.J,{prefixCls:`${Q}-menu`,rootClassName:a()(en,ee),expandIcon:n.createElement("span",{className:`${Q}-menu-submenu-arrow`},"rtl"===V?n.createElement(r.Z,{className:`${Q}-menu-submenu-arrow-icon`}):n.createElement(i.Z,{className:`${Q}-menu-submenu-arrow-icon`})),mode:"vertical",selectable:!1,onClick:eg,validator:({mode:e})=>{}},e)},placement:J,onVisibleChange:em,overlayStyle:Object.assign(Object.assign(Object.assign({},null==G?void 0:G.style),H),{zIndex:eb}),autoDestroy:null!=q?q:_}),el);return eb&&(ef=n.createElement(v.Z.Provider,{value:e$},ef)),et(ef)},Z=(0,b.Z)(P,"align",void 0,"dropdown",e=>e);P._InternalPanelDoNotUseOrYouWillBeFired=e=>n.createElement(Z,Object.assign({},e),n.createElement("span",null));var M=o(71998),D=o(11157),A=o(10707),W=o(71264),L=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let X=e=>{let{getPopupContainer:t,getPrefixCls:o,direction:r}=n.useContext(h.E_),{prefixCls:i,type:l="default",danger:s,disabled:d,loading:c,onClick:m,htmlType:u,children:p,className:g,menu:b,arrow:$,autoFocus:f,overlay:v,trigger:y,align:I,open:w,onOpenChange:x,placement:C,getPopupContainer:O,href:S,icon:B=n.createElement(M.Z,null),title:E,buttonsRender:k=e=>e,mouseEnterDelay:j,mouseLeaveDelay:z,overlayClassName:H,overlayStyle:T,destroyOnHidden:R,destroyPopupOnHide:N,dropdownRender:Z,popupRender:X}=e,q=L(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyOnHidden","destroyPopupOnHide","dropdownRender","popupRender"]),_=o("dropdown",i),F=`${_}-button`,Y={menu:b,arrow:$,autoFocus:f,align:I,disabled:d,trigger:d?[]:y,onOpenChange:x,getPopupContainer:O||t,mouseEnterDelay:j,mouseLeaveDelay:z,overlayClassName:H,overlayStyle:T,destroyOnHidden:R,popupRender:X||Z},{compactSize:V,compactItemClassnames:G}=(0,W.ri)(_,r),K=a()(F,G,g);"destroyPopupOnHide"in e&&(Y.destroyPopupOnHide=N),"overlay"in e&&(Y.overlay=v),"open"in e&&(Y.open=w),"placement"in e?Y.placement=C:Y.placement="rtl"===r?"bottomLeft":"bottomRight";let[U,J]=k([n.createElement(D.ZP,{type:l,danger:s,disabled:d,loading:c,onClick:m,htmlType:u,href:S,title:E},p),n.createElement(D.ZP,{type:l,danger:s,icon:B})]);return n.createElement(A.Z.Compact,Object.assign({className:K,size:V,block:!0},q),U,n.createElement(P,Object.assign({},Y),J))};X.__ANT_BUTTON=!0,P.Button=X;let q=P},7239:(e,t,o)=>{o.d(t,{D:()=>C,Z:()=>S});var n=o(3729),r=o(65651);let i={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"};var l=o(49809),a=n.forwardRef(function(e,t){return n.createElement(l.Z,(0,r.Z)({},e,{ref:t,icon:i}))}),s=o(17438),d=o(67871),c=o(34132),m=o.n(c),u=o(24773),p=o(1937),g=o(84893),b=o(97744),$=o(92959),f=o(1461),v=o(13165);let h=e=>{let{componentCls:t,siderBg:o,motionDurationMid:n,motionDurationSlow:r,antCls:i,triggerHeight:l,triggerColor:a,triggerBg:s,headerHeight:d,zeroTriggerWidth:c,zeroTriggerHeight:m,borderRadiusLG:u,lightSiderBg:p,lightTriggerColor:g,lightTriggerBg:b,bodyBg:f}=e;return{[t]:{position:"relative",minWidth:0,background:o,transition:`all ${n}, background 0s`,"&-has-trigger":{paddingBottom:l},"&-right":{order:1},[`${t}-children`]:{height:"100%",marginTop:-.1,paddingTop:.1,[`${i}-menu${i}-menu-inline-collapsed`]:{width:"auto"}},[`&-zero-width ${t}-children`]:{overflow:"hidden"},[`${t}-trigger`]:{position:"fixed",bottom:0,zIndex:1,height:l,color:a,lineHeight:(0,$.bf)(l),textAlign:"center",background:s,cursor:"pointer",transition:`all ${n}`},[`${t}-zero-width-trigger`]:{position:"absolute",top:d,insetInlineEnd:e.calc(c).mul(-1).equal(),zIndex:1,width:c,height:m,color:a,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:o,borderRadius:`0 ${(0,$.bf)(u)} ${(0,$.bf)(u)} 0`,cursor:"pointer",transition:`background ${r} ease`,"&::after":{position:"absolute",inset:0,background:"transparent",transition:`all ${r}`,content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(c).mul(-1).equal(),borderRadius:`${(0,$.bf)(u)} 0 0 ${(0,$.bf)(u)}`}},"&-light":{background:p,[`${t}-trigger`]:{color:g,background:b},[`${t}-zero-width-trigger`]:{color:g,background:b,border:`1px solid ${f}`,borderInlineStart:0}}}}},y=(0,v.I$)(["Layout","Sider"],e=>[h(e)],f.eh,{deprecatedTokens:f.jn});var I=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let w={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},x=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),C=n.createContext({}),O=(()=>{let e=0;return (t="")=>(e+=1,`${t}${e}`)})(),S=n.forwardRef((e,t)=>{let{prefixCls:o,className:r,trigger:i,children:l,defaultCollapsed:c=!1,theme:$="dark",style:f={},collapsible:v=!1,reverseArrow:h=!1,width:S=200,collapsedWidth:B=80,zeroWidthTriggerStyle:E,breakpoint:k,onCollapse:j,onBreakpoint:z}=e,H=I(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:T}=(0,n.useContext)(b.V),[R,N]=(0,n.useState)("collapsed"in e?e.collapsed:c),[P,Z]=(0,n.useState)(!1);(0,n.useEffect)(()=>{"collapsed"in e&&N(e.collapsed)},[e.collapsed]);let M=(t,o)=>{"collapsed"in e||N(t),null==j||j(t,o)},{getPrefixCls:D,direction:A}=(0,n.useContext)(g.E_),W=D("layout-sider",o),[L,X,q]=y(W),_=(0,n.useRef)(null);_.current=e=>{Z(e.matches),null==z||z(e.matches),R!==e.matches&&M(e.matches,"responsive")},(0,n.useEffect)(()=>{let e;function t(e){var t;return null===(t=_.current)||void 0===t?void 0:t.call(_,e)}return void 0!==(null==window?void 0:window.matchMedia)&&k&&k in w&&(e=window.matchMedia(`screen and (max-width: ${w[k]})`),(0,p.x)(e,t),t(e)),()=>{(0,p.h)(e,t)}},[k]),(0,n.useEffect)(()=>{let e=O("ant-sider-");return T.addSider(e),()=>T.removeSider(e)},[]);let F=()=>{M(!R,"clickTrigger")},Y=(0,u.Z)(H,["collapsed"]),V=R?B:S,G=x(V)?`${V}px`:String(V),K=0===parseFloat(String(B||0))?n.createElement("span",{onClick:F,className:m()(`${W}-zero-width-trigger`,`${W}-zero-width-trigger-${h?"right":"left"}`),style:E},i||n.createElement(a,null)):null,U="rtl"===A==!h,J={expanded:U?n.createElement(d.Z,null):n.createElement(s.Z,null),collapsed:U?n.createElement(s.Z,null):n.createElement(d.Z,null)}[R?"collapsed":"expanded"],Q=null!==i?K||n.createElement("div",{className:`${W}-trigger`,onClick:F,style:{width:G}},i||J):null,ee=Object.assign(Object.assign({},f),{flex:`0 0 ${G}`,maxWidth:G,minWidth:G,width:G}),et=m()(W,`${W}-${$}`,{[`${W}-collapsed`]:!!R,[`${W}-has-trigger`]:v&&null!==i&&!K,[`${W}-below`]:!!P,[`${W}-zero-width`]:0===parseFloat(G)},r,X,q),eo=n.useMemo(()=>({siderCollapsed:R}),[R]);return L(n.createElement(C.Provider,{value:eo},n.createElement("aside",Object.assign({className:et},Y,{style:ee,ref:t}),n.createElement("div",{className:`${W}-children`},l),v||P&&K?Q:null)))})},97744:(e,t,o)=>{o.d(t,{V:()=>n});let n=o(3729).createContext({siderHook:{addSider:()=>null,removeSider:()=>null}})},1461:(e,t,o)=>{o.d(t,{ZP:()=>s,eh:()=>l,jn:()=>a});var n=o(92959),r=o(13165);let i=e=>{let{antCls:t,componentCls:o,colorText:r,footerBg:i,headerHeight:l,headerPadding:a,headerColor:s,footerPadding:d,fontSize:c,bodyBg:m,headerBg:u}=e;return{[o]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:m,"&, *":{boxSizing:"border-box"},[`&${o}-has-sider`]:{flexDirection:"row",[`> ${o}, > ${o}-content`]:{width:0}},[`${o}-header, &${o}-footer`]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},[`${o}-header`]:{height:l,padding:a,color:s,lineHeight:(0,n.bf)(l),background:u,[`${t}-menu`]:{lineHeight:"inherit"}},[`${o}-footer`]:{padding:d,color:r,fontSize:c,background:i},[`${o}-content`]:{flex:"auto",color:r,minHeight:0}}},l=e=>{let{colorBgLayout:t,controlHeight:o,controlHeightLG:n,colorText:r,controlHeightSM:i,marginXXS:l,colorTextLightSolid:a,colorBgContainer:s}=e,d=1.25*n;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:2*o,headerPadding:`0 ${d}px`,headerColor:r,footerPadding:`${i}px ${d}px`,footerBg:t,siderBg:"#001529",triggerHeight:n+2*l,triggerBg:"#002140",triggerColor:a,zeroTriggerWidth:n,zeroTriggerHeight:n,lightSiderBg:s,lightTriggerBg:s,lightTriggerColor:r}},a=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]],s=(0,r.I$)("Layout",e=>[i(e)],l,{deprecatedTokens:a})},70930:(e,t,o)=>{o.d(t,{J:()=>s,Z:()=>d});var n=o(3729),r=o(67862),i=o(65313),l=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let a=n.createContext(null),s=n.forwardRef((e,t)=>{let{children:o}=e,s=l(e,["children"]),d=n.useContext(a),c=n.useMemo(()=>Object.assign(Object.assign({},d),s),[d,s.prefixCls,s.mode,s.selectable,s.rootClassName]),m=(0,r.t4)(o),u=(0,r.x1)(t,m?(0,r.C4)(o):null);return n.createElement(a.Provider,{value:c},n.createElement(i.Z,{space:!0},m?n.cloneElement(o,{ref:u}):o))}),d=a},68222:(e,t,o)=>{o.d(t,{Z:()=>V});var n=o(3729),r=o(85826),i=o(7239),l=o(71998),a=o(34132),s=o.n(a),d=o(67827),c=o(24773),m=o(95295),u=o(29545),p=o(84893),g=o(13878);let b=(0,n.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var $=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let f=e=>{let{prefixCls:t,className:o,dashed:i}=e,l=$(e,["prefixCls","className","dashed"]),{getPrefixCls:a}=n.useContext(p.E_),d=a("menu",t),c=s()({[`${d}-item-divider-dashed`]:!!i},o);return n.createElement(r.iz,Object.assign({className:c},l))};var v=o(89299),h=o(51410);let y=e=>{var t;let{className:o,children:l,icon:a,title:d,danger:m,extra:p}=e,{prefixCls:g,firstLevel:$,direction:f,disableMenuItemTitleTooltip:y,inlineCollapsed:I}=n.useContext(b),{siderCollapsed:w}=n.useContext(i.D),x=d;void 0===d?x=$?l:"":!1===d&&(x="");let C={title:x};w||I||(C.title=null,C.open=!1);let O=(0,v.Z)(l).length,S=n.createElement(r.ck,Object.assign({},(0,c.Z)(e,["title","icon","danger"]),{className:s()({[`${g}-item-danger`]:m,[`${g}-item-only-child`]:(a?O+1:O)===1},o),title:"string"==typeof d?d:void 0}),(0,u.Tm)(a,{className:s()(n.isValidElement(a)?null===(t=a.props)||void 0===t?void 0:t.className:"",`${g}-item-icon`)}),(e=>{let t=null==l?void 0:l[0],o=n.createElement("span",{className:s()(`${g}-title-content`,{[`${g}-title-content-with-extra`]:!!p||0===p})},l);return(!a||n.isValidElement(l)&&"span"===l.type)&&l&&e&&$&&"string"==typeof t?n.createElement("div",{className:`${g}-inline-collapsed-noicon`},t.charAt(0)):o})(I));return y||(S=n.createElement(h.Z,Object.assign({},C,{placement:"rtl"===f?"left":"right",classNames:{root:`${g}-inline-collapsed-tooltip`}}),S)),S};var I=o(70930),w=o(92959),x=o(55002),C=o(22989),O=o(71645),S=o(19532),B=o(96461),E=o(13165),k=o(96373);let j=e=>{let{componentCls:t,motionDurationSlow:o,horizontalLineHeight:n,colorSplit:r,lineWidth:i,lineType:l,itemPaddingInline:a}=e;return{[`${t}-horizontal`]:{lineHeight:n,border:0,borderBottom:`${(0,w.bf)(i)} ${l} ${r}`,boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},[`${t}-item, ${t}-submenu`]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:a},[`> ${t}-item:hover,
        > ${t}-item-active,
        > ${t}-submenu ${t}-submenu-title:hover`]:{backgroundColor:"transparent"},[`${t}-item, ${t}-submenu-title`]:{transition:`border-color ${o},background ${o}`},[`${t}-submenu-arrow`]:{display:"none"}}}},z=({componentCls:e,menuArrowOffset:t,calc:o})=>({[`${e}-rtl`]:{direction:"rtl"},[`${e}-submenu-rtl`]:{transformOrigin:"100% 0"},[`${e}-rtl${e}-vertical,
    ${e}-submenu-rtl ${e}-vertical`]:{[`${e}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateY(${(0,w.bf)(o(t).mul(-1).equal())})`},"&::after":{transform:`rotate(45deg) translateY(${(0,w.bf)(t)})`}}}}),H=e=>Object.assign({},(0,C.oN)(e)),T=(e,t)=>{let{componentCls:o,itemColor:n,itemSelectedColor:r,subMenuItemSelectedColor:i,groupTitleColor:l,itemBg:a,subMenuItemBg:s,itemSelectedBg:d,activeBarHeight:c,activeBarWidth:m,activeBarBorderWidth:u,motionDurationSlow:p,motionEaseInOut:g,motionEaseOut:b,itemPaddingInline:$,motionDurationMid:f,itemHoverColor:v,lineType:h,colorSplit:y,itemDisabledColor:I,dangerItemColor:x,dangerItemHoverColor:C,dangerItemSelectedColor:O,dangerItemActiveBg:S,dangerItemSelectedBg:B,popupBg:E,itemHoverBg:k,itemActiveBg:j,menuSubMenuBg:z,horizontalItemSelectedColor:T,horizontalItemSelectedBg:R,horizontalItemBorderRadius:N,horizontalItemHoverBg:P}=e;return{[`${o}-${t}, ${o}-${t} > ${o}`]:{color:n,background:a,[`&${o}-root:focus-visible`]:Object.assign({},H(e)),[`${o}-item`]:{"&-group-title, &-extra":{color:l}},[`${o}-submenu-selected > ${o}-submenu-title`]:{color:i},[`${o}-item, ${o}-submenu-title`]:{color:n,[`&:not(${o}-item-disabled):focus-visible`]:Object.assign({},H(e))},[`${o}-item-disabled, ${o}-submenu-disabled`]:{color:`${I} !important`},[`${o}-item:not(${o}-item-selected):not(${o}-submenu-selected)`]:{[`&:hover, > ${o}-submenu-title:hover`]:{color:v}},[`&:not(${o}-horizontal)`]:{[`${o}-item:not(${o}-item-selected)`]:{"&:hover":{backgroundColor:k},"&:active":{backgroundColor:j}},[`${o}-submenu-title`]:{"&:hover":{backgroundColor:k},"&:active":{backgroundColor:j}}},[`${o}-item-danger`]:{color:x,[`&${o}-item:hover`]:{[`&:not(${o}-item-selected):not(${o}-submenu-selected)`]:{color:C}},[`&${o}-item:active`]:{background:S}},[`${o}-item a`]:{"&, &:hover":{color:"inherit"}},[`${o}-item-selected`]:{color:r,[`&${o}-item-danger`]:{color:O},"a, a:hover":{color:"inherit"}},[`& ${o}-item-selected`]:{backgroundColor:d,[`&${o}-item-danger`]:{backgroundColor:B}},[`&${o}-submenu > ${o}`]:{backgroundColor:z},[`&${o}-popup > ${o}`]:{backgroundColor:E},[`&${o}-submenu-popup > ${o}`]:{backgroundColor:E},[`&${o}-horizontal`]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{[`> ${o}-item, > ${o}-submenu`]:{top:u,marginTop:e.calc(u).mul(-1).equal(),marginBottom:0,borderRadius:N,"&::after":{position:"absolute",insetInline:$,bottom:0,borderBottom:`${(0,w.bf)(c)} solid transparent`,transition:`border-color ${p} ${g}`,content:'""'},"&:hover, &-active, &-open":{background:P,"&::after":{borderBottomWidth:c,borderBottomColor:T}},"&-selected":{color:T,backgroundColor:R,"&:hover":{backgroundColor:R},"&::after":{borderBottomWidth:c,borderBottomColor:T}}}}),[`&${o}-root`]:{[`&${o}-inline, &${o}-vertical`]:{borderInlineEnd:`${(0,w.bf)(u)} ${h} ${y}`}},[`&${o}-inline`]:{[`${o}-sub${o}-inline`]:{background:s},[`${o}-item`]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:`${(0,w.bf)(m)} solid ${r}`,transform:"scaleY(0.0001)",opacity:0,transition:`transform ${f} ${b},opacity ${f} ${b}`,content:'""'},[`&${o}-item-danger`]:{"&::after":{borderInlineEndColor:O}}},[`${o}-selected, ${o}-item-selected`]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:`transform ${f} ${g},opacity ${f} ${g}`}}}}}},R=e=>{let{componentCls:t,itemHeight:o,itemMarginInline:n,padding:r,menuArrowSize:i,marginXS:l,itemMarginBlock:a,itemWidth:s,itemPaddingInline:d}=e,c=e.calc(i).add(r).add(l).equal();return{[`${t}-item`]:{position:"relative",overflow:"hidden"},[`${t}-item, ${t}-submenu-title`]:{height:o,lineHeight:(0,w.bf)(o),paddingInline:d,overflow:"hidden",textOverflow:"ellipsis",marginInline:n,marginBlock:a,width:s},[`> ${t}-item,
            > ${t}-submenu > ${t}-submenu-title`]:{height:o,lineHeight:(0,w.bf)(o)},[`${t}-item-group-list ${t}-submenu-title,
            ${t}-submenu-title`]:{paddingInlineEnd:c}}},N=e=>{let{componentCls:t,iconCls:o,itemHeight:n,colorTextLightSolid:r,dropdownWidth:i,controlHeightLG:l,motionEaseOut:a,paddingXL:s,itemMarginInline:d,fontSizeLG:c,motionDurationFast:m,motionDurationSlow:u,paddingXS:p,boxShadowSecondary:g,collapsedWidth:b,collapsedIconSize:$}=e,f={height:n,lineHeight:(0,w.bf)(n),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({[`&${t}-root`]:{boxShadow:"none"}},R(e))},[`${t}-submenu-popup`]:{[`${t}-vertical`]:Object.assign(Object.assign({},R(e)),{boxShadow:g})}},{[`${t}-submenu-popup ${t}-vertical${t}-sub`]:{minWidth:i,maxHeight:`calc(100vh - ${(0,w.bf)(e.calc(l).mul(2.5).equal())})`,padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{[`${t}-inline`]:{width:"100%",[`&${t}-root`]:{[`${t}-item, ${t}-submenu-title`]:{display:"flex",alignItems:"center",transition:`border-color ${u},background ${u},padding ${m} ${a}`,[`> ${t}-title-content`]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},[`${t}-sub${t}-inline`]:{padding:0,border:0,borderRadius:0,boxShadow:"none",[`& > ${t}-submenu > ${t}-submenu-title`]:f,[`& ${t}-item-group-title`]:{paddingInlineStart:s}},[`${t}-item`]:f}},{[`${t}-inline-collapsed`]:{width:b,[`&${t}-root`]:{[`${t}-item, ${t}-submenu ${t}-submenu-title`]:{[`> ${t}-inline-collapsed-noicon`]:{fontSize:c,textAlign:"center"}}},[`> ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-item,
          > ${t}-item-group > ${t}-item-group-list > ${t}-submenu > ${t}-submenu-title,
          > ${t}-submenu > ${t}-submenu-title`]:{insetInlineStart:0,paddingInline:`calc(50% - ${(0,w.bf)(e.calc($).div(2).equal())} - ${(0,w.bf)(d)})`,textOverflow:"clip",[`
            ${t}-submenu-arrow,
            ${t}-submenu-expand-icon
          `]:{opacity:0},[`${t}-item-icon, ${o}`]:{margin:0,fontSize:$,lineHeight:(0,w.bf)(n),"+ span":{display:"inline-block",opacity:0}}},[`${t}-item-icon, ${o}`]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",[`${t}-item-icon, ${o}`]:{display:"none"},"a, a:hover":{color:r}},[`${t}-item-group-title`]:Object.assign(Object.assign({},C.vS),{paddingInline:p})}}]},P=e=>{let{componentCls:t,motionDurationSlow:o,motionDurationMid:n,motionEaseInOut:r,motionEaseOut:i,iconCls:l,iconSize:a,iconMarginInlineEnd:s}=e;return{[`${t}-item, ${t}-submenu-title`]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:`border-color ${o},background ${o},padding calc(${o} + 0.1s) ${r}`,[`${t}-item-icon, ${l}`]:{minWidth:a,fontSize:a,transition:`font-size ${n} ${i},margin ${o} ${r},color ${o}`,"+ span":{marginInlineStart:s,opacity:1,transition:`opacity ${o} ${r},margin ${o},color ${o}`}},[`${t}-item-icon`]:Object.assign({},(0,C.Ro)()),[`&${t}-item-only-child`]:{[`> ${l}, > ${t}-item-icon`]:{marginInlineEnd:0}}},[`${t}-item-disabled, ${t}-submenu-disabled`]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},[`> ${t}-submenu-title`]:{color:"inherit !important",cursor:"not-allowed"}}}},Z=e=>{let{componentCls:t,motionDurationSlow:o,motionEaseInOut:n,borderRadius:r,menuArrowSize:i,menuArrowOffset:l}=e;return{[`${t}-submenu`]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:i,color:"currentcolor",transform:"translateY(-50%)",transition:`transform ${o} ${n}, opacity ${o}`},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(i).mul(.6).equal(),height:e.calc(i).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:r,transition:`background ${o} ${n},transform ${o} ${n},top ${o} ${n},color ${o} ${n}`,content:'""'},"&::before":{transform:`rotate(45deg) translateY(${(0,w.bf)(e.calc(l).mul(-1).equal())})`},"&::after":{transform:`rotate(-45deg) translateY(${(0,w.bf)(l)})`}}}}},M=e=>{let{antCls:t,componentCls:o,fontSize:n,motionDurationSlow:r,motionDurationMid:i,motionEaseInOut:l,paddingXS:a,padding:s,colorSplit:d,lineWidth:c,zIndexPopup:m,borderRadiusLG:u,subMenuItemBorderRadius:p,menuArrowSize:g,menuArrowOffset:b,lineType:$,groupTitleLineHeight:f,groupTitleFontSize:v}=e;return[{"":{[o]:Object.assign(Object.assign({},(0,C.dF)()),{"&-hidden":{display:"none"}})},[`${o}-submenu-hidden`]:{display:"none"}},{[o]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,C.Wf)(e)),(0,C.dF)()),{marginBottom:0,paddingInlineStart:0,fontSize:n,lineHeight:0,listStyle:"none",outline:"none",transition:`width ${r} cubic-bezier(0.2, 0, 0, 1) 0s`,"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",[`${o}-item`]:{flex:"none"}},[`${o}-item, ${o}-submenu, ${o}-submenu-title`]:{borderRadius:e.itemBorderRadius},[`${o}-item-group-title`]:{padding:`${(0,w.bf)(a)} ${(0,w.bf)(s)}`,fontSize:v,lineHeight:f,transition:`all ${r}`},[`&-horizontal ${o}-submenu`]:{transition:`border-color ${r} ${l},background ${r} ${l}`},[`${o}-submenu, ${o}-submenu-inline`]:{transition:`border-color ${r} ${l},background ${r} ${l},padding ${i} ${l}`},[`${o}-submenu ${o}-sub`]:{cursor:"initial",transition:`background ${r} ${l},padding ${r} ${l}`},[`${o}-title-content`]:{transition:`color ${r}`,"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},[`> ${t}-typography-ellipsis-single-line`]:{display:"inline",verticalAlign:"unset"},[`${o}-item-extra`]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},[`${o}-item a`]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},[`${o}-item-divider`]:{overflow:"hidden",lineHeight:0,borderColor:d,borderStyle:$,borderWidth:0,borderTopWidth:c,marginBlock:c,padding:0,"&-dashed":{borderStyle:"dashed"}}}),P(e)),{[`${o}-item-group`]:{[`${o}-item-group-list`]:{margin:0,padding:0,[`${o}-item, ${o}-submenu-title`]:{paddingInline:`${(0,w.bf)(e.calc(n).mul(2).equal())} ${(0,w.bf)(s)}`}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:m,borderRadius:u,boxShadow:"none",transformOrigin:"0 0",[`&${o}-submenu`]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},[`> ${o}`]:Object.assign(Object.assign(Object.assign({borderRadius:u},P(e)),Z(e)),{[`${o}-item, ${o}-submenu > ${o}-submenu-title`]:{borderRadius:p},[`${o}-submenu-title::after`]:{transition:`transform ${r} ${l}`}})},[`
          &-placement-leftTop,
          &-placement-bottomRight,
          `]:{transformOrigin:"100% 0"},[`
          &-placement-leftBottom,
          &-placement-topRight,
          `]:{transformOrigin:"100% 100%"},[`
          &-placement-rightBottom,
          &-placement-topLeft,
          `]:{transformOrigin:"0 100%"},[`
          &-placement-bottomLeft,
          &-placement-rightTop,
          `]:{transformOrigin:"0 0"},[`
          &-placement-leftTop,
          &-placement-leftBottom
          `]:{paddingInlineEnd:e.paddingXS},[`
          &-placement-rightTop,
          &-placement-rightBottom
          `]:{paddingInlineStart:e.paddingXS},[`
          &-placement-topRight,
          &-placement-topLeft
          `]:{paddingBottom:e.paddingXS},[`
          &-placement-bottomRight,
          &-placement-bottomLeft
          `]:{paddingTop:e.paddingXS}}}),Z(e)),{[`&-inline-collapsed ${o}-submenu-arrow,
        &-inline ${o}-submenu-arrow`]:{"&::before":{transform:`rotate(-45deg) translateX(${(0,w.bf)(b)})`},"&::after":{transform:`rotate(45deg) translateX(${(0,w.bf)(e.calc(b).mul(-1).equal())})`}},[`${o}-submenu-open${o}-submenu-inline > ${o}-submenu-title > ${o}-submenu-arrow`]:{transform:`translateY(${(0,w.bf)(e.calc(g).mul(.2).mul(-1).equal())})`,"&::after":{transform:`rotate(-45deg) translateX(${(0,w.bf)(e.calc(b).mul(-1).equal())})`},"&::before":{transform:`rotate(45deg) translateX(${(0,w.bf)(b)})`}}})},{[`${t}-layout-header`]:{[o]:{lineHeight:"inherit"}}}]},D=e=>{var t,o,n;let{colorPrimary:r,colorError:i,colorTextDisabled:l,colorErrorBg:a,colorText:s,colorTextDescription:d,colorBgContainer:c,colorFillAlter:m,colorFillContent:u,lineWidth:p,lineWidthBold:g,controlItemBgActive:b,colorBgTextHover:$,controlHeightLG:f,lineHeight:v,colorBgElevated:h,marginXXS:y,padding:I,fontSize:w,controlHeightSM:C,fontSizeLG:O,colorTextLightSolid:S,colorErrorHover:B}=e,E=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,k=null!==(o=e.activeBarBorderWidth)&&void 0!==o?o:p,j=null!==(n=e.itemMarginInline)&&void 0!==n?n:e.marginXXS,z=new x.t(S).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:s,itemColor:s,colorItemTextHover:s,itemHoverColor:s,colorItemTextHoverHorizontal:r,horizontalItemHoverColor:r,colorGroupTitle:d,groupTitleColor:d,colorItemTextSelected:r,itemSelectedColor:r,subMenuItemSelectedColor:r,colorItemTextSelectedHorizontal:r,horizontalItemSelectedColor:r,colorItemBg:c,itemBg:c,colorItemBgHover:$,itemHoverBg:$,colorItemBgActive:u,itemActiveBg:b,colorSubItemBg:m,subMenuItemBg:m,colorItemBgSelected:b,itemSelectedBg:b,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:E,colorActiveBarHeight:g,activeBarHeight:g,colorActiveBarBorderSize:p,activeBarBorderWidth:k,colorItemTextDisabled:l,itemDisabledColor:l,colorDangerItemText:i,dangerItemColor:i,colorDangerItemTextHover:i,dangerItemHoverColor:i,colorDangerItemTextSelected:i,dangerItemSelectedColor:i,colorDangerItemBgActive:a,dangerItemActiveBg:a,colorDangerItemBgSelected:a,dangerItemSelectedBg:a,itemMarginInline:j,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:f,groupTitleLineHeight:v,collapsedWidth:2*f,popupBg:h,itemMarginBlock:y,itemPaddingInline:I,horizontalLineHeight:`${1.15*f}px`,iconSize:w,iconMarginInlineEnd:C-w,collapsedIconSize:O,groupTitleFontSize:w,darkItemDisabledColor:new x.t(S).setA(.25).toRgbString(),darkItemColor:z,darkDangerItemColor:i,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:S,darkItemSelectedBg:r,darkDangerItemSelectedBg:i,darkItemHoverBg:"transparent",darkGroupTitleColor:z,darkItemHoverColor:S,darkDangerItemHoverColor:B,darkDangerItemSelectedColor:S,darkDangerItemActiveBg:i,itemWidth:E?`calc(100% + ${k}px)`:`calc(100% - ${2*j}px)`}},A=(e,t=e,o=!0)=>(0,E.I$)("Menu",e=>{let{colorBgElevated:t,controlHeightLG:o,fontSize:n,darkItemColor:r,darkDangerItemColor:i,darkItemBg:l,darkSubMenuItemBg:a,darkItemSelectedColor:s,darkItemSelectedBg:d,darkDangerItemSelectedBg:c,darkItemHoverBg:m,darkGroupTitleColor:u,darkItemHoverColor:p,darkItemDisabledColor:g,darkDangerItemHoverColor:b,darkDangerItemSelectedColor:$,darkDangerItemActiveBg:f,popupBg:v,darkPopupBg:h}=e,y=e.calc(n).div(7).mul(5).equal(),I=(0,k.IX)(e,{menuArrowSize:y,menuHorizontalHeight:e.calc(o).mul(1.15).equal(),menuArrowOffset:e.calc(y).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:v}),w=(0,k.IX)(I,{itemColor:r,itemHoverColor:p,groupTitleColor:u,itemSelectedColor:s,subMenuItemSelectedColor:s,itemBg:l,popupBg:h,subMenuItemBg:a,itemActiveBg:"transparent",itemSelectedBg:d,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:m,itemDisabledColor:g,dangerItemColor:i,dangerItemHoverColor:b,dangerItemSelectedColor:$,dangerItemActiveBg:f,dangerItemSelectedBg:c,menuSubMenuBg:a,horizontalItemSelectedColor:s,horizontalItemSelectedBg:d});return[M(I),j(I),N(I),T(I,"light"),T(w,"dark"),z(I),(0,O.Z)(I),(0,S.oN)(I,"slide-up"),(0,S.oN)(I,"slide-down"),(0,B._y)(I,"zoom-big")]},D,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:o,unitless:{groupTitleLineHeight:!0}})(e,t);var W=o(43531);let L=e=>{var t;let o;let{popupClassName:i,icon:l,title:a,theme:d}=e,m=n.useContext(b),{prefixCls:p,inlineCollapsed:g,theme:$}=m,f=(0,r.Xl)();if(l){let e=n.isValidElement(a)&&"span"===a.type;o=n.createElement(n.Fragment,null,(0,u.Tm)(l,{className:s()(n.isValidElement(l)?null===(t=l.props)||void 0===t?void 0:t.className:"",`${p}-item-icon`)}),e?a:n.createElement("span",{className:`${p}-title-content`},a))}else o=g&&!f.length&&a&&"string"==typeof a?n.createElement("div",{className:`${p}-inline-collapsed-noicon`},a.charAt(0)):n.createElement("span",{className:`${p}-title-content`},a);let v=n.useMemo(()=>Object.assign(Object.assign({},m),{firstLevel:!1}),[m]),[h]=(0,W.Cn)("Menu");return n.createElement(b.Provider,{value:v},n.createElement(r.Wd,Object.assign({},(0,c.Z)(e,["icon"]),{title:o,popupClassName:s()(p,i,`${p}-${d||$}`),popupStyle:Object.assign({zIndex:h},e.popupStyle)})))};var X=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};function q(e){return null===e||!1===e}let _={item:y,submenu:L,divider:f},F=(0,n.forwardRef)((e,t)=>{var o;let i=n.useContext(I.Z),a=i||{},{getPrefixCls:$,getPopupContainer:f,direction:v,menu:h}=n.useContext(p.E_),y=$(),{prefixCls:w,className:x,style:C,theme:O="light",expandIcon:S,_internalDisableMenuItemTitleTooltip:B,inlineCollapsed:E,siderCollapsed:k,rootClassName:j,mode:z,selectable:H,onClick:T,overflowedIndicatorPopupClassName:R}=e,N=X(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),P=(0,c.Z)(N,["collapsedWidth"]);null===(o=a.validator)||void 0===o||o.call(a,{mode:z});let Z=(0,d.Z)((...e)=>{var t;null==T||T.apply(void 0,e),null===(t=a.onClick)||void 0===t||t.call(a)}),M=a.mode||z,D=null!=H?H:a.selectable,W=null!=E?E:k,L={horizontal:{motionName:`${y}-slide-up`},inline:(0,m.Z)(y),other:{motionName:`${y}-zoom-big`}},F=$("menu",w||a.prefixCls),Y=(0,g.Z)(F),[V,G,K]=A(F,Y,!i),U=s()(`${F}-${O}`,null==h?void 0:h.className,x),J=n.useMemo(()=>{var e,t;if("function"==typeof S||q(S))return S||null;if("function"==typeof a.expandIcon||q(a.expandIcon))return a.expandIcon||null;if("function"==typeof(null==h?void 0:h.expandIcon)||q(null==h?void 0:h.expandIcon))return(null==h?void 0:h.expandIcon)||null;let o=null!==(e=null!=S?S:null==a?void 0:a.expandIcon)&&void 0!==e?e:null==h?void 0:h.expandIcon;return(0,u.Tm)(o,{className:s()(`${F}-submenu-expand-icon`,n.isValidElement(o)?null===(t=o.props)||void 0===t?void 0:t.className:void 0)})},[S,null==a?void 0:a.expandIcon,null==h?void 0:h.expandIcon,F]),Q=n.useMemo(()=>({prefixCls:F,inlineCollapsed:W||!1,direction:v,firstLevel:!0,theme:O,mode:M,disableMenuItemTitleTooltip:B}),[F,W,v,B,O]);return V(n.createElement(I.Z.Provider,{value:null},n.createElement(b.Provider,{value:Q},n.createElement(r.ZP,Object.assign({getPopupContainer:f,overflowedIndicator:n.createElement(l.Z,null),overflowedIndicatorPopupClassName:s()(F,`${F}-${O}`,R),mode:M,selectable:D,onClick:Z},P,{inlineCollapsed:W,style:Object.assign(Object.assign({},null==h?void 0:h.style),C),className:U,prefixCls:F,direction:v,defaultMotions:L,expandIcon:J,ref:t,rootClassName:s()(j,G,a.rootClassName,K,Y),_internalComponents:_})))))}),Y=(0,n.forwardRef)((e,t)=>{let o=(0,n.useRef)(null),r=n.useContext(i.D);return(0,n.useImperativeHandle)(t,()=>({menu:o.current,focus:e=>{var t;null===(t=o.current)||void 0===t||t.focus(e)}})),n.createElement(F,Object.assign({ref:o},e,r))});Y.Item=y,Y.SubMenu=L,Y.Divider=f,Y.ItemGroup=r.BW;let V=Y},71645:(e,t,o)=>{o.d(t,{Z:()=>n});let n=e=>({[e.componentCls]:{[`${e.antCls}-motion-collapse-legacy`]:{overflow:"hidden","&-active":{transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},
        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}},[`${e.antCls}-motion-collapse`]:{overflow:"hidden",transition:`height ${e.motionDurationMid} ${e.motionEaseInOut},
        opacity ${e.motionDurationMid} ${e.motionEaseInOut} !important`}}})},26782:(e,t,o)=>{o.d(t,{Fm:()=>u});var n=o(92959),r=o(5251);let i=new n.E4("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),l=new n.E4("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),a=new n.E4("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),s=new n.E4("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),d=new n.E4("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),c=new n.E4("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),m={"move-up":{inKeyframes:new n.E4("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),outKeyframes:new n.E4("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}})},"move-down":{inKeyframes:i,outKeyframes:l},"move-left":{inKeyframes:a,outKeyframes:s},"move-right":{inKeyframes:d,outKeyframes:c}},u=(e,t)=>{let{antCls:o}=e,n=`${o}-${t}`,{inKeyframes:i,outKeyframes:l}=m[t];return[(0,r.R)(n,i,l,e.motionDurationMid),{[`
        ${n}-enter,
        ${n}-appear
      `]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},[`${n}-leave`]:{animationTimingFunction:e.motionEaseInOutCirc}}]}}};