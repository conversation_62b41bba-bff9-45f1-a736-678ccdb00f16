(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{2723:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(13428),a=r(2265),o={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"}}]},name:"close-circle",theme:"filled"},s=r(46614),i=a.forwardRef(function(e,t){return a.createElement(s.Z,(0,n.Z)({},e,{ref:t,icon:o}))})},74548:function(e){var t,r,n,a,o,s,i,c,l,u,d,m,f,p,h,g,y,v,C,D,S,M;e.exports=(t="millisecond",r="second",n="minute",a="hour",o="week",s="month",i="quarter",c="year",l="date",u="Invalid Date",d=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,m=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,f=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},(h={})[p="en"]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}},g="$isDayjsObject",y=function(e){return e instanceof S||!(!e||!e[g])},v=function e(t,r,n){var a;if(!t)return p;if("string"==typeof t){var o=t.toLowerCase();h[o]&&(a=o),r&&(h[o]=r,a=o);var s=t.split("-");if(!a&&s.length>1)return e(s[0])}else{var i=t.name;h[i]=t,a=i}return!n&&a&&(p=a),a||!n&&p},C=function(e,t){if(y(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new S(r)},(D={s:f,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+f(Math.floor(r/60),2,"0")+":"+f(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),a=t.clone().add(n,s),o=r-a<0,i=t.clone().add(n+(o?-1:1),s);return+(-(n+(r-a)/(o?a-i:i-a))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(e){return({M:s,y:c,w:o,d:"day",D:l,h:a,m:n,s:r,ms:t,Q:i})[e]||String(e||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}}).l=v,D.i=y,D.w=function(e,t){return C(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})},M=(S=function(){function e(e){this.$L=v(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[g]=!0}var f=e.prototype;return f.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(D.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(d);if(n){var a=n[2]-1||0,o=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],a,n[3]||1,n[4]||0,n[5]||0,n[6]||0,o)):new Date(n[1],a,n[3]||1,n[4]||0,n[5]||0,n[6]||0,o)}}return new Date(t)}(e),this.init()},f.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},f.$utils=function(){return D},f.isValid=function(){return this.$d.toString()!==u},f.isSame=function(e,t){var r=C(e);return this.startOf(t)<=r&&r<=this.endOf(t)},f.isAfter=function(e,t){return C(e)<this.startOf(t)},f.isBefore=function(e,t){return this.endOf(t)<C(e)},f.$g=function(e,t,r){return D.u(e)?this[t]:this.set(r,e)},f.unix=function(){return Math.floor(this.valueOf()/1e3)},f.valueOf=function(){return this.$d.getTime()},f.startOf=function(e,t){var i=this,u=!!D.u(t)||t,d=D.p(e),m=function(e,t){var r=D.w(i.$u?Date.UTC(i.$y,t,e):new Date(i.$y,t,e),i);return u?r:r.endOf("day")},f=function(e,t){return D.w(i.toDate()[e].apply(i.toDate("s"),(u?[0,0,0,0]:[23,59,59,999]).slice(t)),i)},p=this.$W,h=this.$M,g=this.$D,y="set"+(this.$u?"UTC":"");switch(d){case c:return u?m(1,0):m(31,11);case s:return u?m(1,h):m(0,h+1);case o:var v=this.$locale().weekStart||0,C=(p<v?p+7:p)-v;return m(u?g-C:g+(6-C),h);case"day":case l:return f(y+"Hours",0);case a:return f(y+"Minutes",1);case n:return f(y+"Seconds",2);case r:return f(y+"Milliseconds",3);default:return this.clone()}},f.endOf=function(e){return this.startOf(e,!1)},f.$set=function(e,o){var i,u=D.p(e),d="set"+(this.$u?"UTC":""),m=((i={}).day=d+"Date",i[l]=d+"Date",i[s]=d+"Month",i[c]=d+"FullYear",i[a]=d+"Hours",i[n]=d+"Minutes",i[r]=d+"Seconds",i[t]=d+"Milliseconds",i)[u],f="day"===u?this.$D+(o-this.$W):o;if(u===s||u===c){var p=this.clone().set(l,1);p.$d[m](f),p.init(),this.$d=p.set(l,Math.min(this.$D,p.daysInMonth())).$d}else m&&this.$d[m](f);return this.init(),this},f.set=function(e,t){return this.clone().$set(e,t)},f.get=function(e){return this[D.p(e)]()},f.add=function(e,t){var i,l=this;e=Number(e);var u=D.p(t),d=function(t){var r=C(l);return D.w(r.date(r.date()+Math.round(t*e)),l)};if(u===s)return this.set(s,this.$M+e);if(u===c)return this.set(c,this.$y+e);if("day"===u)return d(1);if(u===o)return d(7);var m=((i={})[n]=6e4,i[a]=36e5,i[r]=1e3,i)[u]||1,f=this.$d.getTime()+e*m;return D.w(f,this)},f.subtract=function(e,t){return this.add(-1*e,t)},f.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||u;var n=e||"YYYY-MM-DDTHH:mm:ssZ",a=D.z(this),o=this.$H,s=this.$m,i=this.$M,c=r.weekdays,l=r.months,d=r.meridiem,f=function(e,r,a,o){return e&&(e[r]||e(t,n))||a[r].slice(0,o)},p=function(e){return D.s(o%12||12,e,"0")},h=d||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(m,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return D.s(t.$y,4,"0");case"M":return i+1;case"MM":return D.s(i+1,2,"0");case"MMM":return f(r.monthsShort,i,l,3);case"MMMM":return f(l,i);case"D":return t.$D;case"DD":return D.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return f(r.weekdaysMin,t.$W,c,2);case"ddd":return f(r.weekdaysShort,t.$W,c,3);case"dddd":return c[t.$W];case"H":return String(o);case"HH":return D.s(o,2,"0");case"h":return p(1);case"hh":return p(2);case"a":return h(o,s,!0);case"A":return h(o,s,!1);case"m":return String(s);case"mm":return D.s(s,2,"0");case"s":return String(t.$s);case"ss":return D.s(t.$s,2,"0");case"SSS":return D.s(t.$ms,3,"0");case"Z":return a}return null}(e)||a.replace(":","")})},f.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},f.diff=function(e,t,l){var u,d=this,m=D.p(t),f=C(e),p=(f.utcOffset()-this.utcOffset())*6e4,h=this-f,g=function(){return D.m(d,f)};switch(m){case c:u=g()/12;break;case s:u=g();break;case i:u=g()/3;break;case o:u=(h-p)/6048e5;break;case"day":u=(h-p)/864e5;break;case a:u=h/36e5;break;case n:u=h/6e4;break;case r:u=h/1e3;break;default:u=h}return l?u:D.a(u)},f.daysInMonth=function(){return this.endOf(s).$D},f.$locale=function(){return h[this.$L]},f.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=v(e,t,!0);return n&&(r.$L=n),r},f.clone=function(){return D.w(this.$d,this)},f.toDate=function(){return new Date(this.valueOf())},f.toJSON=function(){return this.isValid()?this.toISOString():null},f.toISOString=function(){return this.$d.toISOString()},f.toString=function(){return this.$d.toUTCString()},e}()).prototype,C.prototype=M,[["$ms",t],["$s",r],["$m",n],["$H",a],["$W","day"],["$M",s],["$y",c],["$D",l]].forEach(function(e){M[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),C.extend=function(e,t){return e.$i||(e(t,S,C),e.$i=!0),C},C.locale=v,C.isDayjs=y,C.unix=function(e){return C(1e3*e)},C.en=h[p],C.Ls=h,C.p={},C)},35316:function(e,t,r){var n,a;e.exports=(n=r(74548),a={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:function(e,t){return"W"===t?e+"周":e+"日"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},meridiem:function(e,t){var r=100*e+t;return r<600?"凌晨":r<900?"早上":r<1100?"上午":r<1300?"中午":r<1800?"下午":"晚上"}},(n&&"object"==typeof n&&"default"in n?n:{default:n}).default.locale(a,null,!0),a)},71184:function(e){var t,r,n,a,o,s,i,c,l,u,d,m,f;e.exports=(t={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},r=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\d/,a=/\d\d/,o=/\d\d?/,s=/\d*[^-_:/,()\s\d]+/,i={},c=function(e){return(e=+e)+(e>68?1900:2e3)},l=function(e){return function(t){this[e]=+t}},u=[/[+-]\d\d:?(\d\d)?|Z/,function(e){(this.zone||(this.zone={})).offset=function(e){if(!e||"Z"===e)return 0;var t=e.match(/([+-]|\d\d)/g),r=60*t[1]+(+t[2]||0);return 0===r?0:"+"===t[0]?-r:r}(e)}],d=function(e){var t=i[e];return t&&(t.indexOf?t:t.s.concat(t.f))},m=function(e,t){var r,n=i.meridiem;if(n){for(var a=1;a<=24;a+=1)if(e.indexOf(n(a,0,t))>-1){r=a>12;break}}else r=e===(t?"pm":"PM");return r},f={A:[s,function(e){this.afternoon=m(e,!1)}],a:[s,function(e){this.afternoon=m(e,!0)}],Q:[n,function(e){this.month=3*(e-1)+1}],S:[n,function(e){this.milliseconds=100*+e}],SS:[a,function(e){this.milliseconds=10*+e}],SSS:[/\d{3}/,function(e){this.milliseconds=+e}],s:[o,l("seconds")],ss:[o,l("seconds")],m:[o,l("minutes")],mm:[o,l("minutes")],H:[o,l("hours")],h:[o,l("hours")],HH:[o,l("hours")],hh:[o,l("hours")],D:[o,l("day")],DD:[a,l("day")],Do:[s,function(e){var t=i.ordinal,r=e.match(/\d+/);if(this.day=r[0],t)for(var n=1;n<=31;n+=1)t(n).replace(/\[|\]/g,"")===e&&(this.day=n)}],w:[o,l("week")],ww:[a,l("week")],M:[o,l("month")],MM:[a,l("month")],MMM:[s,function(e){var t=d("months"),r=(d("monthsShort")||t.map(function(e){return e.slice(0,3)})).indexOf(e)+1;if(r<1)throw Error();this.month=r%12||r}],MMMM:[s,function(e){var t=d("months").indexOf(e)+1;if(t<1)throw Error();this.month=t%12||t}],Y:[/[+-]?\d+/,l("year")],YY:[a,function(e){this.year=c(e)}],YYYY:[/\d{4}/,l("year")],Z:u,ZZ:u},function(e,n,a){a.p.customParseFormat=!0,e&&e.parseTwoDigitYear&&(c=e.parseTwoDigitYear);var o=n.prototype,s=o.parse;o.parse=function(e){var n=e.date,o=e.utc,c=e.args;this.$u=o;var l=c[1];if("string"==typeof l){var u=!0===c[2],d=!0===c[3],m=c[2];d&&(m=c[2]),i=this.$locale(),!u&&m&&(i=a.Ls[m]),this.$d=function(e,n,a,o){try{if(["x","X"].indexOf(n)>-1)return new Date(("X"===n?1e3:1)*e);var s=(function(e){var n,a;n=e,a=i&&i.formats;for(var o=(e=n.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,function(e,r,n){var o=n&&n.toUpperCase();return r||a[n]||t[n]||a[o].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,function(e,t,r){return t||r.slice(1)})})).match(r),s=o.length,c=0;c<s;c+=1){var l=o[c],u=f[l],d=u&&u[0],m=u&&u[1];o[c]=m?{regex:d,parser:m}:l.replace(/^\[|\]$/g,"")}return function(e){for(var t={},r=0,n=0;r<s;r+=1){var a=o[r];if("string"==typeof a)n+=a.length;else{var i=a.regex,c=a.parser,l=e.slice(n),u=i.exec(l)[0];c.call(t,u),e=e.replace(u,"")}}return function(e){var t=e.afternoon;if(void 0!==t){var r=e.hours;t?r<12&&(e.hours+=12):12===r&&(e.hours=0),delete e.afternoon}}(t),t}})(n)(e),c=s.year,l=s.month,u=s.day,d=s.hours,m=s.minutes,p=s.seconds,h=s.milliseconds,g=s.zone,y=s.week,v=new Date,C=u||(c||l?1:v.getDate()),D=c||v.getFullYear(),S=0;c&&!l||(S=l>0?l-1:v.getMonth());var M,_=d||0,I=m||0,w=p||0,P=h||0;return g?new Date(Date.UTC(D,S,C,_,I,w,P+60*g.offset*1e3)):a?new Date(Date.UTC(D,S,C,_,I,w,P)):(M=new Date(D,S,C,_,I,w,P),y&&(M=o(M).week(y).toDate()),M)}catch(e){return new Date("")}}(n,l,o,a),this.init(),m&&!0!==m&&(this.$L=this.locale(m).$L),(u||d)&&n!=this.format(l)&&(this.$d=new Date("")),i={}}else if(l instanceof Array)for(var p=l.length,h=1;h<=p;h+=1){c[1]=l[h-1];var g=a.apply(this,c);if(g.isValid()){this.$d=g.$d,this.$L=g.$L,this.init();break}h===p&&(this.$d=new Date(""))}else s.call(this,e)}})},69245:function(e,t,r){Promise.resolve().then(r.t.bind(r,73649,23)),Promise.resolve().then(r.t.bind(r,63385,23)),Promise.resolve().then(r.bind(r,36123))},37148:function(e,t,r){"use strict";r.d(t,{VM:function(){return u},cG:function(){return m},hd:function(){return d}});var n=r(58489),a=r(78387),o=r(12711);let s=e=>{let{componentCls:t}=e;return{[t]:{position:"relative",maxWidth:"100%",minHeight:1}}},i=(e,t)=>{let{prefixCls:r,componentCls:n,gridColumns:a}=e,o={};for(let e=a;e>=0;e--)0===e?(o["".concat(n).concat(t,"-").concat(e)]={display:"none"},o["".concat(n,"-push-").concat(e)]={insetInlineStart:"auto"},o["".concat(n,"-pull-").concat(e)]={insetInlineEnd:"auto"},o["".concat(n).concat(t,"-push-").concat(e)]={insetInlineStart:"auto"},o["".concat(n).concat(t,"-pull-").concat(e)]={insetInlineEnd:"auto"},o["".concat(n).concat(t,"-offset-").concat(e)]={marginInlineStart:0},o["".concat(n).concat(t,"-order-").concat(e)]={order:0}):(o["".concat(n).concat(t,"-").concat(e)]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:"0 0 ".concat(e/a*100,"%"),maxWidth:"".concat(e/a*100,"%")}],o["".concat(n).concat(t,"-push-").concat(e)]={insetInlineStart:"".concat(e/a*100,"%")},o["".concat(n).concat(t,"-pull-").concat(e)]={insetInlineEnd:"".concat(e/a*100,"%")},o["".concat(n).concat(t,"-offset-").concat(e)]={marginInlineStart:"".concat(e/a*100,"%")},o["".concat(n).concat(t,"-order-").concat(e)]={order:e});return o["".concat(n).concat(t,"-flex")]={flex:"var(--".concat(r).concat(t,"-flex)")},o},c=(e,t)=>i(e,t),l=(e,t,r)=>({["@media (min-width: ".concat((0,n.bf)(t),")")]:Object.assign({},c(e,r))}),u=(0,a.I$)("Grid",e=>{let{componentCls:t}=e;return{[t]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},()=>({})),d=e=>({xs:e.screenXSMin,sm:e.screenSMMin,md:e.screenMDMin,lg:e.screenLGMin,xl:e.screenXLMin,xxl:e.screenXXLMin}),m=(0,a.I$)("Grid",e=>{let t=(0,o.IX)(e,{gridColumns:24}),r=d(t);return delete r.xs,[s(t),c(t,""),c(t,"-xs"),Object.keys(r).map(e=>l(t,r[e],"-".concat(e))).reduce((e,t)=>Object.assign(Object.assign({},e),t),{})]},()=>({}))},68764:function(e,t,r){"use strict";var n=r(26314).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(76224));t.default=a.default},76224:function(e,t,r){"use strict";var n=r(26314).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(1462)),o=n(r(39153));let s={lang:Object.assign({placeholder:"请选择日期",yearPlaceholder:"请选择年份",quarterPlaceholder:"请选择季度",monthPlaceholder:"请选择月份",weekPlaceholder:"请选择周",rangePlaceholder:["开始日期","结束日期"],rangeYearPlaceholder:["开始年份","结束年份"],rangeMonthPlaceholder:["开始月份","结束月份"],rangeQuarterPlaceholder:["开始季度","结束季度"],rangeWeekPlaceholder:["开始周","结束周"]},a.default),timePickerLocale:Object.assign({},o.default)};s.lang.ok="确定",t.default=s},40174:function(e,t,r){"use strict";var n=r(26314).default;t.Z=void 0;var a=n(r(79967)),o=n(r(68764)),s=n(r(76224)),i=n(r(39153));let c="${label}不是一个有效的${type}",l={locale:"zh-cn",Pagination:a.default,DatePicker:s.default,TimePicker:i.default,Calendar:o.default,global:{placeholder:"请选择",close:"关闭"},Table:{filterTitle:"筛选",filterConfirm:"确定",filterReset:"重置",filterEmptyText:"无筛选项",filterCheckAll:"全选",filterSearchPlaceholder:"在筛选项中搜索",emptyText:"暂无数据",selectAll:"全选当页",selectInvert:"反选当页",selectNone:"清空所有",selectionAll:"全选所有",sortTitle:"排序",expand:"展开行",collapse:"关闭行",triggerDesc:"点击降序",triggerAsc:"点击升序",cancelSort:"取消排序"},Modal:{okText:"确定",cancelText:"取消",justOkText:"知道了"},Tour:{Next:"下一步",Previous:"上一步",Finish:"结束导览"},Popconfirm:{cancelText:"取消",okText:"确定"},Transfer:{titles:["",""],searchPlaceholder:"请输入搜索内容",itemUnit:"项",itemsUnit:"项",remove:"删除",selectCurrent:"全选当页",removeCurrent:"删除当页",selectAll:"全选所有",deselectAll:"取消全选",removeAll:"删除全部",selectInvert:"反选当页"},Upload:{uploading:"文件上传中",removeFile:"删除文件",uploadError:"上传错误",previewFile:"预览文件",downloadFile:"下载文件"},Empty:{description:"暂无数据"},Icon:{icon:"图标"},Text:{edit:"编辑",copy:"复制",copied:"复制成功",expand:"展开",collapse:"收起"},Form:{optional:"（可选）",defaultValidateMessages:{default:"字段验证错误${label}",required:"请输入${label}",enum:"${label}必须是其中一个[${enum}]",whitespace:"${label}不能为空字符",date:{format:"${label}日期格式无效",parse:"${label}不能转换为日期",invalid:"${label}是一个无效日期"},types:{string:c,method:c,array:c,object:c,number:c,date:c,boolean:c,integer:c,float:c,regexp:c,email:c,url:c,hex:c},string:{len:"${label}须为${len}个字符",min:"${label}最少${min}个字符",max:"${label}最多${max}个字符",range:"${label}须在${min}-${max}字符之间"},number:{len:"${label}必须等于${len}",min:"${label}最小值为${min}",max:"${label}最大值为${max}",range:"${label}须在${min}-${max}之间"},array:{len:"须为${len}个${label}",min:"最少${min}个${label}",max:"最多${max}个${label}",range:"${label}数量须在${min}-${max}之间"},pattern:{mismatch:"${label}与模式不匹配${pattern}"}}},Image:{preview:"预览"},QRCode:{expired:"二维码过期",refresh:"点击刷新",scanned:"已扫描"},ColorPicker:{presetEmpty:"暂无",transparent:"无色",singleColor:"单色",gradientColor:"渐变色"}};t.Z=l},39153:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default={placeholder:"请选择时间",rangePlaceholder:["开始时间","结束时间"]}},36123:function(e,t,r){"use strict";r.r(t),r.d(t,{AntdProvider:function(){return N}});var n=r(57437),a=r(2265),o=r(13292),s=r(57416),i=r(40174),c=r(74548),l=r.n(c);r(35316);var u=r(71184),d=r.n(u),m=r(94660),f=r(74810);let p=(0,m.Ue)()((0,f.mW)((0,f.tJ)((e,t)=>({employees:[],selectedEmployee:null,employeesLoading:!1,departments:[],selectedDepartment:null,departmentsLoading:!1,roles:[],selectedRole:null,rolesLoading:!1,permissions:[],filters:{},setEmployees:t=>{e({employees:t})},setSelectedEmployee:t=>e({selectedEmployee:t}),setEmployeesLoading:t=>e({employeesLoading:t}),setDepartments:t=>{e({departments:t})},setSelectedDepartment:t=>e({selectedDepartment:t}),setDepartmentsLoading:t=>e({departmentsLoading:t}),setRoles:t=>{e({roles:t})},setSelectedRole:t=>e({selectedRole:t}),setRolesLoading:t=>e({rolesLoading:t}),setPermissions:t=>e({permissions:t}),setFilters:t=>e(e=>({filters:{...e.filters,...t}})),clearFilters:()=>e({filters:{}}),getEmployeesByDepartment:e=>t().employees.filter(t=>t.department===e),getDepartmentEmployeeCount:e=>t().employees.filter(t=>t.department===e).length,generateNextEmployeeCode:()=>{let e=t().employees.map(e=>e.employeeCode),r=e.filter(e=>/^A\d{3}$/.test(e));if(e.filter(e=>/^A\d{4}$/.test(e)),r.length<999)for(let t=1;t<=999;t++){let r="A".concat(t.toString().padStart(3,"0"));if(!e.includes(r))return r}for(let t=1e3;t<=9999;t++){let r="A".concat(t);if(!e.includes(r))return r}return"A0001"}}),{name:"employee-store",partialize:e=>({employees:e.employees,departments:e.departments,roles:e.roles,permissions:e.permissions}),version:1,migrate:(e,t)=>0===t?{employees:e.employees||[],departments:e.departments||[],roles:e.roles||[],permissions:e.permissions||[]}:e}))),h={customers:[],selectedCustomer:null,customerVisits:[],selectedVisit:null,customerContacts:[],loading:!1,visitsLoading:!1,contactsLoading:!1,filters:{},pagination:{current:1,pageSize:10,total:0}},g=(0,m.Ue)()((0,f.mW)((0,f.tJ)((e,t)=>({...h,setCustomers:t=>e({customers:t}),getCustomerById:e=>t().customers.find(t=>t.id===e),setSelectedCustomer:t=>e({selectedCustomer:t}),setCustomerVisits:t=>e({customerVisits:t}),getVisitsByCustomer:e=>t().customerVisits.filter(t=>t.customerId===e),setSelectedVisit:t=>e({selectedVisit:t}),setCustomerContacts:t=>e({customerContacts:t}),getContactsByCustomer:e=>t().customerContacts.filter(t=>t.customerId===e),setLoading:t=>e({loading:t}),setVisitsLoading:t=>e({visitsLoading:t}),setContactsLoading:t=>e({contactsLoading:t}),setFilters:t=>e(e=>({filters:{...e.filters,...t}})),clearFilters:()=>e({filters:{}}),getFilteredCustomers:()=>{let{customers:e,filters:r}=t();return e.filter(e=>{if(r.customerLevel&&e.customerLevel!==r.customerLevel||r.customerCategory&&e.customerCategory!==r.customerCategory||r.status&&e.status!==r.status||r.region&&e.region!==r.region)return!1;if(r.searchKeyword){var t;let n=r.searchKeyword.toLowerCase();return e.customerName.toLowerCase().includes(n)||e.customerCode.toLowerCase().includes(n)||(null===(t=e.contactPerson)||void 0===t?void 0:t.toLowerCase().includes(n))}return!0})},setPagination:t=>e(e=>({pagination:{...e.pagination,...t}})),getCustomerStatistics:()=>{let{customers:e}=t(),r=e.length,n=e.filter(e=>"A"===e.customerLevel).length,a=e.filter(e=>"B"===e.customerLevel).length,o=e.filter(e=>"C"===e.customerLevel).length,s=e.reduce((e,t)=>e+(t.annualSalesAmount||0),0);return{total:r,aLevel:n,bLevel:a,cLevel:o,totalSales:s,totalCredit:e.reduce((e,t)=>e+(t.creditLimit||0),0),usedCredit:e.reduce((e,t)=>e+(t.usedCredit||0),0),activeCustomers:e.filter(e=>"active"===e.status).length}},checkCreditLimit:(e,r)=>{let n=t().getCustomerById(e);return!!n&&(n.usedCredit||0)+r<=(n.creditLimit||0)},evaluateCustomerLevel:e=>{let r=t().getCustomerById(e);if(!r)return"C";let n=r.annualSalesAmount||0;return n>=1e6?"A":n>=5e5?"B":"C"},fetchAll:async t=>{e({loading:!0}),e({loading:!1})},fetchById:async t=>{e({loading:!0}),e({loading:!1})},fetchCustomerVisits:async t=>{e({visitsLoading:!0}),e({visitsLoading:!1})},fetchCustomerContacts:async t=>{e({contactsLoading:!0}),e({contactsLoading:!1})},fetchCustomers:async e=>t().fetchAll(e),fetchCustomerById:async e=>t().fetchById(e),reset:()=>e(h)}),{name:"customer-store",partialize:e=>({customers:e.customers,customerVisits:e.customerVisits,customerContacts:e.customerContacts,filters:e.filters,pagination:e.pagination})}),{name:"customer-store-devtools"}));var y=r(57613),v=r(41207),C=r(32779);class D{static async syncSingleProductInventory(e){try{let t=M.getState().productInventory.find(t=>t.id===e);if(!t)return{success:!1,message:"产品库存记录不存在",updatedFields:[]};let n=await C.dataAccessManager.products.getByCode(t.productCode);if("success"!==n.status||!n.data)return{success:!1,message:"产品编码 ".concat(t.productCode," 在产品数据模块中不存在"),updatedFields:[]};let a=n.data,o=[],s={};if(t.productName!==a.modelName&&(s.productName=a.modelName,o.push("产品名称")),!(o.length>0))return{success:!0,message:"数据已是最新，无需同步",updatedFields:[]};{let{dataAccessManager:t}=await Promise.resolve().then(r.bind(r,32779));return await t.inventory.update(e,s),{success:!0,message:"成功同步 ".concat(o.join("、")),updatedFields:o}}}catch(e){return{success:!1,message:"同步失败: ".concat(e instanceof Error?e.message:"未知错误"),updatedFields:[]}}}static async batchSyncProductInventories(){try{let e=M.getState().productInventory,t=[],r=0,n=0;for(let a of e){let e=await this.syncSingleProductInventory(a.id);t.push({productCode:a.productCode,productName:a.productName,success:e.success,message:e.message,updatedFields:e.updatedFields}),e.success?r++:n++}return{success:0===n,totalCount:e.length,syncedCount:r,failedCount:n,details:t}}catch(e){return{success:!1,totalCount:0,syncedCount:0,failedCount:0,details:[]}}}static async handleProductModelChange(e,t){try{let n=M.getState().productInventory.filter(t=>t.productCode===e.modelCode);if(0===n.length)return{success:!0,affectedInventories:0,message:"没有相关的库存记录需要同步"};switch(t){case"create":return{success:!0,affectedInventories:n.length,message:"产品 ".concat(e.modelCode," 已创建，相关库存记录已关联")};case"update":for(let t of n)if(t.productName!==e.modelName){let{dataAccessManager:n}=await Promise.resolve().then(r.bind(r,32779));await n.inventory.update(t.productCode,{productName:e.modelName})}return{success:!0,affectedInventories:n.length,message:"已同步 ".concat(n.length," 条库存记录的产品信息")};case"delete":return{success:!1,affectedInventories:n.length,message:"产品已删除，但仍有 ".concat(n.length," 条库存记录需要处理")};case"status_change":return e.status,{success:!0,affectedInventories:n.length,message:"产品状态已变更为 ".concat("active"===e.status?"激活":"停用")};default:return{success:!1,affectedInventories:n.length,message:"未知的变更类型"}}}catch(e){return{success:!1,affectedInventories:0,message:"处理失败: ".concat(e instanceof Error?e.message:"未知错误")}}}static async checkAndRepairDataInconsistency(){try{let e=M.getState().productInventory,t=[],n=0,a=0;for(let o of e){let e=await v.InventoryValueCalculationService.validateProductConsistency(o);if(!e.isValid)for(let s of(n+=e.issues.length,e.issues)){let e=!1,n="需要手动处理";if(s.includes("名称不一致")){let t=await v.InventoryValueCalculationService.syncProductName(o);if(t){let{dataAccessManager:s}=await Promise.resolve().then(r.bind(r,32779));await s.inventory.update(o.productCode,{productName:t}),e=!0,n="已自动同步产品名称",a++}}t.push({productCode:o.productCode,issueType:this.getIssueType(s),description:s,fixed:e,action:n})}}return{success:!0,totalChecked:e.length,issuesFound:n,issuesFixed:a,remainingIssues:n-a,details:t}}catch(e){return{success:!1,totalChecked:0,issuesFound:0,issuesFixed:0,remainingIssues:0,details:[]}}}static getIssueType(e){return e.includes("不存在")?"产品不存在":e.includes("已停用")?"产品已停用":e.includes("名称不一致")?"名称不一致":e.includes("价格")?"价格问题":"其他问题"}static async validateNewProductInventory(e){let t=[],r=[];try{let n=await v.InventoryValueCalculationService.getProductDetails(e.productCode);return n.productModel?(n.isActive||r.push("产品 ".concat(e.productCode," 已停用")),e.productName!==n.productModel.modelName&&r.push('建议使用产品数据模块中的标准名称: "'.concat(n.productModel.modelName,'"')),n.hasValidPrice||r.push("产品 ".concat(e.productCode," 的价格未设置，库存价值将显示为0"))):t.push("产品编码 ".concat(e.productCode," 在产品数据模块中不存在")),e.productCode.trim()||t.push("产品编码不能为空"),e.productName.trim()||t.push("产品名称不能为空"),e.currentStock<0&&t.push("当前库存不能为负数"),e.safetyStock<0&&t.push("安全库存不能为负数"),e.maxStock<=e.safetyStock&&t.push("最大库存必须大于安全库存"),{isValid:0===t.length,errors:t,warnings:r}}catch(e){return{isValid:!1,errors:["验证过程中发生错误"],warnings:[]}}}static async getDataSyncStatusReport(){try{let e=M.getState().productInventory,t=await v.InventoryValueCalculationService.batchValidateProductConsistency(e);return{totalInventories:e.length,consistentCount:t.validCount,inconsistentCount:t.invalidCount,missingProductCount:t.summary.missingProducts,inactiveProductCount:t.summary.inactiveProducts,priceIssueCount:t.summary.priceIssues,lastSyncTime:new Date().toISOString()}}catch(e){return{totalInventories:0,consistentCount:0,inconsistentCount:0,missingProductCount:0,inactiveProductCount:0,priceIssueCount:0}}}}let S={productInventory:[],selectedProducts:[],materialInventory:[],selectedMaterials:[],stockAdjustments:[],replenishmentAlerts:[],inventoryChecks:[],currentCheck:null,loading:!1,adjustmentLoading:!1,checkLoading:!1,filters:{}},M=(0,m.Ue)()((0,f.mW)((0,f.tJ)((e,t)=>({...S,setProductInventory:t=>e({productInventory:t}),setSelectedProducts:t=>e({selectedProducts:t}),setMaterialInventory:t=>e({materialInventory:t}),setSelectedMaterials:t=>e({selectedMaterials:t}),getStockAdjustmentHistory:e=>{let{stockAdjustments:r}=t();return r.filter(t=>t.itemCode===e).sort((e,t)=>new Date(t.adjustmentDate).getTime()-new Date(e.adjustmentDate).getTime())},getActiveAlerts:()=>{let{replenishmentAlerts:e}=t();return e.filter(e=>"pending"===e.status)},setCurrentCheck:t=>e({currentCheck:t}),updateCheckItem:(t,r,n)=>e(e=>{var a;return{inventoryChecks:e.inventoryChecks.map(e=>e.id===t?{...e,items:e.items.map(e=>e.id===r?{...e,...n}:e)}:e),currentCheck:(null===(a=e.currentCheck)||void 0===a?void 0:a.id)===t&&e.currentCheck?{...e.currentCheck,items:e.currentCheck.items.map(e=>e.id===r?{...e,...n}:e)}:e.currentCheck}}),setLoading:t=>e({loading:t}),setAdjustmentLoading:t=>e({adjustmentLoading:t}),setCheckLoading:t=>e({checkLoading:t}),setFilters:t=>e(e=>({filters:{...e.filters,...t}})),clearFilters:()=>e({filters:{}}),getWarehouseStatistics:()=>{let{productInventory:e,materialInventory:n,replenishmentAlerts:a,inventoryChecks:o}=t(),{InventoryValueCalculationService:s}=r(41207),i=s.batchCalculateProductValues(e);return{totalProducts:e.length,totalMaterials:n.length,totalProductValue:i.summary.totalValue,totalMaterialValue:n.reduce((e,t)=>e+t.totalValue,0),lowStockProducts:e.filter(e=>"low"===e.status).length,lowStockMaterials:n.filter(e=>"low"===e.status).length,activeAlerts:a.filter(e=>"pending"===e.status).length,pendingChecks:o.filter(e=>"planning"===e.status||"in_progress"===e.status).length}},adjustStock:(e,r,n,a,o)=>{Date.now().toString(),new Date().toISOString(),"product"===r?t().productInventory.find(t=>t.productCode===e):t().materialInventory.find(t=>t.materialCode===e)},transferStock:(e,r,n,a,o,s,i)=>{Date.now().toString(),"从 ".concat(n," 转移到 ").concat(a,": ").concat(s),new Date().toISOString(),"product"===r?t().productInventory.find(t=>t.productCode===e):t().materialInventory.find(t=>t.materialCode===e)},checkLowStock:()=>{let{productInventory:n,materialInventory:a}=t(),o=[];n.forEach(e=>{e.currentStock<=e.safetyStock&&o.push({id:"alert_".concat(Date.now(),"_").concat(e.id),itemCode:e.productCode,itemName:e.productName,itemType:"product",currentStock:e.currentStock,minStock:e.safetyStock,shortage:e.safetyStock-e.currentStock,urgencyLevel:e.currentStock<.5*e.safetyStock?"high":"medium",suggestedQuantity:e.maxStock-e.currentStock,estimatedCost:(()=>{let{InventoryValueCalculationService:t}=r(41207),n=t.calculateProductValue(e);return(e.maxStock-e.currentStock)*n.unitPrice})(),supplierName:e.supplier||"未知供应商",leadTime:7,status:"pending"})}),a.forEach(e=>{e.currentStock<=e.minStock&&o.push({id:"alert_".concat(Date.now(),"_").concat(e.id),itemCode:e.materialCode,itemName:e.materialName,itemType:"material",currentStock:e.currentStock,minStock:e.minStock,shortage:e.minStock-e.currentStock,urgencyLevel:e.currentStock<.5*e.minStock?"high":"medium",suggestedQuantity:e.maxStock-e.currentStock,estimatedCost:(e.maxStock-e.currentStock)*e.unitPrice,supplierName:e.supplierName,leadTime:5,status:"pending"})}),e(e=>({replenishmentAlerts:[...e.replenishmentAlerts,...o]}))},getProductInventoryWithValue:()=>{let{productInventory:e}=t(),{results:r}=v.InventoryValueCalculationService.batchCalculateProductValues(e);return r.map(e=>({...e,category:"未分类"}))},getProductInventoryByCode:e=>{let{productInventory:r}=t();return r.find(t=>t.productCode===e)},syncProductInventoryData:async()=>{try{let e=await D.batchSyncProductInventories();return{success:e.success,syncedCount:e.syncedCount,errors:e.details.filter(e=>!e.success).map(e=>e.message)}}catch(e){return{success:!1,syncedCount:0,errors:["同步过程中发生错误"]}}},validateProductInventoryConsistency:()=>{let{productInventory:e}=t(),r=e.filter(e=>e.productCode&&e.productName).length,n=e.length-r;return{validCount:r,invalidCount:n,issues:n>0?["存在产品编码或名称为空的库存记录"]:[]}},reset:()=>e(S)}),{name:"warehouse-store",partialize:e=>({productInventory:e.productInventory,materialInventory:e.materialInventory,stockAdjustments:e.stockAdjustments,replenishmentAlerts:e.replenishmentAlerts,inventoryChecks:e.inventoryChecks,filters:e.filters})}),{name:"warehouse-store"}));var _=r(61735);let I={employees:!1,customers:!1,products:!1,inventory:!1,orders:!1},w=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t||!I[e.flagKey]){if(!t&&e.checkExistingData()){I[e.flagKey]=!0;return}try{e.initializeData(),I[e.flagKey]=!0}catch(t){console.error("❌ [dataInitializationService] ".concat(e.name,"初始化失败:"),t),I[e.flagKey]=!1}}},P=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=p.getState();w({name:"员工数据",flagKey:"employees",checkExistingData:()=>t.employees.length>0,initializeData:()=>{t.setDepartments(_.seedDepartments),t.setRoles(_.seedRoles),t.setPermissions(_.seedPermissions),t.setEmployees(_.seedEmployees)},seedData:_.seedEmployees,logEmoji:"\uD83D\uDC65"},e)},$=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=g.getState();w({name:"客户数据",flagKey:"customers",checkExistingData:()=>t.customers.length>0,initializeData:()=>{t.setCustomers(_.seedCustomers)},seedData:_.seedCustomers,logEmoji:"\uD83C\uDFE2"},e)},b=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=y.rm.getState();w({name:"产品数据",flagKey:"products",checkExistingData:()=>t.productModels.length>0,initializeData:()=>{t.setProductModels(_.seedProductModels)},seedData:_.seedProductModels,logEmoji:"\uD83D\uDCE6"},e)},k=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];console.log("\uD83D\uDCCB [dataInitializationService] initializeOrderData 被调用，force:",e),console.trace("\uD83D\uDD0D [dataInitializationService] initializeOrderData 调用栈:"),console.log("\uD83D\uDCCB [dataInitializationService] 订单数据由dataAccessManager自动管理")},x=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];console.log("\uD83D\uDE80 [dataInitializationService] 开始初始化所有主数据，force:",e);try{P(e),$(e),b(e),L(e),k(e),console.log("✅ [dataInitializationService] 所有主数据初始化完成")}catch(e){console.error("❌ [dataInitializationService] 初始化失败:",e)}},L=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=M.getState();w({name:"库存数据",flagKey:"inventory",checkExistingData:()=>t.productInventory.length>0,initializeData:()=>{t.setProductInventory(_.seedProductInventory)},seedData:_.seedProductInventory,logEmoji:"\uD83D\uDCCA"},e)},Y=!1,A=null,O=async()=>{if(console.log("\uD83D\uDE80 [DataInitializationProvider] performInitialization 被调用"),console.trace("\uD83D\uDD0D [DataInitializationProvider] 调用栈:"),Y){console.log("⏭️ [DataInitializationProvider] 模块已初始化，跳过");return}if(window.__ERP_INITIALIZING__){console.log("⏭️ [DataInitializationProvider] 正在初始化中，跳过");return}try{console.log("\uD83D\uDD27 [DataInitializationProvider] 开始初始化过程"),Y=!0,window.__ERP_INITIALIZING__=!0,console.log("\uD83D\uDCCB [DataInitializationProvider] 调用 initializeAllMasterData()"),x(),window.__ERP_INITIALIZED__=!0,window.__ERP_INITIALIZING__=!1,console.log("✅ [DataInitializationProvider] 初始化完成")}catch(e){throw console.error("❌ [DataInitializationProvider] 初始化失败:",e),Y=!1,window.__ERP_INITIALIZED__=!1,window.__ERP_INITIALIZING__=!1,e}},V=e=>{let{children:t}=e,r=(0,a.useRef)(0),o=(0,a.useRef)(!1),s=(0,a.useRef)(!1);return r.current+=1,s.current||(console.log("\uD83D\uDD04 [DataInitializationProvider] 组件首次渲染"),s.current=!0),(0,a.useEffect)(()=>{if(!o.current){if(window.__ERP_INITIALIZED__){o.current=!0;return}window.__ERP_INITIALIZING__||A||(console.log("\uD83D\uDE80 [DataInitializationProvider] 开始组件级初始化"),o.current=!0,console.log("\uD83D\uDCCB [DataInitializationProvider] 创建初始化Promise"),(A=O()).catch(e=>{A=null,o.current=!1}))}},[]),(0,n.jsx)(n.Fragment,{children:t})};l().extend(d()),l().locale("zh-cn");let N=e=>{let{children:t}=e,r=(0,a.useMemo)(()=>({token:{colorPrimary:"#0ea5e9",colorSuccess:"#10b981",colorWarning:"#f59e0b",colorError:"#ef4444",colorInfo:"#3b82f6",borderRadius:8,fontFamily:'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif'},components:{Layout:{headerBg:"#ffffff",siderBg:"#ffffff",bodyBg:"#f9fafb"},Menu:{itemSelectedBg:"#e6f7ff",itemHoverBg:"#f0f9ff",itemSelectedColor:"#0ea5e9"},Button:{borderRadius:8},Card:{borderRadius:12}}}),[]),c=(0,a.useMemo)(()=>({strict:!1}),[]);return(0,n.jsx)(o.ZP,{locale:i.Z,warning:c,theme:r,children:(0,n.jsx)(s.Z,{children:(0,n.jsx)(V,{children:t})})})}},41207:function(e,t,r){"use strict";r.r(t),r.d(t,{InventoryValueCalculationService:function(){return a}});var n=r(32779);class a{static async calculateProductValue(e){try{let t=await n.dataAccessManager.products.getByCode(e.productCode);if("success"!==t.status||!t.data)return{unitPrice:0,totalValue:0,priceSource:"default"};let r=t.data;if(!r||"active"!==r.status)return{unitPrice:0,totalValue:0,priceSource:"default"};{let t=r.productPrice||0,n=t*e.currentStock;return{unitPrice:t,totalValue:n,priceSource:"product_model",lastPriceUpdate:r.updatedAt}}}catch(e){return{unitPrice:0,totalValue:0,priceSource:"default"}}}static calculateProductValueSync(e){return{unitPrice:10,totalValue:10*e.currentStock,priceSource:"default",lastPriceUpdate:void 0}}static batchCalculateProductValues(e){let t=e.map(e=>{let t=this.calculateProductValueSync(e);return{...e,...t}}),r={totalProducts:t.length,totalValue:t.reduce((e,t)=>e+t.totalValue,0),validPriceCount:t.filter(e=>"product_model"===e.priceSource).length,invalidPriceCount:t.filter(e=>"default"===e.priceSource).length};return{results:t,summary:r}}static async getProductDetails(e){try{let t=await n.dataAccessManager.products.getByCode(e);if("success"!==t.status||!t.data)return{isActive:!1,hasValidPrice:!1};{let e=t.data;return{productModel:e,category:e.category||"未分类",isActive:"active"===e.status,hasValidPrice:(e.productPrice||0)>0}}}catch(e){return{isActive:!1,hasValidPrice:!1}}}static async validateProductConsistency(e){let t=[],r=[];try{let n=await this.getProductDetails(e.productCode);if(n.productModel){let a=n.productModel;n.isActive||(t.push("产品 ".concat(e.productCode," 已停用")),r.push("请激活产品或从库存中移除该产品")),e.productName!==a.modelName&&(t.push('产品名称不一致：库存中为"'.concat(e.productName,'"，产品数据中为"').concat(a.modelName,'"')),r.push("建议同步产品名称以保持数据一致性")),n.hasValidPrice||(t.push("产品 ".concat(e.productCode," 的价格未设置或为0")),r.push("请在产品数据模块中设置正确的产品价格"))}else t.push("产品编码 ".concat(e.productCode," 在产品数据模块中不存在")),r.push("请检查产品编码是否正确，或在产品数据模块中添加该产品");return{isValid:0===t.length,issues:t,suggestions:r}}catch(e){return{isValid:!1,issues:["验证过程中发生错误"],suggestions:["请检查系统状态并重试"]}}}static async batchValidateProductConsistency(e){let t=await Promise.all(e.map(async e=>{let t=await this.validateProductConsistency(e);return{productCode:e.productCode,productName:e.productName,...t}})),r=t.filter(e=>e.isValid).length,n=t.length-r,a=t.reduce((e,t)=>e+t.issues.length,0),o={missingProducts:t.filter(e=>e.issues.some(e=>e.includes("不存在"))).length,inactiveProducts:t.filter(e=>e.issues.some(e=>e.includes("已停用"))).length,nameInconsistencies:t.filter(e=>e.issues.some(e=>e.includes("名称不一致"))).length,priceIssues:t.filter(e=>e.issues.some(e=>e.includes("价格"))).length};return{validCount:r,invalidCount:n,totalIssues:a,detailedResults:t,summary:o}}static async syncProductName(e){try{let t=await this.getProductDetails(e.productCode);if(t.productModel)return t.productModel.modelName;return null}catch(e){return null}}static async getInventoryValueStatistics(e){let{results:t}=this.batchCalculateProductValues(e);if(0===t.length)return{totalValue:0,averageValue:0,highestValue:{productCode:"",value:0},lowestValue:{productCode:"",value:0},categoryBreakdown:[]};let r=t.reduce((e,t)=>e+t.totalValue,0),n=r/t.length,a=t.sort((e,t)=>t.totalValue-e.totalValue),o={productCode:a[0].productCode,value:a[0].totalValue},s={productCode:a[a.length-1].productCode,value:a[a.length-1].totalValue},i=new Map;for(let e of t){let t=(await this.getProductDetails(e.productCode)).category||"未分类";if(i.has(t)){let r=i.get(t);i.set(t,{value:r.value+e.totalValue,count:r.count+1})}else i.set(t,{value:e.totalValue,count:1})}return{totalValue:r,averageValue:n,highestValue:o,lowestValue:s,categoryBreakdown:Array.from(i.entries()).map(e=>{let[t,r]=e;return{category:t,value:r.value,count:r.count}})}}}},57613:function(e,t,r){"use strict";r.d(t,{Tx:function(){return s},rm:function(){return o}});var n=r(94660),a=r(74810);let o=(0,n.Ue)()((0,a.mW)((0,a.tJ)(e=>({materials:[],selectedMaterial:null,materialsLoading:!1,productModels:[],selectedProductModel:null,productModelsLoading:!1,setMaterials:t=>e({materials:t}),setSelectedMaterial:t=>e({selectedMaterial:t}),setMaterialsLoading:t=>e({materialsLoading:t}),setProductModels:t=>{e({productModels:t})},setSelectedProductModel:t=>e({selectedProductModel:t}),setProductModelsLoading:t=>e({productModelsLoading:t}),clearAll:()=>e({materials:[],selectedMaterial:null,materialsLoading:!1,productModels:[],selectedProductModel:null,productModelsLoading:!1}),initializeFromStorage:()=>{}}),{name:"master-data-store",partialize:e=>({materials:e.materials,productModels:e.productModels}),version:1,migrate:(e,t)=>0===t?{materials:e.materials||[],productModels:e.productModels||[]}:e}),{name:"master-data-store-devtools"})),s={clearLocalStorage:()=>{localStorage.removeItem("master-data-storage")},exportData:()=>{let e=o.getState(),t={materials:e.materials,productModels:e.productModels,exportTime:new Date().toISOString(),version:"1.0"},r=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),n=URL.createObjectURL(r),a=document.createElement("a");a.href=n,a.download="master-data-".concat(new Date().toISOString().split("T")[0],".json"),document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(n)},importData:e=>new Promise((t,r)=>{let n=new FileReader;n.onload=e=>{try{var n;let r=JSON.parse(null===(n=e.target)||void 0===n?void 0:n.result),a=o.getState();r.materials&&Array.isArray(r.materials)&&a.setMaterials(r.materials),r.productModels&&Array.isArray(r.productModels)&&a.setProductModels(r.productModels),t()}catch(e){r(e)}},n.onerror=()=>r(Error("文件读取失败")),n.readAsText(e)}),getStorageInfo:()=>{var e;let t=o.getState(),r=localStorage.getItem("master-data-storage");return{materialsCount:t.materials.length,productModelsCount:t.productModels.length,storageSize:r?new Blob([r]).size:0,lastUpdated:r?null===(e=JSON.parse(r).state)||void 0===e?void 0:e.updatedAt:null}}}},63385:function(){},73649:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},79967:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"}},2724:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.commonLocale=void 0,t.commonLocale={yearFormat:"YYYY",dayFormat:"D",cellMeridiemFormat:"A",monthBeforeYear:!0}},1462:function(e,t,r){"use strict";var n=r(26314).default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(r(36386)),o=r(2724),s=(0,a.default)((0,a.default)({},o.commonLocale),{},{locale:"zh_CN",today:"今天",now:"此刻",backToToday:"返回今天",ok:"确定",timeSelect:"选择时间",dateSelect:"选择日期",weekSelect:"选择周",clear:"清除",week:"周",month:"月",year:"年",previousMonth:"上个月 (翻页上键)",nextMonth:"下个月 (翻页下键)",monthSelect:"选择月份",yearSelect:"选择年份",decadeSelect:"选择年代",previousYear:"上一年 (Control键加左方向键)",nextYear:"下一年 (Control键加右方向键)",previousDecade:"上一年代",nextDecade:"下一年代",previousCentury:"上一世纪",nextCentury:"下一世纪",yearFormat:"YYYY年",cellDateFormat:"D",monthBeforeYear:!1});t.default=s},8104:function(e,t,r){var n=r(19040);e.exports=function(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e},e.exports.__esModule=!0,e.exports.default=e.exports},26314:function(e){e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports},36386:function(e,t,r){var n=r(8104);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}e.exports=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){n(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e},e.exports.__esModule=!0,e.exports.default=e.exports},89944:function(e,t,r){var n=r(61565).default;e.exports=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=n(a))return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},19040:function(e,t,r){var n=r(61565).default,a=r(89944);e.exports=function(e){var t=a(e,"string");return"symbol"==n(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports},61565:function(e){function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}},function(e){e.O(0,[9444,6254,6245,2217,7416,5166,2897,2779,2971,4938,1744],function(){return e(e.s=69245)}),_N_E=e.O()}]);