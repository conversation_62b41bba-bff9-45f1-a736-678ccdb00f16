(()=>{var e={};e.id=7809,e.ids=[7809],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},73113:(e,t,l)=>{"use strict";l.r(t),l.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>o,routeModule:()=>p,tree:()=>d});var s=l(50482),r=l(69108),a=l(62563),n=l.n(a),i=l(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);l.d(t,c);let d=["",{children:["master-data",{children:["employees",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,26546)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\employees\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(l.bind(l,52054)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(l.bind(l,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(l.t.bind(l,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\employees\\page.tsx"],m="/master-data/employees/page",u={require:l,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/master-data/employees/page",pathname:"/master-data/employees",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},38514:(e,t,l)=>{Promise.resolve().then(l.bind(l,25455))},54649:(e,t,l)=>{"use strict";l.d(t,{Z:()=>i});var s=l(65651),r=l(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var n=l(49809);let i=r.forwardRef(function(e,t){return r.createElement(n.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},89645:(e,t,l)=>{"use strict";l.d(t,{Z:()=>i});var s=l(65651),r=l(3729);let a={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var n=l(49809);let i=r.forwardRef(function(e,t){return r.createElement(n.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},2383:(e,t,l)=>{"use strict";l.d(t,{Z:()=>i});var s=l(65651),r=l(3729);let a={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM653.3 424.6l52.2 52.2a8.01 8.01 0 01-4.7 13.6l-179.4 21c-5.1.6-9.5-3.7-8.9-8.9l21-179.4c.8-6.6 8.9-9.4 13.6-4.7l52.4 52.4 256.2-256.2c3.1-3.1 8.2-3.1 11.3 0l42.4 42.4c3.1 3.1 3.1 8.2 0 11.3L653.3 424.6z"}}]},name:"import",theme:"outlined"};var n=l(49809);let i=r.forwardRef(function(e,t){return r.createElement(n.Z,(0,s.Z)({},e,{ref:t,icon:a}))})},16407:(e,t,l)=>{"use strict";l.d(t,{ZP:()=>w});var s=l(72375),r=l(3729),a=l.n(r),n=l(13743),i=l(84893),c=l(90263),d=l(73101),o=l(727),m=l(11779),u=l(47190);let p=null,x=e=>e(),h=[],j={};function f(){let{getContainer:e,duration:t,rtl:l,maxCount:s,top:r}=j,a=(null==e?void 0:e())||document.body;return{getContainer:()=>a,duration:t,rtl:l,maxCount:s,top:r}}let Z=a().forwardRef((e,t)=>{let{messageConfig:l,sync:s}=e,{getPrefixCls:c}=(0,r.useContext)(i.E_),d=j.prefixCls||c("message"),o=(0,r.useContext)(n.J),[u,p]=(0,m.K)(Object.assign(Object.assign(Object.assign({},l),{prefixCls:d}),o.message));return a().useImperativeHandle(t,()=>{let e=Object.assign({},u);return Object.keys(e).forEach(t=>{e[t]=(...e)=>(s(),u[t].apply(u,e))}),{instance:e,sync:s}}),p}),g=a().forwardRef((e,t)=>{let[l,s]=a().useState(f),r=()=>{s(f)};a().useEffect(r,[]);let n=(0,c.w6)(),i=n.getRootPrefixCls(),d=n.getIconPrefixCls(),o=n.getTheme(),m=a().createElement(Z,{ref:t,sync:r,messageConfig:l});return a().createElement(c.ZP,{prefixCls:i,iconPrefixCls:d,theme:o},n.holderRender?n.holderRender(m):m)});function v(){if(!p){let e=document.createDocumentFragment(),t={fragment:e};p=t,x(()=>{(0,d.q)()(a().createElement(g,{ref:e=>{let{instance:l,sync:s}=e||{};Promise.resolve().then(()=>{!t.instance&&l&&(t.instance=l,t.sync=s,v())})}}),e)});return}p.instance&&(h.forEach(e=>{let{type:t,skipped:l}=e;if(!l)switch(t){case"open":x(()=>{let t=p.instance.open(Object.assign(Object.assign({},j),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":x(()=>{null==p||p.instance.destroy(e.key)});break;default:x(()=>{var l;let r=(l=p.instance)[t].apply(l,(0,s.Z)(e.args));null==r||r.then(e.resolve),e.setCloseFn(r)})}}),h=[])}let y={open:function(e){let t=(0,u.J)(t=>{let l;let s={type:"open",config:e,resolve:t,setCloseFn:e=>{l=e}};return h.push(s),()=>{l?x(()=>{l()}):s.skipped=!0}});return v(),t},destroy:e=>{h.push({type:"destroy",key:e}),v()},config:function(e){j=Object.assign(Object.assign({},j),e),x(()=>{var e;null===(e=null==p?void 0:p.sync)||void 0===e||e.call(p)})},useMessage:m.Z,_InternalPanelDoNotUseOrYouWillBeFired:o.ZP};["success","info","warning","error","loading"].forEach(e=>{y[e]=(...t)=>(function(e,t){(0,c.w6)();let l=(0,u.J)(l=>{let s;let r={type:e,args:t,resolve:l,setCloseFn:e=>{s=e}};return h.push(r),()=>{s?x(()=>{s()}):r.skipped=!0}});return v(),l})(e,t)});let w=y},16408:(e,t,l)=>{"use strict";l.d(t,{Z:()=>y});var s=l(35864),r=l(4176),a=l(51966),n=l(3729),i=l(34132),c=l.n(i),d=l(74393),o=l(9286),m=l(84893),u=l(13878),p=l(59604),x=l(93142),h=l(59239),j=function(e,t){var l={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(l[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,s=Object.getOwnPropertySymbols(e);r<s.length;r++)0>t.indexOf(s[r])&&Object.prototype.propertyIsEnumerable.call(e,s[r])&&(l[s[r]]=e[s[r]]);return l};let f=(0,o.i)(e=>{let{prefixCls:t,className:l,closeIcon:s,closable:r,type:a,title:i,children:o,footer:f}=e,Z=j(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:g}=n.useContext(m.E_),v=g(),y=t||g("modal"),w=(0,u.Z)(v),[b,C,P]=(0,h.ZP)(y,w),N=`${y}-confirm`,I={};return I=a?{closable:null!=r&&r,title:"",footer:"",children:n.createElement(p.O,Object.assign({},e,{prefixCls:y,confirmPrefixCls:N,rootPrefixCls:v,content:o}))}:{closable:null==r||r,title:i,footer:null!==f&&n.createElement(x.$,Object.assign({},e)),children:o},b(n.createElement(d.s,Object.assign({prefixCls:y,className:c()(C,`${y}-pure-panel`,a&&N,a&&`${N}-${a}`,l,P,w)},Z,{closeIcon:(0,x.b)(y,s),closable:r},I)))});var Z=l(4531);function g(e){return(0,s.ZP)((0,s.uW)(e))}let v=a.Z;v.useModal=Z.Z,v.info=function(e){return(0,s.ZP)((0,s.cw)(e))},v.success=function(e){return(0,s.ZP)((0,s.vq)(e))},v.error=function(e){return(0,s.ZP)((0,s.AQ)(e))},v.warning=g,v.warn=g,v.confirm=function(e){return(0,s.ZP)((0,s.Au)(e))},v.destroyAll=function(){for(;r.Z.length;){let e=r.Z.pop();e&&e()}},v.config=s.ai,v._InternalPanelDoNotUseOrYouWillBeFired=f;let y=v},25455:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>H});var s=l(95344),r=l(3729),a=l(97854),n=l(284),i=l(53869),c=l(32979),d=l(7618),o=l(90377),m=l(10707),u=l(11157),p=l(52788),x=l(63724),h=l(27976),j=l(83984),f=l(43896),Z=l(36527),g=l(16408),v=l(18623),y=l(67383),w=l(46116),b=l(54649),C=l(33537),P=l(27385),N=l(27224),I=l(70469),k=l(46472),S=l(2383),D=l(89645),E=l(58535),A=l(37637),O=l(51221),_=l(16407),q=l(93759);let{Option:M}=a.default,{TextArea:F}=n.default,$=function({visible:e,onClose:t,departments:l,employees:i,onDepartmentUpdate:c,onEmployeeUpdate:f}){let[Z,v]=(0,r.useState)(!1),[y,w]=(0,r.useState)(null),[P]=d.Z.useForm(),I=()=>{let e=l.map(e=>e.departmentCode).filter(e=>/^D\d{3,4}$/.test(e));if(0===e.length)return"D001";let t=Math.max(...e.map(e=>{let t=e.match(/^D(\d{3,4})$/);return t?parseInt(t[1],10):0}))+1;return t>9999?(_.ZP.warning("编码已达到最大值D9999，请手动输入编码"),""):t<=999?`D${t.toString().padStart(3,"0")}`:`D${t.toString()}`},k=(e,t)=>!l.some(l=>l.departmentCode===e&&l.id!==t),S=e=>{w(e),v(!0),P.setFieldsValue(e)},D=e=>{if(l.some(t=>t.parentDepartmentId===e)){_.ZP.error("该部门下还有子部门，无法删除");return}if(i.some(t=>{let s=l.find(t=>t.id===e);return s&&t.department===s.departmentName})){_.ZP.error("该部门下还有员工，无法删除");return}c(l.filter(t=>t.id!==e)),_.ZP.success("部门删除成功")},A=async()=>{try{let e=await P.validateFields(),t=new Date().toISOString().split("T")[0];if(y){let s=l.map(l=>l.id===y.id?{...l,...e,updatedAt:t,employeeCount:i.filter(t=>t.department===e.departmentName).length}:l);if(y.departmentName!==e.departmentName){let l=i.map(l=>l.department===y.departmentName?{...l,department:e.departmentName,updatedAt:t}:l);f(l)}c(s),_.ZP.success("部门信息更新成功")}else{let s={id:Date.now().toString(),...e,level:e.parentDepartmentId?(l.find(t=>t.id===e.parentDepartmentId)?.level||0)+1:1,employeeCount:0,status:"active",createdAt:t,updatedAt:t};c([...l,s]),_.ZP.success("部门创建成功")}v(!1),P.resetFields()}catch(e){}},O=(e=>{let t=new Map;e.forEach(e=>t.set(e.id,e));let l=e.filter(e=>!e.parentDepartmentId),r=t=>{let l=e.filter(e=>e.parentDepartmentId===t.id).map(r);return{key:t.id,title:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[s.jsx(N.Z,{className:"mr-2"}),t.departmentName,(0,s.jsxs)(o.Z,{color:"blue",className:"ml-2",children:[t.employeeCount,"人"]})]}),(0,s.jsxs)(m.Z,{size:"small",children:[s.jsx(u.ZP,{type:"link",size:"small",icon:s.jsx(b.Z,{}),onClick:e=>{e.stopPropagation(),S(t)},children:"编辑"}),s.jsx(p.Z,{title:"确定要删除这个部门吗？",onConfirm:e=>{e?.stopPropagation(),D(t.id)},okText:"确定",cancelText:"取消",children:s.jsx(u.ZP,{type:"link",size:"small",danger:!0,icon:s.jsx(C.Z,{}),onClick:e=>e.stopPropagation(),children:"删除"})})]})]}),children:l.length>0?l:void 0}};return l.map(r)})(l);return(0,s.jsxs)(g.Z,{title:"部门管理",open:e,onCancel:t,footer:[s.jsx(u.ZP,{onClick:t,children:"关闭"},"close")],width:1200,children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[s.jsx("h3",{className:"text-lg font-medium",children:"组织架构"}),s.jsx(u.ZP,{type:"primary",icon:s.jsx(E.Z,{}),onClick:()=>{w(null),v(!0),P.resetFields();let e=I();e&&P.setFieldsValue({departmentCode:e})},children:"新建部门"})]}),(0,s.jsxs)(x.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(j.Z,{title:"部门树形结构",size:"small",children:O.length>0?s.jsx(q.Z,{treeData:O,defaultExpandAll:!0,showLine:!0,showIcon:!0}):s.jsx("div",{className:"text-center text-gray-500 py-8",children:"暂无部门数据"})})}),s.jsx(h.Z,{span:12,children:s.jsx(j.Z,{title:"部门统计",size:"small",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"部门总数："}),(0,s.jsxs)("span",{className:"font-medium",children:[l.length,"个"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"员工总数："}),(0,s.jsxs)("span",{className:"font-medium",children:[i.length,"人"]})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"平均部门规模："}),(0,s.jsxs)("span",{className:"font-medium",children:[l.length>0?Math.round(i.length/l.length):0,"人"]})]})]})})})]})]}),s.jsx(g.Z,{title:y?"编辑部门":"新建部门",open:Z,onOk:A,onCancel:()=>{v(!1),P.resetFields()},okText:"确认",cancelText:"取消",children:(0,s.jsxs)(d.Z,{form:P,layout:"vertical",initialValues:{status:"active"},children:[(0,s.jsxs)(x.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"departmentCode",label:"部门编码",rules:[{required:!0,message:"请输入部门编码"},{pattern:/^D\d{3,4}$/,message:"格式：DXXX或DXXXX（如：D001）"},{validator:(e,t)=>t?k(t,y?.id)?Promise.resolve():Promise.reject(Error("部门编码已存在，请使用其他编码")):Promise.resolve()}],children:s.jsx(n.default,{placeholder:"如：D001（自动生成）"})})}),s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"departmentName",label:"部门名称",rules:[{required:!0,message:"请输入部门名称"}],children:s.jsx(n.default,{placeholder:"请输入部门名称"})})})]}),s.jsx(d.Z.Item,{name:"parentDepartmentId",label:"上级部门",children:s.jsx(a.default,{placeholder:"请选择上级部门（可选）",allowClear:!0,children:l.filter(e=>e.id!==y?.id).map(e=>s.jsx(M,{value:e.id,children:e.departmentName},e.id))})}),s.jsx(d.Z.Item,{name:"departmentHead",label:"部门负责人",children:s.jsx(a.default,{placeholder:"请选择部门负责人（可选）",allowClear:!0,children:i.map(e=>(0,s.jsxs)(M,{value:e.id,children:[e.name," (",e.employeeCode,")"]},e.id))})}),s.jsx(d.Z.Item,{name:"description",label:"部门描述",children:s.jsx(F,{rows:3,placeholder:"请输入部门描述"})})]})})]})},{Option:z}=a.default,{TextArea:R}=n.default,{TabPane:X}=i.default;function T(){let{message:e}=c.Z.useApp(),[t,l]=(0,r.useState)([]),[_,q]=(0,r.useState)([]),[M,F]=(0,r.useState)(["admin","manager","employee","sales","viewer"]),[X,T]=(0,r.useState)(!1),[H,L]=(0,r.useState)(null),[V,U]=(0,r.useState)(!1),[B,G]=(0,r.useState)(!1),[J,K]=(0,r.useState)(!1),[W,Q]=(0,r.useState)(!1),[Y,ee]=(0,r.useState)(null),[et,el]=(0,r.useState)("basic"),[es]=d.Z.useForm(),[er,ea]=(0,r.useState)(""),[en,ei]=(0,r.useState)(void 0),[ec,ed]=(0,r.useState)(void 0),[eo,em]=(0,r.useState)(void 0),eu=async()=>{T(!0);try{let e=await (0,O.Ro)(()=>A.dataAccessManager.employees.getAll(),"获取员工数据");e&&e.items&&l(e.items)}catch(t){console.error("加载员工数据失败:",t),e.error("加载员工数据失败")}finally{T(!1)}},ep=async()=>{try{let e=await (0,O.Ro)(()=>A.dataAccessManager.employees.getAll(),"获取员工数据以提取部门信息");if(e&&e.items){let t=[...new Set(e.items.map(e=>e.department).filter(Boolean))].map((t,l)=>({id:`dept-${l+1}`,departmentName:t,departmentCode:`DEPT${String(l+1).padStart(3,"0")}`,description:`${t}部门`,level:1,employeeCount:e.items.filter(e=>e.department===t).length,status:"active",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}));q(t)}}catch(t){console.error("加载部门数据失败:",t),e.error("加载部门数据失败")}},ex=()=>{let e=t.reduce((e,t)=>{let l=parseInt(t.employeeCode.replace("EMP",""),10);return l>e?l:e},0);return`EMP${(e+1).toString().padStart(4,"0")}`};(0,r.useEffect)(()=>{eu(),ep()},[]);let eh=(e,l)=>!t.some(t=>t.employeeCode===e&&t.id!==l);(0,r.useEffect)(()=>{console.log("员工数据统计:",{employees:t.length,departments:_.length,roles:M.length})},[t.length,_.length,M.length]);let ej={active:{color:"green",text:"在职"},inactive:{color:"orange",text:"停职"},resigned:{color:"red",text:"离职"}},ef={admin:{color:"red",text:"管理员"},manager:{color:"blue",text:"经理"},employee:{color:"green",text:"员工"},sales:{color:"orange",text:"销售员"},viewer:{color:"gray",text:"访客"}},eZ=t.filter(e=>{let t=!er||e.name.toLowerCase().includes(er.toLowerCase())||e.employeeCode.toLowerCase().includes(er.toLowerCase()),l=!en||e.department===en,s=!ec||e.role===ec,r=!eo||e.status===eo;return t&&l&&s&&r}),eg=async()=>{try{let e=await A.dataAccessManager.employees.getAll();"success"===e.status&&e.data&&l(e.data.items||[])}catch(e){console.error("刷新员工数据失败:",e)}};(0,r.useEffect)(()=>{eg()},[]);let ev={total:t.length,active:t.filter(e=>"active"===e.status).length,managers:t.filter(e=>e.isDepartmentHead).length,departments:_.length,avgAge:0,newHires:t.filter(e=>{let t=new Date(e.hireDate),l=new Date;return l.setMonth(l.getMonth()-3),t>l}).length},ey=e=>{ee(e),U(!0),el("basic"),es.setFieldsValue(e)},ew=e=>{L(e),G(!0)},eb=async t=>{try{T(!0),await (0,O.Ro)(()=>A.dataAccessManager.employees.delete(t),"删除员工")&&(await eu(),e.success("员工删除成功"))}catch(t){console.error("删除员工失败:",t),e.error("删除员工失败，请稍后重试")}finally{T(!1)}},eC=async()=>{try{let t=await es.validateFields();if(T(!0),Y){if(!await (0,O.Ro)(()=>A.dataAccessManager.employees.update(Y.id,t),"更新员工信息"))return;await eu(),e.success("员工信息更新成功")}else{let l={...t,permissions:t.permissions||[],subordinates:[]};if(!await (0,O.Ro)(()=>A.dataAccessManager.employees.create(l),"创建员工"))return;await eu(),e.success("员工创建成功")}U(!1),el("basic"),es.resetFields()}catch(t){e.error("操作失败，请稍后重试"),console.error("员工操作失败:",t)}finally{T(!1)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"page-header",children:[s.jsx("h1",{className:"page-title",children:"员工信息"}),s.jsx("p",{className:"page-description",children:"基础数据 - 员工信息 - 管理员工基本信息、组织架构和权限设置"})]}),(0,s.jsxs)(x.Z,{gutter:[16,16],children:[s.jsx(h.Z,{xs:24,sm:6,children:s.jsx(j.Z,{children:s.jsx(f.Z,{title:"员工总数",value:ev.total,suffix:"人",prefix:s.jsx(P.Z,{})})})}),s.jsx(h.Z,{xs:24,sm:6,children:s.jsx(j.Z,{children:s.jsx(f.Z,{title:"在职员工",value:ev.active,suffix:"人",valueStyle:{color:"#52c41a"}})})}),s.jsx(h.Z,{xs:24,sm:6,children:s.jsx(j.Z,{children:s.jsx(f.Z,{title:"部门经理",value:ev.managers,suffix:"人",valueStyle:{color:"#1890ff"},prefix:s.jsx(N.Z,{})})})}),s.jsx(h.Z,{xs:24,sm:6,children:s.jsx(j.Z,{children:s.jsx(f.Z,{title:"部门数量",value:ev.departments,suffix:"个",valueStyle:{color:"#722ed1"}})})})]}),s.jsx(j.Z,{children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[s.jsx(n.default,{placeholder:"搜索员工姓名或编码",prefix:s.jsx(I.Z,{}),value:er,onChange:e=>ea(e.target.value),className:"w-full sm:w-64"}),s.jsx(a.default,{placeholder:"部门",value:en,onChange:ei,className:"w-full sm:w-32",allowClear:!0,children:_.map(e=>s.jsx(z,{value:e.departmentName,children:e.departmentName},e.id))}),(0,s.jsxs)(a.default,{placeholder:"角色",value:ec,onChange:ed,className:"w-full sm:w-32",allowClear:!0,children:[s.jsx(z,{value:"admin",children:"管理员"}),s.jsx(z,{value:"manager",children:"经理"}),s.jsx(z,{value:"employee",children:"员工"}),s.jsx(z,{value:"sales",children:"销售员"}),s.jsx(z,{value:"viewer",children:"访客"})]}),(0,s.jsxs)(a.default,{placeholder:"状态",value:eo,onChange:em,className:"w-full sm:w-32",allowClear:!0,children:[s.jsx(z,{value:"active",children:"在职"}),s.jsx(z,{value:"inactive",children:"停职"}),s.jsx(z,{value:"resigned",children:"离职"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[s.jsx(u.ZP,{icon:s.jsx(k.Z,{}),onClick:()=>{Q(!0)},children:"部门管理"}),s.jsx(u.ZP,{icon:s.jsx(N.Z,{}),onClick:()=>{K(!0)},children:"组织架构"}),s.jsx(u.ZP,{icon:s.jsx(S.Z,{}),onClick:()=>{e.info("导入功能开发中...")},children:"导入"}),s.jsx(u.ZP,{icon:s.jsx(D.Z,{}),onClick:()=>{e.info("导出功能开发中...")},children:"导出"}),s.jsx(u.ZP,{type:"primary",icon:s.jsx(E.Z,{}),onClick:()=>{ee(null),U(!0),el("basic"),es.resetFields();let e=ex();e&&es.setFieldsValue({employeeCode:e})},children:"新建员工"})]})]})}),s.jsx(j.Z,{title:"员工列表",children:s.jsx(Z.Z,{columns:[{title:"员工编码",dataIndex:"employeeCode",key:"employeeCode",width:100,fixed:"left"},{title:"姓名",dataIndex:"name",key:"name",width:100,fixed:"left"},{title:"部门",dataIndex:"department",key:"department",width:120},{title:"职位",dataIndex:"position",key:"position",width:120},{title:"联系电话",dataIndex:"phone",key:"phone",width:130},{title:"角色",dataIndex:"role",key:"role",width:100,render:e=>{let t=ef[e]||{color:"default",text:e};return s.jsx(o.Z,{color:t.color,children:t.text})}},{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>s.jsx(o.Z,{color:ej[e].color,children:ej[e].text})},{title:"入职日期",dataIndex:"hireDate",key:"hireDate",width:110},{title:"操作",key:"action",width:200,render:(e,t)=>(0,s.jsxs)(m.Z,{size:"small",children:[s.jsx(u.ZP,{type:"link",icon:s.jsx(w.Z,{}),onClick:()=>ew(t),children:"查看"}),s.jsx(u.ZP,{type:"link",icon:s.jsx(b.Z,{}),onClick:()=>ey(t),children:"编辑"}),s.jsx(p.Z,{title:"确定要删除这个员工吗？",onConfirm:()=>eb(t.id),okText:"确定",cancelText:"取消",children:s.jsx(u.ZP,{type:"link",danger:!0,icon:s.jsx(C.Z,{}),children:"删除"})})]})}],dataSource:eZ,rowKey:"id",loading:X,pagination:{total:eZ.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`},scroll:{x:1400}})}),s.jsx(g.Z,{title:Y?"编辑员工":"新建员工",open:V,onOk:eC,onCancel:()=>{U(!1),el("basic"),es.resetFields()},width:900,okText:"确认",cancelText:"取消",children:s.jsx(d.Z,{form:es,layout:"vertical",initialValues:{status:"active",role:"employee",permissions:[]},children:s.jsx(i.default,{activeKey:et,onChange:el,items:[{key:"basic",label:"基础信息",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(x.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"employeeCode",label:"员工编码",rules:[{required:!0,message:"请输入员工编码"},{pattern:/^A\d{3,4}$/,message:"格式：AXXX或AXXXX（如：A001）"},{validator:(e,t)=>t?eh(t,Y?.id)?Promise.resolve():Promise.reject(Error("员工编码已存在，请使用其他编码")):Promise.resolve()}],children:s.jsx(n.default,{placeholder:"如：A001（自动生成）"})})}),s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"name",label:"姓名",rules:[{required:!0,message:"请输入姓名"}],children:s.jsx(n.default,{placeholder:"请输入姓名"})})})]}),(0,s.jsxs)(x.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"position",label:"职位",rules:[{required:!0,message:"请输入职位"}],children:s.jsx(n.default,{placeholder:"请输入职位"})})}),s.jsx(h.Z,{span:12})]}),(0,s.jsxs)(x.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"phone",label:"联系电话",rules:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式"}],children:s.jsx(n.default,{placeholder:"请输入联系电话"})})}),s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"email",label:"邮箱",rules:[{type:"email",message:"请输入正确的邮箱格式"}],children:s.jsx(n.default,{placeholder:"请输入邮箱"})})})]}),(0,s.jsxs)(x.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"hireDate",label:"入职日期",rules:[{required:!0,message:"请选择入职日期"}],children:s.jsx(n.default,{type:"date"})})}),s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"status",label:"状态",rules:[{required:!0,message:"请选择状态"}],children:(0,s.jsxs)(a.default,{placeholder:"请选择状态",children:[s.jsx(z,{value:"active",children:"在职"}),s.jsx(z,{value:"inactive",children:"停职"}),s.jsx(z,{value:"resigned",children:"离职"})]})})})]}),s.jsx(d.Z.Item,{name:"remark",label:"备注",children:s.jsx(R,{rows:3,placeholder:"请输入备注信息"})})]})},{key:"organization",label:"组织关系",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(x.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"department",label:"所属部门",rules:[{required:!0,message:"请选择所属部门"}],children:s.jsx(a.default,{placeholder:"请选择所属部门",children:_.map(e=>s.jsx(z,{value:e.departmentName,children:e.departmentName},e.id))})})}),s.jsx(h.Z,{span:12,children:s.jsx(d.Z.Item,{name:"directSupervisor",label:"直接上级",children:s.jsx(a.default,{placeholder:"请选择直接上级",allowClear:!0,children:t.filter(e=>e.id!==Y?.id).map(e=>(0,s.jsxs)(z,{value:e.id,children:[e.name," (",e.employeeCode,")"]},e.id))})})})]}),s.jsx(d.Z.Item,{name:"isDepartmentHead",valuePropName:"checked",children:s.jsx(v.Z,{children:"是否部门负责人"})}),s.jsx("div",{className:"text-gray-500 text-center py-8",children:s.jsx("p",{children:"组织架构图和下属管理功能开发中..."})})]})},{key:"permissions",label:"权限设置",children:(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(d.Z.Item,{name:"role",label:"用户角色",rules:[{required:!0,message:"请选择用户角色"}],children:(0,s.jsxs)(a.default,{placeholder:"请选择用户角色",children:[s.jsx(z,{value:"admin",children:"系统管理员"}),s.jsx(z,{value:"manager",children:"部门经理"}),s.jsx(z,{value:"employee",children:"普通员工"}),s.jsx(z,{value:"sales",children:"销售员"}),s.jsx(z,{value:"viewer",children:"只读用户"})]})}),s.jsx(d.Z.Item,{name:"permissions",label:"功能权限",children:s.jsx(v.Z.Group,{children:(0,s.jsxs)(x.Z,{children:[s.jsx(h.Z,{span:8,children:s.jsx(v.Z,{value:"sales_read",children:"销售查看"})}),s.jsx(h.Z,{span:8,children:s.jsx(v.Z,{value:"sales_write",children:"销售编辑"})}),s.jsx(h.Z,{span:8,children:s.jsx(v.Z,{value:"customer_read",children:"客户查看"})}),s.jsx(h.Z,{span:8,children:s.jsx(v.Z,{value:"customer_write",children:"客户编辑"})}),s.jsx(h.Z,{span:8,children:s.jsx(v.Z,{value:"production_read",children:"生产查看"})}),s.jsx(h.Z,{span:8,children:s.jsx(v.Z,{value:"production_write",children:"生产编辑"})}),s.jsx(h.Z,{span:8,children:s.jsx(v.Z,{value:"finance_read",children:"财务查看"})}),s.jsx(h.Z,{span:8,children:s.jsx(v.Z,{value:"finance_write",children:"财务编辑"})}),s.jsx(h.Z,{span:8,children:s.jsx(v.Z,{value:"report_read",children:"报表查看"})})]})})}),s.jsx("div",{className:"text-gray-500 text-center py-8",children:s.jsx("p",{children:"详细权限配置和角色管理功能开发中..."})})]})}]})})}),s.jsx(g.Z,{title:"员工详情",open:B,onCancel:()=>G(!1),footer:[s.jsx(u.ZP,{onClick:()=>G(!1),children:"关闭"},"close")],width:800,children:H&&H.employeeCode&&H.name&&(0,s.jsxs)(y.Z,{bordered:!0,column:2,children:[s.jsx(y.Z.Item,{label:"员工编码",children:H.employeeCode}),s.jsx(y.Z.Item,{label:"姓名",children:H.name}),s.jsx(y.Z.Item,{label:"部门",children:H.department}),s.jsx(y.Z.Item,{label:"职位",children:H.position}),s.jsx(y.Z.Item,{label:"联系电话",children:H.phone||"-"}),s.jsx(y.Z.Item,{label:"邮箱",children:H.email||"-"}),s.jsx(y.Z.Item,{label:"角色",children:(()=>{let e=ef[H.role]||{color:"default",text:H.role};return s.jsx(o.Z,{color:e.color,children:e.text})})()}),s.jsx(y.Z.Item,{label:"状态",children:s.jsx(o.Z,{color:ej[H.status].color,children:ej[H.status].text})}),s.jsx(y.Z.Item,{label:"是否部门负责人",children:H.isDepartmentHead?"是":"否"}),s.jsx(y.Z.Item,{label:"入职日期",children:H.hireDate}),s.jsx(y.Z.Item,{label:"离职日期",children:H.resignDate||"-"}),s.jsx(y.Z.Item,{label:"直接上级",children:H.directSupervisor&&t.find(e=>e.id===H.directSupervisor)?.name||"-"}),(0,s.jsxs)(y.Z.Item,{label:"权限数量",span:2,children:[H.permissions.length,"个"]}),s.jsx(y.Z.Item,{label:"备注",span:2,children:H.remark||"-"})]})}),s.jsx(g.Z,{title:"组织架构",open:J,onCancel:()=>K(!1),footer:[s.jsx(u.ZP,{onClick:()=>K(!1),children:"关闭"},"close")],width:1e3,children:(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(x.Z,{gutter:16,children:_.map(e=>s.jsx(h.Z,{span:8,className:"mb-4",children:s.jsx(j.Z,{title:e.departmentName,size:"small",extra:(0,s.jsxs)(o.Z,{color:"blue",children:[e.employeeCount,"人"]}),children:s.jsx("div",{className:"space-y-2",children:t.filter(t=>t.department===e.departmentName).map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[s.jsx(P.Z,{className:"mr-1"}),e.name,e.isDepartmentHead&&s.jsx(o.Z,{color:"red",className:"ml-1",children:"负责人"})]}),(()=>{let t=ef[e.role]||{color:"default",text:e.role};return s.jsx(o.Z,{color:t.color,children:t.text})})()]},e.id))})})},e.id))}),s.jsx("div",{className:"text-gray-500 text-center py-8",children:s.jsx("p",{children:"树形组织架构图和层级管理功能开发中..."})})]})}),s.jsx($,{visible:W,onClose:()=>Q(!1),departments:_,employees:t,onDepartmentUpdate:e=>{q(e)},onEmployeeUpdate:e=>{l(e)}})]})}function H(){return s.jsx(c.Z,{children:s.jsx(T,{})})}},51221:(e,t,l)=>{"use strict";l.d(t,{Ro:()=>s});let s=async(e,t)=>{try{let t=await e();if("success"===t.status)return t.data||null;return t.code,t.message,t.message,null}catch(e){return e instanceof Error?e.message:String(e),null}}},26546:(e,t,l)=>{"use strict";l.r(t),l.d(t,{$$typeof:()=>a,__esModule:()=>r,default:()=>n});let s=(0,l(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\master-data\employees\page.tsx`),{__esModule:r,$$typeof:a}=s,n=s.default},52054:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>a});var s=l(25036),r=l(38834);function a({children:e}){return s.jsx(r.Z,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var l=e=>t(t.s=e),s=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,4441,7383,6274,996,6133],()=>l(73113));module.exports=s})();