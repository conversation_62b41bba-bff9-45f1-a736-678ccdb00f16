(()=>{var e={};e.id=9864,e.ids=[9864],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},68190:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>g,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(50482),o=r(69108),l=r(62563),n=r.n(l),a=r(68300),i={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);r.d(t,i);let c=["",{children:["data-status",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,28419)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\data-status\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\data-status\\page.tsx"],u="/data-status/page",g={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/data-status/page",pathname:"/data-status",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},59746:(e,t,r)=>{Promise.resolve().then(r.bind(r,34593))},90377:(e,t,r)=>{"use strict";r.d(t,{Z:()=>$});var s=r(3729),o=r(34132),l=r.n(o),n=r(24773),a=r(22624),i=r(46164),c=r(29545),d=r(30605),u=r(84893),g=r(92959),h=r(55002),p=r(22989),m=r(96373),x=r(13165);let k=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:s,componentCls:o,calc:l}=e,n=l(s).sub(r).equal(),a=l(t).sub(r).equal();return{[o]:Object.assign(Object.assign({},(0,p.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:n,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,g.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:a,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:n}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},f=e=>{let{lineWidth:t,fontSizeIcon:r,calc:s}=e,o=e.fontSizeSM;return(0,m.IX)(e,{tagFontSize:o,tagLineHeight:(0,g.bf)(s(e.lineHeightSM).mul(o).equal()),tagIconSize:s(r).sub(s(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},y=e=>({defaultBg:new h.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),S=(0,x.I$)("Tag",e=>k(f(e)),y);var b=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,s=Object.getOwnPropertySymbols(e);o<s.length;o++)0>t.indexOf(s[o])&&Object.prototype.propertyIsEnumerable.call(e,s[o])&&(r[s[o]]=e[s[o]]);return r};let w=s.forwardRef((e,t)=>{let{prefixCls:r,style:o,className:n,checked:a,onChange:i,onClick:c}=e,d=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:g,tag:h}=s.useContext(u.E_),p=g("tag",r),[m,x,k]=S(p),f=l()(p,`${p}-checkable`,{[`${p}-checkable-checked`]:a},null==h?void 0:h.className,n,x,k);return m(s.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},o),null==h?void 0:h.style),className:f,onClick:e=>{null==i||i(!a),null==c||c(e)}})))});var v=r(78701);let C=e=>(0,v.Z)(e,(t,{textColor:r,lightBorderColor:s,lightColor:o,darkColor:l})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:o,borderColor:s,"&-inverse":{color:e.colorTextLightSolid,background:l,borderColor:l},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),j=(0,x.bk)(["Tag","preset"],e=>C(f(e)),y),O=(e,t,r)=>{let s=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${s}Bg`],borderColor:e[`color${s}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},T=(0,x.bk)(["Tag","status"],e=>{let t=f(e);return[O(t,"success","Success"),O(t,"processing","Info"),O(t,"error","Error"),O(t,"warning","Warning")]},y);var P=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,s=Object.getOwnPropertySymbols(e);o<s.length;o++)0>t.indexOf(s[o])&&Object.prototype.propertyIsEnumerable.call(e,s[o])&&(r[s[o]]=e[s[o]]);return r};let W=s.forwardRef((e,t)=>{let{prefixCls:r,className:o,rootClassName:g,style:h,children:p,icon:m,color:x,onClose:k,bordered:f=!0,visible:y}=e,b=P(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:w,direction:v,tag:C}=s.useContext(u.E_),[O,W]=s.useState(!0),$=(0,n.Z)(b,["closeIcon","closable"]);s.useEffect(()=>{void 0!==y&&W(y)},[y]);let D=(0,a.o2)(x),M=(0,a.yT)(x),E=D||M,I=Object.assign(Object.assign({backgroundColor:x&&!E?x:void 0},null==C?void 0:C.style),h),A=w("tag",r),[N,_,q]=S(A),B=l()(A,null==C?void 0:C.className,{[`${A}-${x}`]:E,[`${A}-has-color`]:x&&!E,[`${A}-hidden`]:!O,[`${A}-rtl`]:"rtl"===v,[`${A}-borderless`]:!f},o,g,_,q),z=e=>{e.stopPropagation(),null==k||k(e),e.defaultPrevented||W(!1)},[,Z]=(0,i.Z)((0,i.w)(e),(0,i.w)(C),{closable:!1,closeIconRender:e=>{let t=s.createElement("span",{className:`${A}-close-icon`,onClick:z},e);return(0,c.wm)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),z(t)},className:l()(null==e?void 0:e.className,`${A}-close-icon`)}))}}),F="function"==typeof b.onClick||p&&"a"===p.type,L=m||null,R=L?s.createElement(s.Fragment,null,L,p&&s.createElement("span",null,p)):p,H=s.createElement("span",Object.assign({},$,{ref:t,className:B,style:I}),R,Z,D&&s.createElement(j,{key:"preset",prefixCls:A}),M&&s.createElement(T,{key:"status",prefixCls:A}));return N(F?s.createElement(d.Z,{component:"Tag"},H):H)});W.CheckableTag=w;let $=W},34593:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var s=r(95344),o=r(3729),l=r(90152),n=r(90377),a=r(10707),i=r(11157),c=r(83984),d=r(36527),u=r(37637);async function g(){try{let{dataAccessManager:e}=await Promise.resolve().then(r.bind(r,37637)),t=await e.workstations.getWorkstations();if("success"!==t.status||!t.data?.items)throw Error("获取工位数据失败");let s=t.data.items,o=0,l=[];for(let e of s){let t=e.batchNumberQueue?.length||0;if(t>0)try{let{dataAccessManager:s}=await Promise.resolve().then(r.bind(r,37637)),n=await s.workstations.update(e.id,{batchNumberQueue:[],currentMoldNumber:null,currentBatchNumber:null,lastEndTime:null});"success"===n.status?(o+=t,l.push(e.code)):console.error(`❌ 清理工位 ${e.code} 失败:`,n.message)}catch(t){console.error(`❌ 清理工位 ${e.code} 异常:`,t)}}return{success:!0,totalClearedItems:o,clearedWorkstations:l,message:`成功清理 ${o} 个队列任务，涉及 ${l.length} 个工位`}}catch(e){return console.error("❌ [clearSchedulingData] 清理工位队列异常:",e),{success:!1,totalClearedItems:0,clearedWorkstations:[],message:"清理工位队列时发生异常"}}}async function h(){try{let{dataAccessManager:e}=await Promise.resolve().then(r.bind(r,37637)),t=await e.workstations.resetAllWorkstationsToIdle();if("success"===t.status&&t.data)return console.log(`✅ [clearSchedulingData] 已重置 ${t.data.resetCount} 个工位状态`),{success:!0,resetWorkstations:t.data.resetCount,message:`成功重置 ${t.data.resetCount} 个工位状态`};return console.error("❌ [clearSchedulingData] 重置工位状态失败:",t.message),{success:!1,resetWorkstations:0,message:t.message||"重置工位状态失败"}}catch(e){return console.error("❌ [clearSchedulingData] 重置工位状态异常:",e),{success:!1,resetWorkstations:0,message:"重置工位状态时发生异常"}}}async function p(){try{let e=await function(){console.warn("⚠️ [clearSchedulingData] clearAllScheduledWorkOrders 函数已废弃，建议使用dataAccessManager重新实现");let{useProductionStore:e}=r(51806),t=e.getState(),s=t.productionWorkOrders;Object.entries(s.reduce((e,t)=>(e[t.status]=(e[t.status]||0)+1,e),{})).forEach(([e,t])=>{});let o=s.filter(e=>e.workstation||e.workstationCode||e.plannedStartTime||e.plannedEndTime);if(0===o.length)return{success:!0,clearedWorkOrders:0,message:"没有已排程的工单需要清理"};let l=[];return o.forEach(e=>{t.updateProductionWorkOrder(e.id,{workstation:void 0,workstationCode:void 0,plannedStartTime:void 0,plannedEndTime:void 0,status:"pending"}),l.push(e.id)}),{success:!0,clearedWorkOrders:l.length,clearedWorkOrderIds:l,message:`成功清理 ${l.length} 个工单的排程信息`}}(),t=await g(),s=await h();return{success:!0,workOrderResult:e,queueResult:t,statusResult:s,summary:{clearedWorkOrders:e.clearedWorkOrders,clearedQueueItems:t.totalClearedItems,resetWorkstations:s.resetWorkstations}}}catch(e){return console.error("❌ [clearSchedulingData] 完整清理异常:",e),{success:!1,error:e instanceof Error?e.message:"未知错误"}}}var m=r(48606);let{Title:x,Text:k}=l.default;function f(){let[e,t]=(0,o.useState)([]),[r,l]=(0,o.useState)([]),[g,h]=(0,o.useState)(0),f=async()=>{try{let e=await u.dataAccessManager.workstations.getAll();if("success"===e.status&&e.data){let r=Array.isArray(e.data)?e.data:e.data.items;t(r)}let r=await u.dataAccessManager.productionWorkOrders.getAll();if("success"===r.status&&r.data){let e=Array.isArray(r.data)?r.data:r.data.items;l(e)}}catch(e){console.error("加载数据失败:",e)}};(0,o.useEffect)(()=>{f()},[g]);let y=()=>{h(e=>e+1)},S=[{title:"工位编码",dataIndex:"code",key:"code",width:100},{title:"工位名称",dataIndex:"name",key:"name",width:200},{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>s.jsx(n.Z,{color:"active"===e?"green":"default",children:"active"===e?"启用":"停用"})},{title:"当前模具",dataIndex:"currentMoldNumber",key:"currentMoldNumber",width:120,render:e=>e||s.jsx(k,{type:"secondary",children:"无"})},{title:"当前批次",dataIndex:"currentBatchNumber",key:"currentBatchNumber",width:150,render:e=>e||s.jsx(k,{type:"secondary",children:"无"})},{title:"队列任务数",dataIndex:"batchNumberQueue",key:"queueCount",width:100,render:e=>s.jsx(n.Z,{color:e?.length>0?"orange":"default",children:e?.length||0})},{title:"最后结束时间",dataIndex:"lastEndTime",key:"lastEndTime",width:180,render:e=>e?new Date(e).toLocaleString():s.jsx(k,{type:"secondary",children:"无"})}],b=[{title:"批次号",dataIndex:"batchNumber",key:"batchNumber",width:150},{title:"产品名称",dataIndex:"productName",key:"productName",width:200},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>{let t=m.ux[e]||{antdColor:"default",text:e};return s.jsx(n.Z,{color:t.antdColor,children:t.text})}},{title:"分配工位",dataIndex:"workstationCode",key:"workstationCode",width:100,render:e=>e||s.jsx(k,{type:"secondary",children:"未分配"})},{title:"预计开始时间",dataIndex:"plannedStartTime",key:"plannedStartTime",width:180,render:e=>e?new Date(e).toLocaleString():s.jsx(k,{type:"secondary",children:"未排程"})},{title:"预计结束时间",dataIndex:"plannedEndTime",key:"plannedEndTime",width:180,render:e=>e?new Date(e).toLocaleString():s.jsx(k,{type:"secondary",children:"未排程"})}],w={total:e.length,active:e.filter(e=>"active"===e.status).length,withQueue:e.filter(e=>e.batchNumberQueue?.length>0).length,withMold:e.filter(e=>e.currentMoldNumber).length},v={total:r.length,pending:r.filter(e=>"pending"===e.status).length,scheduled:r.filter(e=>e.workstationCode||e.plannedStartTime).length,inProgress:r.filter(e=>"in_progress"===e.status).length};return(0,s.jsxs)("div",{style:{padding:"24px",maxWidth:"1400px",margin:"0 auto"},children:[s.jsx(x,{level:2,children:"\uD83D\uDCCA 系统数据状态查看"}),(0,s.jsxs)(a.Z,{style:{marginBottom:"24px"},children:[s.jsx(i.ZP,{type:"primary",onClick:y,children:"\uD83D\uDD04 刷新数据"}),s.jsx(i.ZP,{danger:!0,onClick:()=>{p(),y()},children:"\uD83E\uDDF9 执行完整清理"})]}),(0,s.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"16px",marginBottom:"24px"},children:[s.jsx(c.Z,{size:"small",children:(0,s.jsxs)("div",{style:{textAlign:"center"},children:[s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#1890ff"},children:w.active}),(0,s.jsxs)("div",{children:["启用工位 / ",w.total]})]})}),s.jsx(c.Z,{size:"small",children:(0,s.jsxs)("div",{style:{textAlign:"center"},children:[s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#fa8c16"},children:w.withQueue}),s.jsx("div",{children:"有队列任务的工位"})]})}),s.jsx(c.Z,{size:"small",children:(0,s.jsxs)("div",{style:{textAlign:"center"},children:[s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#52c41a"},children:v.pending}),s.jsx("div",{children:"待开始工单"})]})}),s.jsx(c.Z,{size:"small",children:(0,s.jsxs)("div",{style:{textAlign:"center"},children:[s.jsx("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#722ed1"},children:v.scheduled}),s.jsx("div",{children:"已排程工单"})]})})]}),(0,s.jsxs)(c.Z,{style:{marginBottom:"24px"},children:[s.jsx(x,{level:4,children:"\uD83C\uDFED 工位状态"}),s.jsx(d.Z,{columns:S,dataSource:e,rowKey:"id",size:"small",scroll:{x:1e3},pagination:!1})]}),(0,s.jsxs)(c.Z,{children:[s.jsx(x,{level:4,children:"\uD83D\uDCCB 生产工单状态"}),s.jsx(d.Z,{columns:b,dataSource:r,rowKey:"id",size:"small",scroll:{x:1200},pagination:{pageSize:10}})]})]})}},48606:(e,t,r)=>{"use strict";r.d(t,{Fi:()=>s,ux:()=>o});let s={in_plan:{color:"blue",text:"计划中"},planned:{color:"cyan",text:"已计划"},in_progress:{color:"orange",text:"生产中"},completed:{color:"green",text:"已完成"},cancelled:{color:"red",text:"已取消"}},o={pending:{color:"default",text:"待开始",antdColor:"default"},scheduled:{color:"blue",text:"已排程",antdColor:"blue"},in_progress:{color:"processing",text:"进行中",antdColor:"processing"},completed:{color:"success",text:"已完成",antdColor:"success"},paused:{color:"warning",text:"暂停",antdColor:"warning"},cancelled:{color:"error",text:"已取消",antdColor:"error"},exception:{color:"error",text:"异常",antdColor:"error"}}},51806:(e,t,r)=>{"use strict";r.r(t),r.d(t,{useProductionStore:()=>n});var s=r(43158),o=r(67023);let l={loading:{orders:!1,workOrders:!1,workstations:!1,reports:!1,calculations:!1,scheduling:!1,general:!1},schedulingState:{isPreviewMode:!1,workstationSnapshot:null,lastTransaction:null,selectedDate:null,viewMode:"day",selectedWorkstation:null,draggedTask:null},errors:{orders:null,workOrders:null,workstations:null,reports:null,calculations:null,scheduling:null,general:null},filters:{orderStatus:[],workstationTypes:[],dateRange:null,searchText:""},selections:{selectedOrders:[],selectedWorkOrders:[],selectedWorkstations:[]}},n=(0,s.Ue)()((0,o.tJ)((e,t)=>({...l,setLoading:(t,r)=>e(e=>({loading:{...e.loading,[t]:r}})),setLoadingMultiple:t=>e(e=>({loading:{...e.loading,...t}})),clearAllLoading:()=>e(e=>({loading:Object.keys(e.loading).reduce((e,t)=>({...e,[t]:!1}),{})})),setError:(t,r)=>e(e=>({errors:{...e.errors,[t]:r}})),clearError:t=>e(e=>({errors:{...e.errors,[t]:null}})),clearAllErrors:()=>e(e=>({errors:Object.keys(e.errors).reduce((e,t)=>({...e,[t]:null}),{})})),isInPreviewMode:()=>t().schedulingState.isPreviewMode,enablePreviewMode:()=>e(e=>({schedulingState:{...e.schedulingState,isPreviewMode:!0}})),disablePreviewMode:()=>e(e=>({schedulingState:{...e.schedulingState,isPreviewMode:!1}})),setSchedulingDate:t=>e(e=>({schedulingState:{...e.schedulingState,selectedDate:t}})),setSchedulingViewMode:t=>e(e=>({schedulingState:{...e.schedulingState,viewMode:t}})),setSelectedWorkstation:t=>e(e=>({schedulingState:{...e.schedulingState,selectedWorkstation:t}})),setDraggedTask:t=>e(e=>({schedulingState:{...e.schedulingState,draggedTask:t}})),applySchedulingTransaction:t=>e(e=>({schedulingState:{...e.schedulingState,lastTransaction:t,isPreviewMode:!1}})),rollbackSchedulingTransaction:()=>e(e=>({schedulingState:{...e.schedulingState,isPreviewMode:!1,workstationSnapshot:null,lastTransaction:null}})),createWorkstationSnapshot:()=>{let t={id:"snapshot-"+Date.now(),timestamp:new Date().toISOString(),workstations:[],metadata:{reason:"scheduling_preview",createdBy:"system"}};return e(e=>({schedulingState:{...e.schedulingState,workstationSnapshot:t}})),t},restoreWorkstationSnapshot:t=>e(e=>({schedulingState:{...e.schedulingState,workstationSnapshot:t}})),setOrderStatusFilter:t=>e(e=>({filters:{...e.filters,orderStatus:t}})),setWorkstationTypesFilter:t=>e(e=>({filters:{...e.filters,workstationTypes:t}})),setDateRangeFilter:t=>e(e=>({filters:{...e.filters,dateRange:t}})),setSearchText:t=>e(e=>({filters:{...e.filters,searchText:t}})),clearFilters:()=>e(e=>({filters:{orderStatus:[],workstationTypes:[],dateRange:null,searchText:""}})),setSelectedOrders:t=>e(e=>({selections:{...e.selections,selectedOrders:t}})),setSelectedWorkOrders:t=>e(e=>({selections:{...e.selections,selectedWorkOrders:t}})),setSelectedWorkstations:t=>e(e=>({selections:{...e.selections,selectedWorkstations:t}})),toggleOrderSelection:t=>e(e=>{let r=e.selections.selectedOrders,s=r.includes(t)?r.filter(e=>e!==t):[...r,t];return{selections:{...e.selections,selectedOrders:s}}}),toggleWorkOrderSelection:t=>e(e=>{let r=e.selections.selectedWorkOrders,s=r.includes(t)?r.filter(e=>e!==t):[...r,t];return{selections:{...e.selections,selectedWorkOrders:s}}}),toggleWorkstationSelection:t=>e(e=>{let r=e.selections.selectedWorkstations,s=r.includes(t)?r.filter(e=>e!==t):[...r,t];return{selections:{...e.selections,selectedWorkstations:s}}}),clearAllSelections:()=>e(e=>({selections:{selectedOrders:[],selectedWorkOrders:[],selectedWorkstations:[]}})),resetSchedulingState:()=>e(e=>({schedulingState:{isPreviewMode:!1,workstationSnapshot:null,lastTransaction:null,selectedDate:null,viewMode:"day",selectedWorkstation:null,draggedTask:null}})),resetFilters:()=>e(e=>({filters:{orderStatus:[],workstationTypes:[],dateRange:null,searchText:""}})),resetSelections:()=>e(e=>({selections:{selectedOrders:[],selectedWorkOrders:[],selectedWorkstations:[]}})),resetAllUIState:()=>e(()=>({...l}))}),{name:"production-ui-store",partialize:e=>({schedulingState:{viewMode:e.schedulingState.viewMode,isPreviewMode:e.schedulingState.isPreviewMode},filters:{orderStatus:e.filters.orderStatus,workstationTypes:e.filters.workstationTypes,searchText:e.filters.searchText}}),version:1,migrate:(e,t)=>0===t?{schedulingState:{viewMode:e.schedulingState?.viewMode||"day",isPreviewMode:!1},filters:{orderStatus:e.filters?.orderStatus||[],workstationTypes:e.filters?.workstationTypes||[],searchText:e.filters?.searchText||""}}:e}))},28419:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>l,__esModule:()=>o,default:()=>n});let s=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\data-status\page.tsx`),{__esModule:o,$$typeof:l}=s,n=s.default}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,1880,8811,3984,7883,8699,6527,6879,152,6274,996],()=>r(68190));module.exports=s})();