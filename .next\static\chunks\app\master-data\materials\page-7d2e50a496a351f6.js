(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1469],{8349:function(e,t,l){"use strict";l.d(t,{Z:function(){return n}});var s=l(13428),a=l(2265),r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 144H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H212V212h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16zm-52 268H612V212h200v200zM464 544H160c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H212V612h200v200zm452-268H560c-8.8 0-16 7.2-16 16v304c0 8.8 7.2 16 16 16h304c8.8 0 16-7.2 16-16V560c0-8.8-7.2-16-16-16zm-52 268H612V612h200v200z"}}]},name:"appstore",theme:"outlined"},c=l(46614),n=a.forwardRef(function(e,t){return a.createElement(c.Z,(0,s.Z)({},e,{ref:t,icon:r}))})},65362:function(e,t,l){"use strict";l.d(t,{Z:function(){return n}});var s=l(13428),a=l(2265),r={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"},c=l(46614),n=a.forwardRef(function(e,t){return a.createElement(c.Z,(0,s.Z)({},e,{ref:t,icon:r}))})},51769:function(e,t,l){"use strict";l.d(t,{Z:function(){return n}});var s=l(13428),a=l(2265),r={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"},c=l(46614),n=a.forwardRef(function(e,t){return a.createElement(c.Z,(0,s.Z)({},e,{ref:t,icon:r}))})},33477:function(e,t,l){"use strict";l.d(t,{Z:function(){return n}});var s=l(13428),a=l(2265),r={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM653.3 424.6l52.2 52.2a8.01 8.01 0 01-4.7 13.6l-179.4 21c-5.1.6-9.5-3.7-8.9-8.9l21-179.4c.8-6.6 8.9-9.4 13.6-4.7l52.4 52.4 256.2-256.2c3.1-3.1 8.2-3.1 11.3 0l42.4 42.4c3.1 3.1 3.1 8.2 0 11.3L653.3 424.6z"}}]},name:"import",theme:"outlined"},c=l(46614),n=a.forwardRef(function(e,t){return a.createElement(c.Z,(0,s.Z)({},e,{ref:t,icon:r}))})},51862:function(e,t,l){Promise.resolve().then(l.bind(l,823))},55175:function(e,t,l){"use strict";var s=l(16141),a=l(2265),r=l(40955),c=l(57499),n=l(13292),i=l(38140),d=l(70002),o=l(82432),u=l(83350);let h=null,x=e=>e(),f=[],m={};function p(){let{getContainer:e,duration:t,rtl:l,maxCount:s,top:a}=m,r=(null==e?void 0:e())||document.body;return{getContainer:()=>r,duration:t,rtl:l,maxCount:s,top:a}}let j=a.forwardRef((e,t)=>{let{messageConfig:l,sync:s}=e,{getPrefixCls:n}=(0,a.useContext)(c.E_),i=m.prefixCls||n("message"),d=(0,a.useContext)(r.J),[u,h]=(0,o.K)(Object.assign(Object.assign(Object.assign({},l),{prefixCls:i}),d.message));return a.useImperativeHandle(t,()=>{let e=Object.assign({},u);return Object.keys(e).forEach(t=>{e[t]=function(){for(var e=arguments.length,l=Array(e),a=0;a<e;a++)l[a]=arguments[a];return s(),u[t].apply(u,l)}}),{instance:e,sync:s}}),h}),v=a.forwardRef((e,t)=>{let[l,s]=a.useState(p),r=()=>{s(p)};a.useEffect(r,[]);let c=(0,n.w6)(),i=c.getRootPrefixCls(),d=c.getIconPrefixCls(),o=c.getTheme(),u=a.createElement(j,{ref:t,sync:r,messageConfig:l});return a.createElement(n.ZP,{prefixCls:i,iconPrefixCls:d,theme:o},c.holderRender?c.holderRender(u):u)});function Z(){if(!h){let e=document.createDocumentFragment(),t={fragment:e};h=t,x(()=>{(0,i.q)()(a.createElement(v,{ref:e=>{let{instance:l,sync:s}=e||{};Promise.resolve().then(()=>{!t.instance&&l&&(t.instance=l,t.sync=s,Z())})}}),e)});return}h.instance&&(f.forEach(e=>{let{type:t,skipped:l}=e;if(!l)switch(t){case"open":x(()=>{let t=h.instance.open(Object.assign(Object.assign({},m),e.config));null==t||t.then(e.resolve),e.setCloseFn(t)});break;case"destroy":x(()=>{null==h||h.instance.destroy(e.key)});break;default:x(()=>{var l;let a=(l=h.instance)[t].apply(l,(0,s.Z)(e.args));null==a||a.then(e.resolve),e.setCloseFn(a)})}}),f=[])}let g={open:function(e){let t=(0,u.J)(t=>{let l;let s={type:"open",config:e,resolve:t,setCloseFn:e=>{l=e}};return f.push(s),()=>{l?x(()=>{l()}):s.skipped=!0}});return Z(),t},destroy:e=>{f.push({type:"destroy",key:e}),Z()},config:function(e){m=Object.assign(Object.assign({},m),e),x(()=>{var e;null===(e=null==h?void 0:h.sync)||void 0===e||e.call(h)})},useMessage:o.Z,_InternalPanelDoNotUseOrYouWillBeFired:d.ZP};["success","info","warning","error","loading"].forEach(e=>{g[e]=function(){for(var t=arguments.length,l=Array(t),s=0;s<t;s++)l[s]=arguments[s];return function(e,t){(0,n.w6)();let l=(0,u.J)(l=>{let s;let a={type:e,args:t,resolve:l,setCloseFn:e=>{s=e}};return f.push(a),()=>{s?x(()=>{s()}):a.skipped=!0}});return Z(),l}(e,l)}}),t.ZP=g},823:function(e,t,l){"use strict";l.r(t);var s=l(57437),a=l(2265),r=l(27296),c=l(89198),n=l(55175),i=l(6053),d=l(65270),o=l(94734),u=l(92503),h=l(38302),x=l(28683),f=l(89511),m=l(86155),p=l(39992),j=l(50574),v=l(47628),Z=l(75123),g=l(75216),y=l(65362),w=l(34021),P=l(8349),C=l(75393),b=l(33477),k=l(51769),N=l(74898);let{Option:A}=r.default;t.default=()=>{let[e,t]=(0,a.useState)(!1),[l,z]=(0,a.useState)(null),[I]=c.Z.useForm(),[E,F]=(0,a.useState)([]),[S,H]=(0,a.useState)(!1),O=async()=>{H(!0);try{F([{id:"1",materialCode:"FP-A001",materialName:"聚丙烯原料",purchasePrice:8500,unit:"吨",category:"原材料",supplier:"中石化上海分公司",status:"active",createdAt:"2024-01-15",updatedAt:"2024-01-15"},{id:"2",materialCode:"FP-A002",materialName:"聚乙烯原料",purchasePrice:9200,unit:"吨",category:"原材料",supplier:"中石油北京分公司",status:"active",createdAt:"2024-01-16",updatedAt:"2024-01-16"},{id:"3",materialCode:"AD-B001",materialName:"抗氧剂",purchasePrice:35e3,unit:"公斤",category:"辅料",supplier:"巴斯夫化学有限公司",status:"active",createdAt:"2024-01-17",updatedAt:"2024-01-17"},{id:"4",materialCode:"PK-C001",materialName:"纸箱包装",purchasePrice:12,unit:"个",category:"包装材料",supplier:"华润包装材料有限公司",status:"active",createdAt:"2024-01-18",updatedAt:"2024-01-18"}])}catch(e){console.error("加载物料数据失败:",e),n.ZP.error("加载物料数据失败")}finally{H(!1)}};(0,a.useEffect)(()=>{O()},[]);let V={active:{color:"green",text:"启用"},inactive:{color:"red",text:"停用"}},M=["原材料","辅料","包装材料","半成品","成品"],R=e=>{z(e),t(!0),I.setFieldsValue(e)},_=async e=>{try{H(!0);let t=E.filter(t=>t.id!==e);F(t),n.ZP.success("物料删除成功")}catch(e){n.ZP.error("删除物料失败，请稍后重试"),console.error("删除物料失败:",e)}finally{H(!1)}},q=async()=>{try{let e=await I.validateFields();H(!0);let s=new Date().toISOString().split("T")[0];if(l){let t=E.map(t=>t.id===l.id?{...t,...e,updatedAt:s}:t);F(t),n.ZP.success("物料更新成功")}else{let t={id:Date.now().toString(),...e,createdAt:s,updatedAt:s};F([...E,t]),n.ZP.success("物料创建成功")}t(!1),I.resetFields()}catch(e){n.ZP.error("操作失败，请稍后重试"),console.error("物料操作失败:",e)}finally{H(!1)}},T=E.length,B=E.filter(e=>"active"===e.status).length,L=E.reduce((e,t)=>e+t.purchasePrice,0);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"page-header",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(P.Z,{className:"text-2xl text-blue-600 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"page-title",children:"物料主数据管理"}),(0,s.jsx)("p",{className:"page-description",children:"管理物料编码、名称、价格等基础信息"})]})]})}),(0,s.jsxs)(h.Z,{gutter:[16,16],children:[(0,s.jsx)(x.Z,{xs:24,sm:8,children:(0,s.jsx)(f.Z,{children:(0,s.jsx)(m.Z,{title:"物料总数",value:T,suffix:"种",valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(x.Z,{xs:24,sm:8,children:(0,s.jsx)(f.Z,{children:(0,s.jsx)(m.Z,{title:"启用物料",value:B,suffix:"种",valueStyle:{color:"#52c41a"}})})}),(0,s.jsx)(x.Z,{xs:24,sm:8,children:(0,s.jsx)(f.Z,{children:(0,s.jsx)(m.Z,{title:"平均单价",value:L/T,precision:2,prefix:"\xa5",valueStyle:{color:"#722ed1"}})})})]}),(0,s.jsx)(f.Z,{children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,s.jsx)(p.default,{placeholder:"搜索物料编码或名称",prefix:(0,s.jsx)(C.Z,{}),className:"w-full sm:w-64"}),(0,s.jsxs)(r.default,{placeholder:"物料分类",className:"w-full sm:w-32",children:[(0,s.jsx)(A,{value:"",children:"全部"}),M.map(e=>(0,s.jsx)(A,{value:e,children:e},e))]}),(0,s.jsxs)(r.default,{placeholder:"状态",className:"w-full sm:w-32",children:[(0,s.jsx)(A,{value:"",children:"全部"}),(0,s.jsx)(A,{value:"active",children:"启用"}),(0,s.jsx)(A,{value:"inactive",children:"停用"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(o.ZP,{icon:(0,s.jsx)(b.Z,{}),onClick:()=>{n.ZP.info("导入功能开发中...")},children:"导入"}),(0,s.jsx)(o.ZP,{icon:(0,s.jsx)(k.Z,{}),onClick:()=>{n.ZP.success("数据导出成功")},children:"导出"}),(0,s.jsx)(o.ZP,{type:"primary",icon:(0,s.jsx)(N.Z,{}),onClick:()=>{z(null),t(!0),I.resetFields()},children:"新增物料"})]})]})}),(0,s.jsx)(f.Z,{title:"物料列表",children:(0,s.jsx)(j.Z,{columns:[{title:"物料编码",dataIndex:"materialCode",key:"materialCode",width:120,fixed:"left"},{title:"物料名称",dataIndex:"materialName",key:"materialName",width:150},{title:"采购单价",dataIndex:"purchasePrice",key:"purchasePrice",width:120,render:(e,t)=>"\xa5".concat(e.toLocaleString(),"/").concat(t.unit)},{title:"计量单位",dataIndex:"unit",key:"unit",width:100},{title:"物料分类",dataIndex:"category",key:"category",width:120},{title:"供应商",dataIndex:"supplier",key:"supplier",width:150,ellipsis:!0},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>{let t=V[e];return(0,s.jsx)(i.Z,{color:t.color,children:t.text})}},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:120},{title:"操作",key:"action",width:180,fixed:"right",render:(e,t)=>(0,s.jsxs)(d.Z,{size:"small",children:[(0,s.jsx)(o.ZP,{type:"text",icon:(0,s.jsx)(g.Z,{}),size:"small",children:"查看"}),(0,s.jsx)(o.ZP,{type:"text",icon:(0,s.jsx)(y.Z,{}),size:"small",onClick:()=>R(t),children:"编辑"}),(0,s.jsx)(u.Z,{title:"确定要删除这个物料吗？",onConfirm:()=>_(t.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(o.ZP,{type:"text",danger:!0,icon:(0,s.jsx)(w.Z,{}),size:"small",children:"删除"})})]})}],dataSource:E,rowKey:"id",loading:S,pagination:{total:E.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条")},scroll:{x:1200}})}),(0,s.jsx)(v.Z,{title:l?"编辑物料":"新增物料",open:e,onOk:q,onCancel:()=>{t(!1),I.resetFields()},width:800,okText:"确认",cancelText:"取消",children:(0,s.jsxs)(c.Z,{form:I,layout:"vertical",initialValues:{status:"active"},children:[(0,s.jsxs)(h.Z,{gutter:16,children:[(0,s.jsx)(x.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"物料编码",name:"materialCode",rules:[{required:!0,message:"请输入物料编码"},{pattern:/^[A-Z]{2}-[A-Z0-9]{4}$/,message:"格式：XX-XXXX（如：FP-A001）"}],children:(0,s.jsx)(p.default,{placeholder:"如：FP-A001"})})}),(0,s.jsx)(x.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"物料名称",name:"materialName",rules:[{required:!0,message:"请输入物料名称"}],children:(0,s.jsx)(p.default,{placeholder:"请输入物料名称"})})})]}),(0,s.jsxs)(h.Z,{gutter:16,children:[(0,s.jsx)(x.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"采购单价",name:"purchasePrice",rules:[{required:!0,message:"请输入采购单价"}],children:(0,s.jsx)(Z.Z,{className:"w-full",placeholder:"请输入采购单价",prefix:"\xa5",min:0,precision:2})})}),(0,s.jsx)(x.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"计量单位",name:"unit",rules:[{required:!0,message:"请选择计量单位"}],children:(0,s.jsxs)(r.default,{placeholder:"请选择计量单位",children:[(0,s.jsx)(A,{value:"吨",children:"吨"}),(0,s.jsx)(A,{value:"公斤",children:"公斤"}),(0,s.jsx)(A,{value:"米",children:"米"}),(0,s.jsx)(A,{value:"个",children:"个"}),(0,s.jsx)(A,{value:"箱",children:"箱"})]})})})]}),(0,s.jsxs)(h.Z,{gutter:16,children:[(0,s.jsx)(x.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"物料分类",name:"category",rules:[{required:!0,message:"请选择物料分类"}],children:(0,s.jsx)(r.default,{placeholder:"请选择物料分类",children:M.map(e=>(0,s.jsx)(A,{value:e,children:e},e))})})}),(0,s.jsx)(x.Z,{span:12,children:(0,s.jsx)(c.Z.Item,{label:"状态",name:"status",rules:[{required:!0,message:"请选择状态"}],children:(0,s.jsxs)(r.default,{placeholder:"请选择状态",children:[(0,s.jsx)(A,{value:"active",children:"启用"}),(0,s.jsx)(A,{value:"inactive",children:"停用"})]})})})]}),(0,s.jsx)(c.Z.Item,{label:"供应商",name:"supplier",children:(0,s.jsx)(p.default,{placeholder:"请输入供应商名称（可选）"})})]})})]})}}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,1157,8236,2971,4938,1744],function(){return e(e.s=51862)}),_N_E=e.O()}]);