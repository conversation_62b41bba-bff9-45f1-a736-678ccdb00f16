(()=>{var e={};e.id=3007,e.ids=[3007],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},47020:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>o});var r=s(50482),l=s(69108),a=s(62563),n=s.n(a),c=s(68300),i={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>c[e]);s.d(t,i);let o=["",{children:["sales",{children:["customers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,44886)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\customers\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,75497)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\customers\\page.tsx"],u="/sales/customers/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/sales/customers/page",pathname:"/sales/customers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6389:(e,t,s)=>{Promise.resolve().then(s.bind(s,26915))},54649:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var r=s(65651),l=s(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var n=s(49809);let c=l.forwardRef(function(e,t){return l.createElement(n.Z,(0,r.Z)({},e,{ref:t,icon:a}))})},89645:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var r=s(65651),l=s(3729);let a={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var n=s(49809);let c=l.forwardRef(function(e,t){return l.createElement(n.Z,(0,r.Z)({},e,{ref:t,icon:a}))})},12391:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var r=s(65651),l=s(3729);let a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"};var n=s(49809);let c=l.forwardRef(function(e,t){return l.createElement(n.Z,(0,r.Z)({},e,{ref:t,icon:a}))})},16408:(e,t,s)=>{"use strict";s.d(t,{Z:()=>y});var r=s(35864),l=s(4176),a=s(51966),n=s(3729),c=s(34132),i=s.n(c),o=s(74393),d=s(9286),u=s(84893),x=s(13878),h=s(59604),m=s(93142),p=s(59239),f=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,r=Object.getOwnPropertySymbols(e);l<r.length;l++)0>t.indexOf(r[l])&&Object.prototype.propertyIsEnumerable.call(e,r[l])&&(s[r[l]]=e[r[l]]);return s};let j=(0,d.i)(e=>{let{prefixCls:t,className:s,closeIcon:r,closable:l,type:a,title:c,children:d,footer:j}=e,Z=f(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:v}=n.useContext(u.E_),g=v(),y=t||v("modal"),b=(0,x.Z)(g),[C,w,P]=(0,p.ZP)(y,b),L=`${y}-confirm`,I={};return I=a?{closable:null!=l&&l,title:"",footer:"",children:n.createElement(h.O,Object.assign({},e,{prefixCls:y,confirmPrefixCls:L,rootPrefixCls:g,content:d}))}:{closable:null==l||l,title:c,footer:null!==j&&n.createElement(m.$,Object.assign({},e)),children:d},C(n.createElement(o.s,Object.assign({prefixCls:y,className:i()(w,`${y}-pure-panel`,a&&L,a&&`${L}-${a}`,s,P,b)},Z,{closeIcon:(0,m.b)(y,r),closable:l},I)))});var Z=s(4531);function v(e){return(0,r.ZP)((0,r.uW)(e))}let g=a.Z;g.useModal=Z.Z,g.info=function(e){return(0,r.ZP)((0,r.cw)(e))},g.success=function(e){return(0,r.ZP)((0,r.vq)(e))},g.error=function(e){return(0,r.ZP)((0,r.AQ)(e))},g.warning=v,g.warn=v,g.confirm=function(e){return(0,r.ZP)((0,r.Au)(e))},g.destroyAll=function(){for(;l.Z.length;){let e=l.Z.pop();e&&e()}},g.config=r.ai,g._InternalPanelDoNotUseOrYouWillBeFired=j;let y=g},26915:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>V});var r=s(95344),l=s(3729),a=s(97854),n=s(284),c=s(32979),i=s(7618),o=s(90377),d=s(87049),u=s(39470),x=s(10707),h=s(11157),m=s(52788),p=s(63724),f=s(27976),j=s(83984),Z=s(43896),v=s(36527),g=s(16408),y=s(53869),b=s(67383),C=s(12391),w=s(46116),P=s(54649),L=s(33537),I=s(27385),S=s(70469),k=s(89645),A=s(58535),N=s(65651);let q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M877.1 238.7L770.6 132.3c-13-13-30.4-20.3-48.8-20.3s-35.8 7.2-48.8 20.3L558.3 246.8c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l89.6 89.7a405.46 405.46 0 01-86.4 127.3c-36.7 36.9-79.6 66-127.2 86.6l-89.6-89.7c-13-13-30.4-20.3-48.8-20.3a68.2 68.2 0 00-48.8 20.3L132.3 673c-13 13-20.3 30.5-20.3 48.9 0 18.5 7.2 35.8 20.3 48.9l106.4 106.4c22.2 22.2 52.8 34.9 84.2 34.9 6.5 0 12.8-.5 19.2-1.6 132.4-21.8 263.8-92.3 369.9-198.3C818 606 888.4 474.6 910.4 342.1c6.3-37.6-6.3-76.3-33.3-103.4zm-37.6 91.5c-19.5 117.9-82.9 235.5-178.4 331s-213 158.9-330.9 178.4c-14.8 2.5-30-2.5-40.8-13.2L184.9 721.9 295.7 611l119.8 120 .9.9 21.6-8a481.29 481.29 0 00285.7-285.8l8-21.6-120.8-120.7 110.8-110.9 104.5 104.5c10.8 10.8 15.8 26 13.3 40.8z"}}]},name:"phone",theme:"outlined"};var z=s(49809),E=l.forwardRef(function(e,t){return l.createElement(z.Z,(0,N.Z)({},e,{ref:t,icon:q}))});let H={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232L512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0068.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"}}]},name:"mail",theme:"outlined"};var M=l.forwardRef(function(e,t){return l.createElement(z.Z,(0,N.Z)({},e,{ref:t,icon:H}))}),_=s(37637),R=s(51221);let{Option:$}=a.default,{TextArea:O}=n.default;function K(){let{message:e}=c.Z.useApp(),[t,s]=(0,l.useState)([]),[N,q]=(0,l.useState)(!1),[z,H]=(0,l.useState)(null),[O,K]=(0,l.useState)(!1),[V,B]=(0,l.useState)(!1),[F,T]=(0,l.useState)(null),[X,D]=(0,l.useState)("basic"),[U]=i.Z.useForm(),[G,Q]=(0,l.useState)(""),[W,J]=(0,l.useState)(""),[Y,ee]=(0,l.useState)(""),et=()=>{let s=t.map(e=>e.customerCode).filter(e=>/^KH\d{4,5}$/.test(e));if(0===s.length)return"KH0001";let r=Math.max(...s.map(e=>{let t=e.match(/^KH(\d{4,5})$/);return t?parseInt(t[1],10):0}))+1;return r>99999?(e.warning("编码已达到最大值KH99999，请手动输入编码"),""):r<=9999?`KH${r.toString().padStart(4,"0")}`:`KH${r.toString()}`},es=(e,s)=>!t.some(t=>t.customerCode===e&&t.id!==s);(0,l.useEffect)(()=>{console.log("客户数据已加载",{customers:t.length})},[t.length]);let er=e=>{switch(e){case"A":return"gold";case"B":return"blue";case"C":return"green";default:return"default"}},el=e=>{switch(e){case"important":return"red";case"general":return"orange";case"small":return"cyan";default:return"default"}},ea=e=>{switch(e){case"important":return"重要客户";case"general":return"一般客户";case"small":return"小客户";default:return"未分类"}},en=(e,t)=>t>0?e/t*100:0,ec=[{title:"客户编码",dataIndex:"customerCode",key:"customerCode",width:120,fixed:"left"},{title:"客户名称",dataIndex:"customerName",key:"customerName",width:200,fixed:"left"},{title:"客户等级",dataIndex:"customerLevel",key:"customerLevel",width:100,render:e=>(0,r.jsxs)(o.Z,{color:er(e),icon:r.jsx(C.Z,{}),children:[e,"类"]})},{title:"客户分类",dataIndex:"customerCategory",key:"customerCategory",width:120,render:e=>r.jsx(o.Z,{color:el(e),children:ea(e)})},{title:"联系人",dataIndex:"contactPerson",key:"contactPerson",width:100},{title:"联系电话",dataIndex:"contactPhone",key:"contactPhone",width:130},{title:"年销售额",dataIndex:"annualSalesAmount",key:"annualSalesAmount",width:120,render:e=>`\xa5${(e||0).toLocaleString()}`},{title:"信用额度",key:"creditInfo",width:150,render:(e,t)=>{let s=t.creditLimit||0,l=t.usedCredit||0,a=en(l,s);return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{children:["\xa5",s.toLocaleString()]}),r.jsx(d.Z,{percent:a,size:"small",status:a>80?"exception":"normal",showInfo:!1}),(0,r.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["已用: \xa5",l.toLocaleString()]})]})}},{title:"所属业务员",dataIndex:"salesRepresentative",key:"salesRepresentative",width:120},{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>r.jsx(u.Z,{status:"active"===e?"success":"default",text:"active"===e?"正常":"停用"})},{title:"操作",key:"action",width:200,fixed:"right",render:(e,t)=>(0,r.jsxs)(x.Z,{size:"small",children:[r.jsx(h.ZP,{type:"link",icon:r.jsx(w.Z,{}),onClick:()=>ex(t),children:"详情"}),r.jsx(h.ZP,{type:"link",icon:r.jsx(P.Z,{}),onClick:()=>eu(t),children:"编辑"}),r.jsx(m.Z,{title:"确定要删除这个客户吗？",onConfirm:()=>eh(t.id),okText:"确定",cancelText:"取消",children:r.jsx(h.ZP,{type:"link",danger:!0,icon:r.jsx(L.Z,{}),children:"删除"})})]})}],ei=t.filter(e=>{let t=!G||e.customerName.toLowerCase().includes(G.toLowerCase())||e.customerCode.toLowerCase().includes(G.toLowerCase())||(e.contactPerson||"").toLowerCase().includes(G.toLowerCase()),s=!W||e.customerLevel===W,r=!Y||e.customerCategory===Y;return t&&s&&r}),eo=async()=>{try{q(!0);let e=await (0,R.Ro)(()=>_.dataAccessManager.customers.getAll(),"获取客户数据");e&&e.items&&s(e.items)}catch(t){console.error("刷新客户数据失败:",t),e.error("刷新客户数据失败")}finally{q(!1)}};(0,l.useEffect)(()=>{eo()},[]);let ed={total:t.length,aLevel:t.filter(e=>"A"===e.customerLevel).length,bLevel:t.filter(e=>"B"===e.customerLevel).length,cLevel:t.filter(e=>"C"===e.customerLevel).length,totalSales:t.reduce((e,t)=>e+(t.annualSalesAmount||0),0),totalCredit:t.reduce((e,t)=>e+(t.creditLimit||0),0),usedCredit:t.reduce((e,t)=>e+(t.usedCredit||0),0)},eu=e=>{T(e),K(!0),D("basic"),U.setFieldsValue(e)},ex=e=>{H(e),B(!0)},eh=async t=>{try{q(!0),await (0,R.Ro)(()=>_.dataAccessManager.customers.delete(t),"删除客户")&&(await eo(),e.success("客户删除成功"))}catch(t){console.error("删除客户失败:",t),e.error("删除客户失败，请稍后重试")}finally{q(!1)}},em=async()=>{try{let t=await U.validateFields();if(q(!0),F){if(!await (0,R.Ro)(()=>_.dataAccessManager.customers.update(F.id,t),"更新客户信息"))return;await eo(),e.success("客户信息更新成功")}else{let s={...t,creditLimit:t.creditLimit||0,usedCredit:t.usedCredit||0,annualSalesAmount:t.annualSalesAmount||0,discountRate:t.discountRate||0};if(!await (0,R.Ro)(()=>_.dataAccessManager.customers.create(s),"创建客户"))return;await eo(),e.success("客户创建成功")}K(!1),D("basic"),U.resetFields()}catch(t){e.error("操作失败，请稍后重试"),console.error("客户操作失败:",t)}finally{q(!1)}};return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"page-header",children:[r.jsx("h1",{className:"page-title",children:"客户管理"}),r.jsx("p",{className:"page-description",children:"管理客户档案、信用额度和客户关系"})]}),(0,r.jsxs)(p.Z,{gutter:[16,16],children:[r.jsx(f.Z,{xs:24,sm:6,children:r.jsx(j.Z,{children:r.jsx(Z.Z,{title:"客户总数",value:ed.total,suffix:"个",prefix:r.jsx(I.Z,{})})})}),r.jsx(f.Z,{xs:24,sm:6,children:r.jsx(j.Z,{children:r.jsx(Z.Z,{title:"年销售总额",value:ed.totalSales,precision:0,prefix:"\xa5",valueStyle:{color:"#3f8600"}})})}),r.jsx(f.Z,{xs:24,sm:6,children:r.jsx(j.Z,{children:r.jsx(Z.Z,{title:"信用总额度",value:ed.totalCredit,precision:0,prefix:"\xa5",valueStyle:{color:"#1890ff"}})})}),r.jsx(f.Z,{xs:24,sm:6,children:r.jsx(j.Z,{children:r.jsx(Z.Z,{title:"信用使用率",value:ed.totalCredit>0?ed.usedCredit/ed.totalCredit*100:0,precision:1,suffix:"%",valueStyle:{color:ed.totalCredit>0&&ed.usedCredit/ed.totalCredit>.8?"#cf1322":"#1890ff"}})})})]}),(0,r.jsxs)(p.Z,{gutter:[16,16],children:[r.jsx(f.Z,{xs:24,sm:8,children:r.jsx(j.Z,{children:r.jsx(Z.Z,{title:"A级客户",value:ed.aLevel,suffix:"个",valueStyle:{color:"#faad14"}})})}),r.jsx(f.Z,{xs:24,sm:8,children:r.jsx(j.Z,{children:r.jsx(Z.Z,{title:"B级客户",value:ed.bLevel,suffix:"个",valueStyle:{color:"#1890ff"}})})}),r.jsx(f.Z,{xs:24,sm:8,children:r.jsx(j.Z,{children:r.jsx(Z.Z,{title:"C级客户",value:ed.cLevel,suffix:"个",valueStyle:{color:"#52c41a"}})})})]}),r.jsx(j.Z,{children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[r.jsx(n.default,{placeholder:"搜索客户名称、编码或联系人",prefix:r.jsx(S.Z,{}),value:G,onChange:e=>Q(e.target.value),className:"w-full sm:w-64"}),(0,r.jsxs)(a.default,{placeholder:"客户等级",value:W,onChange:J,className:"w-full sm:w-32",allowClear:!0,children:[r.jsx($,{value:"A",children:"A级"}),r.jsx($,{value:"B",children:"B级"}),r.jsx($,{value:"C",children:"C级"})]}),(0,r.jsxs)(a.default,{placeholder:"客户分类",value:Y,onChange:ee,className:"w-full sm:w-32",allowClear:!0,children:[r.jsx($,{value:"important",children:"重要客户"}),r.jsx($,{value:"general",children:"一般客户"}),r.jsx($,{value:"small",children:"小客户"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(h.ZP,{icon:r.jsx(k.Z,{}),children:"导出"}),r.jsx(h.ZP,{type:"primary",icon:r.jsx(A.Z,{}),onClick:()=>{T(null),K(!0),D("basic"),U.resetFields();let e=et();e&&U.setFieldsValue({customerCode:e})},children:"新建客户"})]})]})}),r.jsx(j.Z,{title:"客户列表",children:r.jsx(v.Z,{columns:ec,dataSource:ei,rowKey:"id",loading:N,pagination:{total:ei.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`},scroll:{x:1400}})}),r.jsx(g.Z,{title:F?"编辑客户":"新建客户",open:O,onOk:em,onCancel:()=>{K(!1),D("basic"),U.resetFields()},width:900,okText:"确认",cancelText:"取消",children:r.jsx(i.Z,{form:U,layout:"vertical",initialValues:{status:"active"},children:r.jsx(y.default,{activeKey:X,onChange:D,items:[{key:"basic",label:"基础信息",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(p.Z,{gutter:16,children:[r.jsx(f.Z,{span:12,children:r.jsx(i.Z.Item,{name:"customerCode",label:"客户编码",rules:[{required:!0,message:"请输入客户编码"},{pattern:/^KH\d{4,5}$/,message:"格式：KHXXXX或KHXXXXX（如：KH0001）"},{validator:(e,t)=>t?es(t,F?.id)?Promise.resolve():Promise.reject(Error("客户编码已存在，请使用其他编码")):Promise.resolve()}],children:r.jsx(n.default,{placeholder:"如：KH0001（自动生成）"})})}),r.jsx(f.Z,{span:12,children:r.jsx(i.Z.Item,{name:"customerName",label:"客户名称",rules:[{required:!0,message:"请输入客户名称"}],children:r.jsx(n.default,{placeholder:"请输入客户名称"})})})]}),(0,r.jsxs)(p.Z,{gutter:16,children:[r.jsx(f.Z,{span:12,children:r.jsx(i.Z.Item,{name:"contactPerson",label:"联系人",children:r.jsx(n.default,{placeholder:"请输入联系人"})})}),r.jsx(f.Z,{span:12,children:r.jsx(i.Z.Item,{name:"contactPhone",label:"联系电话",rules:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号格式"}],children:r.jsx(n.default,{placeholder:"请输入联系电话"})})})]}),(0,r.jsxs)(p.Z,{gutter:16,children:[r.jsx(f.Z,{span:12,children:r.jsx(i.Z.Item,{name:"referrer",label:"介绍人",children:r.jsx(n.default,{placeholder:"请输入介绍人"})})}),r.jsx(f.Z,{span:12,children:r.jsx(i.Z.Item,{name:"salesRepresentative",label:"所属业务员",rules:[{required:!0,message:"请输入所属业务员"}],children:r.jsx(n.default,{placeholder:"请输入所属业务员"})})})]})]})},{key:"financial",label:"财务信息",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)(p.Z,{gutter:16,children:[r.jsx(f.Z,{span:12,children:r.jsx(i.Z.Item,{name:"bankName",label:"开户行",children:r.jsx(n.default,{placeholder:"请输入开户行"})})}),r.jsx(f.Z,{span:12,children:r.jsx(i.Z.Item,{name:"bankAccount",label:"开户账号",children:r.jsx(n.default,{placeholder:"请输入开户账号"})})})]}),r.jsx(p.Z,{gutter:16,children:r.jsx(f.Z,{span:12,children:r.jsx(i.Z.Item,{name:"taxNumber",label:"单位税号",children:r.jsx(n.default,{placeholder:"请输入单位税号"})})})}),r.jsx("div",{className:"text-gray-500 text-center py-8",children:r.jsx("p",{children:"更多财务信息功能开发中..."})})]})}]})})}),r.jsx(g.Z,{title:"客户详情",open:V,onCancel:()=>B(!1),footer:[r.jsx(h.ZP,{onClick:()=>B(!1),children:"关闭"},"close")],width:800,children:z&&z.customerCode&&z.customerName&&r.jsx(y.default,{defaultActiveKey:"basic",items:[{key:"basic",label:"基本信息",children:(0,r.jsxs)(b.Z,{column:2,bordered:!0,children:[r.jsx(b.Z.Item,{label:"客户编码",children:z.customerCode}),r.jsx(b.Z.Item,{label:"客户名称",children:z.customerName}),r.jsx(b.Z.Item,{label:"客户等级",children:(0,r.jsxs)(o.Z,{color:er(z.customerLevel||"C"),children:[z.customerLevel,"类"]})}),r.jsx(b.Z.Item,{label:"客户分类",children:r.jsx(o.Z,{color:el(z.customerCategory||"general"),children:ea(z.customerCategory||"general")})}),r.jsx(b.Z.Item,{label:"联系人",children:(0,r.jsxs)(x.Z,{children:[r.jsx(I.Z,{}),z.contactPerson]})}),r.jsx(b.Z.Item,{label:"联系电话",children:(0,r.jsxs)(x.Z,{children:[r.jsx(E,{}),z.contactPhone]})}),r.jsx(b.Z.Item,{label:"联系邮箱",children:(0,r.jsxs)(x.Z,{children:[r.jsx(M,{}),z.contactEmail||"未填写"]})}),r.jsx(b.Z.Item,{label:"地址",children:z.address}),r.jsx(b.Z.Item,{label:"税号",children:z.taxNumber||"未填写"}),r.jsx(b.Z.Item,{label:"付款条件",children:z.paymentTerms}),(0,r.jsxs)(b.Z.Item,{label:"年销售额",children:["\xa5",(z.annualSalesAmount||0).toLocaleString()]}),(0,r.jsxs)(b.Z.Item,{label:"专属折扣率",children:[(100*(z.discountRate||0)).toFixed(1),"%"]}),r.jsx(b.Z.Item,{label:"所属业务员",children:z.salesRepresentative}),r.jsx(b.Z.Item,{label:"状态",children:r.jsx(u.Z,{status:"active"===z.status?"success":"default",text:"active"===z.status?"正常":"停用"})}),r.jsx(b.Z.Item,{label:"创建时间",span:2,children:z.createdAt}),r.jsx(b.Z.Item,{label:"更新时间",span:2,children:z.updatedAt}),r.jsx(b.Z.Item,{label:"备注",span:2,children:z.remark||"无"})]})},{key:"credit",label:"信用信息",children:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(p.Z,{gutter:16,children:[r.jsx(f.Z,{span:12,children:r.jsx(j.Z,{children:r.jsx(Z.Z,{title:"信用额度",value:z.creditLimit||0,precision:0,prefix:"\xa5",valueStyle:{color:"#1890ff"}})})}),r.jsx(f.Z,{span:12,children:r.jsx(j.Z,{children:r.jsx(Z.Z,{title:"已用额度",value:z.usedCredit||0,precision:0,prefix:"\xa5",valueStyle:{color:"#cf1322"}})})})]}),(0,r.jsxs)("div",{style:{marginTop:16},children:[r.jsx("h4",{children:"信用使用情况"}),r.jsx(d.Z,{percent:en(z.usedCredit||0,z.creditLimit||0),status:en(z.usedCredit||0,z.creditLimit||0)>80?"exception":"normal",strokeColor:{"0%":"#108ee9","100%":"#87d068"}}),(0,r.jsxs)("p",{style:{marginTop:8,color:"#666"},children:["可用额度: \xa5",((z.creditLimit||0)-(z.usedCredit||0)).toLocaleString()]})]})]})},{key:"products",label:"产品偏好",children:(0,r.jsxs)("div",{children:[r.jsx("h4",{children:"常购产品型号"}),(0,r.jsxs)("div",{style:{marginTop:16},children:[(z.preferredProducts||[]).map(e=>r.jsx(o.Z,{color:"blue",style:{marginBottom:8},children:e},e)),(!z.preferredProducts||0===z.preferredProducts.length)&&r.jsx("span",{style:{color:"#999"},children:"暂无产品偏好数据"})]})]})}]})})]})}function V(){return r.jsx(c.Z,{children:r.jsx(K,{})})}},51221:(e,t,s)=>{"use strict";s.d(t,{Ro:()=>r});let r=async(e,t)=>{try{let t=await e();if("success"===t.status)return t.data||null;return t.code,t.message,t.message,null}catch(e){return e instanceof Error?e.message:String(e),null}}},44886:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>l,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\sales\customers\page.tsx`),{__esModule:l,$$typeof:a}=r,n=r.default},75497:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(25036),l=s(38834);function a({children:e}){return r.jsx(l.Z,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,7049,4441,7383,6274,996,6133],()=>s(47020));module.exports=r})();