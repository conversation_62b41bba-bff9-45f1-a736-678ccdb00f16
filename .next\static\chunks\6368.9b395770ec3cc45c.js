"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6368],{46368:function(e,t,r){r.d(t,{mrpService:function(){return c}});var o=r(32779),a=r(53641);class d{static getInstance(){return d.instance||(d.instance=new d),d.instance}async executeMRP(e){let{salesOrder:t,executedBy:r,executionDate:o}=e;console.log("\uD83D\uDE80 [MRPService] 开始执行MRP",{orderId:t.id,orderNumber:t.orderNumber,executedBy:r});try{let e=await this.checkInventory(t),a=await this.analyzeSharedMolds(t,e),d="mrp-".concat(Date.now()),c=await this.generateProductionOrdersWithSharedMold(t,e,a,d,r),u=e.reduce((e,t)=>e+100*t.shortageQuantity,0),s=c.filter(e=>e.isSharedMold).length,n=c.filter(e=>!e.isSharedMold).length,i={id:d,salesOrderId:t.id,salesOrderNumber:t.orderNumber,executionDate:o,status:c.length>0?"success":"partial_shortage",totalProductionOrders:c.length,sharedMoldOrders:s,traditionalOrders:n,inventoryCheckResults:e,generatedProductionOrders:c,totalShortageValue:u,executedBy:r,executedAt:new Date().toISOString()};return console.log("✅ [MRPService] MRP执行完成",{resultId:i.id,totalOrders:i.totalProductionOrders,sharedMoldOrders:i.sharedMoldOrders,traditionalOrders:i.traditionalOrders,status:i.status}),i}catch(e){return console.error("❌ [MRPService] MRP执行失败:",e),{id:"mrp-error-".concat(Date.now()),salesOrderId:t.id,salesOrderNumber:t.orderNumber,executionDate:o,status:"failed",totalProductionOrders:0,sharedMoldOrders:0,traditionalOrders:0,inventoryCheckResults:[],generatedProductionOrders:[],totalShortageValue:0,executedBy:r,executedAt:new Date().toISOString()}}}async checkInventory(e){let t=[];for(let r of e.items)try{let e=await o.dataAccessManager.inventory.getByProductCode(r.productCode),a=0;"success"===e.status&&e.data&&(a=e.data.currentStock||0);let d=r.quantity,c=Math.max(0,d-a);t.push({productCode:r.productCode,productName:r.productName,requiredQuantity:d,availableQuantity:a,shortageQuantity:c,isSufficient:0===c})}catch(e){console.error("❌ [MRPService] 库存检查失败 - 产品: ".concat(r.productCode),e),t.push({productCode:r.productCode,productName:r.productName,requiredQuantity:r.quantity,availableQuantity:0,shortageQuantity:r.quantity,isSufficient:!1})}return t}async analyzeSharedMolds(e,t){console.log("\uD83D\uDD0D [MRPService] 开始分析共享模具情况");let r=[],a=new Map;for(let r of t)if(r.shortageQuantity>0)try{let t=await o.dataAccessManager.products.getByCode(r.productCode);if("success"===t.status&&t.data){let o=t.data.formingMold||"";if(o){let d={productCode:r.productCode,productName:r.productName,productModelCode:t.data.modelCode,requiredQuantity:r.requiredQuantity,deliveryDate:e.deliveryDate,sourceOrderId:e.id,sourceOrderNumber:e.orderNumber,customerName:e.customerName,urgencyLevel:"medium"};a.has(o)||a.set(o,[]),a.get(o).push(d)}}}catch(e){console.error("❌ 获取产品 ".concat(r.productCode," 主数据失败:"),e)}for(let[o,d]of a.entries())if(d.length>1){console.log("\uD83D\uDD27 发现共享模具: ".concat(o,", 包含 ").concat(d.length," 个产品"));let a=Math.max(...d.map(e=>e.requiredQuantity)),c=Math.max(...d.map(e=>{let r=t.find(t=>t.productCode===e.productCode);return(null==r?void 0:r.shortageQuantity)||0})),u={moldNumber:o,deliveryDate:e.deliveryDate,products:d,isFirstOccurrence:!0,maxRequiredQuantity:a,maxShortageQuantity:c};r.push(u)}return console.log("✅ [MRPService] 共享模具分析完成，发现 ".concat(r.length," 个共享模具组")),r}async generateProductionOrdersWithSharedMold(e,t,r,o,a){let d=[],c=new Set;for(let u of r){let r=await this.generateSharedMoldOrder(u,t,e,o,a);r&&(d.push(r),u.products.forEach(e=>c.add(e.productCode)))}for(let r of t)if(r.shortageQuantity>0&&!c.has(r.productCode)){let t=await this.generateTraditionalOrder(r,e,o,a);t&&d.push(t)}return d}async generateTraditionalOrder(e,t,r,d){try{let c,u="",s=await o.dataAccessManager.products.getByCode(e.productCode);"success"===s.status&&s.data?(u=s.data.formingMold||"",console.log("✅ 获取产品 ".concat(e.productCode," 的成型模具编号: ").concat(u))):console.warn("⚠️ 未找到产品 ".concat(e.productCode," 的主数据，模具编号将为空"));let n=await o.dataAccessManager.customers.getById(t.customerId);"success"===n.status&&n.data?(c=n.data.customerLevel,console.log("✅ 获取客户 ".concat(t.customerName," 的信用等级: ").concat(c))):console.warn("⚠️ 未找到客户 ".concat(t.customerId," 的主数据，信用等级将为空"));let i=a.s.generateProductionOrderId(t.orderNumber),l={id:"po-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),orderNumber:i,productName:e.productName,productCode:e.productCode,formingMoldNumber:u,isSharedMold:!1,productItems:[],plannedQuantity:e.shortageQuantity,producedQuantity:0,startDate:new Date().toISOString().split("T")[0],endDate:t.deliveryDate,deliveryDate:t.deliveryDate,status:"in_plan",workstation:"默认工位",customerName:t.customerName,customerId:t.customerId,customerCreditLevel:c,prioritySource:"auto",salesOrderNumber:t.orderNumber,sourceOrderNumbers:[t.orderNumber],sourceOrderIds:[t.id],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},m=await o.dataAccessManager.productionOrders.createFromMRP({...l,mrpExecutionId:r,mrpExecutedBy:d,mrpExecutedAt:new Date().toISOString()});if("success"!==m.status)throw Error("生产订单创建失败: ".concat(m.message));return console.log("✅ [MRPService] 传统生产订单已保存: ".concat(l.orderNumber,", MRP执行ID: ").concat(r)),l}catch(t){return console.error("❌ [MRPService] 生成传统订单失败 - 产品: ".concat(e.productCode),t),null}}async generateSharedMoldOrder(e,t,r,d,c){try{var u;let t;console.log("\uD83D\uDD27 [MRPService] 开始生成共享模具订单: ".concat(e.moldNumber));let s=await o.dataAccessManager.customers.getById(r.customerId);"success"===s.status&&s.data&&(t=s.data.customerLevel);let n=a.s.generateProductionOrderId(r.orderNumber),i=e.products.map(e=>({id:"item-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),productCode:e.productCode,productName:e.productName,plannedQuantity:e.requiredQuantity,producedQuantity:0,requiredQuantity:e.requiredQuantity,sourceOrderItems:[r.id]})),l=null!==(u=e.maxShortageQuantity)&&void 0!==u?u:e.maxRequiredQuantity,m=e.products[0],p={id:"po-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),orderNumber:n,productName:"".concat(e.moldNumber," 共享模具生产"),productCode:m.productCode,formingMoldNumber:e.moldNumber,isSharedMold:!0,moldGroup:e.moldNumber,productItems:i,plannedQuantity:l,producedQuantity:0,startDate:new Date().toISOString().split("T")[0],endDate:e.deliveryDate,deliveryDate:e.deliveryDate,status:"in_plan",workstation:"默认工位",customerName:r.customerName,customerId:r.customerId,customerCreditLevel:t,prioritySource:"auto",salesOrderNumber:r.orderNumber,sourceOrderNumbers:[...new Set(e.products.map(e=>e.sourceOrderNumber))],sourceOrderIds:[...new Set(e.products.map(e=>e.sourceOrderId))],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},g=await o.dataAccessManager.productionOrders.createFromMRP({...p,mrpExecutionId:d,mrpExecutedBy:c,mrpExecutedAt:new Date().toISOString()});if("success"!==g.status)throw Error("共享模具生产订单创建失败: ".concat(g.message));return console.log("✅ [MRPService] 共享模具生产订单已保存: ".concat(p.orderNumber,", 生产数量: ").concat(l,", MRP执行ID: ").concat(d)),p}catch(t){return console.error("❌ [MRPService] 生成共享模具订单失败 - 模具: ".concat(e.moldNumber),t),null}}}let c=d.getInstance()}}]);