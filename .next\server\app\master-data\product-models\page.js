(()=>{var e={};e.id=8149,e.ids=[8149],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},75674:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=r(50482),i=r(69108),l=r(62563),a=r.n(l),n=r(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let o=["",{children:["master-data",{children:["product-models",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57490)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\product-models\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52054)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\product-models\\page.tsx"],m="/master-data/product-models/page",h={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/master-data/product-models/page",pathname:"/master-data/product-models",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},36127:(e,t,r)=>{Promise.resolve().then(r.bind(r,95397))},54649:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(65651),i=r(3729);let l={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var a=r(49809);let n=i.forwardRef(function(e,t){return i.createElement(a.Z,(0,s.Z)({},e,{ref:t,icon:l}))})},89645:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(65651),i=r(3729);let l={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var a=r(49809);let n=i.forwardRef(function(e,t){return i.createElement(a.Z,(0,s.Z)({},e,{ref:t,icon:l}))})},2383:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(65651),i=r(3729);let l={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM653.3 424.6l52.2 52.2a8.01 8.01 0 01-4.7 13.6l-179.4 21c-5.1.6-9.5-3.7-8.9-8.9l21-179.4c.8-6.6 8.9-9.4 13.6-4.7l52.4 52.4 256.2-256.2c3.1-3.1 8.2-3.1 11.3 0l42.4 42.4c3.1 3.1 3.1 8.2 0 11.3L653.3 424.6z"}}]},name:"import",theme:"outlined"};var a=r(49809);let n=i.forwardRef(function(e,t){return i.createElement(a.Z,(0,s.Z)({},e,{ref:t,icon:l}))})},13113:(e,t,r)=>{"use strict";r.d(t,{Z:()=>g});var s=r(3729),i=r(34132),l=r.n(i),a=r(84893),n=r(54527),d=r(92959),o=r(22989),c=r(13165),m=r(96373);let h=e=>{let{componentCls:t}=e;return{[t]:{"&-horizontal":{[`&${t}`]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},u=e=>{let{componentCls:t,sizePaddingEdgeHorizontal:r,colorSplit:s,lineWidth:i,textPaddingInline:l,orientationMargin:a,verticalMarginInline:n}=e;return{[t]:Object.assign(Object.assign({},(0,o.Wf)(e)),{borderBlockStart:`${(0,d.bf)(i)} solid ${s}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:n,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,d.bf)(i)} solid ${s}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,d.bf)(e.marginLG)} 0`},[`&-horizontal${t}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,d.bf)(e.dividerHorizontalWithTextGutterMargin)} 0`,color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${s}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,d.bf)(i)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${t}-with-text-start`]:{"&::before":{width:`calc(${a} * 100%)`},"&::after":{width:`calc(100% - ${a} * 100%)`}},[`&-horizontal${t}-with-text-end`]:{"&::before":{width:`calc(100% - ${a} * 100%)`},"&::after":{width:`calc(${a} * 100%)`}},[`${t}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:l},"&-dashed":{background:"none",borderColor:s,borderStyle:"dashed",borderWidth:`${(0,d.bf)(i)} 0 0`},[`&-horizontal${t}-with-text${t}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${t}-dashed`]:{borderInlineStartWidth:i,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:s,borderStyle:"dotted",borderWidth:`${(0,d.bf)(i)} 0 0`},[`&-horizontal${t}-with-text${t}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${t}-dotted`]:{borderInlineStartWidth:i,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${t}-with-text`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},[`&-horizontal${t}-with-text-start${t}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${t}-inner-text`]:{paddingInlineStart:r}},[`&-horizontal${t}-with-text-end${t}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${t}-inner-text`]:{paddingInlineEnd:r}}})}},x=(0,c.I$)("Divider",e=>{let t=(0,m.IX)(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[u(t),h(t)]},e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),{unitless:{orientationMargin:!0}});var p=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&0>t.indexOf(s)&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,s=Object.getOwnPropertySymbols(e);i<s.length;i++)0>t.indexOf(s[i])&&Object.prototype.propertyIsEnumerable.call(e,s[i])&&(r[s[i]]=e[s[i]]);return r};let f={small:"sm",middle:"md"},g=e=>{let{getPrefixCls:t,direction:r,className:i,style:d}=(0,a.dj)("divider"),{prefixCls:o,type:c="horizontal",orientation:m="center",orientationMargin:h,className:u,rootClassName:g,children:j,dashed:v,variant:b="solid",plain:Z,style:y,size:P}=e,w=p(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),$=t("divider",o),[M,k,S]=x($),N=f[(0,n.Z)(P)],I=!!j,R=s.useMemo(()=>"left"===m?"rtl"===r?"end":"start":"right"===m?"rtl"===r?"start":"end":m,[r,m]),E="start"===R&&null!=h,C="end"===R&&null!=h,q=l()($,i,k,S,`${$}-${c}`,{[`${$}-with-text`]:I,[`${$}-with-text-${R}`]:I,[`${$}-dashed`]:!!v,[`${$}-${b}`]:"solid"!==b,[`${$}-plain`]:!!Z,[`${$}-rtl`]:"rtl"===r,[`${$}-no-default-orientation-margin-start`]:E,[`${$}-no-default-orientation-margin-end`]:C,[`${$}-${N}`]:!!N},u,g),O=s.useMemo(()=>"number"==typeof h?h:/^\d+$/.test(h)?Number(h):h,[h]);return M(s.createElement("div",Object.assign({className:q,style:Object.assign(Object.assign({},d),y)},w,{role:"separator"}),j&&"vertical"!==c&&s.createElement("span",{className:`${$}-inner-text`,style:{marginInlineStart:E?O:void 0,marginInlineEnd:C?O:void 0}},j)))}},95397:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(95344),i=r(3729),l=r(97854),a=r(32979),n=r(7618),d=r(90377),o=r(10707),c=r(11157),m=r(52788),h=r(63724),u=r(27976),x=r(83984),p=r(43896),f=r(284),g=r(36527),j=r(16408),v=r(53869),b=r(6025),Z=r(67383),y=r(13113),P=r(46116),w=r(54649),$=r(33537),M=r(35720),k=r(70469),S=r(2383),N=r(89645),I=r(58535),R=r(21754),E=r(75397),C=r(37637),q=r(51221),O=r(85287);class z{static validateProductCode(e){let t=[];return e?/^P\d{5}$/.test(e)||t.push("产品编码格式错误，请使用格式：P + 5位数字（如：P00001）"):t.push("产品编码不能为空"),{isValid:0===t.length,errors:t}}static validateWorkstationCode(e){let t=[];return e?/^WS\d{3}$/.test(e)||t.push("工位编码格式错误，请使用格式：WS + 3位数字（如：WS001）"):t.push("工位编码不能为空"),{isValid:0===t.length,errors:t}}static validateMoldNumber(e){let t=[];return e?/^M-[A-Z]{2}-\d{2}$/.test(e)||t.push("模具编号格式错误，请使用格式：M-XX-XX（如：M-JX-05）"):t.push("模具编号不能为空"),{isValid:0===t.length,errors:t}}static checkUniqueness(e,t,r,s={}){let i=[],{excludeId:l,fieldName:a}=s;if(!e)return{isValid:!0,errors:[]};if(t.some(t=>t[r]===e&&t.id!==l)){let e=a||String(r);i.push(`${e}已存在，请使用其他值`)}return{isValid:0===i.length,errors:i}}static checkProductCodeUniqueness(e,t,r){return this.checkUniqueness(e,t,"modelCode",{excludeId:r,fieldName:"产品编码"})}static checkWorkstationCodeUniqueness(e,t,r){return this.checkUniqueness(e,t,"code",{excludeId:r,fieldName:"工位编码"})}static checkOrderNumberUniqueness(e,t,r){return this.checkUniqueness(e,t,"orderNumber",{excludeId:r,fieldName:"订单号"})}static validateData(e,t){let r=[];for(let s of t){let t=e[s.field];if(s.required&&(!t||"string"==typeof t&&""===t.trim())){r.push(s.message||`${s.field}不能为空`);continue}if((t||s.required)&&(s.maxLength&&"string"==typeof t&&t.length>s.maxLength&&r.push(`${s.field}不能超过${s.maxLength}个字符`),s.minLength&&"string"==typeof t&&t.length<s.minLength&&r.push(`${s.field}不能少于${s.minLength}个字符`),s.pattern&&"string"==typeof t&&!s.pattern.test(t)&&r.push(s.message||`${s.field}格式不正确`),s.validator)){let i=s.validator(t,e);i.isValid||r.push(...i.errors)}}return{isValid:0===r.length,errors:r}}static validateBatch(e,t){let r=[];for(let s of e){let e=this.validateData(s,t);r.push({item:s,validation:e})}let s=r.filter(e=>!e.validation.isValid);return{isValid:0===s.length,totalItems:e.length,validItems:r.length-s.length,invalidItems:s.length,results:r}}}O.s.ORDER_NUMBER_PATTERNS.SALES_ORDER,O.s.ORDER_NUMBER_FORMATS.SALES_ORDER,O.s.ORDER_NUMBER_PATTERNS.PRODUCTION_ORDER,O.s.ORDER_NUMBER_FORMATS.PRODUCTION_ORDER,O.s.ORDER_NUMBER_PATTERNS.WORK_ORDER,O.s.ORDER_NUMBER_FORMATS.WORK_ORDER;let{Option:_}=l.default,D=()=>{let{message:e}=a.Z.useApp(),[t,r]=(0,i.useState)(!1),[O,D]=(0,i.useState)(!1),[A,T]=(0,i.useState)(null),[W,B]=(0,i.useState)(null),[U,F]=(0,i.useState)(""),[X,V]=(0,i.useState)(!1),[L,Q]=(0,i.useState)(void 0),[H]=n.Z.useForm(),[G,J]=(0,i.useState)([]),[K,Y]=(0,i.useState)(!1),ee=async()=>{Y(!0);try{let e=await (0,q.Ro)(()=>C.dataAccessManager.products.getAll(),"获取产品数据");e&&e.items&&J(e.items)}catch(t){console.error("加载产品数据失败:",t),e.error("加载产品数据失败")}finally{Y(!1)}};(0,i.useEffect)(()=>{V(!0),ee()},[]);let et={active:{color:"green",text:"启用"},inactive:{color:"red",text:"停用"}},er=()=>{let t=G.map(e=>e.modelCode).filter(e=>/^P\d{5}$/.test(e));if(0===t.length)return"P00001";let r=Math.max(...t.map(e=>{let t=e.match(/^P(\d{5})$/);return t?parseInt(t[1],10):0}))+1;return r>99999?(e.warning("编码已达到最大值P99999，请手动输入编码"),""):`P${r.toString().padStart(5,"0")}`},es=(e,t)=>z.checkProductCodeUniqueness(e,G,t).isValid,ei=e=>{T(e),r(!0),F(e.formingMold),H.setFieldsValue(e)},el=e=>{B(e),D(!0)},ea=async t=>{try{Y(!0);let r=await C.dataAccessManager.products.delete(t);"success"===r.status?(await ee(),e.success("产品删除成功")):e.error(r.message||"删除产品失败")}catch(t){e.error("删除产品失败，请稍后重试"),console.error("删除产品失败:",t)}finally{Y(!1)}},en=e=>/^M-[A-Z]{2}-\d{2}$/.test(e),ed=(e,t)=>G.filter(r=>"forming"===t?r.formingMold===e:r.hotPressMold===e),eo=async()=>{try{let t=await H.validateFields();if(Y(!0),A){let r=await C.dataAccessManager.products.update(A.id,t);if("success"===r.status)await ee(),e.success("产品更新成功");else{e.error(r.message||"更新产品失败");return}}else{let r=await C.dataAccessManager.products.create(t);if("success"===r.status)await ee(),e.success("产品创建成功");else{e.error(r.message||"创建产品失败");return}}r(!1),H.resetFields()}catch(t){e.error("操作失败，请稍后重试"),console.error("产品操作失败:",t)}finally{Y(!1)}},ec=X?G.length:0,em=X?G.filter(e=>"active"===e.status).length:0;X&&ec>0&&G.reduce((e,t)=>e+t.formingPiecePrice,0),X&&ec>0&&G.reduce((e,t)=>e+t.hotPressPiecePrice,0);let eh=X&&ec>0?G.reduce((e,t)=>e+t.productPrice,0)/ec:0,eu=X&&ec>0?G.reduce((e,t)=>e+t.productWeight,0)/ec:0;return(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx("div",{className:"page-header",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(M.Z,{className:"text-2xl text-purple-600 mr-3"}),(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"page-title",children:"产品数据管理"}),s.jsx("p",{className:"page-description",children:"管理产品信息、模具关联和计件单价信息"})]})]})}),(0,s.jsxs)(h.Z,{gutter:[16,16],children:[s.jsx(u.Z,{xs:24,sm:6,children:s.jsx(x.Z,{children:s.jsx(p.Z,{title:"型号总数",value:ec,suffix:"个",valueStyle:{color:"#1890ff"}})})}),s.jsx(u.Z,{xs:24,sm:6,children:s.jsx(x.Z,{children:s.jsx(p.Z,{title:"启用型号",value:em,suffix:"个",valueStyle:{color:"#52c41a"}})})}),s.jsx(u.Z,{xs:24,sm:6,children:s.jsx(x.Z,{children:s.jsx(p.Z,{title:"平均产品价格",value:eh,precision:3,prefix:"\xa5",valueStyle:{color:"#722ed1"}})})}),s.jsx(u.Z,{xs:24,sm:6,children:s.jsx(x.Z,{children:s.jsx(p.Z,{title:"平均产品重量",value:eu,precision:2,suffix:"克",valueStyle:{color:"#fa8c16"}})})})]}),s.jsx(x.Z,{children:(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[s.jsx(f.default,{placeholder:"搜索产品编码或名称",prefix:s.jsx(k.Z,{}),className:"w-full sm:w-64"}),(0,s.jsxs)(l.default,{placeholder:"状态",className:"w-full sm:w-32",value:L,onChange:Q,allowClear:!0,children:[s.jsx(_,{value:"active",children:"启用"}),s.jsx(_,{value:"inactive",children:"停用"})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[s.jsx(c.ZP,{icon:s.jsx(S.Z,{}),onClick:()=>{e.info("导入功能开发中...")},children:"导入"}),s.jsx(c.ZP,{icon:s.jsx(N.Z,{}),onClick:()=>{e.success("数据导出成功")},children:"导出"}),s.jsx(c.ZP,{type:"primary",icon:s.jsx(I.Z,{}),onClick:()=>{T(null),r(!0),F(""),H.resetFields();let e=er();e&&H.setFieldsValue({modelCode:e})},children:"新增产品"})]})]})}),s.jsx(x.Z,{title:"产品数据列表",children:s.jsx(g.Z,{columns:[{title:"产品编码",dataIndex:"modelCode",key:"modelCode",width:140,fixed:"left"},{title:"产品名称",dataIndex:"modelName",key:"modelName",width:150},{title:"成型模具",key:"formingMold",width:180,render:(e,t)=>{let r=ed(t.formingMold,"forming").filter(e=>e.id!==t.id);return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[s.jsx("span",{children:t.formingMold}),r.length>0&&(0,s.jsxs)(d.Z,{color:"blue",children:["共享 ",r.length]})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[t.formingMoldQuantity,"个/模"]}),r.length>0&&(0,s.jsxs)("div",{className:"text-xs text-blue-600 mt-1",children:["与 ",r.map(e=>e.modelCode).join("、")," 共享"]})]})}},{title:"热压模具",key:"hotPressMold",width:150,render:(e,t)=>(0,s.jsxs)("div",{children:[s.jsx("div",{children:t.hotPressMold}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[t.hotPressMoldQuantity,"个/模"]})]})},{title:"成型单价",dataIndex:"formingPiecePrice",key:"formingPiecePrice",width:120,render:e=>`\xa5${e.toFixed(2)}/模`},{title:"热压单价",dataIndex:"hotPressPiecePrice",key:"hotPressPiecePrice",width:120,render:e=>`\xa5${e.toFixed(2)}/模`},{title:"产品价格",dataIndex:"productPrice",key:"productPrice",width:120,render:e=>`\xa5${e.toFixed(3)}`,sorter:(e,t)=>e.productPrice-t.productPrice},{title:"产品重量",dataIndex:"productWeight",key:"productWeight",width:120,render:e=>`${e.toFixed(2)}克`,sorter:(e,t)=>e.productWeight-t.productWeight},{title:"箱规",dataIndex:"boxSpecification",key:"boxSpecification",width:150,ellipsis:!0},{title:"装箱数",dataIndex:"packingQuantity",key:"packingQuantity",width:100,render:e=>`${e}个/箱`,sorter:(e,t)=>e.packingQuantity-t.packingQuantity},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>{let t=et[e];return s.jsx(d.Z,{color:t.color,children:t.text})}},{title:"操作",key:"action",width:200,fixed:"right",render:(e,t)=>(0,s.jsxs)(o.Z,{size:"small",children:[s.jsx(c.ZP,{type:"text",icon:s.jsx(P.Z,{}),size:"small",onClick:()=>el(t),children:"查看"}),s.jsx(c.ZP,{type:"text",icon:s.jsx(w.Z,{}),size:"small",onClick:()=>ei(t),children:"编辑"}),s.jsx(m.Z,{title:"确定要删除这个产品吗？",onConfirm:()=>ea(t.id),okText:"确定",cancelText:"取消",children:s.jsx(c.ZP,{type:"text",danger:!0,icon:s.jsx($.Z,{}),size:"small",children:"删除"})})]})}],dataSource:G,rowKey:"id",loading:K,pagination:{total:G.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`,pageSizeOptions:["10","20","50","100"]},scroll:{x:1400}})}),s.jsx(j.Z,{title:A?"编辑产品":"新增产品",open:t,onOk:eo,onCancel:()=>{r(!1),H.resetFields()},width:900,okText:"确认",cancelText:"取消",children:(0,s.jsxs)(n.Z,{form:H,layout:"vertical",initialValues:{status:"active"},children:[(0,s.jsxs)(h.Z,{gutter:16,children:[s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"产品编码",name:"modelCode",rules:[{required:!0,message:"请输入产品编码"},{pattern:/^P\d{5}$/,message:"格式：P + 5位数字（如：P00001）"},{validator:(e,t)=>t?es(t,A?.id)?Promise.resolve():Promise.reject(Error("产品编码已存在，请使用其他编码")):Promise.resolve()}],children:s.jsx(f.default,{placeholder:"如：P00001（自动生成）"})})}),s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"产品名称",name:"modelName",rules:[{required:!0,message:"请输入产品名称"}],children:s.jsx(f.default,{placeholder:"请输入产品名称"})})})]}),s.jsx(v.default,{defaultActiveKey:"mold",items:[{key:"mold",label:(0,s.jsxs)("span",{children:[s.jsx(R.Z,{}),"模具信息"]}),children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(h.Z,{gutter:16,children:[s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"成型模具编号",name:"formingMold",rules:[{required:!0,message:"请输入成型模具编号"},{validator:(e,t)=>!t||en(t)?Promise.resolve():Promise.reject(Error("模具编号格式错误，请使用格式：M-XX-XX（如：M-JX-05）"))}],extra:U&&ed(U,"forming").length>0&&(0,s.jsxs)("div",{style:{marginTop:"4px"},children:[(0,s.jsxs)("span",{style:{color:"#1890ff",fontSize:"12px"},children:["\uD83D\uDCA1 此模具已被 ",ed(U,"forming").length," 个产品使用："]}),s.jsx("div",{style:{fontSize:"11px",color:"#666",marginTop:"2px"},children:ed(U,"forming").filter(e=>e.id!==A?.id).map(e=>e.modelName).join("、")})]}),children:s.jsx(f.default,{placeholder:"如：M-JX-05（支持多产品共享）",onChange:e=>F(e.target.value)})})}),s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"成型模具单模数量",name:"formingMoldQuantity",rules:[{required:!0,message:"请输入单模数量"}],children:s.jsx(b.Z,{className:"w-full",placeholder:"请输入数量",suffix:"个/模",min:1})})})]}),(0,s.jsxs)(h.Z,{gutter:16,children:[s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"热压模具编号",name:"hotPressMold",rules:[{required:!0,message:"请输入热压模具编号"},{validator:(e,t)=>t?en(t)?(A?G.filter(e=>e.id!==A.id):G).some(e=>e.hotPressMold===t)?Promise.reject(Error("热压模具编号已存在，请使用其他编号")):Promise.resolve():Promise.reject(Error("模具编号格式错误，请使用格式：M-XX-XX（如：M-RY-12）")):Promise.resolve()}],children:s.jsx(f.default,{placeholder:"如：M-RY-12"})})}),s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"热压模具单模数量",name:"hotPressMoldQuantity",rules:[{required:!0,message:"请输入单模数量"}],children:s.jsx(b.Z,{className:"w-full",placeholder:"请输入数量",suffix:"个/模",min:1})})})]})]})},{key:"price",label:(0,s.jsxs)("span",{children:[s.jsx(E.Z,{}),"计件单价"]}),children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(h.Z,{gutter:16,children:[s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"成型计件单价",name:"formingPiecePrice",rules:[{required:!0,message:"请输入成型计件单价"}],children:s.jsx(b.Z,{className:"w-full",placeholder:"请输入单价",prefix:"\xa5",suffix:"/模",min:0,precision:2})})}),s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"热压计件单价",name:"hotPressPiecePrice",rules:[{required:!0,message:"请输入热压计件单价"}],children:s.jsx(b.Z,{className:"w-full",placeholder:"请输入单价",prefix:"\xa5",suffix:"/模",min:0,precision:2})})})]}),(0,s.jsxs)(h.Z,{gutter:16,children:[s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"产品价格",name:"productPrice",rules:[{required:!0,message:"请输入产品价格"},{type:"number",min:.01,message:"产品价格必须大于0"}],children:s.jsx(b.Z,{className:"w-full",placeholder:"请输入产品价格",prefix:"\xa5",min:0,precision:3,step:.001})})}),s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"产品重量",name:"productWeight",rules:[{required:!0,message:"请输入产品重量"},{type:"number",min:.01,message:"产品重量必须大于0"}],children:s.jsx(b.Z,{className:"w-full",placeholder:"请输入产品重量",suffix:"克",min:0,precision:2,step:.01})})})]}),(0,s.jsxs)(h.Z,{gutter:16,children:[s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"箱规",name:"boxSpecification",rules:[{required:!0,message:"请输入箱规"}],children:s.jsx(f.default,{placeholder:"如：30\xd720\xd715 cm"})})}),s.jsx(u.Z,{span:12,children:s.jsx(n.Z.Item,{label:"装箱数",name:"packingQuantity",rules:[{required:!0,message:"请输入装箱数"},{type:"number",min:1,message:"装箱数必须大于0"}],children:s.jsx(b.Z,{className:"w-full",placeholder:"请输入装箱数",suffix:"个/箱",min:1,precision:0,step:1})})})]}),s.jsx(n.Z.Item,{label:"状态",name:"status",rules:[{required:!0,message:"请选择状态"}],children:(0,s.jsxs)(l.default,{placeholder:"请选择状态",children:[s.jsx(_,{value:"active",children:"启用"}),s.jsx(_,{value:"inactive",children:"停用"})]})})]})}]})]})}),s.jsx(j.Z,{title:"产品型号详情",open:O,onCancel:()=>D(!1),footer:[s.jsx(c.ZP,{onClick:()=>D(!1),children:"关闭"},"close")],width:900,children:W&&W.modelCode&&W.modelName&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(Z.Z,{bordered:!0,column:2,children:[s.jsx(Z.Z.Item,{label:"产品编码",children:W.modelCode}),s.jsx(Z.Z.Item,{label:"产品名称",children:W.modelName}),s.jsx(Z.Z.Item,{label:"成型模具编号",children:W.formingMold}),(0,s.jsxs)(Z.Z.Item,{label:"成型模具单模数量",children:[W.formingMoldQuantity,"个/模"]}),s.jsx(Z.Z.Item,{label:"热压模具编号",children:W.hotPressMold}),(0,s.jsxs)(Z.Z.Item,{label:"热压模具单模数量",children:[W.hotPressMoldQuantity,"个/模"]}),(0,s.jsxs)(Z.Z.Item,{label:"成型计件单价",children:["\xa5",W.formingPiecePrice.toFixed(2),"/模"]}),(0,s.jsxs)(Z.Z.Item,{label:"热压计件单价",children:["\xa5",W.hotPressPiecePrice.toFixed(2),"/模"]}),(0,s.jsxs)(Z.Z.Item,{label:"产品价格",children:["\xa5",W.productPrice.toFixed(3)]}),(0,s.jsxs)(Z.Z.Item,{label:"产品重量",children:[W.productWeight.toFixed(2),"克"]}),s.jsx(Z.Z.Item,{label:"箱规",children:W.boxSpecification}),(0,s.jsxs)(Z.Z.Item,{label:"装箱数",children:[W.packingQuantity,"个/箱"]}),s.jsx(Z.Z.Item,{label:"状态",children:s.jsx(d.Z,{color:et[W.status].color,children:et[W.status].text})}),s.jsx(Z.Z.Item,{label:"创建时间",span:2,children:W.createdAt}),s.jsx(Z.Z.Item,{label:"更新时间",span:2,children:W.updatedAt})]}),(()=>{let e=ed(W.formingMold,"forming").filter(e=>e.id!==W.id),t=ed(W.hotPressMold,"hotPress").filter(e=>e.id!==W.id);return(e.length>0||t.length>0)&&(0,s.jsxs)("div",{children:[s.jsx(y.Z,{orientation:"left",children:"模具共享信息"}),e.length>0&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:["\uD83D\uDD27 共享成型模具 ",W.formingMold," 的其他产品："]}),s.jsx("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,s.jsxs)(d.Z,{color:"blue",className:"mb-1",children:[e.modelCode," - ",e.modelName]},e.id))})]}),t.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:["\uD83D\uDD25 共享热压模具 ",W.hotPressMold," 的其他产品："]}),s.jsx("div",{className:"flex flex-wrap gap-2",children:t.map(e=>(0,s.jsxs)(d.Z,{color:"orange",className:"mb-1",children:[e.modelCode," - ",e.modelName]},e.id))})]})]})})()]})})]})};function A(){return s.jsx(a.Z,{children:s.jsx(D,{})})}},51221:(e,t,r)=>{"use strict";r.d(t,{Ro:()=>s});let s=async(e,t)=>{try{let t=await e();if("success"===t.status)return t.data||null;return t.code,t.message,t.message,null}catch(e){return e instanceof Error?e.message:String(e),null}}},52054:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(25036),i=r(38834);function l({children:e}){return s.jsx(i.Z,{children:e})}},57490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>l,__esModule:()=>i,default:()=>a});let s=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\master-data\product-models\page.tsx`),{__esModule:i,$$typeof:l}=s,a=s.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,2345,4441,7383,6274,996,6133],()=>r(75674));module.exports=s})();