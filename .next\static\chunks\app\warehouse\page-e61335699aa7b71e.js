(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5137],{69677:function(e,t,c){Promise.resolve().then(c.bind(c,54770))},54770:function(e,t,c){"use strict";c.r(t);var l=c(57437),a=c(2265),s=c(27296),r=c(89198),i=c(6053),o=c(34863),n=c(65270),d=c(94734),x=c(38302),u=c(28683),h=c(89511),m=c(86155),p=c(2012),j=c(39992),y=c(50574),f=c(81453),k=c(65362),w=c(78740),v=c(23455),Z=c(75393),S=c(51769),g=c(74898);let{Option:N}=s.default;t.default=()=>{let[e,t]=(0,a.useState)(!1),[c,P]=(0,a.useState)("inventory"),[C]=r.Z.useForm(),I=[{id:"1",productCode:"P001",productName:"高强度钢材",category:"原材料",currentStock:1250,minStock:500,maxStock:2e3,unitPrice:45.5,location:"A区-01",lastUpdated:"2024-01-15"},{id:"2",productCode:"P002",productName:"精密轴承",category:"零部件",currentStock:320,minStock:200,maxStock:800,unitPrice:125,location:"B区-05",lastUpdated:"2024-01-14"},{id:"3",productCode:"P003",productName:"电机组件",category:"成品",currentStock:85,minStock:100,maxStock:300,unitPrice:850,location:"C区-12",lastUpdated:"2024-01-13"}],b=[{id:"1",productCode:"P001",productName:"高强度钢材",type:"in",quantity:500,toLocation:"A区-01",date:"2024-01-15",operator:"张三",reason:"采购入库"},{id:"2",productCode:"P002",productName:"精密轴承",type:"out",quantity:50,fromLocation:"B区-05",date:"2024-01-14",operator:"李四",reason:"生产领料"}],z=(e,t,c)=>e<t?{status:"exception",text:"库存不足",color:"red"}:e>c?{status:"active",text:"库存过多",color:"orange"}:{status:"success",text:"正常",color:"green"};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("div",{className:"page-header",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(w.Z,{className:"text-2xl text-green-600 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"page-title",children:"仓库管理"}),(0,l.jsx)("p",{className:"page-description",children:"管理库存、库位和出入库操作"})]})]})}),(0,l.jsxs)(x.Z,{gutter:[16,16],children:[(0,l.jsx)(u.Z,{xs:24,sm:6,children:(0,l.jsx)(h.Z,{children:(0,l.jsx)(m.Z,{title:"库存总值",value:2456789,precision:2,prefix:"\xa5",valueStyle:{color:"#1890ff"}})})}),(0,l.jsx)(u.Z,{xs:24,sm:6,children:(0,l.jsx)(h.Z,{children:(0,l.jsx)(m.Z,{title:"库存品种",value:1256,suffix:"种",valueStyle:{color:"#52c41a"}})})}),(0,l.jsx)(u.Z,{xs:24,sm:6,children:(0,l.jsx)(h.Z,{children:(0,l.jsx)(m.Z,{title:"库存不足",value:23,suffix:"种",valueStyle:{color:"#ff4d4f"},prefix:(0,l.jsx)(v.Z,{})})})}),(0,l.jsx)(u.Z,{xs:24,sm:6,children:(0,l.jsx)(h.Z,{children:(0,l.jsx)(m.Z,{title:"今日出入库",value:156,suffix:"次",valueStyle:{color:"#722ed1"}})})})]}),(0,l.jsx)(h.Z,{children:(0,l.jsx)(p.default,{activeKey:c,onChange:P,items:[{key:"inventory",label:"库存管理",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,l.jsx)(j.default,{placeholder:"搜索产品编码或名称",prefix:(0,l.jsx)(Z.Z,{}),className:"w-full sm:w-64"}),(0,l.jsxs)(s.default,{placeholder:"产品分类",className:"w-full sm:w-32",children:[(0,l.jsx)(N,{value:"",children:"全部"}),(0,l.jsx)(N,{value:"原材料",children:"原材料"}),(0,l.jsx)(N,{value:"零部件",children:"零部件"}),(0,l.jsx)(N,{value:"成品",children:"成品"})]}),(0,l.jsxs)(s.default,{placeholder:"库存状态",className:"w-full sm:w-32",children:[(0,l.jsx)(N,{value:"",children:"全部"}),(0,l.jsx)(N,{value:"normal",children:"正常"}),(0,l.jsx)(N,{value:"low",children:"库存不足"}),(0,l.jsx)(N,{value:"high",children:"库存过多"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(d.ZP,{icon:(0,l.jsx)(S.Z,{}),children:"导出"}),(0,l.jsx)(d.ZP,{type:"primary",icon:(0,l.jsx)(g.Z,{}),children:"新增产品"})]})]}),(0,l.jsx)(y.Z,{columns:[{title:"产品编码",dataIndex:"productCode",key:"productCode",width:100},{title:"产品名称",dataIndex:"productName",key:"productName",width:150},{title:"分类",dataIndex:"category",key:"category",width:100},{title:"当前库存",dataIndex:"currentStock",key:"currentStock",width:100,render:(e,t)=>{let c=z(t.currentStock,t.minStock,t.maxStock);return(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:e}),(0,l.jsx)(i.Z,{color:c.color,children:c.text})]})}},{title:"库存水位",key:"stockLevel",width:150,render:(e,t)=>{let c=t.currentStock/t.maxStock*100,a=z(t.currentStock,t.minStock,t.maxStock);return(0,l.jsx)(o.Z,{percent:c,size:"small",status:a.status,format:()=>"".concat(t.currentStock,"/").concat(t.maxStock)})}},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:100,render:e=>"\xa5".concat(e.toFixed(2))},{title:"库位",dataIndex:"location",key:"location",width:100},{title:"操作",key:"action",width:150,render:(e,t)=>(0,l.jsxs)(n.Z,{size:"small",children:[(0,l.jsx)(d.ZP,{type:"text",icon:(0,l.jsx)(f.Z,{}),size:"small",children:"调拨"}),(0,l.jsx)(d.ZP,{type:"text",icon:(0,l.jsx)(k.Z,{}),size:"small",children:"编辑"})]})}],dataSource:I,rowKey:"id",pagination:{total:I.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条")},scroll:{x:1e3}})]})},{key:"movement",label:"出入库记录",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,l.jsx)(j.default,{placeholder:"搜索产品编码或名称",prefix:(0,l.jsx)(Z.Z,{}),className:"w-full sm:w-64"}),(0,l.jsxs)(s.default,{placeholder:"变动类型",className:"w-full sm:w-32",children:[(0,l.jsx)(N,{value:"",children:"全部"}),(0,l.jsx)(N,{value:"in",children:"入库"}),(0,l.jsx)(N,{value:"out",children:"出库"}),(0,l.jsx)(N,{value:"transfer",children:"调拨"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(d.ZP,{icon:(0,l.jsx)(S.Z,{}),children:"导出"}),(0,l.jsx)(d.ZP,{type:"primary",icon:(0,l.jsx)(g.Z,{}),children:"新增记录"})]})]}),(0,l.jsx)(y.Z,{columns:[{title:"产品编码",dataIndex:"productCode",key:"productCode",width:100},{title:"产品名称",dataIndex:"productName",key:"productName",width:150},{title:"变动类型",dataIndex:"type",key:"type",width:100,render:e=>{let t={in:{color:"green",text:"入库"},out:{color:"red",text:"出库"},transfer:{color:"blue",text:"调拨"}}[e];return(0,l.jsx)(i.Z,{color:t.color,children:t.text})}},{title:"数量",dataIndex:"quantity",key:"quantity",width:80},{title:"库位",key:"location",width:120,render:(e,t)=>"in"===t.type?t.toLocation:"out"===t.type?t.fromLocation:"".concat(t.fromLocation," → ").concat(t.toLocation)},{title:"操作人",dataIndex:"operator",key:"operator",width:100},{title:"日期",dataIndex:"date",key:"date",width:100},{title:"原因",dataIndex:"reason",key:"reason",width:120}],dataSource:b,rowKey:"id",pagination:{total:b.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条")},scroll:{x:1e3}})]})}]})})]})}}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,128,9198,9992,4863,7453,2971,4938,1744],function(){return e(e.s=69677)}),_N_E=e.O()}]);