(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8149],{13652:function(e,s,t){Promise.resolve().then(t.bind(t,30931))},30931:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return q}});var l=t(57437),i=t(2265),r=t(27296),a=t(57416),n=t(89198),c=t(6053),d=t(65270),o=t(94734),h=t(92503),u=t(38302),m=t(28683),x=t(89511),p=t(86155),f=t(39992),j=t(50574),g=t(47628),Z=t(2012),R=t(75123),P=t(99617),N=t(28116),v=t(75216),y=t(65362),E=t(34021),b=t(88652),I=t(75393),M=t(33477),_=t(51769),w=t(74898),O=t(8399),S=t(40856),k=t(32779),D=t(50538),C=t(53641);class T{static validateProductCode(e){let s=[];return e?/^P\d{5}$/.test(e)||s.push("产品编码格式错误，请使用格式：P + 5位数字（如：P00001）"):s.push("产品编码不能为空"),{isValid:0===s.length,errors:s}}static validateWorkstationCode(e){let s=[];return e?/^WS\d{3}$/.test(e)||s.push("工位编码格式错误，请使用格式：WS + 3位数字（如：WS001）"):s.push("工位编码不能为空"),{isValid:0===s.length,errors:s}}static validateMoldNumber(e){let s=[];return e?/^M-[A-Z]{2}-\d{2}$/.test(e)||s.push("模具编号格式错误，请使用格式：M-XX-XX（如：M-JX-05）"):s.push("模具编号不能为空"),{isValid:0===s.length,errors:s}}static checkUniqueness(e,s,t){let l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=[],{excludeId:r,fieldName:a}=l;if(!e)return{isValid:!0,errors:[]};if(s.some(s=>s[t]===e&&s.id!==r)){let e=a||String(t);i.push("".concat(e,"已存在，请使用其他值"))}return{isValid:0===i.length,errors:i}}static checkProductCodeUniqueness(e,s,t){return this.checkUniqueness(e,s,"modelCode",{excludeId:t,fieldName:"产品编码"})}static checkWorkstationCodeUniqueness(e,s,t){return this.checkUniqueness(e,s,"code",{excludeId:t,fieldName:"工位编码"})}static checkOrderNumberUniqueness(e,s,t){return this.checkUniqueness(e,s,"orderNumber",{excludeId:t,fieldName:"订单号"})}static validateData(e,s){let t=[];for(let l of s){let s=e[l.field];if(l.required&&(!s||"string"==typeof s&&""===s.trim())){t.push(l.message||"".concat(l.field,"不能为空"));continue}if((s||l.required)&&(l.maxLength&&"string"==typeof s&&s.length>l.maxLength&&t.push("".concat(l.field,"不能超过").concat(l.maxLength,"个字符")),l.minLength&&"string"==typeof s&&s.length<l.minLength&&t.push("".concat(l.field,"不能少于").concat(l.minLength,"个字符")),l.pattern&&"string"==typeof s&&!l.pattern.test(s)&&t.push(l.message||"".concat(l.field,"格式不正确")),l.validator)){let i=l.validator(s,e);i.isValid||t.push(...i.errors)}}return{isValid:0===t.length,errors:t}}static validateBatch(e,s){let t=[];for(let l of e){let e=this.validateData(l,s);t.push({item:l,validation:e})}let l=t.filter(e=>!e.validation.isValid);return{isValid:0===l.length,totalItems:e.length,validItems:t.length-l.length,invalidItems:l.length,results:t}}}C.s.ORDER_NUMBER_PATTERNS.SALES_ORDER,C.s.ORDER_NUMBER_FORMATS.SALES_ORDER,C.s.ORDER_NUMBER_PATTERNS.PRODUCTION_ORDER,C.s.ORDER_NUMBER_FORMATS.PRODUCTION_ORDER,C.s.ORDER_NUMBER_PATTERNS.WORK_ORDER,C.s.ORDER_NUMBER_FORMATS.WORK_ORDER;let{Option:U}=r.default,F=()=>{let{message:e}=a.Z.useApp(),[s,t]=(0,i.useState)(!1),[C,F]=(0,i.useState)(!1),[q,A]=(0,i.useState)(null),[X,W]=(0,i.useState)(null),[V,Q]=(0,i.useState)(""),[B,L]=(0,i.useState)(!1),[z,$]=(0,i.useState)(void 0),[K]=n.Z.useForm(),[J,Y]=(0,i.useState)([]),[H,G]=(0,i.useState)(!1),ee=async()=>{G(!0);try{let e=await (0,D.Ro)(()=>k.dataAccessManager.products.getAll(),"获取产品数据");e&&e.items&&Y(e.items)}catch(s){console.error("加载产品数据失败:",s),e.error("加载产品数据失败")}finally{G(!1)}};(0,i.useEffect)(()=>{L(!0),ee()},[]);let es={active:{color:"green",text:"启用"},inactive:{color:"red",text:"停用"}},et=()=>{let s=J.map(e=>e.modelCode).filter(e=>/^P\d{5}$/.test(e));if(0===s.length)return"P00001";let t=Math.max(...s.map(e=>{let s=e.match(/^P(\d{5})$/);return s?parseInt(s[1],10):0}))+1;return t>99999?(e.warning("编码已达到最大值P99999，请手动输入编码"),""):"P".concat(t.toString().padStart(5,"0"))},el=(e,s)=>T.checkProductCodeUniqueness(e,J,s).isValid,ei=e=>{A(e),t(!0),Q(e.formingMold),K.setFieldsValue(e)},er=e=>{W(e),F(!0)},ea=async s=>{try{G(!0);let t=await k.dataAccessManager.products.delete(s);"success"===t.status?(await ee(),e.success("产品删除成功")):e.error(t.message||"删除产品失败")}catch(s){e.error("删除产品失败，请稍后重试"),console.error("删除产品失败:",s)}finally{G(!1)}},en=e=>/^M-[A-Z]{2}-\d{2}$/.test(e),ec=(e,s)=>J.filter(t=>"forming"===s?t.formingMold===e:t.hotPressMold===e),ed=async()=>{try{let s=await K.validateFields();if(G(!0),q){let t=await k.dataAccessManager.products.update(q.id,s);if("success"===t.status)await ee(),e.success("产品更新成功");else{e.error(t.message||"更新产品失败");return}}else{let t=await k.dataAccessManager.products.create(s);if("success"===t.status)await ee(),e.success("产品创建成功");else{e.error(t.message||"创建产品失败");return}}t(!1),K.resetFields()}catch(s){e.error("操作失败，请稍后重试"),console.error("产品操作失败:",s)}finally{G(!1)}},eo=B?J.length:0,eh=B?J.filter(e=>"active"===e.status).length:0;B&&eo>0&&J.reduce((e,s)=>e+s.formingPiecePrice,0),B&&eo>0&&J.reduce((e,s)=>e+s.hotPressPiecePrice,0);let eu=B&&eo>0?J.reduce((e,s)=>e+s.productPrice,0)/eo:0,em=B&&eo>0?J.reduce((e,s)=>e+s.productWeight,0)/eo:0;return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)("div",{className:"page-header",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(b.Z,{className:"text-2xl text-purple-600 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"page-title",children:"产品数据管理"}),(0,l.jsx)("p",{className:"page-description",children:"管理产品信息、模具关联和计件单价信息"})]})]})}),(0,l.jsxs)(u.Z,{gutter:[16,16],children:[(0,l.jsx)(m.Z,{xs:24,sm:6,children:(0,l.jsx)(x.Z,{children:(0,l.jsx)(p.Z,{title:"型号总数",value:eo,suffix:"个",valueStyle:{color:"#1890ff"}})})}),(0,l.jsx)(m.Z,{xs:24,sm:6,children:(0,l.jsx)(x.Z,{children:(0,l.jsx)(p.Z,{title:"启用型号",value:eh,suffix:"个",valueStyle:{color:"#52c41a"}})})}),(0,l.jsx)(m.Z,{xs:24,sm:6,children:(0,l.jsx)(x.Z,{children:(0,l.jsx)(p.Z,{title:"平均产品价格",value:eu,precision:3,prefix:"\xa5",valueStyle:{color:"#722ed1"}})})}),(0,l.jsx)(m.Z,{xs:24,sm:6,children:(0,l.jsx)(x.Z,{children:(0,l.jsx)(p.Z,{title:"平均产品重量",value:em,precision:2,suffix:"克",valueStyle:{color:"#fa8c16"}})})})]}),(0,l.jsx)(x.Z,{children:(0,l.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,l.jsx)(f.default,{placeholder:"搜索产品编码或名称",prefix:(0,l.jsx)(I.Z,{}),className:"w-full sm:w-64"}),(0,l.jsxs)(r.default,{placeholder:"状态",className:"w-full sm:w-32",value:z,onChange:$,allowClear:!0,children:[(0,l.jsx)(U,{value:"active",children:"启用"}),(0,l.jsx)(U,{value:"inactive",children:"停用"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[(0,l.jsx)(o.ZP,{icon:(0,l.jsx)(M.Z,{}),onClick:()=>{e.info("导入功能开发中...")},children:"导入"}),(0,l.jsx)(o.ZP,{icon:(0,l.jsx)(_.Z,{}),onClick:()=>{e.success("数据导出成功")},children:"导出"}),(0,l.jsx)(o.ZP,{type:"primary",icon:(0,l.jsx)(w.Z,{}),onClick:()=>{A(null),t(!0),Q(""),K.resetFields();let e=et();e&&K.setFieldsValue({modelCode:e})},children:"新增产品"})]})]})}),(0,l.jsx)(x.Z,{title:"产品数据列表",children:(0,l.jsx)(j.Z,{columns:[{title:"产品编码",dataIndex:"modelCode",key:"modelCode",width:140,fixed:"left"},{title:"产品名称",dataIndex:"modelName",key:"modelName",width:150},{title:"成型模具",key:"formingMold",width:180,render:(e,s)=>{let t=ec(s.formingMold,"forming").filter(e=>e.id!==s.id);return(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)("span",{children:s.formingMold}),t.length>0&&(0,l.jsxs)(c.Z,{color:"blue",children:["共享 ",t.length]})]}),(0,l.jsxs)("div",{className:"text-xs text-gray-500",children:[s.formingMoldQuantity,"个/模"]}),t.length>0&&(0,l.jsxs)("div",{className:"text-xs text-blue-600 mt-1",children:["与 ",t.map(e=>e.modelCode).join("、")," 共享"]})]})}},{title:"热压模具",key:"hotPressMold",width:150,render:(e,s)=>(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:s.hotPressMold}),(0,l.jsxs)("div",{className:"text-xs text-gray-500",children:[s.hotPressMoldQuantity,"个/模"]})]})},{title:"成型单价",dataIndex:"formingPiecePrice",key:"formingPiecePrice",width:120,render:e=>"\xa5".concat(e.toFixed(2),"/模")},{title:"热压单价",dataIndex:"hotPressPiecePrice",key:"hotPressPiecePrice",width:120,render:e=>"\xa5".concat(e.toFixed(2),"/模")},{title:"产品价格",dataIndex:"productPrice",key:"productPrice",width:120,render:e=>"\xa5".concat(e.toFixed(3)),sorter:(e,s)=>e.productPrice-s.productPrice},{title:"产品重量",dataIndex:"productWeight",key:"productWeight",width:120,render:e=>"".concat(e.toFixed(2),"克"),sorter:(e,s)=>e.productWeight-s.productWeight},{title:"箱规",dataIndex:"boxSpecification",key:"boxSpecification",width:150,ellipsis:!0},{title:"装箱数",dataIndex:"packingQuantity",key:"packingQuantity",width:100,render:e=>"".concat(e,"个/箱"),sorter:(e,s)=>e.packingQuantity-s.packingQuantity},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>{let s=es[e];return(0,l.jsx)(c.Z,{color:s.color,children:s.text})}},{title:"操作",key:"action",width:200,fixed:"right",render:(e,s)=>(0,l.jsxs)(d.Z,{size:"small",children:[(0,l.jsx)(o.ZP,{type:"text",icon:(0,l.jsx)(v.Z,{}),size:"small",onClick:()=>er(s),children:"查看"}),(0,l.jsx)(o.ZP,{type:"text",icon:(0,l.jsx)(y.Z,{}),size:"small",onClick:()=>ei(s),children:"编辑"}),(0,l.jsx)(h.Z,{title:"确定要删除这个产品吗？",onConfirm:()=>ea(s.id),okText:"确定",cancelText:"取消",children:(0,l.jsx)(o.ZP,{type:"text",danger:!0,icon:(0,l.jsx)(E.Z,{}),size:"small",children:"删除"})})]})}],dataSource:J,rowKey:"id",loading:H,pagination:{total:J.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,s)=>"第 ".concat(s[0],"-").concat(s[1]," 条/共 ").concat(e," 条"),pageSizeOptions:["10","20","50","100"]},scroll:{x:1400}})}),(0,l.jsx)(g.Z,{title:q?"编辑产品":"新增产品",open:s,onOk:ed,onCancel:()=>{t(!1),K.resetFields()},width:900,okText:"确认",cancelText:"取消",children:(0,l.jsxs)(n.Z,{form:K,layout:"vertical",initialValues:{status:"active"},children:[(0,l.jsxs)(u.Z,{gutter:16,children:[(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"产品编码",name:"modelCode",rules:[{required:!0,message:"请输入产品编码"},{pattern:/^P\d{5}$/,message:"格式：P + 5位数字（如：P00001）"},{validator:(e,s)=>s?el(s,null==q?void 0:q.id)?Promise.resolve():Promise.reject(Error("产品编码已存在，请使用其他编码")):Promise.resolve()}],children:(0,l.jsx)(f.default,{placeholder:"如：P00001（自动生成）"})})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"产品名称",name:"modelName",rules:[{required:!0,message:"请输入产品名称"}],children:(0,l.jsx)(f.default,{placeholder:"请输入产品名称"})})})]}),(0,l.jsx)(Z.default,{defaultActiveKey:"mold",items:[{key:"mold",label:(0,l.jsxs)("span",{children:[(0,l.jsx)(O.Z,{}),"模具信息"]}),children:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(u.Z,{gutter:16,children:[(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"成型模具编号",name:"formingMold",rules:[{required:!0,message:"请输入成型模具编号"},{validator:(e,s)=>!s||en(s)?Promise.resolve():Promise.reject(Error("模具编号格式错误，请使用格式：M-XX-XX（如：M-JX-05）"))}],extra:V&&ec(V,"forming").length>0&&(0,l.jsxs)("div",{style:{marginTop:"4px"},children:[(0,l.jsxs)("span",{style:{color:"#1890ff",fontSize:"12px"},children:["\uD83D\uDCA1 此模具已被 ",ec(V,"forming").length," 个产品使用："]}),(0,l.jsx)("div",{style:{fontSize:"11px",color:"#666",marginTop:"2px"},children:ec(V,"forming").filter(e=>e.id!==(null==q?void 0:q.id)).map(e=>e.modelName).join("、")})]}),children:(0,l.jsx)(f.default,{placeholder:"如：M-JX-05（支持多产品共享）",onChange:e=>Q(e.target.value)})})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"成型模具单模数量",name:"formingMoldQuantity",rules:[{required:!0,message:"请输入单模数量"}],children:(0,l.jsx)(R.Z,{className:"w-full",placeholder:"请输入数量",suffix:"个/模",min:1})})})]}),(0,l.jsxs)(u.Z,{gutter:16,children:[(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"热压模具编号",name:"hotPressMold",rules:[{required:!0,message:"请输入热压模具编号"},{validator:(e,s)=>s?en(s)?(q?J.filter(e=>e.id!==q.id):J).some(e=>e.hotPressMold===s)?Promise.reject(Error("热压模具编号已存在，请使用其他编号")):Promise.resolve():Promise.reject(Error("模具编号格式错误，请使用格式：M-XX-XX（如：M-RY-12）")):Promise.resolve()}],children:(0,l.jsx)(f.default,{placeholder:"如：M-RY-12"})})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"热压模具单模数量",name:"hotPressMoldQuantity",rules:[{required:!0,message:"请输入单模数量"}],children:(0,l.jsx)(R.Z,{className:"w-full",placeholder:"请输入数量",suffix:"个/模",min:1})})})]})]})},{key:"price",label:(0,l.jsxs)("span",{children:[(0,l.jsx)(S.Z,{}),"计件单价"]}),children:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(u.Z,{gutter:16,children:[(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"成型计件单价",name:"formingPiecePrice",rules:[{required:!0,message:"请输入成型计件单价"}],children:(0,l.jsx)(R.Z,{className:"w-full",placeholder:"请输入单价",prefix:"\xa5",suffix:"/模",min:0,precision:2})})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"热压计件单价",name:"hotPressPiecePrice",rules:[{required:!0,message:"请输入热压计件单价"}],children:(0,l.jsx)(R.Z,{className:"w-full",placeholder:"请输入单价",prefix:"\xa5",suffix:"/模",min:0,precision:2})})})]}),(0,l.jsxs)(u.Z,{gutter:16,children:[(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"产品价格",name:"productPrice",rules:[{required:!0,message:"请输入产品价格"},{type:"number",min:.01,message:"产品价格必须大于0"}],children:(0,l.jsx)(R.Z,{className:"w-full",placeholder:"请输入产品价格",prefix:"\xa5",min:0,precision:3,step:.001})})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"产品重量",name:"productWeight",rules:[{required:!0,message:"请输入产品重量"},{type:"number",min:.01,message:"产品重量必须大于0"}],children:(0,l.jsx)(R.Z,{className:"w-full",placeholder:"请输入产品重量",suffix:"克",min:0,precision:2,step:.01})})})]}),(0,l.jsxs)(u.Z,{gutter:16,children:[(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"箱规",name:"boxSpecification",rules:[{required:!0,message:"请输入箱规"}],children:(0,l.jsx)(f.default,{placeholder:"如：30\xd720\xd715 cm"})})}),(0,l.jsx)(m.Z,{span:12,children:(0,l.jsx)(n.Z.Item,{label:"装箱数",name:"packingQuantity",rules:[{required:!0,message:"请输入装箱数"},{type:"number",min:1,message:"装箱数必须大于0"}],children:(0,l.jsx)(R.Z,{className:"w-full",placeholder:"请输入装箱数",suffix:"个/箱",min:1,precision:0,step:1})})})]}),(0,l.jsx)(n.Z.Item,{label:"状态",name:"status",rules:[{required:!0,message:"请选择状态"}],children:(0,l.jsxs)(r.default,{placeholder:"请选择状态",children:[(0,l.jsx)(U,{value:"active",children:"启用"}),(0,l.jsx)(U,{value:"inactive",children:"停用"})]})})]})}]})]})}),(0,l.jsx)(g.Z,{title:"产品型号详情",open:C,onCancel:()=>F(!1),footer:[(0,l.jsx)(o.ZP,{onClick:()=>F(!1),children:"关闭"},"close")],width:900,children:X&&X.modelCode&&X.modelName&&(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(P.Z,{bordered:!0,column:2,children:[(0,l.jsx)(P.Z.Item,{label:"产品编码",children:X.modelCode}),(0,l.jsx)(P.Z.Item,{label:"产品名称",children:X.modelName}),(0,l.jsx)(P.Z.Item,{label:"成型模具编号",children:X.formingMold}),(0,l.jsxs)(P.Z.Item,{label:"成型模具单模数量",children:[X.formingMoldQuantity,"个/模"]}),(0,l.jsx)(P.Z.Item,{label:"热压模具编号",children:X.hotPressMold}),(0,l.jsxs)(P.Z.Item,{label:"热压模具单模数量",children:[X.hotPressMoldQuantity,"个/模"]}),(0,l.jsxs)(P.Z.Item,{label:"成型计件单价",children:["\xa5",X.formingPiecePrice.toFixed(2),"/模"]}),(0,l.jsxs)(P.Z.Item,{label:"热压计件单价",children:["\xa5",X.hotPressPiecePrice.toFixed(2),"/模"]}),(0,l.jsxs)(P.Z.Item,{label:"产品价格",children:["\xa5",X.productPrice.toFixed(3)]}),(0,l.jsxs)(P.Z.Item,{label:"产品重量",children:[X.productWeight.toFixed(2),"克"]}),(0,l.jsx)(P.Z.Item,{label:"箱规",children:X.boxSpecification}),(0,l.jsxs)(P.Z.Item,{label:"装箱数",children:[X.packingQuantity,"个/箱"]}),(0,l.jsx)(P.Z.Item,{label:"状态",children:(0,l.jsx)(c.Z,{color:es[X.status].color,children:es[X.status].text})}),(0,l.jsx)(P.Z.Item,{label:"创建时间",span:2,children:X.createdAt}),(0,l.jsx)(P.Z.Item,{label:"更新时间",span:2,children:X.updatedAt})]}),(()=>{let e=ec(X.formingMold,"forming").filter(e=>e.id!==X.id),s=ec(X.hotPressMold,"hotPress").filter(e=>e.id!==X.id);return(e.length>0||s.length>0)&&(0,l.jsxs)("div",{children:[(0,l.jsx)(N.Z,{orientation:"left",children:"模具共享信息"}),e.length>0&&(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:["\uD83D\uDD27 共享成型模具 ",X.formingMold," 的其他产品："]}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,l.jsxs)(c.Z,{color:"blue",className:"mb-1",children:[e.modelCode," - ",e.modelName]},e.id))})]}),s.length>0&&(0,l.jsxs)("div",{children:[(0,l.jsxs)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:["\uD83D\uDD25 共享热压模具 ",X.hotPressMold," 的其他产品："]}),(0,l.jsx)("div",{className:"flex flex-wrap gap-2",children:s.map(e=>(0,l.jsxs)(c.Z,{color:"orange",className:"mb-1",children:[e.modelCode," - ",e.modelName]},e.id))})]})]})})()]})})]})};function q(){return(0,l.jsx)(a.Z,{children:(0,l.jsx)(F,{})})}},50538:function(e,s,t){"use strict";t.d(s,{Ro:function(){return l}});let l=async(e,s)=>{try{let t=await e();if("success"===t.status)return t.data||null;return t.code,t.message,t.message,s&&r(String(t.code||"UNKNOWN_ERROR")),null}catch(e){return e instanceof Error?e.message:String(e),null}},i={ERR_NOT_FOUND:"资源不存在",ERR_UNAUTHORIZED:"未授权访问",ERR_FORBIDDEN:"禁止访问",ERR_INTERNAL_ERROR:"内部服务器错误",ERR_PRODUCT_NOT_FOUND:"产品不存在",ERR_PRODUCT_CODE_EXISTS:"产品编码已存在",ERR_CUSTOMER_NOT_FOUND:"客户不存在",ERR_INVENTORY_INSUFFICIENT:"库存不足",ERR_ORDER_NOT_FOUND:"订单不存在",ERR_ORDER_NUMBER_EXISTS:"订单号已存在",DUPLICATE_ORDER_NUMBER:"订单号重复"},r=e=>i[e]||"操作失败"}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,1157,7416,8236,9617,2897,8304,2779,2971,4938,1744],function(){return e(e.s=13652)}),_N_E=e.O()}]);