'use client'

import React, { useState, useEffect, useMemo } from 'react'
import { useRouter } from 'next/navigation'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  DatePicker,
  Popconfirm,
  Descriptions,
  Divider,
  Alert,
  Progress,
  Timeline,
  Badge,
  Tooltip,
  Steps,
  App,
  Row,
  Col,
  Statistic,
  InputNumber
} from 'antd'
import { SearchOutlined, DeleteOutlined, ExportOutlined, SwapOutlined, InboxOutlined, CheckOutlined, CloseOutlined, ThunderboltOutlined, PlusOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { SalesOrder, SalesOrderItem, OrderChange, MRPResult } from '@/types'
// 移除useSalesStore依赖，使用dataAccessManager统一数据访问
import { dataAccessManager } from '@/services/dataAccess/DataAccessManager'
import { handleApiResponse } from '@/utils/dataAccessErrorHandler'
import dayjs from 'dayjs'


import AddOrderModal from '@/components/sales/AddOrderModal'
// 🔧 新增：导入生产订单相关组件和Hook
import { useMRPProductionOrders } from './hooks/useProductionOrders'
import { HistoricalProductionOrderTable } from './components/ProductionOrderTable'
import { OrderCancellationService } from '@/services/OrderCancellationService'
import { OrderQuantityChangeService } from '@/services/OrderQuantityChangeService'
import { OrderDeliveryDateChangeService } from '@/services/OrderDeliveryDateChangeService'
// ✅ 架构合规：使用DataAccessManager统一监控和合规的Hooks
import { useDebouncedSearch } from '@/hooks/useDebouncedCallback'
import { useDataChangeListener } from '@/hooks/useEventListener'
import { useDataAccessMonitor } from '@/hooks/useDataAccessMonitor'
import { useOrdersData } from '@/hooks/useOrdersData'
// ✅ 架构合规性验证工具
import { performArchitectureComplianceCheck, printComplianceReport } from '@/utils/architectureCompliance'
// 🔧 P4-3数据验证统一：导入统一验证服务
import OrderValidationService from '@/services/validation/OrderValidationService'
// 🔧 P5-1订单详情组件重构：导入通用订单详情组件
import BaseOrderDetailModal from '@/components/common/OrderDetailModal'
import salesOrderConfig from '@/components/common/OrderDetailModal/configs/salesOrderConfig'



const { Option } = Select
const { TextArea } = Input
const { RangePicker } = DatePicker
const { Step } = Steps

const OrderManagement: React.FC = () => {
  const { message, modal } = App.useApp()

  // MRP相关状态保留用于UI显示
  const [mrpExecuting, setMRPExecuting] = useState(false)

  const [loading, setLoading] = useState(false)
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false)
  const [isChangeModalVisible, setIsChangeModalVisible] = useState(false)
  const [isAddOrderModalVisible, setIsAddOrderModalVisible] = useState(false)
  const [selectedOrder, setSelectedOrder] = useState<SalesOrder | null>(null)
  const [selectedChangeType, setSelectedChangeType] = useState<string>('')

  // Form实例 - 用于订单变更
  const [changeForm] = Form.useForm()

  // ✅ 架构合规：使用useOrdersData Hook替代手动数据加载
  const {
    orders: ordersFromHook,
    loading: ordersLoading,
    error: ordersError,
    refreshOrders,
    loadOrders,
    hasOrders,
    isEmpty
  } = useOrdersData({
    autoLoad: true,
    enableCache: true
  })

  // 保持原有的orders状态以兼容现有代码
  const [orders, setOrders] = useState<SalesOrder[]>([])

  // 同步Hook数据到本地状态
  useEffect(() => {
    setOrders(ordersFromHook)
  }, [ordersFromHook])

  // 显示错误信息
  useEffect(() => {
    if (ordersError) {
      message.error(ordersError)
    }
  }, [ordersError, message])

  // ✅ 架构合规性检查功能（仅开发环境）
  const handleArchitectureComplianceCheck = async () => {
    try {
      console.log('🔍 开始执行架构合规性检查...')
      const result = await performArchitectureComplianceCheck()
      printComplianceReport(result)

      if (result.compliant) {
        message.success(`架构合规性检查通过！评分: ${result.score}/100`)
      } else {
        message.warning(`架构合规性检查未通过，评分: ${result.score}/100，请查看控制台详情`)
      }
    } catch (error) {
      console.error('架构合规性检查失败:', error)
      message.error('架构合规性检查失败')
    }
  }

  // 🔧 P4-2架构升级：使用数据变更监听器自动刷新数据
  useDataChangeListener('sales-orders-page', {
    onOrderCreated: () => {
      console.log('📝 检测到销售订单创建，自动刷新数据')
      refreshOrders()
    },
    onOrderUpdated: () => {
      console.log('📝 检测到销售订单更新，自动刷新数据')
      refreshOrders()
    },
    onOrderDeleted: () => {
      console.log('📝 检测到销售订单删除，自动刷新数据')
      refreshOrders()
    }
  })

  // 🔧 P4-2架构升级：优化搜索性能，使用防抖搜索
  const [searchText, setSearchText] = useState('')
  const [searchInput, setSearchInput] = useState('') // 输入框的即时值
  const [filterStatus, setFilterStatus] = useState<string | undefined>(undefined)
  const [filterProductionStatus, setFilterProductionStatus] = useState<string | undefined>(undefined)

  // 🔧 P4-2架构升级：使用防抖搜索优化性能
  const debouncedSearch = useDebouncedSearch(
    (query: string) => {
      setSearchText(query)
    },
    300, // 300ms防抖延迟
    []
  )

  // ✅ 架构合规：使用DataAccessManager统一监控体系
  const {
    metrics,
    cacheStats,
    isMonitoring,
    clearCache,
    getPerformanceAlerts,
    formatMemorySize,
    formatPercentage,
    isHealthy,
    needsOptimization
  } = useDataAccessMonitor({
    interval: 60000, // 1分钟间隔，符合架构文档
    enabled: true,
    showDetails: process.env.NODE_ENV === 'development'
  })

  // 批量操作相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [selectedOrders, setSelectedOrders] = useState<SalesOrder[]>([])
  const [batchLoading, setBatchLoading] = useState(false)

  // MRP相关状态 - 仅保留UI显示需要的
  const [mrpResult, setMrpResult] = useState<MRPResult | null>(null)
  const [showMRPResult, setShowMRPResult] = useState(false)
  const [mrpExecutionStep, setMrpExecutionStep] = useState(0)

  const [mrpExecutionSteps] = useState([
    '启动MRP',
    'MRP分析',
    '生成生产订单',
    '完成'
  ])

  const [productInventory] = useState({})

  // 🔧 新增：获取选中订单的历史生产订单
  const {
    productionOrders: historicalProductionOrders,
    loading: productionOrdersLoading
  } = useMRPProductionOrders(selectedOrder?.orderNumber || '')

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      'pending': { color: 'red', text: '未审核' },
      'confirmed': { color: 'green', text: '已审核' },
      'completed': { color: 'gray', text: '完成' },
      'cancelled': { color: 'orange', text: '已取消' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取生产状态标签（与订单状态联动）
  const getProductionStatusTag = (orderStatus: string, productionStatus: string) => {
    // 如果订单未审核或已取消，不显示生产状态
    if (orderStatus === 'pending' || orderStatus === 'cancelled') {
      return <span style={{ color: '#999' }}>-</span>
    }

    // 如果订单已审核，显示生产状态（默认为未开始）
    const actualStatus = orderStatus === 'confirmed' && !productionStatus ? 'not_started' : productionStatus

    const statusMap = {
      'not_started': { color: 'orange', text: '未开始' },
      'pending': { color: 'blue', text: '待生产' },
      'in_progress': { color: 'green', text: '生产中' },
      'completed': { color: 'cyan', text: '已完成' }
    }
    const config = statusMap[actualStatus as keyof typeof statusMap] || { color: 'orange', text: '未开始' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取MRP状态标签
  const getMRPStatusTag = (mrpStatus: string) => {
    const statusMap = {
      'not_started': { color: 'default', text: '未启动' },
      'in_progress': { color: 'processing', text: '执行中' },
      'completed': { color: 'success', text: '已完成' },
      'failed': { color: 'error', text: '执行失败' }
    }
    const config = statusMap[mrpStatus as keyof typeof statusMap] || { color: 'default', text: '未启动' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取付款状态标签
  const getPaymentStatusTag = (status: string) => {
    const statusMap = {
      'unpaid': { color: 'red', text: '未付款' },
      'partial': { color: 'orange', text: '部分付款' },
      'paid': { color: 'green', text: '已付款' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: '未知' }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 计算智能交期
  const calculateDeliveryDate = (productModelCode: string, quantity: number): string => {
    const inventory = productInventory[productModelCode as keyof typeof productInventory]
    if (!inventory) return '待确认'
    
    const { stock, dailyCapacity } = inventory
    const needProduction = Math.max(0, quantity - stock)
    const productionDays = Math.ceil(needProduction / dailyCapacity)
    const totalDays = productionDays + 3
    
    const deliveryDate = new Date()
    deliveryDate.setDate(deliveryDate.getDate() + totalDays)
    return deliveryDate.toISOString().split('T')[0]
  }

  const convertUnit = (quantity: number, fromUnit: string, toUnit: string, productModelCode: string): number => {
    const defaultWeightPerPiece = 12.0

    if (fromUnit === '个' && toUnit === '吨') {
      return (quantity * defaultWeightPerPiece) / 1000000
    } else if (fromUnit === '吨' && toUnit === '个') {
      return (quantity * 1000000) / defaultWeightPerPiece
    } else if (fromUnit === '个' && toUnit === '克') {
      return quantity * defaultWeightPerPiece
    } else if (fromUnit === '克' && toUnit === '个') {
      return quantity / defaultWeightPerPiece
    }

    return quantity
  }

  // 表格列定义
  const columns: ColumnsType<SalesOrder> = [
    {
      title: '销售订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 140,
      fixed: 'left',
      render: (orderNumber: string, record: SalesOrder) => (
        <Button
          type="link"
          onClick={() => handleViewDetail(record)}
          style={{ padding: 0, height: 'auto', fontWeight: 'bold' }}
        >
          {orderNumber}
        </Button>
      )
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      sorter: (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      defaultSortOrder: 'descend' as const,
      render: (createdAt: string) => {
        if (!createdAt) return '-'
        return dayjs(createdAt).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: '客户名称',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 180,
      ellipsis: true
    },
    {
      title: '订单日期',
      dataIndex: 'orderDate',
      key: 'orderDate',
      width: 120,
      sorter: (a, b) => new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime()
    },
    {
      title: '订单金额',
      dataIndex: 'finalAmount',
      key: 'finalAmount',
      width: 150,
      sorter: (a, b) => a.finalAmount - b.finalAmount,
      render: (finalAmount: number, record: SalesOrder) => (
        <div>
          <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
            ¥{(finalAmount || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </div>
          {(record.discountAmount || 0) > 0 && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              折扣: ¥{(record.discountAmount || 0).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </div>
          )}
        </div>
      )
    },
    {
      title: '生产状态',
      dataIndex: 'productionStatus',
      key: 'productionStatus',
      width: 120,
      render: (productionStatus: string, record: SalesOrder) =>
        getProductionStatusTag(record.status, productionStatus)
    },
    {
      title: '付款状态',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      width: 100,
      render: (status: string) => getPaymentStatusTag(status)
    },
    {
      title: '变更次数',
      key: 'changeCount',
      width: 100,
      render: (_, record) => (
        <div>
          {record.changes.length > 0 ? (
            <Tooltip title="点击查看变更历史">
              <Badge count={record.changes.length} size="small">
                <Button type="link" size="small">
                  {record.changes.length}次
                </Button>
              </Badge>
            </Tooltip>
          ) : (
            <span style={{ color: '#666' }}>无</span>
          )}
        </div>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">

          <Button
            type="link"
            icon={<SwapOutlined />}
            onClick={() => handleOrderChange(record)}
          >
            变更
          </Button>
          <Popconfirm
            title="确定要删除这个订单吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 过滤后的订单数据
  const filteredOrders = orders.filter(order => {
    const matchesSearch = !searchText ||
      order.orderNumber.toLowerCase().includes(searchText.toLowerCase()) ||
      order.customerName.toLowerCase().includes(searchText.toLowerCase()) ||
      order.customerContact.toLowerCase().includes(searchText.toLowerCase())

    const matchesStatus = !filterStatus || order.status === filterStatus
    const matchesProductionStatus = !filterProductionStatus || order.productionStatus === filterProductionStatus

    return matchesSearch && matchesStatus && matchesProductionStatus
  })

  // 统计数据
  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    confirmed: orders.filter(o => o.status === 'confirmed').length,
    completed: orders.filter(o => o.status === 'completed').length,
    cancelled: orders.filter(o => o.status === 'cancelled').length,
    totalAmount: orders.reduce((sum, o) => sum + o.finalAmount, 0),
    delayedOrders: orders.filter(o => new Date(o.deliveryDate) < new Date() && o.status !== 'completed' && o.status !== 'cancelled').length
  }

  const router = useRouter()





  const handleViewDetail = (order: SalesOrder) => {
    setSelectedOrder(order)
    setIsDetailModalVisible(true)
  }

  const handleOrderChange = (order: SalesOrder) => {
    setSelectedOrder(order)
    setSelectedChangeType('') // 重置变更类型
    setIsChangeModalVisible(true)
    changeForm.resetFields()
  }

  // 🔧 新增：根据变更类型自动填充原始值
  const handleChangeTypeSelect = (changeType: string) => {
    if (!selectedOrder) return

    setSelectedChangeType(changeType) // 更新选择的变更类型
    let originalValue = ''

    switch (changeType) {
      case 'quantity':
        // 获取订单总数量
        const totalQuantity = selectedOrder.items?.reduce((sum, item) => sum + item.quantity, 0) || 0
        originalValue = totalQuantity.toString()
        break
      case 'delivery_date':
        // 获取交期，格式化为YYYY-MM-DD
        try {
          const date = new Date(selectedOrder.deliveryDate)
          originalValue = date.toISOString().split('T')[0] // 格式化为YYYY-MM-DD
        } catch (error) {
          originalValue = selectedOrder.deliveryDate.split('T')[0] // 备用方案
        }
        break

      case 'cancel':
        // 订单取消时，原始值为当前状态
        const statusMap: Record<string, string> = {
          'pending': '未审核',
          'confirmed': '已审核',
          'completed': '完成',
          'cancelled': '已取消'
        }
        originalValue = statusMap[selectedOrder.status] || selectedOrder.status
        break
      default:
        originalValue = ''
    }

    // 自动填充原始值，并清空新值
    changeForm.setFieldsValue({
      originalValue: originalValue,
      newValue: '' // 清空新值，让用户重新输入
    })
  }

  // 🔧 新增：根据变更类型获取输入提示
  const getPlaceholderByChangeType = (changeType: string, field: 'originalValue' | 'newValue') => {
    if (!changeType) return field === 'originalValue' ? '请输入原始值' : '请输入新值'

    const placeholders = {
      quantity: {
        originalValue: '当前订单总数量',
        newValue: '请输入新的数量'
      },
      delivery_date: {
        originalValue: '当前交期日期',
        newValue: '请选择新的交期日期'
      },
      cancel: {
        originalValue: '当前订单状态',
        newValue: '取消原因'
      }
    }

    return placeholders[changeType as keyof typeof placeholders]?.[field] ||
           (field === 'originalValue' ? '请输入原始值' : '请输入新值')
  }

  // 🔧 新增：根据变更类型渲染不同的输入组件
  const renderInputByChangeType = (changeType: string, field: 'originalValue' | 'newValue') => {
    const placeholder = getPlaceholderByChangeType(changeType, field)
    const isReadOnly = field === 'originalValue' && !!selectedChangeType

    switch (changeType) {
      case 'quantity':
        return (
          <InputNumber
            placeholder={placeholder}
            min={0}
            style={{ width: '100%' }}
            readOnly={isReadOnly}
          />
        )
      case 'delivery_date':
        if (field === 'originalValue') {
          return <Input placeholder={placeholder} readOnly />
        } else {
          return <DatePicker placeholder={placeholder} style={{ width: '100%' }} />
        }

      default:
        return (
          <Input
            placeholder={placeholder}
            readOnly={isReadOnly}
          />
        )
    }
  }

  const handleDelete = async (id: string) => {
    const result = await handleApiResponse(
      () => dataAccessManager.orders.delete(id),
      '删除订单'
    )

    if (result !== null) {
      await refreshOrders()
      message.success('订单删除成功')
    } else {
      message.error('删除订单失败')
    }
  }

  // 新增订单处理函数
  const handleAddOrder = () => {
    setIsAddOrderModalVisible(true)
  }

  const handleAddOrderSuccess = async (newOrder: SalesOrder) => {
    // 刷新订单列表以获取最新数据
    await refreshOrders()
    message.success('订单创建成功')
    setIsAddOrderModalVisible(false)
  }

  const handleAddOrderCancel = () => {
    setIsAddOrderModalVisible(false)
  }

  const handleStartMRP = async (order: SalesOrder) => {
    try {
      // 验证前置条件
      if (order.status !== 'confirmed') {
        message.error('只有已审核的订单才能启动MRP')
        return
      }

      if (order.mrpStatus === 'completed') {
        message.warning('该订单的MRP已经执行完成')
        return
      }

      if (order.mrpStatus === 'in_progress') {
        message.warning('该订单的MRP正在执行中，请等待完成')
        return
      }

      // 开始MRP执行
      setMRPExecuting(true)
      setMrpExecutionStep(1)
      setShowMRPResult(false)
      setMrpResult(null)

      // 更新订单MRP状态为执行中
      try {
        await dataAccessManager.orders.update(order.id, {
          mrpStatus: 'in_progress' as const,
          updatedAt: new Date().toISOString()
        })

        // 🔧 修复：立即更新selectedOrder状态，确保按钮立即禁用
        if (selectedOrder && selectedOrder.id === order.id) {
          setSelectedOrder({
            ...selectedOrder,
            mrpStatus: 'in_progress' as const,
            updatedAt: new Date().toISOString()
          })
        }
        // 立即刷新本地状态
        await refreshOrders()
      } catch (error) {
        console.error('更新订单MRP状态失败:', error)
      }

      // 动态导入MRP服务
      const { mrpService } = await import('@/services/mrpService')

      // 步骤1: 启动MRP
      message.info('正在启动MRP...')
      await new Promise(resolve => setTimeout(resolve, 1000))
      setMrpExecutionStep(2)

      // 步骤2: MRP分析
      message.info('正在进行MRP分析...')
      await new Promise(resolve => setTimeout(resolve, 1500))
      setMrpExecutionStep(3)

      // 步骤3: 生成生产订单
      message.info('正在生成生产订单...')

      // 执行MRP
      const mrpResult = await mrpService.executeMRP({
        salesOrder: order,
        executedBy: '当前用户', // 实际应该从用户状态获取
        executionDate: new Date().toISOString()
      })

      setMrpExecutionStep(4)
      await new Promise(resolve => setTimeout(resolve, 500))

      // 步骤4: 完成
      setMRPExecuting(false)
      setMrpResult(mrpResult)
      setShowMRPResult(true)

      // 更新订单MRP状态为已完成
      try {
        await dataAccessManager.orders.update(order.id, {
          mrpStatus: 'completed' as const,
          mrpExecutedAt: new Date().toISOString(),
          mrpExecutedBy: '当前用户',
          mrpResultId: mrpResult.id,
          updatedAt: new Date().toISOString()
        })

        // 🔧 修复：立即更新selectedOrder状态，确保按钮状态正确
        if (selectedOrder && selectedOrder.id === order.id) {
          setSelectedOrder({
            ...selectedOrder,
            mrpStatus: 'completed' as const,
            mrpExecutedAt: new Date().toISOString(),
            mrpExecutedBy: '当前用户',
            mrpResultId: mrpResult.id,
            updatedAt: new Date().toISOString()
          })
        }
      } catch (error) {
        console.error('更新订单MRP完成状态失败:', error)
      }

      // 步骤5: MRP执行完成，刷新订单数据
      await refreshOrders() // 刷新订单列表以获取最新状态

      if (mrpResult.generatedProductionOrders && mrpResult.generatedProductionOrders.length > 0) {
        message.success(`MRP执行完成！生成了 ${mrpResult.totalProductionOrders} 个生产订单，请前往生产管理模块查看`)
      } else {
        message.success(`MRP执行完成！未生成新的生产订单（可能库存充足）`)
      }

    } catch (error) {
      setMRPExecuting(false)

      // 更新订单MRP状态为失败
      const updateResult = await handleApiResponse(
        () => dataAccessManager.orders.update(order.id, {
          mrpStatus: 'failed' as const,
          updatedAt: new Date().toISOString()
        }),
        '更新订单MRP状态'
      )

      if (updateResult) {
        // 🔧 修复：立即更新selectedOrder状态
        if (selectedOrder && selectedOrder.id === order.id) {
          setSelectedOrder({
            ...selectedOrder,
            mrpStatus: 'failed' as const,
            updatedAt: new Date().toISOString()
          })
        }

        await refreshOrders()
      }

      message.error(`MRP执行失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }



  const handleChangeModalOk = () => {
    changeForm.validateFields().then(async (values) => {
      if (!selectedOrder) return

      // 🔧 P4-3数据验证统一：使用OrderValidationService验证订单变更
      const changeValidation = OrderValidationService.validateOrderChange(
        selectedOrder,
        values.changeType,
        values.originalValue,
        values.newValue
      )

      if (!changeValidation.isValid) {
        message.error(`变更验证失败：${changeValidation.errors[0]}`)
        return
      }

      // 显示警告信息（如果有）
      if (changeValidation.warnings && changeValidation.warnings.length > 0) {
        changeValidation.warnings.forEach(warning => {
          message.warning(warning)
        })
      }

      const now = new Date().toISOString()
      const newChange: OrderChange = {
        id: Date.now().toString(),
        orderNumber: selectedOrder.orderNumber,
        ...values,
        changeStatus: 'pending',
        applicant: '当前用户',
        customerConfirmed: false,
        productionStatus: selectedOrder.productionStatus,
        createdAt: now
      }

      // 更新订单变更记录
      const currentOrder = orders.find(o => o.id === selectedOrder.id)
      if (currentOrder) {
        const updatedChanges = [...(currentOrder.changes || []), newChange]
        const result = await handleApiResponse(
          () => dataAccessManager.orders.update(selectedOrder.id, {
            changes: updatedChanges,
            updatedAt: now
          }),
          '提交变更申请'
        )

        if (result) {
          await refreshOrders()
          setIsChangeModalVisible(false)
          changeForm.resetFields()
          message.success('订单变更申请提交成功')
        } else {
          message.error('提交变更申请失败')
        }
      }
    })
  }

  const handleChangeModalCancel = () => {
    setIsChangeModalVisible(false)
    setSelectedChangeType('') // 重置变更类型
    changeForm.resetFields()
  }

  // 处理变更审批
  const handleChangeApproval = async (changeId: string, action: 'approve' | 'reject', reason?: string) => {
    if (!selectedOrder) return

    try {
      const now = new Date().toISOString()
      const currentOrder = orders.find(o => o.id === selectedOrder.id)
      if (!currentOrder) return

      // 更新变更记录状态
      const updatedChanges = currentOrder.changes?.map(change => {
        if (change.id === changeId) {
          return {
            ...change,
            changeStatus: action === 'approve' ? 'approved' as const : 'rejected' as const,
            approver: '当前用户',
            approvedAt: now,
            ...(reason && { rejectionReason: reason })
          }
        }
        return change
      }) || []

      // 如果变更被批准，需要执行相应的变更逻辑
      const approvedChange = updatedChanges.find(c => c.id === changeId)
      if (action === 'approve' && approvedChange) {
        switch (approvedChange.changeType) {
          case 'cancel':
            // 验证订单是否可以取消
            const cancelValidation = await OrderCancellationService.validateCancellation(selectedOrder)

            if (!cancelValidation.canCancel) {
              message.error(`无法取消订单: ${cancelValidation.reason}`)
              return
            }

            if (cancelValidation.warnings.length > 0) {
              // 显示警告信息，让用户确认
              await new Promise<void>((resolve, reject) => {
                modal.confirm({
                  title: '订单取消确认',
                  content: (
                    <div>
                      <p>确定要取消此订单吗？</p>
                      <ul style={{ marginTop: 8, paddingLeft: 20 }}>
                        {cancelValidation.warnings.map((warning, index) => (
                          <li key={index} style={{ color: '#fa8c16' }}>{warning}</li>
                        ))}
                      </ul>
                    </div>
                  ),
                  onOk: () => resolve(),
                  onCancel: () => reject(new Error('用户取消操作'))
                })
              })
            }

            await handleOrderCancellation(selectedOrder, approvedChange)
            break;

          case 'quantity':
            // 执行数量变更
            await handleQuantityChange(selectedOrder, approvedChange)
            break;

          case 'delivery_date':
            // 执行交期变更
            await handleDeliveryDateChange(selectedOrder, approvedChange)
            break;
        }

        // 更新变更记录状态为已执行
        const finalChanges = updatedChanges.map(change => {
          if (change.id === changeId) {
            return {
              ...change,
              changeStatus: 'executed' as const,
              executedAt: new Date().toISOString()
            }
          }
          return change
        })

        // 更新订单变更记录
        const updateResult = await handleApiResponse(
          () => dataAccessManager.orders.update(selectedOrder.id, {
            changes: finalChanges,
            updatedAt: now
          }),
          '更新订单变更记录'
        )

        if (!updateResult) {
          message.error(`变更${action === 'approve' ? '批准' : '拒绝'}失败`)
          return
        }
      } else {
        // 如果是拒绝变更，只更新变更记录
        const updateResult = await handleApiResponse(
          () => dataAccessManager.orders.update(selectedOrder.id, {
            changes: updatedChanges,
            updatedAt: now
          }),
          '更新变更记录'
        )

        if (!updateResult) {
          message.error(`变更${action === 'approve' ? '批准' : '拒绝'}失败`)
          return
        }
      }

      await refreshOrders()
      message.success(`变更${action === 'approve' ? '批准' : '拒绝'}成功`)
    } catch (error) {
      message.error(`变更${action === 'approve' ? '批准' : '拒绝'}失败: ${error instanceof Error ? error.message : '未知错误'}`)
      console.error('变更审批失败:', error)
    }
  }

  // 处理订单取消的核心逻辑
  const handleOrderCancellation = async (order: SalesOrder, change: OrderChange) => {
    try {
      // 使用专门的订单取消服务
      const result = await OrderCancellationService.executeOrderCancellation(order, change)

      if (result.success) {
        message.success(
          `订单 ${order.orderNumber} 已成功取消，` +
          `同时取消了 ${result.productionOrdersCancelled} 个生产订单和 ${result.workOrdersCancelled} 个工单`
        )
      } else {
        throw new Error(result.errors.join('; '))
      }
    } catch (error) {
      console.error('订单取消处理失败:', error)
      throw error
    }
  }

  // 处理数量变更的核心逻辑
  const handleQuantityChange = async (order: SalesOrder, change: OrderChange) => {
    try {
      // 使用专门的数量变更服务
      const result = await OrderQuantityChangeService.executeQuantityChange(order, change)

      if (result.success) {
        message.success(
          `订单 ${order.orderNumber} 数量变更成功，` +
          `从 ${change.originalValue} 变更为 ${change.newValue}，` +
          `影响了 ${result.productionOrdersAffected} 个生产订单和 ${result.workOrdersAffected} 个工单`
        )
      } else {
        throw new Error(result.errors.join('; '))
      }
    } catch (error) {
      console.error('数量变更执行失败:', error)
      throw error
    }
  }

  // 处理交期变更的核心逻辑
  const handleDeliveryDateChange = async (order: SalesOrder, change: OrderChange) => {
    try {
      // 使用专门的交期变更服务
      const result = await OrderDeliveryDateChangeService.executeDeliveryDateChange(order, change)

      if (result.success) {
        const successMessage = `订单 ${order.orderNumber} 交期变更成功，` +
          `从 ${change.originalValue} 变更为 ${change.newValue}，` +
          `影响了 ${result.productionOrdersAffected} 个生产订单和 ${result.workOrdersAffected} 个工单`

        if (result.scheduleAdjustmentsRequired) {
          message.warning(
            successMessage + `\n注意：有 ${result.scheduleImpact.conflictingOrders.length} 个订单的排程需要调整`
          )
        } else {
          message.success(successMessage)
        }
      } else {
        throw new Error(result.errors.join('; '))
      }
    } catch (error) {
      console.error('交期变更执行失败:', error)
      throw error
    }
  }

  // 批量操作函数
  const handleBatchApprove = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要审核的订单')
      return
    }

    const pendingOrders = selectedOrders.filter(order => order.status === 'pending')
    if (pendingOrders.length === 0) {
      message.warning('所选订单中没有未审核的订单')
      return
    }

    modal.confirm({
      title: '批量审核确认',
      content: `确定要审核 ${pendingOrders.length} 个订单吗？`,
      onOk: async () => {
        setBatchLoading(true)
        try {
          // 批量更新订单状态
          const updatePromises = pendingOrders.map(order =>
            handleApiResponse(
              () => dataAccessManager.orders.update(order.id, {
                status: 'confirmed' as const,
                productionStatus: 'not_started' as const,
                updatedAt: new Date().toISOString()
              }),
              `审核订单 ${order.orderNumber}`
            )
          )

          const results = await Promise.all(updatePromises)
          const successCount = results.filter(result => result !== null).length

          if (successCount > 0) {
            await refreshOrders()
            setSelectedRowKeys([])
            setSelectedOrders([])
            message.success(`成功审核 ${successCount} 个订单`)
          } else {
            message.error('批量审核失败，没有订单被成功审核')
          }
        } catch (error) {
          message.error(`批量审核失败: ${error instanceof Error ? error.message : '未知错误'}`)
          console.error('批量审核异常:', error)
        } finally {
          setBatchLoading(false)
        }
      }
    })
  }

  const handleBatchUnapprove = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要反审核的订单')
      return
    }

    const confirmedOrders = selectedOrders.filter(order => order.status === 'confirmed')
    if (confirmedOrders.length === 0) {
      message.warning('所选订单中没有已审核的订单')
      return
    }

    modal.confirm({
      title: '批量反审核确认',
      content: `确定要反审核 ${confirmedOrders.length} 个订单吗？`,
      onOk: async () => {
        setBatchLoading(true)
        try {
          // 批量更新订单状态
          const updatePromises = confirmedOrders.map(order =>
            handleApiResponse(
              () => dataAccessManager.orders.update(order.id, {
                status: 'pending' as const,
                productionStatus: 'not_started' as const,
                updatedAt: new Date().toISOString()
              }),
              `反审核订单 ${order.orderNumber}`
            )
          )

          const results = await Promise.all(updatePromises)
          const successCount = results.filter(result => result !== null).length

          if (successCount > 0) {
            await refreshOrders()
            setSelectedRowKeys([])
            setSelectedOrders([])
            message.success(`成功反审核 ${successCount} 个订单`)
          } else {
            message.error('批量反审核失败，没有订单被成功反审核')
          }
        } catch (error) {
          message.error(`批量反审核失败: ${error instanceof Error ? error.message : '未知错误'}`)
          console.error('批量反审核异常:', error)
        } finally {
          setBatchLoading(false)
        }
      }
    })
  }



  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="订单总数"
              value={stats.total}
              suffix="个"
              prefix={<InboxOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="订单总额"
              value={stats.totalAmount}
              precision={0}
              prefix="¥"
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="生产中订单"
              value={orders.filter(o => o.productionStatus === 'in_progress').length}
              suffix="个"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已取消订单"
              value={stats.cancelled}
              suffix="个"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* ✅ 架构合规：使用DataAccessManager统一监控替代违规MemoryManager */}
      {metrics && (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Card size="small">
              <Statistic
                title="平均响应时间"
                value={`${metrics.averageResponseTime}ms`}
                valueStyle={{
                  color: metrics.averageResponseTime > 1000 ? '#ff4d4f' :
                         metrics.averageResponseTime > 500 ? '#fa8c16' : '#3f8600'
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card size="small">
              <Statistic
                title="缓存命中率"
                value={formatPercentage(cacheStats?.hitRate || 0)}
                valueStyle={{
                  color: (cacheStats?.hitRate || 0) < 0.3 ? '#ff4d4f' :
                         (cacheStats?.hitRate || 0) < 0.6 ? '#fa8c16' : '#3f8600'
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card size="small">
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <div>
                  <div style={{ fontSize: '14px', color: '#6b7280' }}>监控状态</div>
                  <div style={{ fontSize: '18px', fontWeight: '500' }}>
                    <Badge
                      status={isMonitoring ? 'processing' : 'default'}
                      text={isMonitoring ? '运行中' : '已停止'}
                    />
                  </div>
                </div>
                {!isHealthy && (
                  <Tooltip title="系统性能需要优化">
                    <Button
                      size="small"
                      type="text"
                      danger
                      onClick={() => clearCache()}
                    >
                      ⚠️ 优化
                    </Button>
                  </Tooltip>
                )}
              </div>
            </Card>
          </Col>
        </Row>
      )}

      <Card size="small">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <Input
              placeholder="搜索订单号、客户名称或联系人"
              prefix={<SearchOutlined />}
              value={searchInput}
              onChange={(e) => {
                const value = e.target.value
                setSearchInput(value)
                debouncedSearch(value)
              }}
              className="w-full sm:w-64"
            />
            <Select
              placeholder="订单状态"
              value={filterStatus}
              onChange={setFilterStatus}
              className="w-full sm:w-32"
              allowClear
            >
              <Option value="pending">未审核</Option>
              <Option value="confirmed">已审核</Option>
              <Option value="completed">完成</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
            <Select
              placeholder="生产状态"
              value={filterProductionStatus}
              onChange={setFilterProductionStatus}
              className="w-full sm:w-32"
              allowClear
            >
              <Option value="not_started">未开始</Option>
              <Option value="pending">待生产</Option>
              <Option value="in_progress">生产中</Option>
              <Option value="completed">已完成</Option>
            </Select>
          </div>
          <div className="flex space-x-2">
            {/* ✅ 架构合规性检查按钮（仅开发环境） */}
            {process.env.NODE_ENV === 'development' && (
              <Button
                icon={<CheckOutlined />}
                onClick={handleArchitectureComplianceCheck}
                title="执行DataAccessManager架构合规性检查"
              >
                架构检查
              </Button>
            )}
            <Button icon={<ExportOutlined />}>导出</Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddOrder}
            >
              新增订单
            </Button>
          </div>
        </div>
      </Card>

      <Card title="订单列表" size="small">
        <div className="mb-3 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0">
          <Space wrap>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleBatchApprove}
              disabled={selectedRowKeys.length === 0 || !selectedOrders.some(order => order.status === 'pending')}
              loading={batchLoading}
            >
              审核 ({selectedOrders.filter(order => order.status === 'pending').length})
            </Button>
            <Button
              icon={<CloseOutlined />}
              onClick={handleBatchUnapprove}
              disabled={selectedRowKeys.length === 0 || !selectedOrders.some(order => order.status === 'confirmed')}
              loading={batchLoading}
            >
              反审核 ({selectedOrders.filter(order => order.status === 'confirmed').length})
            </Button>
          </Space>

          <div className="text-sm text-gray-500">
            {selectedRowKeys.length > 0 ? `已选择 ${selectedRowKeys.length} 个订单` : `共 ${filteredOrders.length} 个订单`}
          </div>
        </div>

        <Table
          columns={columns}
          dataSource={filteredOrders}
          rowKey="id"
          loading={ordersLoading || batchLoading}
          rowSelection={{
            selectedRowKeys,
            onChange: (newSelectedRowKeys: React.Key[], newSelectedRows: SalesOrder[]) => {
              setSelectedRowKeys(newSelectedRowKeys)
              setSelectedOrders(newSelectedRows)
            },
            getCheckboxProps: (record: SalesOrder) => ({
              disabled: false,
            }),
          }}
          pagination={{
            total: filteredOrders.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
            defaultPageSize: 10,
          }}
          scroll={{ x: 1600 }}
        />
      </Card>




      <Modal
        title="订单变更申请"
        open={isChangeModalVisible}
        onOk={handleChangeModalOk}
        onCancel={handleChangeModalCancel}
        width={600}
        okText="提交申请"
        cancelText="取消"
        destroyOnHidden={true}
      >
        <Alert
          message="变更控制流程"
          description="系统将根据生产状态自动判断变更可行性"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={changeForm}
          layout="vertical"
        >
          <Form.Item
            name="changeType"
            label="变更类型"
            rules={[{ required: true, message: '请选择变更类型' }]}
          >
            <Select
              placeholder="请选择变更类型"
              onChange={handleChangeTypeSelect}
            >
              <Option value="quantity">数量变更</Option>
              <Option value="delivery_date">交期变更</Option>
              <Option value="cancel">订单取消</Option>
            </Select>
          </Form.Item>

          {selectedChangeType && (
            <Alert
              message={`${selectedChangeType === 'quantity' ? '数量变更' :
                        selectedChangeType === 'delivery_date' ? '交期变更' : '订单取消'}提示`}
              description={
                selectedChangeType === 'quantity' ? '系统已自动填入当前订单总数量，请在新值中输入变更后的数量。' :
                selectedChangeType === 'delivery_date' ? '系统已自动填入当前交期，请选择新的交期日期。' :
                '系统已自动填入当前订单状态，请在新值中说明取消原因。'
              }
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="originalValue"
                label="原始值"
                rules={[{ required: true, message: '请输入原始值' }]}
              >
                {renderInputByChangeType(selectedChangeType, 'originalValue')}
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="newValue"
                label="新值"
                rules={[{ required: true, message: '请输入新值' }]}
              >
                {renderInputByChangeType(selectedChangeType, 'newValue')}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="changeReason"
            label="变更原因"
            rules={[{ required: true, message: '请输入变更原因' }]}
          >
            <TextArea rows={3} placeholder="请详细说明变更原因" />
          </Form.Item>



          {selectedOrder && (
            <div style={{ marginTop: 16 }}>
              <h4>当前订单状态</h4>
              <Descriptions size="small" column={2}>
                <Descriptions.Item label="销售订单号">{selectedOrder.orderNumber}</Descriptions.Item>
                <Descriptions.Item label="生产状态">
                  {getProductionStatusTag(selectedOrder.status, selectedOrder.productionStatus)}
                </Descriptions.Item>
                <Descriptions.Item label="订单状态">
                  {getStatusTag(selectedOrder.status)}
                </Descriptions.Item>
                <Descriptions.Item label="变更次数">
                  {selectedOrder.changes.length}次
                </Descriptions.Item>
              </Descriptions>
            </div>
          )}
        </Form>
      </Modal>

      {/* 🔧 P5-1订单详情组件重构：使用通用的BaseOrderDetailModal */}
      <BaseOrderDetailModal
        open={isDetailModalVisible}
        order={selectedOrder}
        onClose={() => {
          setIsDetailModalVisible(false)
          setShowMRPResult(false)
          setMrpResult(null)
          // 🔧 修复：关闭Modal时重置MRP执行状态，防止状态残留
          setMRPExecuting(false)
          setMrpExecutionStep(0)
        }}
        config={{
          ...salesOrderConfig,
          actions: [
            {
              key: 'mrp',
              text: (order: SalesOrder) => {
                if (mrpExecuting) return '执行中...'
                if (order.mrpStatus === 'completed') return 'MRP已完成'
                if (order.mrpStatus === 'in_progress') return 'MRP执行中'
                if (order.mrpStatus === 'failed') return '重新启动MRP'
                if (showMRPResult) return 'MRP已完成'
                return '启动MRP'
              },
              type: 'primary',
              icon: <ThunderboltOutlined />,
              onClick: (order: SalesOrder) => handleStartMRP(order),
              loading: mrpExecuting,
              hidden: (order: SalesOrder) => order.status !== 'confirmed',
              disabled: (order: SalesOrder) =>
                mrpExecuting ||
                order.mrpStatus === 'completed' ||
                order.mrpStatus === 'in_progress' ||
                showMRPResult,
              style: { marginRight: 8 }
            }
          ]
        }}
      />





      <AddOrderModal
        open={isAddOrderModalVisible}
        onCancel={handleAddOrderCancel}
        onSuccess={handleAddOrderSuccess}
      />


    </div>
  )
}


export default function OrderManagementPage() {
  return (
    <App>
      <OrderManagement />
    </App>
  )
}
