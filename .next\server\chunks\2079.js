"use strict";exports.id=2079,exports.ids=[2079],exports.modules={27976:(e,t,r)=>{r.d(t,{Z:()=>n});let n=r(73371).Z},34953:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(3729).createContext)({})},73371:(e,t,r)=>{r.d(t,{Z:()=>u});var n=r(3729),l=r(34132),a=r.n(l),o=r(84893),s=r(34953),i=r(19249),c=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)0>t.indexOf(n[l])&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(r[n[l]]=e[n[l]]);return r};function f(e){return"number"==typeof e?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}let p=["xs","sm","md","lg","xl","xxl"],u=n.forwardRef((e,t)=>{let{getPrefixCls:r,direction:l}=n.useContext(o.E_),{gutter:u,wrap:m}=n.useContext(s.Z),{prefixCls:d,span:g,order:y,offset:$,push:b,pull:O,className:v,children:x,flex:j,style:h}=e,E=c(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),w=r("col",d),[S,N,Z]=(0,i.cG)(w),C={},P={};p.forEach(t=>{let r={},n=e[t];"number"==typeof n?r.span=n:"object"==typeof n&&(r=n||{}),delete E[t],P=Object.assign(Object.assign({},P),{[`${w}-${t}-${r.span}`]:void 0!==r.span,[`${w}-${t}-order-${r.order}`]:r.order||0===r.order,[`${w}-${t}-offset-${r.offset}`]:r.offset||0===r.offset,[`${w}-${t}-push-${r.push}`]:r.push||0===r.push,[`${w}-${t}-pull-${r.pull}`]:r.pull||0===r.pull,[`${w}-rtl`]:"rtl"===l}),r.flex&&(P[`${w}-${t}-flex`]=!0,C[`--${w}-${t}-flex`]=f(r.flex))});let M=a()(w,{[`${w}-${g}`]:void 0!==g,[`${w}-order-${y}`]:y,[`${w}-offset-${$}`]:$,[`${w}-push-${b}`]:b,[`${w}-pull-${O}`]:O},v,P,N,Z),I={};if(u&&u[0]>0){let e=u[0]/2;I.paddingLeft=e,I.paddingRight=e}return j&&(I.flex=f(j),!1!==m||I.minWidth||(I.minWidth=0)),S(n.createElement("div",Object.assign({},E,{style:Object.assign(Object.assign(Object.assign({},I),h),C),className:M,ref:t}),x))})},66074:(e,t,r)=>{r.d(t,{Z:()=>m});var n=r(3729),l=r(34132),a=r.n(l),o=r(91782),s=r(84893),i=r(91735),c=r(34953),f=r(19249),p=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)0>t.indexOf(n[l])&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(r[n[l]]=e[n[l]]);return r};function u(e,t){let[r,l]=n.useState("string"==typeof e?e:""),a=()=>{if("string"==typeof e&&l(e),"object"==typeof e)for(let r=0;r<o.c4.length;r++){let n=o.c4[r];if(!t||!t[n])continue;let a=e[n];if(void 0!==a){l(a);return}}};return n.useEffect(()=>{a()},[JSON.stringify(e),t]),r}let m=n.forwardRef((e,t)=>{let{prefixCls:r,justify:l,align:m,className:d,style:g,children:y,gutter:$=0,wrap:b}=e,O=p(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:v,direction:x}=n.useContext(s.E_),j=(0,i.Z)(!0,null),h=u(m,j),E=u(l,j),w=v("row",r),[S,N,Z]=(0,f.VM)(w),C=function(e,t){let r=[void 0,void 0],n=Array.isArray(e)?e:[e,void 0],l=t||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return n.forEach((e,t)=>{if("object"==typeof e&&null!==e)for(let n=0;n<o.c4.length;n++){let a=o.c4[n];if(l[a]&&void 0!==e[a]){r[t]=e[a];break}}else r[t]=e}),r}($,j),P=a()(w,{[`${w}-no-wrap`]:!1===b,[`${w}-${E}`]:E,[`${w}-${h}`]:h,[`${w}-rtl`]:"rtl"===x},d,N,Z),M={},I=null!=C[0]&&C[0]>0?-(C[0]/2):void 0;I&&(M.marginLeft=I,M.marginRight=I);let[R,k]=C;M.rowGap=k;let z=n.useMemo(()=>({gutter:[R,k],wrap:b}),[R,k,b]);return S(n.createElement(c.Z.Provider,{value:z},n.createElement("div",Object.assign({},O,{className:P,style:Object.assign(Object.assign({},M),g),ref:t}),y)))})},63724:(e,t,r)=>{r.d(t,{Z:()=>n});let n=r(66074).Z},43896:(e,t,r)=>{r.d(t,{Z:()=>E});var n=r(3729),l=r(71782),a=r(42534),o=r(29545),s=r(34132),i=r.n(s),c=r(7305),f=r(84893),p=r(56989);let u=e=>{let t;let{value:r,formatter:l,precision:a,decimalSeparator:o,groupSeparator:s="",prefixCls:i}=e;if("function"==typeof l)t=l(r);else{let e=String(r),l=e.match(/^(-?)(\d*)(\.(\d+))?$/);if(l&&"-"!==e){let e=l[1],r=l[2]||"0",c=l[4]||"";r=r.replace(/\B(?=(\d{3})+(?!\d))/g,s),"number"==typeof a&&(c=c.padEnd(a,"0").slice(0,a>0?a:0)),c&&(c=`${o}${c}`),t=[n.createElement("span",{key:"int",className:`${i}-content-value-int`},e,r),c&&n.createElement("span",{key:"decimal",className:`${i}-content-value-decimal`},c)]}else t=e}return n.createElement("span",{className:`${i}-content-value`},t)};var m=r(22989),d=r(13165),g=r(96373);let y=e=>{let{componentCls:t,marginXXS:r,padding:n,colorTextDescription:l,titleFontSize:a,colorTextHeading:o,contentFontSize:s,fontFamily:i}=e;return{[t]:Object.assign(Object.assign({},(0,m.Wf)(e)),{[`${t}-title`]:{marginBottom:r,color:l,fontSize:a},[`${t}-skeleton`]:{paddingTop:n},[`${t}-content`]:{color:o,fontSize:s,fontFamily:i,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:r},[`${t}-content-suffix`]:{marginInlineStart:r}}})}},$=(0,d.I$)("Statistic",e=>[y((0,g.IX)(e,{}))],e=>{let{fontSizeHeading3:t,fontSize:r}=e;return{titleFontSize:r,contentFontSize:t}});var b=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)0>t.indexOf(n[l])&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(r[n[l]]=e[n[l]]);return r};let O=n.forwardRef((e,t)=>{let{prefixCls:r,className:l,rootClassName:a,style:o,valueStyle:s,value:m=0,title:d,valueRender:g,prefix:y,suffix:O,loading:v=!1,formatter:x,precision:j,decimalSeparator:h=".",groupSeparator:E=",",onMouseEnter:w,onMouseLeave:S}=e,N=b(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:Z,direction:C,className:P,style:M}=(0,f.dj)("statistic"),I=Z("statistic",r),[R,k,z]=$(I),D=n.createElement(u,{decimalSeparator:h,groupSeparator:E,prefixCls:I,formatter:x,precision:j,value:m}),T=i()(I,{[`${I}-rtl`]:"rtl"===C},P,l,a,k,z),H=n.useRef(null);n.useImperativeHandle(t,()=>({nativeElement:H.current}));let L=(0,c.Z)(N,{aria:!0,data:!0});return R(n.createElement("div",Object.assign({},L,{ref:H,className:T,style:Object.assign(Object.assign({},M),o),onMouseEnter:w,onMouseLeave:S}),d&&n.createElement("div",{className:`${I}-title`},d),n.createElement(p.Z,{paragraph:!1,loading:v,className:`${I}-skeleton`},n.createElement("div",{style:s,className:`${I}-content`},y&&n.createElement("span",{className:`${I}-content-prefix`},y),g?g(D):D,O&&n.createElement("span",{className:`${I}-content-suffix`},O)))))}),v=[["Y",31536e6],["M",2592e6],["D",864e5],["H",36e5],["m",6e4],["s",1e3],["S",1]];var x=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var l=0,n=Object.getOwnPropertySymbols(e);l<n.length;l++)0>t.indexOf(n[l])&&Object.prototype.propertyIsEnumerable.call(e,n[l])&&(r[n[l]]=e[n[l]]);return r};let j=e=>{let{value:t,format:r="HH:mm:ss",onChange:s,onFinish:i,type:c}=e,f=x(e,["value","format","onChange","onFinish","type"]),p="countdown"===c,[u,m]=n.useState(null),d=(0,l.zX)(()=>{let e=Date.now(),r=new Date(t).getTime();return m({}),null==s||s(p?r-e:e-r),!p||!(r<e)||(null==i||i(),!1)});return n.useEffect(()=>{let e;let t=()=>{e=(0,a.Z)(()=>{d()&&t()})};return t(),()=>a.Z.cancel(e)},[t,p]),n.useEffect(()=>{m({})},[]),n.createElement(O,Object.assign({},f,{value:t,valueRender:e=>(0,o.Tm)(e,{title:void 0}),formatter:(e,t)=>u?function(e,t,r){let{format:n=""}=t,l=new Date(e).getTime(),a=Date.now();return function(e,t){let r=e,n=/\[[^\]]*]/g,l=(t.match(n)||[]).map(e=>e.slice(1,-1)),a=t.replace(n,"[]"),o=v.reduce((e,[t,n])=>{if(e.includes(t)){let l=Math.floor(r/n);return r-=l*n,e.replace(RegExp(`${t}+`,"g"),e=>{let t=e.length;return l.toString().padStart(t,"0")})}return e},a),s=0;return o.replace(n,()=>{let e=l[s];return s+=1,e})}(r?Math.max(l-a,0):Math.max(a-l,0),n)}(e,Object.assign(Object.assign({},t),{format:r}),p):"-"}))},h=n.memo(e=>n.createElement(j,Object.assign({},e,{type:"countdown"})));O.Timer=j,O.Countdown=h;let E=O}};