(()=>{var e={};e.id=5182,e.ids=[5182],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},97336:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var l=r(50482),a=r(69108),n=r(62563),s=r.n(n),i=r(68300),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let c=["",{children:["warehouse",{children:["materials",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,98472)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\materials\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,76485)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\materials\\page.tsx"],u="/warehouse/materials/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new l.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/warehouse/materials/page",pathname:"/warehouse/materials",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6997:(e,t,r)=>{Promise.resolve().then(r.bind(r,58715))},3448:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var l=r(65651),a=r(3729);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M193 796c0 17.7 14.3 32 32 32h574c17.7 0 32-14.3 32-32V563c0-176.2-142.8-319-319-319S193 386.8 193 563v233zm72-233c0-136.4 110.6-247 247-247s247 110.6 247 247v193H404V585c0-5.5-4.5-10-10-10h-44c-5.5 0-10 4.5-10 10v171h-75V563zm-48.1-252.5l39.6-39.6c3.1-3.1 3.1-8.2 0-11.3l-67.9-67.9a8.03 8.03 0 00-11.3 0l-39.6 39.6a8.03 8.03 0 000 11.3l67.9 67.9c3.1 3.1 8.1 3.1 11.3 0zm669.6-79.2l-39.6-39.6a8.03 8.03 0 00-11.3 0l-67.9 67.9a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l67.9-67.9c3.1-3.2 3.1-8.2 0-11.3zM832 892H192c-17.7 0-32 14.3-32 32v24c0 4.4 3.6 8 8 8h688c4.4 0 8-3.6 8-8v-24c0-17.7-14.3-32-32-32zM484 180h56c4.4 0 8-3.6 8-8V76c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v96c0 4.4 3.6 8 8 8z"}}]},name:"alert",theme:"outlined"};var s=r(49809);let i=a.forwardRef(function(e,t){return a.createElement(s.Z,(0,l.Z)({},e,{ref:t,icon:n}))})},55741:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var l=r(65651),a=r(3729);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};var s=r(49809);let i=a.forwardRef(function(e,t){return a.createElement(s.Z,(0,l.Z)({},e,{ref:t,icon:n}))})},89645:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var l=r(65651),a=r(3729);let n={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM770.87 199.13l-52.2-52.2a8.01 8.01 0 014.7-13.6l179.4-21c5.1-.6 9.5 3.7 8.9 8.9l-21 179.4c-.8 6.6-8.9 9.4-13.6 4.7l-52.4-52.4-256.2 256.2a8.03 8.03 0 01-11.3 0l-42.4-42.4a8.03 8.03 0 010-11.3l256.1-256.3z"}}]},name:"export",theme:"outlined"};var s=r(49809);let i=a.forwardRef(function(e,t){return a.createElement(s.Z,(0,l.Z)({},e,{ref:t,icon:n}))})},94505:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var l=r(65651),a=r(3729);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"};var s=r(49809);let i=a.forwardRef(function(e,t){return a.createElement(s.Z,(0,l.Z)({},e,{ref:t,icon:n}))})},96291:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var l=r(65651),a=r(3729);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M168 504.2c1-43.7 10-86.1 26.9-126 17.3-41 42.1-77.7 73.7-109.4S337 212.3 378 195c42.4-17.9 87.4-27 133.9-27s91.5 9.1 133.8 27A341.5 341.5 0 01755 268.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.7 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c0-6.7-7.7-10.5-12.9-6.3l-56.4 44.1C765.8 155.1 646.2 92 511.8 92 282.7 92 96.3 275.6 92 503.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8zm756 7.8h-60c-4.4 0-7.9 3.5-8 7.8-1 43.7-10 86.1-26.9 126-17.3 41-42.1 77.8-73.7 109.4A342.45 342.45 0 01512.1 856a342.24 342.24 0 01-243.2-100.8c-9.9-9.9-19.2-20.4-27.8-31.4l60.2-47a8 8 0 00-3-14.1l-175.7-43c-5-1.2-9.9 2.6-9.9 7.7l-.7 181c0 6.7 7.7 10.5 12.9 6.3l56.4-44.1C258.2 868.9 377.8 932 512.2 932c229.2 0 415.5-183.7 419.8-411.8a8 8 0 00-8-8.2z"}}]},name:"sync",theme:"outlined"};var s=r(49809);let i=a.forwardRef(function(e,t){return a.createElement(s.Z,(0,l.Z)({},e,{ref:t,icon:n}))})},37372:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var l=r(65651),a=r(3729);let n={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"};var s=r(49809);let i=a.forwardRef(function(e,t){return a.createElement(s.Z,(0,l.Z)({},e,{ref:t,icon:n}))})},14223:(e,t,r)=>{"use strict";r.d(t,{Z:()=>L});var l=r(3729),a=r(33795),n=r(57629),s=r(32066),i=r(2523),o=r(29513),c=r(34132),d=r.n(c),u=r(27335),m=r(7305),h=r(67862),x=r(29545),p=r(84893),g=r(92959),j=r(22989),f=r(13165);let v=(e,t,r,l,a)=>({background:e,border:`${(0,g.bf)(l.lineWidth)} ${l.lineType} ${t}`,[`${a}-icon`]:{color:r}}),y=e=>{let{componentCls:t,motionDurationSlow:r,marginXS:l,marginSM:a,fontSize:n,fontSizeLG:s,lineHeight:i,borderRadiusLG:o,motionEaseInOutCirc:c,withDescriptionIconSize:d,colorText:u,colorTextHeading:m,withDescriptionPadding:h,defaultPadding:x}=e;return{[t]:Object.assign(Object.assign({},(0,j.Wf)(e)),{position:"relative",display:"flex",alignItems:"center",padding:x,wordWrap:"break-word",borderRadius:o,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:l,lineHeight:0},"&-description":{display:"none",fontSize:n,lineHeight:i},"&-message":{color:m},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${r} ${c}, opacity ${r} ${c},
        padding-top ${r} ${c}, padding-bottom ${r} ${c},
        margin-bottom ${r} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:h,[`${t}-icon`]:{marginInlineEnd:a,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:l,color:m,fontSize:s},[`${t}-description`]:{display:"block",color:u}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},b=e=>{let{componentCls:t,colorSuccess:r,colorSuccessBorder:l,colorSuccessBg:a,colorWarning:n,colorWarningBorder:s,colorWarningBg:i,colorError:o,colorErrorBorder:c,colorErrorBg:d,colorInfo:u,colorInfoBorder:m,colorInfoBg:h}=e;return{[t]:{"&-success":v(a,l,r,e,t),"&-info":v(h,m,u,e,t),"&-warning":v(i,s,n,e,t),"&-error":Object.assign(Object.assign({},v(d,c,o,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},Z=e=>{let{componentCls:t,iconCls:r,motionDurationMid:l,marginXS:a,fontSizeIcon:n,colorIcon:s,colorIconHover:i}=e;return{[t]:{"&-action":{marginInlineStart:a},[`${t}-close-icon`]:{marginInlineStart:a,padding:0,overflow:"hidden",fontSize:n,lineHeight:(0,g.bf)(n),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${r}-close`]:{color:s,transition:`color ${l}`,"&:hover":{color:i}}},"&-close-text":{color:s,transition:`color ${l}`,"&:hover":{color:i}}}}},C=(0,f.I$)("Alert",e=>[y(e),b(e),Z(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var w=function(e,t){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(r[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(r[l[a]]=e[l[a]]);return r};let S={success:a.Z,info:o.Z,error:n.Z,warning:i.Z},k=e=>{let{icon:t,prefixCls:r,type:a}=e,n=S[a]||null;return t?(0,x.wm)(t,l.createElement("span",{className:`${r}-icon`},t),()=>({className:d()(`${r}-icon`,t.props.className)})):l.createElement(n,{className:`${r}-icon`})},$=e=>{let{isClosable:t,prefixCls:r,closeIcon:a,handleClose:n,ariaProps:i}=e,o=!0===a||void 0===a?l.createElement(s.Z,null):a;return t?l.createElement("button",Object.assign({type:"button",onClick:n,className:`${r}-close-icon`,tabIndex:0},i),o):null},N=l.forwardRef((e,t)=>{let{description:r,prefixCls:a,message:n,banner:s,className:i,rootClassName:o,style:c,onMouseEnter:x,onMouseLeave:g,onClick:j,afterClose:f,showIcon:v,closable:y,closeText:b,closeIcon:Z,action:S,id:N}=e,I=w(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[P,M]=l.useState(!1),E=l.useRef(null);l.useImperativeHandle(t,()=>({nativeElement:E.current}));let{getPrefixCls:O,direction:z,closable:q,closeIcon:L,className:R,style:D}=(0,p.dj)("alert"),H=O("alert",a),[B,T,V]=C(H),A=t=>{var r;M(!0),null===(r=e.onClose)||void 0===r||r.call(e,t)},_=l.useMemo(()=>void 0!==e.type?e.type:s?"warning":"info",[e.type,s]),U=l.useMemo(()=>"object"==typeof y&&!!y.closeIcon||!!b||("boolean"==typeof y?y:!1!==Z&&null!=Z||!!q),[b,Z,y,q]),F=!!s&&void 0===v||v,Q=d()(H,`${H}-${_}`,{[`${H}-with-description`]:!!r,[`${H}-no-icon`]:!F,[`${H}-banner`]:!!s,[`${H}-rtl`]:"rtl"===z},R,i,o,V,T),W=(0,m.Z)(I,{aria:!0,data:!0}),G=l.useMemo(()=>"object"==typeof y&&y.closeIcon?y.closeIcon:b||(void 0!==Z?Z:"object"==typeof q&&q.closeIcon?q.closeIcon:L),[Z,y,b,L]),X=l.useMemo(()=>{let e=null!=y?y:q;if("object"==typeof e){let{closeIcon:t}=e;return w(e,["closeIcon"])}return{}},[y,q]);return B(l.createElement(u.ZP,{visible:!P,motionName:`${H}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:f},({className:t,style:a},s)=>l.createElement("div",Object.assign({id:N,ref:(0,h.sQ)(E,s),"data-show":!P,className:d()(Q,t),style:Object.assign(Object.assign(Object.assign({},D),c),a),onMouseEnter:x,onMouseLeave:g,onClick:j,role:"alert"},W),F?l.createElement(k,{description:r,icon:e.icon,prefixCls:H,type:_}):null,l.createElement("div",{className:`${H}-content`},n?l.createElement("div",{className:`${H}-message`},n):null,r?l.createElement("div",{className:`${H}-description`},r):null),S?l.createElement("div",{className:`${H}-action`},S):null,l.createElement($,{isClosable:U,prefixCls:H,closeIcon:G,handleClose:A,ariaProps:X}))))});var I=r(31475),P=r(24142),M=r(61792),E=r(50804),O=r(6392),z=r(94977);let q=function(e){function t(){var e,r,l;return(0,I.Z)(this,t),r=t,l=arguments,r=(0,M.Z)(r),(e=(0,O.Z)(this,(0,E.Z)()?Reflect.construct(r,l||[],(0,M.Z)(this).constructor):r.apply(this,l))).state={error:void 0,info:{componentStack:""}},e}return(0,z.Z)(t,e),(0,P.Z)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:r,children:a}=this.props,{error:n,info:s}=this.state,i=(null==s?void 0:s.componentStack)||null,o=void 0===e?(n||"").toString():e;return n?l.createElement(N,{id:r,type:"error",message:o,description:l.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?i:t)}):a}}])}(l.Component);N.ErrorBoundary=q;let L=N},90377:(e,t,r)=>{"use strict";r.d(t,{Z:()=>P});var l=r(3729),a=r(34132),n=r.n(a),s=r(24773),i=r(22624),o=r(46164),c=r(29545),d=r(30605),u=r(84893),m=r(92959),h=r(55002),x=r(22989),p=r(96373),g=r(13165);let j=e=>{let{paddingXXS:t,lineWidth:r,tagPaddingHorizontal:l,componentCls:a,calc:n}=e,s=n(l).sub(r).equal(),i=n(t).sub(r).equal();return{[a]:Object.assign(Object.assign({},(0,x.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:s,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,m.bf)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:i,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:s}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},f=e=>{let{lineWidth:t,fontSizeIcon:r,calc:l}=e,a=e.fontSizeSM;return(0,p.IX)(e,{tagFontSize:a,tagLineHeight:(0,m.bf)(l(e.lineHeightSM).mul(a).equal()),tagIconSize:l(r).sub(l(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},v=e=>({defaultBg:new h.t(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),y=(0,g.I$)("Tag",e=>j(f(e)),v);var b=function(e,t){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(r[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(r[l[a]]=e[l[a]]);return r};let Z=l.forwardRef((e,t)=>{let{prefixCls:r,style:a,className:s,checked:i,onChange:o,onClick:c}=e,d=b(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:m,tag:h}=l.useContext(u.E_),x=m("tag",r),[p,g,j]=y(x),f=n()(x,`${x}-checkable`,{[`${x}-checkable-checked`]:i},null==h?void 0:h.className,s,g,j);return p(l.createElement("span",Object.assign({},d,{ref:t,style:Object.assign(Object.assign({},a),null==h?void 0:h.style),className:f,onClick:e=>{null==o||o(!i),null==c||c(e)}})))});var C=r(78701);let w=e=>(0,C.Z)(e,(t,{textColor:r,lightBorderColor:l,lightColor:a,darkColor:n})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:r,background:a,borderColor:l,"&-inverse":{color:e.colorTextLightSolid,background:n,borderColor:n},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),S=(0,g.bk)(["Tag","preset"],e=>w(f(e)),v),k=(e,t,r)=>{let l=function(e){return"string"!=typeof e?e:e.charAt(0).toUpperCase()+e.slice(1)}(r);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${r}`],background:e[`color${l}Bg`],borderColor:e[`color${l}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},$=(0,g.bk)(["Tag","status"],e=>{let t=f(e);return[k(t,"success","Success"),k(t,"processing","Info"),k(t,"error","Error"),k(t,"warning","Warning")]},v);var N=function(e,t){var r={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(r[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,l=Object.getOwnPropertySymbols(e);a<l.length;a++)0>t.indexOf(l[a])&&Object.prototype.propertyIsEnumerable.call(e,l[a])&&(r[l[a]]=e[l[a]]);return r};let I=l.forwardRef((e,t)=>{let{prefixCls:r,className:a,rootClassName:m,style:h,children:x,icon:p,color:g,onClose:j,bordered:f=!0,visible:v}=e,b=N(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:Z,direction:C,tag:w}=l.useContext(u.E_),[k,I]=l.useState(!0),P=(0,s.Z)(b,["closeIcon","closable"]);l.useEffect(()=>{void 0!==v&&I(v)},[v]);let M=(0,i.o2)(g),E=(0,i.yT)(g),O=M||E,z=Object.assign(Object.assign({backgroundColor:g&&!O?g:void 0},null==w?void 0:w.style),h),q=Z("tag",r),[L,R,D]=y(q),H=n()(q,null==w?void 0:w.className,{[`${q}-${g}`]:O,[`${q}-has-color`]:g&&!O,[`${q}-hidden`]:!k,[`${q}-rtl`]:"rtl"===C,[`${q}-borderless`]:!f},a,m,R,D),B=e=>{e.stopPropagation(),null==j||j(e),e.defaultPrevented||I(!1)},[,T]=(0,o.Z)((0,o.w)(e),(0,o.w)(w),{closable:!1,closeIconRender:e=>{let t=l.createElement("span",{className:`${q}-close-icon`,onClick:B},e);return(0,c.wm)(e,t,e=>({onClick:t=>{var r;null===(r=null==e?void 0:e.onClick)||void 0===r||r.call(e,t),B(t)},className:n()(null==e?void 0:e.className,`${q}-close-icon`)}))}}),V="function"==typeof b.onClick||x&&"a"===x.type,A=p||null,_=A?l.createElement(l.Fragment,null,A,x&&l.createElement("span",null,x)):x,U=l.createElement("span",Object.assign({},P,{ref:t,className:H,style:z}),_,T,M&&l.createElement(S,{key:"preset",prefixCls:q}),E&&l.createElement($,{key:"status",prefixCls:q}));return L(V?l.createElement(d.Z,{component:"Tag"},U):U)});I.CheckableTag=Z;let P=I},58715:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>V});var l=r(95344),a=r(3729),n=r(97854),s=r(97557),i=r(32979),o=r(7618),c=r(90377),d=r(39470),u=r(87049),m=r(10707),h=r(51410),x=r(11157),p=r(63724),g=r(27976),j=r(14223),f=r(83984),v=r(43896),y=r(284),b=r(36527),Z=r(16408),C=r(6025),w=r(37372),S=r(3448),k=r(21754),$=r(94505),N=r(10592),I=r(46116),P=r(2778),M=r(92561),E=r(70469),O=r(96291),z=r(55741),q=r(89645),L=r(58535),R=r(48869),D=r.n(R);let{Option:H}=n.default,{RangePicker:B}=s.default,T=()=>{let{message:e,modal:t}=i.Z.useApp(),[r,s]=(0,a.useState)(!1),[R,B]=(0,a.useState)([]),[T,V]=(0,a.useState)(!1),[A,_]=(0,a.useState)(!1),[U,F]=(0,a.useState)(!1),[Q,W]=(0,a.useState)(null),[G,X]=(0,a.useState)(null),[Y,K]=(0,a.useState)(""),[J,ee]=(0,a.useState)(""),[et,er]=(0,a.useState)(""),[el,ea]=(0,a.useState)(""),[en]=o.Z.useForm(),[es]=o.Z.useForm(),[ei,eo]=(0,a.useState)([{id:"1",materialCode:"RM001",materialName:"45号钢",specification:"Φ50\xd73000mm",category:"金属材料",unit:"根",currentStock:150,minStock:200,maxStock:500,unitPrice:280.5,totalValue:42075,location:"M区-01-001",batchNumber:"RM20240110001",receivedDate:"2024-01-10",supplierCode:"SUP001",supplierName:"钢材供应商A",supplierContact:"张经理 13800138001",status:"low",lastUpdated:"2024-01-15 10:30:00",qualityStatus:"qualified",storageCondition:"常温干燥"},{id:"2",materialCode:"RM002",materialName:"铝合金板",specification:"6061-T6 10\xd71000\xd72000mm",category:"金属材料",unit:"张",currentStock:85,minStock:50,maxStock:200,unitPrice:450,totalValue:38250,location:"M区-02-015",batchNumber:"RM20240112002",receivedDate:"2024-01-12",supplierCode:"SUP002",supplierName:"有色金属公司",supplierContact:"李经理 13900139002",status:"normal",lastUpdated:"2024-01-14 14:20:00",qualityStatus:"qualified",storageCondition:"常温干燥"},{id:"3",materialCode:"RM003",materialName:"液压油",specification:"46# 抗磨液压油",category:"化工原料",unit:"升",currentStock:2800,minStock:1e3,maxStock:5e3,unitPrice:12.5,totalValue:35e3,location:"M区-03-008",batchNumber:"RM20240108003",receivedDate:"2024-01-08",expiryDate:"2025-01-08",supplierCode:"SUP003",supplierName:"化工材料厂",supplierContact:"王经理 13700137003",status:"normal",lastUpdated:"2024-01-13 09:15:00",qualityStatus:"qualified",storageCondition:"阴凉通风"},{id:"4",materialCode:"RM004",materialName:"橡胶密封圈",specification:"NBR Φ50\xd73mm",category:"橡胶制品",unit:"个",currentStock:25,minStock:100,maxStock:500,unitPrice:8.5,totalValue:212.5,location:"M区-04-012",batchNumber:"RM20240105004",receivedDate:"2024-01-05",expiryDate:"2026-01-05",supplierCode:"SUP004",supplierName:"橡胶制品厂",supplierContact:"赵经理 13600136004",status:"low",lastUpdated:"2024-01-12 16:45:00",qualityStatus:"qualified",storageCondition:"常温避光"}]),ec=[{id:"1",materialCode:"RM001",materialName:"45号钢",currentStock:150,minStock:200,shortage:50,urgencyLevel:"high",suggestedQuantity:100,estimatedCost:28050,supplierName:"钢材供应商A",leadTime:7,lastOrderDate:"2023-12-15"},{id:"2",materialCode:"RM004",materialName:"橡胶密封圈",currentStock:25,minStock:100,shortage:75,urgencyLevel:"high",suggestedQuantity:200,estimatedCost:1700,supplierName:"橡胶制品厂",leadTime:5,lastOrderDate:"2023-12-20"}],ed=e=>{let{currentStock:t,minStock:r,maxStock:a,status:n}=e;switch(n){case"low":return{color:"red",text:"库存不足",icon:l.jsx(w.Z,{}),percentage:t/a*100};case"high":return{color:"orange",text:"库存过多",icon:l.jsx(S.Z,{}),percentage:t/a*100};case"expired":return{color:"purple",text:"已过期",icon:l.jsx(S.Z,{}),percentage:t/a*100};case"damaged":return{color:"volcano",text:"损坏",icon:l.jsx(S.Z,{}),percentage:t/a*100};case"reserved":return{color:"blue",text:"已预留",icon:l.jsx(S.Z,{}),percentage:t/a*100};default:return{color:"green",text:"正常",icon:null,percentage:t/a*100}}},eu=e=>{switch(e){case"qualified":return l.jsx(c.Z,{color:"green",children:"合格"});case"unqualified":return l.jsx(c.Z,{color:"red",children:"不合格"});case"pending":return l.jsx(c.Z,{color:"orange",children:"待检"});default:return l.jsx(c.Z,{children:"未知"})}},em=r=>{t.confirm({title:"库存盘点确认",content:`确认对 ${r.materialName} 进行库存盘点吗？`,onOk(){e.success("库存盘点任务已创建")}})},eh=e=>{X(e),es.setFieldsValue({materialCode:e.materialCode,materialName:e.materialName,currentLocation:e.location,currentStock:e.currentStock,unit:e.unit}),_(!0)},ex=e=>{F(!0)},ep=e=>{t.info({title:"原料库存详情",width:700,content:l.jsx("div",{className:"space-y-4",children:(0,l.jsxs)(p.Z,{gutter:[16,16],children:[l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"原料编码:"})," ",e.materialCode]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"原料名称:"})," ",e.materialName]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"规格型号:"})," ",e.specification]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"分类:"})," ",e.category]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"当前库存:"})," ",e.currentStock," ",e.unit]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"最小库存:"})," ",e.minStock," ",e.unit]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"库位:"})," ",e.location]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"批次号:"})," ",e.batchNumber]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"到货日期:"})," ",e.receivedDate]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"供应商:"})," ",e.supplierName]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"存储条件:"})," ",e.storageCondition]})}),l.jsx(g.Z,{span:12,children:(0,l.jsxs)("div",{children:[l.jsx("strong",{children:"质量状态:"})," ",eu(e.qualityStatus)]})})]})})})},eg={totalMaterials:ei.length,totalValue:ei.reduce((e,t)=>e+t.totalValue,0),lowStockCount:ei.filter(e=>"low"===e.status).length,normalStockCount:ei.filter(e=>"normal"===e.status).length,alertCount:ec.length};return(0,l.jsxs)("div",{className:"space-y-6",children:[l.jsx("div",{className:"page-header",children:(0,l.jsxs)("div",{className:"flex items-center",children:[l.jsx(P.Z,{className:"text-2xl text-blue-600 mr-3"}),(0,l.jsxs)("div",{children:[l.jsx("h1",{className:"page-title",children:"原料库存管理"}),l.jsx("p",{className:"page-description",children:"管理原材料库存，包括原料入库、出库、库存查询、采购预警等"})]})]})}),ec.length>0&&l.jsx(j.Z,{message:"补货提醒",description:(0,l.jsxs)("div",{children:[l.jsx("p",{children:"以下原料库存不足，建议及时补货："}),l.jsx("ul",{className:"mt-2",children:ec.map(e=>(0,l.jsxs)("li",{className:"flex justify-between items-center py-1",children:[(0,l.jsxs)("span",{children:[e.materialName," - 当前库存: ",e.currentStock,"， 缺口: ",e.shortage,"，建议采购: ",e.suggestedQuantity]}),l.jsx(x.ZP,{size:"small",type:"link",icon:l.jsx(M.Z,{}),onClick:()=>F(!0),children:"立即采购"})]},e.id))})]}),type:"warning",showIcon:!0,closable:!0}),(0,l.jsxs)(p.Z,{gutter:[16,16],children:[l.jsx(g.Z,{xs:24,sm:6,children:l.jsx(f.Z,{children:l.jsx(v.Z,{title:"原料总数",value:eg.totalMaterials,suffix:"种",prefix:l.jsx(P.Z,{}),valueStyle:{color:"#1890ff"}})})}),l.jsx(g.Z,{xs:24,sm:6,children:l.jsx(f.Z,{children:l.jsx(v.Z,{title:"库存总值",value:eg.totalValue,precision:2,prefix:"\xa5",valueStyle:{color:"#52c41a"}})})}),l.jsx(g.Z,{xs:24,sm:6,children:l.jsx(f.Z,{children:l.jsx(v.Z,{title:"库存不足",value:eg.lowStockCount,suffix:"种",valueStyle:{color:"#ff4d4f"},prefix:l.jsx(w.Z,{})})})}),l.jsx(g.Z,{xs:24,sm:6,children:l.jsx(f.Z,{children:l.jsx(v.Z,{title:"补货提醒",value:eg.alertCount,suffix:"项",valueStyle:{color:"#fa8c16"},prefix:l.jsx(N.Z,{})})})})]}),l.jsx(f.Z,{children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[l.jsx(y.default,{placeholder:"搜索原料编码、名称或规格",prefix:l.jsx(E.Z,{}),value:Y,onChange:e=>K(e.target.value),className:"w-full sm:w-64"}),(0,l.jsxs)(n.default,{placeholder:"原料分类",value:et,onChange:er,className:"w-full sm:w-32",allowClear:!0,children:[l.jsx(H,{value:"",children:"全部分类"}),l.jsx(H,{value:"金属材料",children:"金属材料"}),l.jsx(H,{value:"化工原料",children:"化工原料"}),l.jsx(H,{value:"橡胶制品",children:"橡胶制品"}),l.jsx(H,{value:"电子元件",children:"电子元件"})]}),(0,l.jsxs)(n.default,{placeholder:"库存状态",value:J,onChange:ee,className:"w-full sm:w-32",allowClear:!0,children:[l.jsx(H,{value:"",children:"全部状态"}),l.jsx(H,{value:"normal",children:"正常"}),l.jsx(H,{value:"low",children:"库存不足"}),l.jsx(H,{value:"high",children:"库存过多"}),l.jsx(H,{value:"expired",children:"已过期"}),l.jsx(H,{value:"damaged",children:"损坏"}),l.jsx(H,{value:"reserved",children:"已预留"})]}),(0,l.jsxs)(n.default,{placeholder:"供应商",value:el,onChange:ea,className:"w-full sm:w-40",allowClear:!0,children:[l.jsx(H,{value:"",children:"全部供应商"}),l.jsx(H,{value:"钢材供应商A",children:"钢材供应商A"}),l.jsx(H,{value:"有色金属公司",children:"有色金属公司"}),l.jsx(H,{value:"化工材料厂",children:"化工材料厂"}),l.jsx(H,{value:"橡胶制品厂",children:"橡胶制品厂"})]})]}),(0,l.jsxs)("div",{className:"flex space-x-2",children:[l.jsx(x.ZP,{icon:l.jsx(O.Z,{}),onClick:()=>s(!0),children:"刷新"}),l.jsx(x.ZP,{icon:l.jsx(z.Z,{}),children:"库存报表"}),l.jsx(x.ZP,{icon:l.jsx(q.Z,{}),children:"导出数据"}),l.jsx(x.ZP,{icon:l.jsx(M.Z,{}),children:"采购申请"}),l.jsx(x.ZP,{type:"primary",icon:l.jsx(L.Z,{}),children:"原料入库"})]})]}),l.jsx(b.Z,{columns:[{title:"原料编码",dataIndex:"materialCode",key:"materialCode",width:120,fixed:"left",render:e=>l.jsx("span",{className:"font-mono text-blue-600",children:e})},{title:"原料信息",key:"materialInfo",width:200,render:(e,t)=>(0,l.jsxs)("div",{children:[l.jsx("div",{className:"font-medium",children:t.materialName}),(0,l.jsxs)("div",{className:"text-gray-500 text-sm",children:["规格: ",t.specification]}),(0,l.jsxs)("div",{className:"text-gray-400 text-xs",children:["分类: ",t.category]})]})},{title:"质量状态",dataIndex:"qualityStatus",key:"qualityStatus",width:80,render:e=>eu(e)},{title:"当前库存",key:"currentStock",width:120,render:(e,t)=>{let r=ed(t);return(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("div",{className:"text-lg font-bold",children:[t.currentStock.toLocaleString()," ",t.unit]}),l.jsx(d.Z,{status:r.color,text:r.text})]})}},{title:"库存水位",key:"stockLevel",width:150,render:(e,t)=>{let r=ed(t);return(0,l.jsxs)("div",{children:[l.jsx(u.Z,{percent:r.percentage,size:"small",status:"normal"===t.status?"success":"exception",format:()=>`${t.currentStock}/${t.maxStock}`}),(0,l.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["最小库存: ",t.minStock," ",t.unit]})]})}},{title:"库存价值",key:"value",width:120,render:(e,t)=>(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"font-medium",children:["\xa5",t.totalValue.toLocaleString()]}),(0,l.jsxs)("div",{className:"text-gray-500 text-sm",children:["单价: \xa5",t.unitPrice,"/",t.unit]})]})},{title:"供应商信息",key:"supplierInfo",width:150,render:(e,t)=>(0,l.jsxs)("div",{children:[l.jsx("div",{className:"font-medium",children:t.supplierName}),l.jsx("div",{className:"text-gray-500 text-sm",children:t.supplierContact})]})},{title:"库位信息",key:"locationInfo",width:120,render:(e,t)=>(0,l.jsxs)("div",{children:[l.jsx("div",{className:"font-medium",children:t.location}),(0,l.jsxs)("div",{className:"text-gray-500 text-sm",children:["批次: ",t.batchNumber]})]})},{title:"存储条件",dataIndex:"storageCondition",key:"storageCondition",width:100,render:e=>l.jsx(c.Z,{color:"cyan",children:e})},{title:"到货日期",dataIndex:"receivedDate",key:"receivedDate",width:100,render:e=>l.jsx("span",{children:D()(e).format("YYYY-MM-DD")})},{title:"操作",key:"action",width:200,fixed:"right",render:(e,t)=>(0,l.jsxs)(m.Z,{size:"small",children:[l.jsx(h.Z,{title:"库存盘点",children:l.jsx(x.ZP,{type:"text",icon:l.jsx(k.Z,{}),size:"small",onClick:()=>em(t),children:"盘点"})}),l.jsx(h.Z,{title:"库存转移",children:l.jsx(x.ZP,{type:"text",icon:l.jsx($.Z,{}),size:"small",onClick:()=>eh(t),children:"转移"})}),l.jsx(h.Z,{title:"补货提醒",children:l.jsx(x.ZP,{type:"text",icon:l.jsx(N.Z,{}),size:"small",onClick:()=>ex(t),disabled:"low"!==t.status,children:"补货"})}),l.jsx(h.Z,{title:"查看详情",children:l.jsx(x.ZP,{type:"text",icon:l.jsx(I.Z,{}),size:"small",onClick:()=>ep(t),children:"详情"})})]})}],dataSource:ei.filter(e=>{let t=!Y||e.materialCode.toLowerCase().includes(Y.toLowerCase())||e.materialName.toLowerCase().includes(Y.toLowerCase())||e.specification.toLowerCase().includes(Y.toLowerCase()),r=!et||e.category===et,l=!J||e.status===J,a=!el||e.supplierName===el;return t&&r&&l&&a}),rowKey:"id",loading:r,rowSelection:{selectedRowKeys:R,onChange:B,getCheckboxProps:e=>({disabled:"damaged"===e.status})},pagination:{total:ei.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>`第 ${t[0]}-${t[1]} 条/共 ${e} 条`},scroll:{x:1600},size:"small"})]})}),l.jsx(Z.Z,{title:"库存转移",open:A,onCancel:()=>{_(!1),X(null),es.resetFields()},footer:[l.jsx(x.ZP,{onClick:()=>_(!1),children:"取消"},"cancel"),l.jsx(x.ZP,{type:"primary",onClick:()=>{es.validateFields().then(t=>{e.success("库存转移成功"),_(!1),es.resetFields()})},children:"确认转移"},"submit")],width:600,children:(0,l.jsxs)(o.Z,{form:es,layout:"vertical",className:"space-y-4",children:[(0,l.jsxs)(p.Z,{gutter:16,children:[l.jsx(g.Z,{span:12,children:l.jsx(o.Z.Item,{label:"原料编码",name:"materialCode",children:l.jsx(y.default,{disabled:!0})})}),l.jsx(g.Z,{span:12,children:l.jsx(o.Z.Item,{label:"原料名称",name:"materialName",children:l.jsx(y.default,{disabled:!0})})})]}),(0,l.jsxs)(p.Z,{gutter:16,children:[l.jsx(g.Z,{span:12,children:l.jsx(o.Z.Item,{label:"当前库位",name:"currentLocation",children:l.jsx(y.default,{disabled:!0})})}),l.jsx(g.Z,{span:12,children:l.jsx(o.Z.Item,{label:"当前库存",name:"currentStock",children:l.jsx(C.Z,{disabled:!0,style:{width:"100%"},addonAfter:G?.unit})})})]}),(0,l.jsxs)(p.Z,{gutter:16,children:[l.jsx(g.Z,{span:12,children:l.jsx(o.Z.Item,{label:"目标库位",name:"targetLocation",rules:[{required:!0,message:"请选择目标库位"}],children:(0,l.jsxs)(n.default,{placeholder:"请选择目标库位",children:[l.jsx(H,{value:"M区-01-002",children:"M区-01-002"}),l.jsx(H,{value:"M区-01-003",children:"M区-01-003"}),l.jsx(H,{value:"M区-02-001",children:"M区-02-001"}),l.jsx(H,{value:"M区-02-002",children:"M区-02-002"}),l.jsx(H,{value:"M区-03-001",children:"M区-03-001"})]})})}),l.jsx(g.Z,{span:12,children:l.jsx(o.Z.Item,{label:"转移数量",name:"transferQuantity",rules:[{required:!0,message:"请输入转移数量"},{type:"number",min:1,message:"数量必须大于0"}],children:l.jsx(C.Z,{style:{width:"100%"},placeholder:"请输入转移数量",min:1,max:G?.currentStock,addonAfter:G?.unit})})})]}),l.jsx(o.Z.Item,{label:"转移原因",name:"transferReason",rules:[{required:!0,message:"请选择转移原因"}],children:(0,l.jsxs)(n.default,{placeholder:"请选择转移原因",children:[l.jsx(H,{value:"库位优化",children:"库位优化"}),l.jsx(H,{value:"生产需要",children:"生产需要"}),l.jsx(H,{value:"库位维护",children:"库位维护"}),l.jsx(H,{value:"安全考虑",children:"安全考虑"}),l.jsx(H,{value:"其他",children:"其他"})]})}),l.jsx(o.Z.Item,{label:"备注",name:"remark",children:l.jsx(y.default.TextArea,{rows:3,placeholder:"请输入转移备注信息"})})]})}),l.jsx(Z.Z,{title:"补货提醒管理",open:U,onCancel:()=>F(!1),footer:[l.jsx(x.ZP,{onClick:()=>F(!1),children:"关闭"},"cancel"),l.jsx(x.ZP,{type:"primary",icon:l.jsx(M.Z,{}),children:"创建采购申请"},"purchase")],width:800,children:(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx(j.Z,{message:"系统检测到以下原料需要补货",type:"info",showIcon:!0}),l.jsx(b.Z,{dataSource:ec,rowKey:"id",pagination:!1,size:"small",columns:[{title:"原料编码",dataIndex:"materialCode",width:100},{title:"原料名称",dataIndex:"materialName",width:120},{title:"当前库存",dataIndex:"currentStock",width:80,render:e=>l.jsx("span",{className:"text-red-500 font-bold",children:e})},{title:"最小库存",dataIndex:"minStock",width:80},{title:"缺口数量",dataIndex:"shortage",width:80,render:e=>l.jsx("span",{className:"text-red-500",children:e})},{title:"建议采购",dataIndex:"suggestedQuantity",width:80,render:e=>l.jsx("span",{className:"text-blue-600 font-bold",children:e})},{title:"预估成本",dataIndex:"estimatedCost",width:100,render:e=>`\xa5${e.toLocaleString()}`},{title:"供应商",dataIndex:"supplierName",width:120},{title:"交期",dataIndex:"leadTime",width:60,render:e=>`${e}天`},{title:"紧急程度",dataIndex:"urgencyLevel",width:80,render:e=>l.jsx(c.Z,{color:"high"===e?"red":"medium"===e?"orange":"green",children:"high"===e?"紧急":"medium"===e?"一般":"不急"})}]})]})})]})},V=()=>l.jsx(i.Z,{children:l.jsx(T,{})})},76485:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var l=r(25036),a=r(38834);function n({children:e}){return l.jsx(a.Z,{children:e})}},98472:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>s});let l=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\warehouse\materials\page.tsx`),{__esModule:a,$$typeof:n}=l,s=l.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),l=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,2345,7049,7557,6274,996,6133],()=>r(97336));module.exports=l})();