(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6823],{53971:function(e,t,l){Promise.resolve().then(l.bind(l,36626))},36626:function(e,t,l){"use strict";l.r(t);var i=l(57437),r=l(2265),s=l(27296),a=l(39992),n=l(50030),d=l(89198),c=l(6053),o=l(65270),x=l(94734),u=l(92503),h=l(55175),m=l(38302),j=l(28683),p=l(89511),v=l(86155),Z=l(50574),f=l(47628),y=l(75123),g=l(59189),b=l(44753),I=l(99617),w=l(13924),S=l(87304),N=l(36476),T=l(75216),k=l(65362),A=l(34021),D=l(75393),C=l(51769),P=l(72973),L=l(74898),_=l(12102),F=l(74548),q=l.n(F);let{Option:V}=s.default,{TextArea:W}=a.default,{RangePicker:z}=n.default;t.default=()=>{let[e,t]=(0,r.useState)([]),[l,F]=(0,r.useState)(!1),[O,E]=(0,r.useState)(!1),[R,Y]=(0,r.useState)(!1),[B,M]=(0,r.useState)(!1),[K,J]=(0,r.useState)(null),[Q,U]=(0,r.useState)(null),[X]=d.Z.useForm(),[G]=d.Z.useForm(),[H,$]=(0,r.useState)(""),[ee,et]=(0,r.useState)(void 0),[el,ei]=(0,r.useState)(void 0);(0,r.useEffect)(()=>{t([{id:"1",invoiceNumber:"INV-2024-001",invoiceType:"vat_special",customerId:"1",customerName:"上海包装材料有限公司",taxNumber:"91310000123456789X",invoiceDate:"2024-01-25",totalAmount:106250,taxAmount:13812.5,totalWithTax:120062.5,status:"issued",triggerType:"delivery",relatedOrders:["SO-2024-001"],relatedDeliveries:["DN-2024-001"],items:[{id:"1",invoiceNumber:"INV-2024-001",productName:"圆形餐盘202mm",specification:"CP-202",unit:"个",quantity:3e3,unitPrice:.125,amount:375,taxRate:.13,taxAmount:48.75}],remark:"发货完成自动开票",createdAt:"2024-01-25T10:00:00",updatedAt:"2024-01-25T10:00:00"},{id:"2",invoiceNumber:"INV-2024-002",invoiceType:"vat_ordinary",customerId:"2",customerName:"北京绿色包装科技公司",taxNumber:"91110000987654321Y",invoiceDate:"2024-01-28",totalAmount:73e3,taxAmount:9490,totalWithTax:82490,status:"sent",triggerType:"monthly",relatedOrders:["SO-2024-002"],relatedDeliveries:["DN-2024-002"],items:[{id:"2",invoiceNumber:"INV-2024-002",productName:"方形餐盒180mm",specification:"CP-201",unit:"个",quantity:2e4,unitPrice:.165,amount:3300,taxRate:.13,taxAmount:429}],remark:"月结客户统一开票",createdAt:"2024-01-28T16:00:00",updatedAt:"2024-01-28T16:00:00"}])},[]);let er=e=>{let t={draft:{color:"default",text:"草稿",icon:(0,i.jsx)(w.Z,{})},issued:{color:"blue",text:"已开票",icon:(0,i.jsx)(S.Z,{})},sent:{color:"green",text:"已发送",icon:(0,i.jsx)(N.Z,{})},confirmed:{color:"cyan",text:"已确认",icon:(0,i.jsx)(S.Z,{})}}[e]||{color:"default",text:"未知",icon:null};return(0,i.jsx)(c.Z,{color:t.color,icon:t.icon,children:t.text})},es=e=>{let t={vat_special:{color:"red",text:"增值税专用发票"},vat_ordinary:{color:"blue",text:"增值税普通发票"},receipt:{color:"green",text:"收据"}}[e]||{color:"default",text:"其他"};return(0,i.jsx)(c.Z,{color:t.color,children:t.text})},ea=e=>{let t={delivery:{color:"blue",text:"发货触发"},prepayment:{color:"orange",text:"预付款"},monthly:{color:"purple",text:"月结"}}[e]||{color:"default",text:"手动"};return(0,i.jsx)(c.Z,{color:t.color,children:t.text})},en=[{title:"发票号码",dataIndex:"invoiceNumber",key:"invoiceNumber",width:140,fixed:"left"},{title:"客户名称",dataIndex:"customerName",key:"customerName",width:180,ellipsis:!0},{title:"发票类型",dataIndex:"invoiceType",key:"invoiceType",width:140,render:e=>es(e)},{title:"开票日期",dataIndex:"invoiceDate",key:"invoiceDate",width:120,sorter:(e,t)=>new Date(e.invoiceDate).getTime()-new Date(t.invoiceDate).getTime()},{title:"金额信息",key:"amountInfo",width:150,render:(e,t)=>(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{children:["不含税: \xa5",t.totalAmount.toLocaleString()]}),(0,i.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["税额: \xa5",t.taxAmount.toLocaleString()]}),(0,i.jsxs)("div",{style:{fontWeight:"bold",color:"#3f8600"},children:["含税: \xa5",t.totalWithTax.toLocaleString()]})]})},{title:"触发方式",dataIndex:"triggerType",key:"triggerType",width:100,render:e=>ea(e)},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>er(e)},{title:"关联订单",dataIndex:"relatedOrders",key:"relatedOrders",width:120,render:e=>(0,i.jsx)("div",{children:e.map(e=>(0,i.jsx)(c.Z,{children:e},e))})},{title:"操作",key:"action",width:220,fixed:"right",render:(e,t)=>(0,i.jsxs)(o.Z,{size:"small",children:[(0,i.jsx)(x.ZP,{type:"link",icon:(0,i.jsx)(T.Z,{}),onClick:()=>ex(t),children:"详情"}),(0,i.jsx)(x.ZP,{type:"link",icon:(0,i.jsx)(k.Z,{}),onClick:()=>eo(t),children:"编辑"}),"issued"===t.status&&(0,i.jsx)(x.ZP,{type:"link",icon:(0,i.jsx)(N.Z,{}),onClick:()=>eu(t.id),children:"发送"}),(0,i.jsx)(u.Z,{title:"确定要删除这张发票吗？",onConfirm:()=>eh(t.id),okText:"确定",cancelText:"取消",children:(0,i.jsx)(x.ZP,{type:"link",danger:!0,icon:(0,i.jsx)(A.Z,{}),children:"删除"})})]})}],ed=e.filter(e=>{let t=!H||e.invoiceNumber.toLowerCase().includes(H.toLowerCase())||e.customerName.toLowerCase().includes(H.toLowerCase())||e.taxNumber.toLowerCase().includes(H.toLowerCase()),l=!ee||e.status===ee,i=!el||e.invoiceType===el;return t&&l&&i}),ec={total:e.length,draft:e.filter(e=>"draft"===e.status).length,issued:e.filter(e=>"issued"===e.status).length,sent:e.filter(e=>"sent"===e.status).length,totalAmount:e.reduce((e,t)=>e+t.totalWithTax,0),totalTax:e.reduce((e,t)=>e+t.taxAmount,0)},eo=e=>{J(e),E(!0),X.setFieldsValue({...e,invoiceDate:q()(e.invoiceDate)})},ex=e=>{U(e),Y(!0)},eu=l=>{t(e.map(e=>e.id===l?{...e,status:"sent"}:e)),h.ZP.success("发票发送成功")},eh=l=>{t(e.filter(e=>e.id!==l)),h.ZP.success("发票删除成功")};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"page-header",children:[(0,i.jsx)("h1",{className:"page-title",children:"开票管理"}),(0,i.jsx)("p",{className:"page-description",children:"自动化开票流程，发货触发、预付款处理、月结统一开票"})]}),(0,i.jsxs)(m.Z,{gutter:[16,16],children:[(0,i.jsx)(j.Z,{xs:24,sm:6,children:(0,i.jsx)(p.Z,{children:(0,i.jsx)(v.Z,{title:"发票总数",value:ec.total,suffix:"张",prefix:(0,i.jsx)(w.Z,{})})})}),(0,i.jsx)(j.Z,{xs:24,sm:6,children:(0,i.jsx)(p.Z,{children:(0,i.jsx)(v.Z,{title:"开票总额",value:ec.totalAmount,precision:0,prefix:"\xa5",valueStyle:{color:"#3f8600"}})})}),(0,i.jsx)(j.Z,{xs:24,sm:6,children:(0,i.jsx)(p.Z,{children:(0,i.jsx)(v.Z,{title:"税额合计",value:ec.totalTax,precision:0,prefix:"\xa5",valueStyle:{color:"#1890ff"}})})}),(0,i.jsx)(j.Z,{xs:24,sm:6,children:(0,i.jsx)(p.Z,{children:(0,i.jsx)(v.Z,{title:"待发送发票",value:ec.issued,suffix:"张",valueStyle:{color:"#faad14"}})})})]}),(0,i.jsxs)(m.Z,{gutter:[16,16],children:[(0,i.jsx)(j.Z,{xs:24,sm:8,children:(0,i.jsx)(p.Z,{children:(0,i.jsx)(v.Z,{title:"草稿",value:ec.draft,suffix:"张",valueStyle:{color:"#666"}})})}),(0,i.jsx)(j.Z,{xs:24,sm:8,children:(0,i.jsx)(p.Z,{children:(0,i.jsx)(v.Z,{title:"已开票",value:ec.issued,suffix:"张",valueStyle:{color:"#1890ff"}})})}),(0,i.jsx)(j.Z,{xs:24,sm:8,children:(0,i.jsx)(p.Z,{children:(0,i.jsx)(v.Z,{title:"已发送",value:ec.sent,suffix:"张",valueStyle:{color:"#52c41a"}})})})]}),(0,i.jsx)(p.Z,{children:(0,i.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0",children:[(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4",children:[(0,i.jsx)(a.default,{placeholder:"搜索发票号、客户名称或税号",prefix:(0,i.jsx)(D.Z,{}),value:H,onChange:e=>$(e.target.value),className:"w-full sm:w-64"}),(0,i.jsxs)(_.Z,{placeholder:"发票状态",value:ee,onChange:et,className:"w-full sm:w-32",allowClear:!0,children:[(0,i.jsx)(V,{value:"draft",children:"草稿"}),(0,i.jsx)(V,{value:"issued",children:"已开票"}),(0,i.jsx)(V,{value:"sent",children:"已发送"}),(0,i.jsx)(V,{value:"confirmed",children:"已确认"})]}),(0,i.jsxs)(_.Z,{placeholder:"发票类型",value:el,onChange:ei,className:"w-full sm:w-40",allowClear:!0,children:[(0,i.jsx)(V,{value:"vat_special",children:"增值税专用发票"}),(0,i.jsx)(V,{value:"vat_ordinary",children:"增值税普通发票"}),(0,i.jsx)(V,{value:"receipt",children:"收据"})]})]}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)(x.ZP,{icon:(0,i.jsx)(C.Z,{}),children:"导出"}),(0,i.jsx)(x.ZP,{icon:(0,i.jsx)(P.Z,{}),onClick:()=>{M(!0),G.resetFields()},children:"自动开票"}),(0,i.jsx)(x.ZP,{type:"primary",icon:(0,i.jsx)(L.Z,{}),onClick:()=>{J(null),E(!0),X.resetFields()},children:"新建发票"})]})]})}),(0,i.jsx)(p.Z,{title:"发票列表",children:(0,i.jsx)(Z.Z,{columns:en,dataSource:ed,rowKey:"id",loading:l,pagination:{total:ed.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条")},scroll:{x:1400}})}),(0,i.jsx)(f.Z,{title:K?"编辑发票":"新建发票",open:O,onOk:()=>{X.validateFields().then(l=>{let i=new Date().toISOString(),r={...l,invoiceDate:l.invoiceDate.format("YYYY-MM-DD")};if(K)t(e.map(e=>e.id===K.id?{...e,...r,updatedAt:i}:e)),h.ZP.success("发票更新成功");else{let l={id:Date.now().toString(),invoiceNumber:"INV-".concat(new Date().getFullYear(),"-").concat(String(e.length+1).padStart(3,"0")),...r,items:[],createdAt:i,updatedAt:i};t([...e,l]),h.ZP.success("发票创建成功")}E(!1),X.resetFields()})},onCancel:()=>{E(!1),X.resetFields()},width:800,okText:"确认",cancelText:"取消",children:(0,i.jsxs)(d.Z,{form:X,layout:"vertical",initialValues:{invoiceType:"vat_ordinary",status:"draft",triggerType:"delivery",taxRate:.13},children:[(0,i.jsxs)(m.Z,{gutter:16,children:[(0,i.jsx)(j.Z,{span:12,children:(0,i.jsx)(d.Z.Item,{name:"customerId",label:"客户ID",rules:[{required:!0,message:"请输入客户ID"}],children:(0,i.jsx)(a.default,{placeholder:"请输入客户ID"})})}),(0,i.jsx)(j.Z,{span:12,children:(0,i.jsx)(d.Z.Item,{name:"customerName",label:"客户名称",rules:[{required:!0,message:"请输入客户名称"}],children:(0,i.jsx)(a.default,{placeholder:"请输入客户名称"})})})]}),(0,i.jsxs)(m.Z,{gutter:16,children:[(0,i.jsx)(j.Z,{span:12,children:(0,i.jsx)(d.Z.Item,{name:"invoiceType",label:"发票类型",rules:[{required:!0,message:"请选择发票类型"}],children:(0,i.jsxs)(s.default,{children:[(0,i.jsx)(V,{value:"vat_special",children:"增值税专用发票"}),(0,i.jsx)(V,{value:"vat_ordinary",children:"增值税普通发票"}),(0,i.jsx)(V,{value:"receipt",children:"收据"})]})})}),(0,i.jsx)(j.Z,{span:12,children:(0,i.jsx)(d.Z.Item,{name:"triggerType",label:"触发方式",rules:[{required:!0,message:"请选择触发方式"}],children:(0,i.jsxs)(s.default,{children:[(0,i.jsx)(V,{value:"delivery",children:"发货完成触发"}),(0,i.jsx)(V,{value:"prepayment",children:"预付款处理"}),(0,i.jsx)(V,{value:"monthly",children:"月结客户"})]})})})]}),(0,i.jsxs)(m.Z,{gutter:16,children:[(0,i.jsx)(j.Z,{span:12,children:(0,i.jsx)(d.Z.Item,{name:"invoiceDate",label:"开票日期",rules:[{required:!0,message:"请选择开票日期"}],children:(0,i.jsx)(n.default,{style:{width:"100%"}})})}),(0,i.jsx)(j.Z,{span:12,children:(0,i.jsx)(d.Z.Item,{name:"taxNumber",label:"纳税人识别号",rules:[{required:!0,message:"请输入纳税人识别号"}],children:(0,i.jsx)(a.default,{placeholder:"请输入纳税人识别号"})})})]}),(0,i.jsxs)(m.Z,{gutter:16,children:[(0,i.jsx)(j.Z,{span:8,children:(0,i.jsx)(d.Z.Item,{name:"totalAmount",label:"不含税金额",rules:[{required:!0,message:"请输入不含税金额"}],children:(0,i.jsx)(y.Z,{placeholder:"请输入不含税金额",min:0,style:{width:"100%"},formatter:e=>"\xa5 ".concat(e).replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>e.replace(/¥\s?|(,*)/g,"")})})}),(0,i.jsx)(j.Z,{span:8,children:(0,i.jsx)(d.Z.Item,{name:"taxAmount",label:"税额",rules:[{required:!0,message:"请输入税额"}],children:(0,i.jsx)(y.Z,{placeholder:"请输入税额",min:0,style:{width:"100%"},formatter:e=>"\xa5 ".concat(e).replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>e.replace(/¥\s?|(,*)/g,"")})})}),(0,i.jsx)(j.Z,{span:8,children:(0,i.jsx)(d.Z.Item,{name:"totalWithTax",label:"价税合计",rules:[{required:!0,message:"请输入价税合计"}],children:(0,i.jsx)(y.Z,{placeholder:"请输入价税合计",min:0,style:{width:"100%"},formatter:e=>"\xa5 ".concat(e).replace(/\B(?=(\d{3})+(?!\d))/g,","),parser:e=>e.replace(/¥\s?|(,*)/g,"")})})})]}),(0,i.jsx)(d.Z.Item,{name:"remark",label:"备注",children:(0,i.jsx)(W,{rows:3,placeholder:"请输入备注信息"})})]})}),(0,i.jsxs)(f.Z,{title:"自动开票设置",open:B,onOk:()=>{G.validateFields().then(e=>{let{triggerType:t,customerIds:l,dateRange:i}=e;h.ZP.success("已触发".concat("delivery"===t?"发货完成":"prepayment"===t?"预付款":"月结","自动开票")),M(!1),G.resetFields()})},onCancel:()=>{M(!1),G.resetFields()},width:600,okText:"执行开票",cancelText:"取消",children:[(0,i.jsx)(g.Z,{message:"自动开票规则",description:"系统将根据设置的触发条件自动生成发票，支持发货完成触发、预付款处理、月结客户统一开票",type:"info",showIcon:!0,style:{marginBottom:16}}),(0,i.jsxs)(d.Z,{form:G,layout:"vertical",initialValues:{triggerType:"delivery"},children:[(0,i.jsx)(d.Z.Item,{name:"triggerType",label:"触发类型",rules:[{required:!0,message:"请选择触发类型"}],children:(0,i.jsxs)(s.default,{children:[(0,i.jsx)(V,{value:"delivery",children:"发货完成触发"}),(0,i.jsx)(V,{value:"prepayment",children:"预付款处理"}),(0,i.jsx)(V,{value:"monthly",children:"月结客户统一开票"})]})}),(0,i.jsx)(d.Z.Item,{name:"customerIds",label:"客户范围",children:(0,i.jsxs)(s.default,{mode:"multiple",placeholder:"选择客户（不选则全部客户）",children:[(0,i.jsx)(V,{value:"1",children:"上海包装材料有限公司"}),(0,i.jsx)(V,{value:"2",children:"北京绿色包装科技公司"}),(0,i.jsx)(V,{value:"3",children:"广州环保餐具厂"})]})}),(0,i.jsx)(d.Z.Item,{name:"dateRange",label:"日期范围",children:(0,i.jsx)(z,{style:{width:"100%"}})}),(0,i.jsx)(d.Z.Item,{name:"invoiceType",label:"默认发票类型",children:(0,i.jsxs)(s.default,{placeholder:"请选择默认发票类型",children:[(0,i.jsx)(V,{value:"vat_special",children:"增值税专用发票"}),(0,i.jsx)(V,{value:"vat_ordinary",children:"增值税普通发票"}),(0,i.jsx)(V,{value:"receipt",children:"收据"})]})}),(0,i.jsxs)("div",{style:{marginTop:16},children:[(0,i.jsx)("h4",{children:"自动开票流程"}),(0,i.jsxs)(b.Z,{children:[(0,i.jsx)(b.Z.Item,{color:"blue",children:"检测触发条件（发货完成/预付款到账/月结时间）"}),(0,i.jsx)(b.Z.Item,{color:"blue",children:"获取客户开票信息和税号"}),(0,i.jsx)(b.Z.Item,{color:"blue",children:"自动计算金额和税额"}),(0,i.jsx)(b.Z.Item,{color:"blue",children:"生成发票并推送给客户"})]})]})]})]}),(0,i.jsx)(f.Z,{title:"发票详情",open:R,onCancel:()=>Y(!1),footer:[(0,i.jsx)(x.ZP,{onClick:()=>Y(!1),children:"关闭"},"close")],width:900,children:Q&&Q.invoiceNumber&&Q.customerName&&(0,i.jsxs)("div",{children:[(0,i.jsxs)(I.Z,{column:2,bordered:!0,children:[(0,i.jsx)(I.Z.Item,{label:"发票号码",children:Q.invoiceNumber}),(0,i.jsx)(I.Z.Item,{label:"客户名称",children:Q.customerName}),(0,i.jsx)(I.Z.Item,{label:"发票类型",children:es(Q.invoiceType)}),(0,i.jsx)(I.Z.Item,{label:"触发方式",children:ea(Q.triggerType)}),(0,i.jsx)(I.Z.Item,{label:"开票日期",children:Q.invoiceDate}),(0,i.jsx)(I.Z.Item,{label:"纳税人识别号",children:Q.taxNumber}),(0,i.jsxs)(I.Z.Item,{label:"不含税金额",children:["\xa5",Q.totalAmount.toLocaleString()]}),(0,i.jsxs)(I.Z.Item,{label:"税额",children:["\xa5",Q.taxAmount.toLocaleString()]}),(0,i.jsx)(I.Z.Item,{label:"价税合计",children:(0,i.jsxs)("span",{style:{fontSize:"16px",fontWeight:"bold",color:"#3f8600"},children:["\xa5",Q.totalWithTax.toLocaleString()]})}),(0,i.jsx)(I.Z.Item,{label:"状态",children:er(Q.status)}),(0,i.jsx)(I.Z.Item,{label:"创建时间",children:new Date(Q.createdAt).toLocaleString()}),(0,i.jsx)(I.Z.Item,{label:"更新时间",children:new Date(Q.updatedAt).toLocaleString()}),(0,i.jsx)(I.Z.Item,{label:"关联订单",span:2,children:Q.relatedOrders.map(e=>(0,i.jsx)(c.Z,{color:"blue",style:{marginRight:8},children:e},e))}),(0,i.jsx)(I.Z.Item,{label:"关联发货单",span:2,children:Q.relatedDeliveries.map(e=>(0,i.jsx)(c.Z,{color:"green",style:{marginRight:8},children:e},e))}),(0,i.jsx)(I.Z.Item,{label:"备注",span:2,children:Q.remark||"无"})]}),(0,i.jsxs)("div",{style:{marginTop:24},children:[(0,i.jsx)("h4",{children:"发票明细"}),(0,i.jsx)(Z.Z,{dataSource:Q.items,rowKey:"id",pagination:!1,size:"small",columns:[{title:"产品名称",dataIndex:"productName",key:"productName",width:150},{title:"规格型号",dataIndex:"specification",key:"specification",width:120},{title:"单位",dataIndex:"unit",key:"unit",width:80},{title:"数量",dataIndex:"quantity",key:"quantity",width:100,render:e=>e.toLocaleString()},{title:"单价",dataIndex:"unitPrice",key:"unitPrice",width:100,render:e=>"\xa5".concat(e.toFixed(3))},{title:"金额",dataIndex:"amount",key:"amount",width:120,render:e=>(0,i.jsxs)("span",{style:{fontWeight:"bold"},children:["\xa5",e.toLocaleString()]})},{title:"税率",dataIndex:"taxRate",key:"taxRate",width:80,render:e=>"".concat((100*e).toFixed(0),"%")},{title:"税额",dataIndex:"taxAmount",key:"taxAmount",width:100,render:e=>"\xa5".concat(e.toLocaleString())}]})]}),(0,i.jsxs)("div",{style:{marginTop:24},children:[(0,i.jsx)("h4",{children:"开票流程"}),(0,i.jsxs)(b.Z,{children:[(0,i.jsx)(b.Z.Item,{color:"green",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{style:{fontWeight:"bold"},children:"发票创建"}),(0,i.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:new Date(Q.createdAt).toLocaleString()}),(0,i.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["触发方式: ","delivery"===Q.triggerType?"发货完成触发":"prepayment"===Q.triggerType?"预付款处理":"月结客户"]})]})}),"draft"!==Q.status&&(0,i.jsx)(b.Z.Item,{color:"blue",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{style:{fontWeight:"bold"},children:"发票开具"}),(0,i.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["发票号: ",Q.invoiceNumber]}),(0,i.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["金额: \xa5",Q.totalWithTax.toLocaleString()]})]})}),"sent"===Q.status&&(0,i.jsx)(b.Z.Item,{color:"cyan",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{style:{fontWeight:"bold"},children:"发票发送"}),(0,i.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:"已发送给客户确认"})]})}),"confirmed"===Q.status&&(0,i.jsx)(b.Z.Item,{color:"green",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{style:{fontWeight:"bold"},children:"客户确认"}),(0,i.jsx)("div",{style:{fontSize:"12px",color:"#666"},children:"客户已确认收到发票"})]})})]})]})]})})]})}},12102:function(e,t,l){"use strict";var i=l(57437),r=l(2265),s=l(27296);t.Z=e=>{let{value:t,options:l,children:a,onChange:n,...d}=e,c=(0,r.useRef)(null),o=(0,r.useRef)(void 0),x=r.useMemo(()=>{let e=new Set;return l&&Array.isArray(l)&&l.forEach(t=>e.add(t.value)),a&&r.Children.forEach(a,t=>{var l;(null==t?void 0:null===(l=t.props)||void 0===l?void 0:l.value)!==void 0&&e.add(t.props.value)}),e},[l,a]),u=r.useMemo(()=>{if(null!=t&&""!==t&&("string"!=typeof t||""!==t.trim())){if(x.has(t))return o.current=t,t;console.warn("FormSelect: 值不在可用选项中",{value:t,availableValues:Array.from(x),placeholder:d.placeholder})}},[t,x,d.placeholder]);(0,r.useEffect)(()=>{""===t&&n&&setTimeout(()=>{n(void 0,void 0)},0)},[t,n]),(0,r.useEffect)(()=>{if(c.current){let e=c.current.nativeElement||c.current;if(e){let t=e.querySelector('input[type="hidden"]');t&&""===t.value&&n&&n(void 0,void 0)}}},[u,n]);let h=""===u?void 0:u;return(0,i.jsx)(s.default,{ref:c,...d,value:h,onChange:(e,t)=>{let l=""===e?void 0:e;console.log("FormSelect onChange:",{placeholder:d.placeholder,originalValue:e,safeValue:l,option:t,isEmptyString:""===e,isUndefined:void 0===e,isValidValue:x.has(e),availableValues:Array.from(x)}),n&&n(l,t)},options:l,children:a})}}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,1157,8236,30,9617,5470,8138,2971,4938,1744],function(){return e(e.s=53971)}),_N_E=e.O()}]);