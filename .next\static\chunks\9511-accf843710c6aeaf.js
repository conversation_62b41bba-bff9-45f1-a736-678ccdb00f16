"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9511],{74898:function(t,e,n){n.d(e,{Z:function(){return i}});var a=n(13428),o=n(2265),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},r=n(46614),i=o.forwardRef(function(t,e){return o.createElement(r.Z,(0,a.Z)({},t,{ref:e,icon:c}))})},89511:function(t,e,n){n.d(e,{Z:function(){return T}});var a=n(2265),o=n(42744),c=n.n(o),r=n(54925),i=n(57499),l=n(10693),d=n(38188),s=n(2012),u=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n},b=t=>{var{prefixCls:e,className:n,hoverable:o=!0}=t,r=u(t,["prefixCls","className","hoverable"]);let{getPrefixCls:l}=a.useContext(i.E_),d=l("card",e),s=c()("".concat(d,"-grid"),n,{["".concat(d,"-grid-hoverable")]:o});return a.createElement("div",Object.assign({},r,{className:s}))},f=n(58489),v=n(11303),p=n(78387),m=n(12711);let g=t=>{let{antCls:e,componentCls:n,headerHeight:a,headerPadding:o,tabsMarginBottom:c}=t;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:a,marginBottom:-1,padding:"0 ".concat((0,f.bf)(o)),color:t.colorTextHeading,fontWeight:t.fontWeightStrong,fontSize:t.headerFontSize,background:t.headerBg,borderBottom:"".concat((0,f.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorderSecondary),borderRadius:"".concat((0,f.bf)(t.borderRadiusLG)," ").concat((0,f.bf)(t.borderRadiusLG)," 0 0")},(0,v.dF)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},v.vS),{["\n          > ".concat(n,"-typography,\n          > ").concat(n,"-typography-edit-content\n        ")]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),["".concat(e,"-tabs-top")]:{clear:"both",marginBottom:c,color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,"&-bar":{borderBottom:"".concat((0,f.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorderSecondary)}}})},h=t=>{let{cardPaddingBase:e,colorBorderSecondary:n,cardShadow:a,lineWidth:o}=t;return{width:"33.33%",padding:e,border:0,borderRadius:0,boxShadow:"\n      ".concat((0,f.bf)(o)," 0 0 0 ").concat(n,",\n      0 ").concat((0,f.bf)(o)," 0 0 ").concat(n,",\n      ").concat((0,f.bf)(o)," ").concat((0,f.bf)(o)," 0 0 ").concat(n,",\n      ").concat((0,f.bf)(o)," 0 0 0 ").concat(n," inset,\n      0 ").concat((0,f.bf)(o)," 0 0 ").concat(n," inset;\n    "),transition:"all ".concat(t.motionDurationMid),"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:a}}},y=t=>{let{componentCls:e,iconCls:n,actionsLiMargin:a,cardActionsIconSize:o,colorBorderSecondary:c,actionsBg:r}=t;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:r,borderTop:"".concat((0,f.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(c),display:"flex",borderRadius:"0 0 ".concat((0,f.bf)(t.borderRadiusLG)," ").concat((0,f.bf)(t.borderRadiusLG))},(0,v.dF)()),{"& > li":{margin:a,color:t.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:t.calc(t.cardActionsIconSize).mul(2).equal(),fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer","&:hover":{color:t.colorPrimary,transition:"color ".concat(t.motionDurationMid)},["a:not(".concat(e,"-btn), > ").concat(n)]:{display:"inline-block",width:"100%",color:t.colorIcon,lineHeight:(0,f.bf)(t.fontHeight),transition:"color ".concat(t.motionDurationMid),"&:hover":{color:t.colorPrimary}},["> ".concat(n)]:{fontSize:o,lineHeight:(0,f.bf)(t.calc(o).mul(t.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:"".concat((0,f.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(c)}}})},k=t=>Object.assign(Object.assign({margin:"".concat((0,f.bf)(t.calc(t.marginXXS).mul(-1).equal())," 0"),display:"flex"},(0,v.dF)()),{"&-avatar":{paddingInlineEnd:t.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:t.marginXS}},"&-title":Object.assign({color:t.colorTextHeading,fontWeight:t.fontWeightStrong,fontSize:t.fontSizeLG},v.vS),"&-description":{color:t.colorTextDescription}}),x=t=>{let{componentCls:e,colorFillAlter:n,headerPadding:a,bodyPadding:o}=t;return{["".concat(e,"-head")]:{padding:"0 ".concat((0,f.bf)(a)),background:n,"&-title":{fontSize:t.fontSize}},["".concat(e,"-body")]:{padding:"".concat((0,f.bf)(t.padding)," ").concat((0,f.bf)(o))}}},S=t=>{let{componentCls:e}=t;return{overflow:"hidden",["".concat(e,"-body")]:{userSelect:"none"}}},w=t=>{let{componentCls:e,cardShadow:n,cardHeadPadding:a,colorBorderSecondary:o,boxShadowTertiary:c,bodyPadding:r,extraColor:i}=t;return{[e]:Object.assign(Object.assign({},(0,v.Wf)(t)),{position:"relative",background:t.colorBgContainer,borderRadius:t.borderRadiusLG,["&:not(".concat(e,"-bordered)")]:{boxShadow:c},["".concat(e,"-head")]:g(t),["".concat(e,"-extra")]:{marginInlineStart:"auto",color:i,fontWeight:"normal",fontSize:t.fontSize},["".concat(e,"-body")]:Object.assign({padding:r,borderRadius:"0 0 ".concat((0,f.bf)(t.borderRadiusLG)," ").concat((0,f.bf)(t.borderRadiusLG))},(0,v.dF)()),["".concat(e,"-grid")]:h(t),["".concat(e,"-cover")]:{"> *":{display:"block",width:"100%",borderRadius:"".concat((0,f.bf)(t.borderRadiusLG)," ").concat((0,f.bf)(t.borderRadiusLG)," 0 0")}},["".concat(e,"-actions")]:y(t),["".concat(e,"-meta")]:k(t)}),["".concat(e,"-bordered")]:{border:"".concat((0,f.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(o),["".concat(e,"-cover")]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},["".concat(e,"-hoverable")]:{cursor:"pointer",transition:"box-shadow ".concat(t.motionDurationMid,", border-color ").concat(t.motionDurationMid),"&:hover":{borderColor:"transparent",boxShadow:n}},["".concat(e,"-contain-grid")]:{borderRadius:"".concat((0,f.bf)(t.borderRadiusLG)," ").concat((0,f.bf)(t.borderRadiusLG)," 0 0 "),["".concat(e,"-body")]:{display:"flex",flexWrap:"wrap"},["&:not(".concat(e,"-loading) ").concat(e,"-body")]:{marginBlockStart:t.calc(t.lineWidth).mul(-1).equal(),marginInlineStart:t.calc(t.lineWidth).mul(-1).equal(),padding:0}},["".concat(e,"-contain-tabs")]:{["> div".concat(e,"-head")]:{minHeight:0,["".concat(e,"-head-title, ").concat(e,"-extra")]:{paddingTop:a}}},["".concat(e,"-type-inner")]:x(t),["".concat(e,"-loading")]:S(t),["".concat(e,"-rtl")]:{direction:"rtl"}}},_=t=>{let{componentCls:e,bodyPaddingSM:n,headerPaddingSM:a,headerHeightSM:o,headerFontSizeSM:c}=t;return{["".concat(e,"-small")]:{["> ".concat(e,"-head")]:{minHeight:o,padding:"0 ".concat((0,f.bf)(a)),fontSize:c,["> ".concat(e,"-head-wrapper")]:{["> ".concat(e,"-extra")]:{fontSize:t.fontSize}}},["> ".concat(e,"-body")]:{padding:n}},["".concat(e,"-small").concat(e,"-contain-tabs")]:{["> ".concat(e,"-head")]:{["".concat(e,"-head-title, ").concat(e,"-extra")]:{paddingTop:0,display:"flex",alignItems:"center"}}}}};var E=(0,p.I$)("Card",t=>{let e=(0,m.IX)(t,{cardShadow:t.boxShadowCard,cardHeadPadding:t.padding,cardPaddingBase:t.paddingLG,cardActionsIconSize:t.fontSize});return[w(e),_(e)]},t=>{var e,n;return{headerBg:"transparent",headerFontSize:t.fontSizeLG,headerFontSizeSM:t.fontSize,headerHeight:t.fontSizeLG*t.lineHeightLG+2*t.padding,headerHeightSM:t.fontSize*t.lineHeight+2*t.paddingXS,actionsBg:t.colorBgContainer,actionsLiMargin:"".concat(t.paddingSM,"px 0"),tabsMarginBottom:-t.padding-t.lineWidth,extraColor:t.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:null!==(e=t.bodyPadding)&&void 0!==e?e:t.paddingLG,headerPadding:null!==(n=t.headerPadding)&&void 0!==n?n:t.paddingLG}}),C=n(8443),O=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};let Z=t=>{let{actionClasses:e,actions:n=[],actionStyle:o}=t;return a.createElement("ul",{className:e,style:o},n.map((t,e)=>a.createElement("li",{style:{width:"".concat(100/n.length,"%")},key:"action-".concat(e)},a.createElement("span",null,t))))},R=a.forwardRef((t,e)=>{let n;let{prefixCls:o,className:u,rootClassName:f,style:v,extra:p,headStyle:m={},bodyStyle:g={},title:h,loading:y,bordered:k,variant:x,size:S,type:w,cover:_,actions:R,tabList:P,children:T,activeTabKey:I,defaultActiveTabKey:j,tabBarExtraContent:L,hoverable:z,tabProps:M={},classNames:N,styles:B}=t,D=O(t,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:G,direction:H,card:W}=a.useContext(i.E_),[A]=(0,C.Z)("card",x,k),X=t=>{var e;return c()(null===(e=null==W?void 0:W.classNames)||void 0===e?void 0:e[t],null==N?void 0:N[t])},q=t=>{var e;return Object.assign(Object.assign({},null===(e=null==W?void 0:W.styles)||void 0===e?void 0:e[t]),null==B?void 0:B[t])},K=a.useMemo(()=>{let t=!1;return a.Children.forEach(T,e=>{(null==e?void 0:e.type)===b&&(t=!0)}),t},[T]),F=G("card",o),[V,Y,U]=E(F),Q=a.createElement(d.Z,{loading:!0,active:!0,paragraph:{rows:4},title:!1},T),J=void 0!==I,$=Object.assign(Object.assign({},M),{[J?"activeKey":"defaultActiveKey"]:J?I:j,tabBarExtraContent:L}),tt=(0,l.Z)(S),te=tt&&"default"!==tt?tt:"large",tn=P?a.createElement(s.default,Object.assign({size:te},$,{className:"".concat(F,"-head-tabs"),onChange:e=>{var n;null===(n=t.onTabChange)||void 0===n||n.call(t,e)},items:P.map(t=>{var{tab:e}=t;return Object.assign({label:e},O(t,["tab"]))})})):null;if(h||p||tn){let t=c()("".concat(F,"-head"),X("header")),e=c()("".concat(F,"-head-title"),X("title")),o=c()("".concat(F,"-extra"),X("extra")),r=Object.assign(Object.assign({},m),q("header"));n=a.createElement("div",{className:t,style:r},a.createElement("div",{className:"".concat(F,"-head-wrapper")},h&&a.createElement("div",{className:e,style:q("title")},h),p&&a.createElement("div",{className:o,style:q("extra")},p)),tn)}let ta=c()("".concat(F,"-cover"),X("cover")),to=_?a.createElement("div",{className:ta,style:q("cover")},_):null,tc=c()("".concat(F,"-body"),X("body")),tr=Object.assign(Object.assign({},g),q("body")),ti=a.createElement("div",{className:tc,style:tr},y?Q:T),tl=c()("".concat(F,"-actions"),X("actions")),td=(null==R?void 0:R.length)?a.createElement(Z,{actionClasses:tl,actionStyle:q("actions"),actions:R}):null,ts=(0,r.Z)(D,["onTabChange"]),tu=c()(F,null==W?void 0:W.className,{["".concat(F,"-loading")]:y,["".concat(F,"-bordered")]:"borderless"!==A,["".concat(F,"-hoverable")]:z,["".concat(F,"-contain-grid")]:K,["".concat(F,"-contain-tabs")]:null==P?void 0:P.length,["".concat(F,"-").concat(tt)]:tt,["".concat(F,"-type-").concat(w)]:!!w,["".concat(F,"-rtl")]:"rtl"===H},u,f,Y,U),tb=Object.assign(Object.assign({},null==W?void 0:W.style),v);return V(a.createElement("div",Object.assign({ref:e},ts,{className:tu,style:tb}),n,to,ti,td))});var P=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};R.Grid=b,R.Meta=t=>{let{prefixCls:e,className:n,avatar:o,title:r,description:l}=t,d=P(t,["prefixCls","className","avatar","title","description"]),{getPrefixCls:s}=a.useContext(i.E_),u=s("card",e),b=c()("".concat(u,"-meta"),n),f=o?a.createElement("div",{className:"".concat(u,"-meta-avatar")},o):null,v=r?a.createElement("div",{className:"".concat(u,"-meta-title")},r):null,p=l?a.createElement("div",{className:"".concat(u,"-meta-description")},l):null,m=v||p?a.createElement("div",{className:"".concat(u,"-meta-detail")},v,p):null;return a.createElement("div",Object.assign({},d,{className:b}),f,m)};var T=R},8443:function(t,e,n){var a=n(2265),o=n(47137),c=n(57499);e.Z=function(t,e){var n,r;let i,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,{variant:d,[t]:s}=a.useContext(c.E_),u=a.useContext(o.pg),b=null==s?void 0:s.variant;i=void 0!==e?e:!1===l?"borderless":null!==(r=null!==(n=null!=u?u:b)&&void 0!==n?n:d)&&void 0!==r?r:"outlined";let f=c.tr.includes(i);return[i,f]}},2012:function(t,e,n){n.d(e,{default:function(){return tw}});var a=n(2265),o=n(73297),c=n(51842),r=n(74898),i=n(42744),l=n.n(i),d=n(13428),s=n(21076),u=n(10870),b=n(98961),f=n(60075),v=n(82554),p=n(73310),m=n(77971),g=(0,a.createContext)(null),h=n(16141),y=n(11288),k=n(28788),x=n(17146),S=n(43197),w=function(t){var e=t.activeTabOffset,n=t.horizontal,o=t.rtl,c=t.indicator,r=void 0===c?{}:c,i=r.size,l=r.align,d=void 0===l?"center":l,s=(0,a.useState)(),u=(0,b.Z)(s,2),f=u[0],v=u[1],p=(0,a.useRef)(),m=a.useCallback(function(t){return"function"==typeof i?i(t):"number"==typeof i?i:t},[i]);function g(){S.Z.cancel(p.current)}return(0,a.useEffect)(function(){var t={};if(e){if(n){t.width=m(e.width);var a=o?"right":"left";"start"===d&&(t[a]=e[a]),"center"===d&&(t[a]=e[a]+e.width/2,t.transform=o?"translateX(50%)":"translateX(-50%)"),"end"===d&&(t[a]=e[a]+e.width,t.transform="translateX(-100%)")}else t.height=m(e.height),"start"===d&&(t.top=e.top),"center"===d&&(t.top=e.top+e.height/2,t.transform="translateY(-50%)"),"end"===d&&(t.top=e.top+e.height,t.transform="translateY(-100%)")}return g(),p.current=(0,S.Z)(function(){f&&t&&Object.keys(t).every(function(e){var n=t[e],a=f[e];return"number"==typeof n&&"number"==typeof a?Math.round(n)===Math.round(a):n===a})||v(t)}),g},[JSON.stringify(e),n,o,d,m]),{style:f}},_={width:0,height:0,left:0,top:0};function E(t,e){var n=a.useRef(t),o=a.useState({}),c=(0,b.Z)(o,2)[1];return[n.current,function(t){var a="function"==typeof t?t(n.current):t;a!==n.current&&e(a,n.current),n.current=a,c({})}]}var C=n(19836);function O(t){var e=(0,a.useState)(0),n=(0,b.Z)(e,2),o=n[0],c=n[1],r=(0,a.useRef)(0),i=(0,a.useRef)();return i.current=t,(0,C.o)(function(){var t;null===(t=i.current)||void 0===t||t.call(i)},[o]),function(){r.current===o&&(r.current+=1,c(r.current))}}var Z={width:0,height:0,left:0,top:0,right:0};function R(t){var e;return t instanceof Map?(e={},t.forEach(function(t,n){e[n]=t})):e=t,JSON.stringify(e)}function P(t){return String(t).replace(/"/g,"TABS_DQ")}function T(t,e,n,a){return!!n&&!a&&!1!==t&&(void 0!==t||!1!==e&&null!==e)}var I=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.editable,c=t.locale,r=t.style;return o&&!1!==o.showAdd?a.createElement("button",{ref:e,type:"button",className:"".concat(n,"-nav-add"),style:r,"aria-label":(null==c?void 0:c.addAriaLabel)||"Add tab",onClick:function(t){o.onEdit("add",{event:t})}},o.addIcon||"+"):null}),j=a.forwardRef(function(t,e){var n,o=t.position,c=t.prefixCls,r=t.extra;if(!r)return null;var i={};return"object"!==(0,f.Z)(r)||a.isValidElement(r)?i.right=r:i=r,"right"===o&&(n=i.right),"left"===o&&(n=i.left),n?a.createElement("div",{className:"".concat(c,"-extra-content"),ref:e},n):null}),L=n(23803),z=n(93706),M=n(89017),N=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.id,c=t.tabs,r=t.locale,i=t.mobile,u=t.more,f=void 0===u?{}:u,v=t.style,p=t.className,m=t.editable,g=t.tabBarGutter,h=t.rtl,y=t.removeAriaLabel,k=t.onTabClick,x=t.getPopupContainer,S=t.popupClassName,w=(0,a.useState)(!1),_=(0,b.Z)(w,2),E=_[0],C=_[1],O=(0,a.useState)(null),Z=(0,b.Z)(O,2),R=Z[0],P=Z[1],j=f.icon,N="".concat(o,"-more-popup"),B="".concat(n,"-dropdown"),D=null!==R?"".concat(N,"-").concat(R):null,G=null==r?void 0:r.dropdownAriaLabel,H=a.createElement(z.ZP,{onClick:function(t){k(t.key,t.domEvent),C(!1)},prefixCls:"".concat(B,"-menu"),id:N,tabIndex:-1,role:"listbox","aria-activedescendant":D,selectedKeys:[R],"aria-label":void 0!==G?G:"expanded dropdown"},c.map(function(t){var e=t.closable,n=t.disabled,c=t.closeIcon,r=t.key,i=t.label,l=T(e,c,m,n);return a.createElement(z.sN,{key:r,id:"".concat(N,"-").concat(r),role:"option","aria-controls":o&&"".concat(o,"-panel-").concat(r),disabled:n},a.createElement("span",null,i),l&&a.createElement("button",{type:"button","aria-label":y||"remove",tabIndex:0,className:"".concat(B,"-menu-item-remove"),onClick:function(t){t.stopPropagation(),t.preventDefault(),t.stopPropagation(),m.onEdit("remove",{key:r,event:t})}},c||m.removeIcon||"\xd7"))}));function W(t){for(var e=c.filter(function(t){return!t.disabled}),n=e.findIndex(function(t){return t.key===R})||0,a=e.length,o=0;o<a;o+=1){var r=e[n=(n+t+a)%a];if(!r.disabled){P(r.key);return}}}(0,a.useEffect)(function(){var t=document.getElementById(D);t&&t.scrollIntoView&&t.scrollIntoView(!1)},[R]),(0,a.useEffect)(function(){E||P(null)},[E]);var A=(0,s.Z)({},h?"marginRight":"marginLeft",g);c.length||(A.visibility="hidden",A.order=1);var X=l()((0,s.Z)({},"".concat(B,"-rtl"),h)),q=i?null:a.createElement(L.Z,(0,d.Z)({prefixCls:B,overlay:H,visible:!!c.length&&E,onVisibleChange:C,overlayClassName:l()(X,S),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:x},f),a.createElement("button",{type:"button",className:"".concat(n,"-nav-more"),style:A,"aria-haspopup":"listbox","aria-controls":N,id:"".concat(o,"-more"),"aria-expanded":E,onKeyDown:function(t){var e=t.which;if(!E){[M.Z.DOWN,M.Z.SPACE,M.Z.ENTER].includes(e)&&(C(!0),t.preventDefault());return}switch(e){case M.Z.UP:W(-1),t.preventDefault();break;case M.Z.DOWN:W(1),t.preventDefault();break;case M.Z.ESC:C(!1);break;case M.Z.SPACE:case M.Z.ENTER:null!==R&&k(R,t)}}},void 0===j?"More":j));return a.createElement("div",{className:l()("".concat(n,"-nav-operations"),p),style:v,ref:e},q,a.createElement(I,{prefixCls:n,locale:r,editable:m}))}),B=a.memo(N,function(t,e){return e.tabMoving}),D=function(t){var e=t.prefixCls,n=t.id,o=t.active,c=t.focus,r=t.tab,i=r.key,d=r.label,u=r.disabled,b=r.closeIcon,f=r.icon,v=t.closable,p=t.renderWrapper,m=t.removeAriaLabel,g=t.editable,h=t.onClick,y=t.onFocus,k=t.onBlur,x=t.onKeyDown,S=t.onMouseDown,w=t.onMouseUp,_=t.style,E=t.tabCount,C=t.currentPosition,O="".concat(e,"-tab"),Z=T(v,b,g,u);function R(t){u||h(t)}var I=a.useMemo(function(){return f&&"string"==typeof d?a.createElement("span",null,d):d},[d,f]),j=a.useRef(null);a.useEffect(function(){c&&j.current&&j.current.focus()},[c]);var L=a.createElement("div",{key:i,"data-node-key":P(i),className:l()(O,(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(O,"-with-remove"),Z),"".concat(O,"-active"),o),"".concat(O,"-disabled"),u),"".concat(O,"-focus"),c)),style:_,onClick:R},a.createElement("div",{ref:j,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(i),className:"".concat(O,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(i),"aria-disabled":u,tabIndex:u?null:o?0:-1,onClick:function(t){t.stopPropagation(),R(t)},onKeyDown:x,onMouseDown:S,onMouseUp:w,onFocus:y,onBlur:k},c&&a.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(C," of ").concat(E)),f&&a.createElement("span",{className:"".concat(O,"-icon")},f),d&&I),Z&&a.createElement("button",{type:"button",role:"tab","aria-label":m||"remove",tabIndex:o?0:-1,className:"".concat(O,"-remove"),onClick:function(t){t.stopPropagation(),t.preventDefault(),t.stopPropagation(),g.onEdit("remove",{key:i,event:t})}},b||g.removeIcon||"\xd7"));return p?p(L):L},G=function(t,e){var n=t.offsetWidth,a=t.offsetHeight,o=t.offsetTop,c=t.offsetLeft,r=t.getBoundingClientRect(),i=r.width,l=r.height,d=r.left,s=r.top;return 1>Math.abs(i-n)?[i,l,d-e.left,s-e.top]:[n,a,c,o]},H=function(t){var e=t.current||{},n=e.offsetWidth,a=void 0===n?0:n,o=e.offsetHeight;if(t.current){var c=t.current.getBoundingClientRect(),r=c.width,i=c.height;if(1>Math.abs(r-a))return[r,i]}return[a,void 0===o?0:o]},W=function(t,e){return t[e?0:1]},A=a.forwardRef(function(t,e){var n,o,c,r,i,f,v,p,m,S,C,L,z,M,N,A,X,q,K,F,V,Y,U,Q,J,$,tt,te,tn,ta,to,tc,tr,ti,tl,td,ts,tu,tb,tf=t.className,tv=t.style,tp=t.id,tm=t.animated,tg=t.activeKey,th=t.rtl,ty=t.extra,tk=t.editable,tx=t.locale,tS=t.tabPosition,tw=t.tabBarGutter,t_=t.children,tE=t.onTabClick,tC=t.onTabScroll,tO=t.indicator,tZ=a.useContext(g),tR=tZ.prefixCls,tP=tZ.tabs,tT=(0,a.useRef)(null),tI=(0,a.useRef)(null),tj=(0,a.useRef)(null),tL=(0,a.useRef)(null),tz=(0,a.useRef)(null),tM=(0,a.useRef)(null),tN=(0,a.useRef)(null),tB="top"===tS||"bottom"===tS,tD=E(0,function(t,e){tB&&tC&&tC({direction:t>e?"left":"right"})}),tG=(0,b.Z)(tD,2),tH=tG[0],tW=tG[1],tA=E(0,function(t,e){!tB&&tC&&tC({direction:t>e?"top":"bottom"})}),tX=(0,b.Z)(tA,2),tq=tX[0],tK=tX[1],tF=(0,a.useState)([0,0]),tV=(0,b.Z)(tF,2),tY=tV[0],tU=tV[1],tQ=(0,a.useState)([0,0]),tJ=(0,b.Z)(tQ,2),t$=tJ[0],t0=tJ[1],t1=(0,a.useState)([0,0]),t2=(0,b.Z)(t1,2),t8=t2[0],t4=t2[1],t7=(0,a.useState)([0,0]),t9=(0,b.Z)(t7,2),t3=t9[0],t6=t9[1],t5=(n=new Map,o=(0,a.useRef)([]),c=(0,a.useState)({}),r=(0,b.Z)(c,2)[1],i=(0,a.useRef)("function"==typeof n?n():n),f=O(function(){var t=i.current;o.current.forEach(function(e){t=e(t)}),o.current=[],i.current=t,r({})}),[i.current,function(t){o.current.push(t),f()}]),et=(0,b.Z)(t5,2),ee=et[0],en=et[1],ea=(v=t$[0],(0,a.useMemo)(function(){for(var t=new Map,e=ee.get(null===(o=tP[0])||void 0===o?void 0:o.key)||_,n=e.left+e.width,a=0;a<tP.length;a+=1){var o,c,r=tP[a].key,i=ee.get(r);i||(i=ee.get(null===(c=tP[a-1])||void 0===c?void 0:c.key)||_);var l=t.get(r)||(0,u.Z)({},i);l.right=n-l.left-l.width,t.set(r,l)}return t},[tP.map(function(t){return t.key}).join("_"),ee,v])),eo=W(tY,tB),ec=W(t$,tB),er=W(t8,tB),ei=W(t3,tB),el=Math.floor(eo)<Math.floor(ec+er),ed=el?eo-ei:eo-er,es="".concat(tR,"-nav-operations-hidden"),eu=0,eb=0;function ef(t){return t<eu?eu:t>eb?eb:t}tB&&th?(eu=0,eb=Math.max(0,ec-ed)):(eu=Math.min(0,ed-ec),eb=0);var ev=(0,a.useRef)(null),ep=(0,a.useState)(),em=(0,b.Z)(ep,2),eg=em[0],eh=em[1];function ey(){eh(Date.now())}function ek(){ev.current&&clearTimeout(ev.current)}p=function(t,e){function n(t,e){t(function(t){return ef(t+e)})}return!!el&&(tB?n(tW,t):n(tK,e),ek(),ey(),!0)},m=(0,a.useState)(),C=(S=(0,b.Z)(m,2))[0],L=S[1],z=(0,a.useState)(0),N=(M=(0,b.Z)(z,2))[0],A=M[1],X=(0,a.useState)(0),K=(q=(0,b.Z)(X,2))[0],F=q[1],V=(0,a.useState)(),U=(Y=(0,b.Z)(V,2))[0],Q=Y[1],J=(0,a.useRef)(),$=(0,a.useRef)(),(tt=(0,a.useRef)(null)).current={onTouchStart:function(t){var e=t.touches[0];L({x:e.screenX,y:e.screenY}),window.clearInterval(J.current)},onTouchMove:function(t){if(C){var e=t.touches[0],n=e.screenX,a=e.screenY;L({x:n,y:a});var o=n-C.x,c=a-C.y;p(o,c);var r=Date.now();A(r),F(r-N),Q({x:o,y:c})}},onTouchEnd:function(){if(C&&(L(null),Q(null),U)){var t=U.x/K,e=U.y/K;if(!(.1>Math.max(Math.abs(t),Math.abs(e)))){var n=t,a=e;J.current=window.setInterval(function(){if(.01>Math.abs(n)&&.01>Math.abs(a)){window.clearInterval(J.current);return}n*=.9046104802746175,a*=.9046104802746175,p(20*n,20*a)},20)}}},onWheel:function(t){var e=t.deltaX,n=t.deltaY,a=0,o=Math.abs(e),c=Math.abs(n);o===c?a="x"===$.current?e:n:o>c?(a=e,$.current="x"):(a=n,$.current="y"),p(-a,-a)&&t.preventDefault()}},a.useEffect(function(){function t(t){tt.current.onTouchMove(t)}function e(t){tt.current.onTouchEnd(t)}return document.addEventListener("touchmove",t,{passive:!1}),document.addEventListener("touchend",e,{passive:!0}),tL.current.addEventListener("touchstart",function(t){tt.current.onTouchStart(t)},{passive:!0}),tL.current.addEventListener("wheel",function(t){tt.current.onWheel(t)},{passive:!1}),function(){document.removeEventListener("touchmove",t),document.removeEventListener("touchend",e)}},[]),(0,a.useEffect)(function(){return ek(),eg&&(ev.current=setTimeout(function(){eh(0)},100)),ek},[eg]);var ex=(te=tB?tH:tq,tr=(tn=(0,u.Z)((0,u.Z)({},t),{},{tabs:tP})).tabs,ti=tn.tabPosition,tl=tn.rtl,["top","bottom"].includes(ti)?(ta="width",to=tl?"right":"left",tc=Math.abs(te)):(ta="height",to="top",tc=-te),(0,a.useMemo)(function(){if(!tr.length)return[0,0];for(var t=tr.length,e=t,n=0;n<t;n+=1){var a=ea.get(tr[n].key)||Z;if(Math.floor(a[to]+a[ta])>Math.floor(tc+ed)){e=n-1;break}}for(var o=0,c=t-1;c>=0;c-=1)if((ea.get(tr[c].key)||Z)[to]<tc){o=c+1;break}return o>=e?[0,0]:[o,e]},[ea,ed,ec,er,ei,tc,ti,tr.map(function(t){return t.key}).join("_"),tl])),eS=(0,b.Z)(ex,2),ew=eS[0],e_=eS[1],eE=(0,k.Z)(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:tg,e=ea.get(t)||{width:0,height:0,left:0,right:0,top:0};if(tB){var n=tH;th?e.right<tH?n=e.right:e.right+e.width>tH+ed&&(n=e.right+e.width-ed):e.left<-tH?n=-e.left:e.left+e.width>-tH+ed&&(n=-(e.left+e.width-ed)),tK(0),tW(ef(n))}else{var a=tq;e.top<-tq?a=-e.top:e.top+e.height>-tq+ed&&(a=-(e.top+e.height-ed)),tW(0),tK(ef(a))}}),eC=(0,a.useState)(),eO=(0,b.Z)(eC,2),eZ=eO[0],eR=eO[1],eP=(0,a.useState)(!1),eT=(0,b.Z)(eP,2),eI=eT[0],ej=eT[1],eL=tP.filter(function(t){return!t.disabled}).map(function(t){return t.key}),ez=function(t){var e=eL.indexOf(eZ||tg),n=eL.length;eR(eL[(e+t+n)%n])},eM=function(t){var e=t.code,n=th&&tB,a=eL[0],o=eL[eL.length-1];switch(e){case"ArrowLeft":tB&&ez(n?1:-1);break;case"ArrowRight":tB&&ez(n?-1:1);break;case"ArrowUp":t.preventDefault(),tB||ez(-1);break;case"ArrowDown":t.preventDefault(),tB||ez(1);break;case"Home":t.preventDefault(),eR(a);break;case"End":t.preventDefault(),eR(o);break;case"Enter":case"Space":t.preventDefault(),tE(null!=eZ?eZ:tg,t);break;case"Backspace":case"Delete":var c=eL.indexOf(eZ),r=tP.find(function(t){return t.key===eZ});T(null==r?void 0:r.closable,null==r?void 0:r.closeIcon,tk,null==r?void 0:r.disabled)&&(t.preventDefault(),t.stopPropagation(),tk.onEdit("remove",{key:eZ,event:t}),c===eL.length-1?ez(-1):ez(1))}},eN={};tB?eN[th?"marginRight":"marginLeft"]=tw:eN.marginTop=tw;var eB=tP.map(function(t,e){var n=t.key;return a.createElement(D,{id:tp,prefixCls:tR,key:n,tab:t,style:0===e?void 0:eN,closable:t.closable,editable:tk,active:n===tg,focus:n===eZ,renderWrapper:t_,removeAriaLabel:null==tx?void 0:tx.removeAriaLabel,tabCount:eL.length,currentPosition:e+1,onClick:function(t){tE(n,t)},onKeyDown:eM,onFocus:function(){eI||eR(n),eE(n),ey(),tL.current&&(th||(tL.current.scrollLeft=0),tL.current.scrollTop=0)},onBlur:function(){eR(void 0)},onMouseDown:function(){ej(!0)},onMouseUp:function(){ej(!1)}})}),eD=function(){return en(function(){var t,e=new Map,n=null===(t=tz.current)||void 0===t?void 0:t.getBoundingClientRect();return tP.forEach(function(t){var a,o=t.key,c=null===(a=tz.current)||void 0===a?void 0:a.querySelector('[data-node-key="'.concat(P(o),'"]'));if(c){var r=G(c,n),i=(0,b.Z)(r,4),l=i[0],d=i[1],s=i[2],u=i[3];e.set(o,{width:l,height:d,left:s,top:u})}}),e})};(0,a.useEffect)(function(){eD()},[tP.map(function(t){return t.key}).join("_")]);var eG=O(function(){var t=H(tT),e=H(tI),n=H(tj);tU([t[0]-e[0]-n[0],t[1]-e[1]-n[1]]);var a=H(tN);t4(a),t6(H(tM));var o=H(tz);t0([o[0]-a[0],o[1]-a[1]]),eD()}),eH=tP.slice(0,ew),eW=tP.slice(e_+1),eA=[].concat((0,h.Z)(eH),(0,h.Z)(eW)),eX=ea.get(tg),eq=w({activeTabOffset:eX,horizontal:tB,indicator:tO,rtl:th}).style;(0,a.useEffect)(function(){eE()},[tg,eu,eb,R(eX),R(ea),tB]),(0,a.useEffect)(function(){eG()},[th]);var eK=!!eA.length,eF="".concat(tR,"-nav-wrap");return tB?th?(ts=tH>0,td=tH!==eb):(td=tH<0,ts=tH!==eu):(tu=tq<0,tb=tq!==eu),a.createElement(y.Z,{onResize:eG},a.createElement("div",{ref:(0,x.x1)(e,tT),role:"tablist","aria-orientation":tB?"horizontal":"vertical",className:l()("".concat(tR,"-nav"),tf),style:tv,onKeyDown:function(){ey()}},a.createElement(j,{ref:tI,position:"left",extra:ty,prefixCls:tR}),a.createElement(y.Z,{onResize:eG},a.createElement("div",{className:l()(eF,(0,s.Z)((0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(eF,"-ping-left"),td),"".concat(eF,"-ping-right"),ts),"".concat(eF,"-ping-top"),tu),"".concat(eF,"-ping-bottom"),tb)),ref:tL},a.createElement(y.Z,{onResize:eG},a.createElement("div",{ref:tz,className:"".concat(tR,"-nav-list"),style:{transform:"translate(".concat(tH,"px, ").concat(tq,"px)"),transition:eg?"none":void 0}},eB,a.createElement(I,{ref:tN,prefixCls:tR,locale:tx,editable:tk,style:(0,u.Z)((0,u.Z)({},0===eB.length?void 0:eN),{},{visibility:eK?"hidden":null})}),a.createElement("div",{className:l()("".concat(tR,"-ink-bar"),(0,s.Z)({},"".concat(tR,"-ink-bar-animated"),tm.inkBar)),style:eq}))))),a.createElement(B,(0,d.Z)({},t,{removeAriaLabel:null==tx?void 0:tx.removeAriaLabel,ref:tM,prefixCls:tR,tabs:eA,className:!eK&&es,tabMoving:!!eg})),a.createElement(j,{ref:tj,position:"right",extra:ty,prefixCls:tR})))}),X=a.forwardRef(function(t,e){var n=t.prefixCls,o=t.className,c=t.style,r=t.id,i=t.active,d=t.tabKey,s=t.children;return a.createElement("div",{id:r&&"".concat(r,"-panel-").concat(d),role:"tabpanel",tabIndex:i?0:-1,"aria-labelledby":r&&"".concat(r,"-tab-").concat(d),"aria-hidden":!i,style:c,className:l()(n,i&&"".concat(n,"-active"),o),ref:e},s)}),q=["renderTabBar"],K=["label","key"],F=function(t){var e=t.renderTabBar,n=(0,v.Z)(t,q),o=a.useContext(g).tabs;return e?e((0,u.Z)((0,u.Z)({},n),{},{panes:o.map(function(t){var e=t.label,n=t.key,o=(0,v.Z)(t,K);return a.createElement(X,(0,d.Z)({tab:e,key:n,tabKey:n},o))})}),A):a.createElement(A,n)},V=n(32467),Y=["key","forceRender","style","className","destroyInactiveTabPane"],U=function(t){var e=t.id,n=t.activeKey,o=t.animated,c=t.tabPosition,r=t.destroyInactiveTabPane,i=a.useContext(g),b=i.prefixCls,f=i.tabs,p=o.tabPane,m="".concat(b,"-tabpane");return a.createElement("div",{className:l()("".concat(b,"-content-holder"))},a.createElement("div",{className:l()("".concat(b,"-content"),"".concat(b,"-content-").concat(c),(0,s.Z)({},"".concat(b,"-content-animated"),p))},f.map(function(t){var c=t.key,i=t.forceRender,s=t.style,b=t.className,f=t.destroyInactiveTabPane,g=(0,v.Z)(t,Y),h=c===n;return a.createElement(V.ZP,(0,d.Z)({key:c,visible:h,forceRender:i,removeOnLeave:!!(r||f),leavedClassName:"".concat(m,"-hidden")},o.tabPaneMotion),function(t,n){var o=t.style,r=t.className;return a.createElement(X,(0,d.Z)({},g,{prefixCls:m,id:e,tabKey:c,animated:p,active:h,style:(0,u.Z)((0,u.Z)({},s),o),className:l()(b,r),ref:n}))})})))};n(54812);var Q=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],J=0,$=a.forwardRef(function(t,e){var n=t.id,o=t.prefixCls,c=void 0===o?"rc-tabs":o,r=t.className,i=t.items,h=t.direction,y=t.activeKey,k=t.defaultActiveKey,x=t.editable,S=t.animated,w=t.tabPosition,_=void 0===w?"top":w,E=t.tabBarGutter,C=t.tabBarStyle,O=t.tabBarExtraContent,Z=t.locale,R=t.more,P=t.destroyInactiveTabPane,T=t.renderTabBar,I=t.onChange,j=t.onTabClick,L=t.onTabScroll,z=t.getPopupContainer,M=t.popupClassName,N=t.indicator,B=(0,v.Z)(t,Q),D=a.useMemo(function(){return(i||[]).filter(function(t){return t&&"object"===(0,f.Z)(t)&&"key"in t})},[i]),G="rtl"===h,H=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{inkBar:!0,tabPane:!1};return(t=!1===e?{inkBar:!1,tabPane:!1}:!0===e?{inkBar:!0,tabPane:!1}:(0,u.Z)({inkBar:!0},"object"===(0,f.Z)(e)?e:{})).tabPaneMotion&&void 0===t.tabPane&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}(S),W=(0,a.useState)(!1),A=(0,b.Z)(W,2),X=A[0],q=A[1];(0,a.useEffect)(function(){q((0,m.Z)())},[]);var K=(0,p.Z)(function(){var t;return null===(t=D[0])||void 0===t?void 0:t.key},{value:y,defaultValue:k}),V=(0,b.Z)(K,2),Y=V[0],$=V[1],tt=(0,a.useState)(function(){return D.findIndex(function(t){return t.key===Y})}),te=(0,b.Z)(tt,2),tn=te[0],ta=te[1];(0,a.useEffect)(function(){var t,e=D.findIndex(function(t){return t.key===Y});-1===e&&(e=Math.max(0,Math.min(tn,D.length-1)),$(null===(t=D[e])||void 0===t?void 0:t.key)),ta(e)},[D.map(function(t){return t.key}).join("_"),Y,tn]);var to=(0,p.Z)(null,{value:n}),tc=(0,b.Z)(to,2),tr=tc[0],ti=tc[1];(0,a.useEffect)(function(){n||(ti("rc-tabs-".concat(J)),J+=1)},[]);var tl={id:tr,activeKey:Y,animated:H,tabPosition:_,rtl:G,mobile:X},td=(0,u.Z)((0,u.Z)({},tl),{},{editable:x,locale:Z,more:R,tabBarGutter:E,onTabClick:function(t,e){null==j||j(t,e);var n=t!==Y;$(t),n&&(null==I||I(t))},onTabScroll:L,extra:O,style:C,panes:null,getPopupContainer:z,popupClassName:M,indicator:N});return a.createElement(g.Provider,{value:{tabs:D,prefixCls:c}},a.createElement("div",(0,d.Z)({ref:e,id:n,className:l()(c,"".concat(c,"-").concat(_),(0,s.Z)((0,s.Z)((0,s.Z)({},"".concat(c,"-mobile"),X),"".concat(c,"-editable"),x),"".concat(c,"-rtl"),G),r)},B),a.createElement(F,(0,d.Z)({},td,{renderTabBar:T})),a.createElement(U,(0,d.Z)({destroyInactiveTabPane:P},tl,{animated:H}))))}),tt=n(57499),te=n(92935),tn=n(10693),ta=n(47387);let to={motionAppear:!1,motionEnter:!0,motionLeave:!0};var tc=n(79173),tr=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n},ti=n(58489),tl=n(11303),td=n(78387),ts=n(12711),tu=n(202),tb=t=>{let{componentCls:e,motionDurationSlow:n}=t;return[{[e]:{["".concat(e,"-switch")]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:"opacity ".concat(n)}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:"opacity ".concat(n)}}}}},[(0,tu.oN)(t,"slide-up"),(0,tu.oN)(t,"slide-down")]]};let tf=t=>{let{componentCls:e,tabsCardPadding:n,cardBg:a,cardGutter:o,colorBorderSecondary:c,itemSelectedColor:r}=t;return{["".concat(e,"-card")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:0,padding:n,background:a,border:"".concat((0,ti.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(c),transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut)},["".concat(e,"-tab-active")]:{color:r,background:t.colorBgContainer},["".concat(e,"-tab-focus:has(").concat(e,"-tab-btn:focus-visible)")]:(0,tl.oN)(t,-3),["& ".concat(e,"-tab").concat(e,"-tab-focus ").concat(e,"-tab-btn:focus-visible")]:{outline:"none"},["".concat(e,"-ink-bar")]:{visibility:"hidden"}},["&".concat(e,"-top, &").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginLeft:{_skip_check_:!0,value:(0,ti.bf)(o)}}}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"".concat((0,ti.bf)(t.borderRadiusLG)," ").concat((0,ti.bf)(t.borderRadiusLG)," 0 0")},["".concat(e,"-tab-active")]:{borderBottomColor:t.colorBgContainer}}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:"0 0 ".concat((0,ti.bf)(t.borderRadiusLG)," ").concat((0,ti.bf)(t.borderRadiusLG))},["".concat(e,"-tab-active")]:{borderTopColor:t.colorBgContainer}}},["&".concat(e,"-left, &").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginTop:(0,ti.bf)(o)}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ti.bf)(t.borderRadiusLG)," 0 0 ").concat((0,ti.bf)(t.borderRadiusLG))}},["".concat(e,"-tab-active")]:{borderRightColor:{_skip_check_:!0,value:t.colorBgContainer}}}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ti.bf)(t.borderRadiusLG)," ").concat((0,ti.bf)(t.borderRadiusLG)," 0")}},["".concat(e,"-tab-active")]:{borderLeftColor:{_skip_check_:!0,value:t.colorBgContainer}}}}}}},tv=t=>{let{componentCls:e,itemHoverColor:n,dropdownEdgeChildVerticalPadding:a}=t;return{["".concat(e,"-dropdown")]:Object.assign(Object.assign({},(0,tl.Wf)(t)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:t.zIndexPopup,display:"block","&-hidden":{display:"none"},["".concat(e,"-dropdown-menu")]:{maxHeight:t.tabsDropdownHeight,margin:0,padding:"".concat((0,ti.bf)(a)," 0"),overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:t.colorBgContainer,backgroundClip:"padding-box",borderRadius:t.borderRadiusLG,outline:"none",boxShadow:t.boxShadowSecondary,"&-item":Object.assign(Object.assign({},tl.vS),{display:"flex",alignItems:"center",minWidth:t.tabsDropdownWidth,margin:0,padding:"".concat((0,ti.bf)(t.paddingXXS)," ").concat((0,ti.bf)(t.paddingSM)),color:t.colorText,fontWeight:"normal",fontSize:t.fontSize,lineHeight:t.lineHeight,cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:t.marginSM},color:t.colorIcon,fontSize:t.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:n}},"&:hover":{background:t.controlItemBgHover},"&-disabled":{"&, &:hover":{color:t.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},tp=t=>{let{componentCls:e,margin:n,colorBorderSecondary:a,horizontalMargin:o,verticalItemPadding:c,verticalItemMargin:r,calc:i}=t;return{["".concat(e,"-top, ").concat(e,"-bottom")]:{flexDirection:"column",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:"".concat((0,ti.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(a),content:"''"},["".concat(e,"-ink-bar")]:{height:t.lineWidthBold,"&-animated":{transition:"width ".concat(t.motionDurationSlow,", left ").concat(t.motionDurationSlow,",\n            right ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-wrap")]:{"&::before, &::after":{top:0,bottom:0,width:t.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:t.boxShadowTabsOverflowRight},["&".concat(e,"-nav-wrap-ping-left::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-right::after")]:{opacity:1}}}},["".concat(e,"-top")]:{["> ".concat(e,"-nav,\n        > div > ").concat(e,"-nav")]:{"&::before":{bottom:0},["".concat(e,"-ink-bar")]:{bottom:0}}},["".concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,marginTop:n,marginBottom:0,"&::before":{top:0},["".concat(e,"-ink-bar")]:{top:0}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0}},["".concat(e,"-left, ").concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{flexDirection:"column",minWidth:i(t.controlHeight).mul(1.25).equal(),["".concat(e,"-tab")]:{padding:c,textAlign:"center"},["".concat(e,"-tab + ").concat(e,"-tab")]:{margin:r},["".concat(e,"-nav-wrap")]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:t.controlHeight},"&::before":{top:0,boxShadow:t.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:t.boxShadowTabsOverflowBottom},["&".concat(e,"-nav-wrap-ping-top::before")]:{opacity:1},["&".concat(e,"-nav-wrap-ping-bottom::after")]:{opacity:1}},["".concat(e,"-ink-bar")]:{width:t.lineWidthBold,"&-animated":{transition:"height ".concat(t.motionDurationSlow,", top ").concat(t.motionDurationSlow)}},["".concat(e,"-nav-list, ").concat(e,"-nav-operations")]:{flex:"1 0 auto",flexDirection:"column"}}},["".concat(e,"-left")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-ink-bar")]:{right:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{marginLeft:{_skip_check_:!0,value:(0,ti.bf)(i(t.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:"".concat((0,ti.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingLeft:{_skip_check_:!0,value:t.paddingLG}}}},["".concat(e,"-right")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{order:1,["".concat(e,"-ink-bar")]:{left:{_skip_check_:!0,value:0}}},["> ".concat(e,"-content-holder, > div > ").concat(e,"-content-holder")]:{order:0,marginRight:{_skip_check_:!0,value:i(t.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:"".concat((0,ti.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(t.colorBorder)},["> ".concat(e,"-content > ").concat(e,"-tabpane")]:{paddingRight:{_skip_check_:!0,value:t.paddingLG}}}}}},tm=t=>{let{componentCls:e,cardPaddingSM:n,cardPaddingLG:a,cardHeightSM:o,cardHeightLG:c,horizontalItemPaddingSM:r,horizontalItemPaddingLG:i}=t;return{[e]:{"&-small":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:r,fontSize:t.titleFontSizeSM}}},"&-large":{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:i,fontSize:t.titleFontSizeLG,lineHeight:t.lineHeightLG}}}},["".concat(e,"-card")]:{["&".concat(e,"-small")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:n},["".concat(e,"-nav-add")]:{minWidth:o,minHeight:o}},["&".concat(e,"-bottom")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"0 0 ".concat((0,ti.bf)(t.borderRadius)," ").concat((0,ti.bf)(t.borderRadius))}},["&".concat(e,"-top")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:"".concat((0,ti.bf)(t.borderRadius)," ").concat((0,ti.bf)(t.borderRadius)," 0 0")}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"0 ".concat((0,ti.bf)(t.borderRadius)," ").concat((0,ti.bf)(t.borderRadius)," 0")}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav ").concat(e,"-tab")]:{borderRadius:{_skip_check_:!0,value:"".concat((0,ti.bf)(t.borderRadius)," 0 0 ").concat((0,ti.bf)(t.borderRadius))}}}},["&".concat(e,"-large")]:{["> ".concat(e,"-nav")]:{["".concat(e,"-tab")]:{padding:a},["".concat(e,"-nav-add")]:{minWidth:c,minHeight:c}}}}}},tg=t=>{let{componentCls:e,itemActiveColor:n,itemHoverColor:a,iconCls:o,tabsHorizontalItemMargin:c,horizontalItemPadding:r,itemSelectedColor:i,itemColor:l}=t,d="".concat(e,"-tab");return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:r,fontSize:t.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:l,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:n}},"&-btn":{outline:"none",transition:"all ".concat(t.motionDurationSlow),["".concat(d,"-icon:not(:last-child)")]:{marginInlineEnd:t.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:t.calc(t.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:t.marginXS},color:t.colorIcon,fontSize:t.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:"all ".concat(t.motionDurationSlow),"&:hover":{color:t.colorTextHeading}},(0,tl.Qy)(t)),"&:hover":{color:a},["&".concat(d,"-active ").concat(d,"-btn")]:{color:i,textShadow:t.tabsActiveTextShadow},["&".concat(d,"-focus ").concat(d,"-btn:focus-visible")]:(0,tl.oN)(t),["&".concat(d,"-disabled")]:{color:t.colorTextDisabled,cursor:"not-allowed"},["&".concat(d,"-disabled ").concat(d,"-btn, &").concat(d,"-disabled ").concat(e,"-remove")]:{"&:focus, &:active":{color:t.colorTextDisabled}},["& ".concat(d,"-remove ").concat(o)]:{margin:0},["".concat(o,":not(:last-child)")]:{marginRight:{_skip_check_:!0,value:t.marginSM}}},["".concat(d," + ").concat(d)]:{margin:{_skip_check_:!0,value:c}}}},th=t=>{let{componentCls:e,tabsHorizontalItemMarginRTL:n,iconCls:a,cardGutter:o,calc:c}=t;return{["".concat(e,"-rtl")]:{direction:"rtl",["".concat(e,"-nav")]:{["".concat(e,"-tab")]:{margin:{_skip_check_:!0,value:n},["".concat(e,"-tab:last-of-type")]:{marginLeft:{_skip_check_:!0,value:0}},[a]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,ti.bf)(t.marginSM)}},["".concat(e,"-tab-remove")]:{marginRight:{_skip_check_:!0,value:(0,ti.bf)(t.marginXS)},marginLeft:{_skip_check_:!0,value:(0,ti.bf)(c(t.marginXXS).mul(-1).equal())},[a]:{margin:0}}}},["&".concat(e,"-left")]:{["> ".concat(e,"-nav")]:{order:1},["> ".concat(e,"-content-holder")]:{order:0}},["&".concat(e,"-right")]:{["> ".concat(e,"-nav")]:{order:0},["> ".concat(e,"-content-holder")]:{order:1}},["&".concat(e,"-card").concat(e,"-top, &").concat(e,"-card").concat(e,"-bottom")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-tab + ").concat(e,"-tab")]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},["".concat(e,"-dropdown-rtl")]:{direction:"rtl"},["".concat(e,"-menu-item")]:{["".concat(e,"-dropdown-rtl")]:{textAlign:{_skip_check_:!0,value:"right"}}}}},ty=t=>{let{componentCls:e,tabsCardPadding:n,cardHeight:a,cardGutter:o,itemHoverColor:c,itemActiveColor:r,colorBorderSecondary:i}=t;return{[e]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,tl.Wf)(t)),{display:"flex",["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{position:"relative",display:"flex",flex:"none",alignItems:"center",["".concat(e,"-nav-wrap")]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:"opacity ".concat(t.motionDurationSlow),content:"''",pointerEvents:"none"}},["".concat(e,"-nav-list")]:{position:"relative",display:"flex",transition:"opacity ".concat(t.motionDurationSlow)},["".concat(e,"-nav-operations")]:{display:"flex",alignSelf:"stretch"},["".concat(e,"-nav-operations-hidden")]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},["".concat(e,"-nav-more")]:{position:"relative",padding:n,background:"transparent",border:0,color:t.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:t.calc(t.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},["".concat(e,"-nav-add")]:Object.assign({minWidth:a,minHeight:a,marginLeft:{_skip_check_:!0,value:o},background:"transparent",border:"".concat((0,ti.bf)(t.lineWidth)," ").concat(t.lineType," ").concat(i),borderRadius:"".concat((0,ti.bf)(t.borderRadiusLG)," ").concat((0,ti.bf)(t.borderRadiusLG)," 0 0"),outline:"none",cursor:"pointer",color:t.colorText,transition:"all ".concat(t.motionDurationSlow," ").concat(t.motionEaseInOut),"&:hover":{color:c},"&:active, &:focus:not(:focus-visible)":{color:r}},(0,tl.Qy)(t,-3))},["".concat(e,"-extra-content")]:{flex:"none"},["".concat(e,"-ink-bar")]:{position:"absolute",background:t.inkBarColor,pointerEvents:"none"}}),tg(t)),{["".concat(e,"-content")]:{position:"relative",width:"100%"},["".concat(e,"-content-holder")]:{flex:"auto",minWidth:0,minHeight:0},["".concat(e,"-tabpane")]:Object.assign(Object.assign({},(0,tl.Qy)(t)),{"&-hidden":{display:"none"}})}),["".concat(e,"-centered")]:{["> ".concat(e,"-nav, > div > ").concat(e,"-nav")]:{["".concat(e,"-nav-wrap")]:{["&:not([class*='".concat(e,"-nav-wrap-ping']) > ").concat(e,"-nav-list")]:{margin:"auto"}}}}}};var tk=(0,td.I$)("Tabs",t=>{let e=(0,ts.IX)(t,{tabsCardPadding:t.cardPadding,dropdownEdgeChildVerticalPadding:t.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:"0 0 0 ".concat((0,ti.bf)(t.horizontalItemGutter)),tabsHorizontalItemMarginRTL:"0 0 0 ".concat((0,ti.bf)(t.horizontalItemGutter))});return[tm(e),th(e),tp(e),tv(e),tf(e),ty(e),tb(e)]},t=>{let{cardHeight:e,cardHeightSM:n,cardHeightLG:a,controlHeight:o,controlHeightLG:c}=t,r=e||c,i=n||o,l=a||c+8;return{zIndexPopup:t.zIndexPopupBase+50,cardBg:t.colorFillAlter,cardHeight:r,cardHeightSM:i,cardHeightLG:l,cardPadding:"".concat((r-t.fontHeight)/2-t.lineWidth,"px ").concat(t.padding,"px"),cardPaddingSM:"".concat((i-t.fontHeight)/2-t.lineWidth,"px ").concat(t.paddingXS,"px"),cardPaddingLG:"".concat((l-t.fontHeightLG)/2-t.lineWidth,"px ").concat(t.padding,"px"),titleFontSize:t.fontSize,titleFontSizeLG:t.fontSizeLG,titleFontSizeSM:t.fontSize,inkBarColor:t.colorPrimary,horizontalMargin:"0 0 ".concat(t.margin,"px 0"),horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:"".concat(t.paddingSM,"px 0"),horizontalItemPaddingSM:"".concat(t.paddingXS,"px 0"),horizontalItemPaddingLG:"".concat(t.padding,"px 0"),verticalItemPadding:"".concat(t.paddingXS,"px ").concat(t.paddingLG,"px"),verticalItemMargin:"".concat(t.margin,"px 0 0 0"),itemColor:t.colorText,itemSelectedColor:t.colorPrimary,itemHoverColor:t.colorPrimaryHover,itemActiveColor:t.colorPrimaryActive,cardGutter:t.marginXXS/2}}),tx=function(t,e){var n={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(n[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,a=Object.getOwnPropertySymbols(t);o<a.length;o++)0>e.indexOf(a[o])&&Object.prototype.propertyIsEnumerable.call(t,a[o])&&(n[a[o]]=t[a[o]]);return n};let tS=t=>{var e,n,i,d,s,u,b,f,v,p,m;let g;let{type:h,className:y,rootClassName:k,size:x,onEdit:S,hideAdd:w,centered:_,addIcon:E,removeIcon:C,moreIcon:O,more:Z,popupClassName:R,children:P,items:T,animated:I,style:j,indicatorSize:L,indicator:z,destroyInactiveTabPane:M,destroyOnHidden:N}=t,B=tx(t,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:D}=B,{direction:G,tabs:H,getPrefixCls:W,getPopupContainer:A}=a.useContext(tt.E_),X=W("tabs",D),q=(0,te.Z)(X),[K,F,V]=tk(X,q);"editable-card"===h&&(g={onEdit:(t,e)=>{let{key:n,event:a}=e;null==S||S("add"===t?a:n,t)},removeIcon:null!==(e=null!=C?C:null==H?void 0:H.removeIcon)&&void 0!==e?e:a.createElement(o.Z,null),addIcon:(null!=E?E:null==H?void 0:H.addIcon)||a.createElement(r.Z,null),showAdd:!0!==w});let Y=W(),U=(0,tn.Z)(x),Q=T?T.map(t=>{var e;let n=null!==(e=t.destroyOnHidden)&&void 0!==e?e:t.destroyInactiveTabPane;return Object.assign(Object.assign({},t),{destroyInactiveTabPane:n})}):(0,tc.Z)(P).map(t=>{if(a.isValidElement(t)){let{key:e,props:n}=t,a=n||{},{tab:o}=a,c=tr(a,["tab"]);return Object.assign(Object.assign({key:String(e)},c),{label:o})}return null}).filter(t=>t),J=function(t){let e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{inkBar:!0,tabPane:!1};return(e=!1===n?{inkBar:!1,tabPane:!1}:!0===n?{inkBar:!0,tabPane:!0}:Object.assign({inkBar:!0},"object"==typeof n?n:{})).tabPane&&(e.tabPaneMotion=Object.assign(Object.assign({},to),{motionName:(0,ta.m)(t,"switch")})),e}(X,I),ti=Object.assign(Object.assign({},null==H?void 0:H.style),j),tl={align:null!==(n=null==z?void 0:z.align)&&void 0!==n?n:null===(i=null==H?void 0:H.indicator)||void 0===i?void 0:i.align,size:null!==(b=null!==(s=null!==(d=null==z?void 0:z.size)&&void 0!==d?d:L)&&void 0!==s?s:null===(u=null==H?void 0:H.indicator)||void 0===u?void 0:u.size)&&void 0!==b?b:null==H?void 0:H.indicatorSize};return K(a.createElement($,Object.assign({direction:G,getPopupContainer:A},B,{items:Q,className:l()({["".concat(X,"-").concat(U)]:U,["".concat(X,"-card")]:["card","editable-card"].includes(h),["".concat(X,"-editable-card")]:"editable-card"===h,["".concat(X,"-centered")]:_},null==H?void 0:H.className,y,k,F,V,q),popupClassName:l()(R,F,V,q),style:ti,editable:g,more:Object.assign({icon:null!==(m=null!==(p=null!==(v=null===(f=null==H?void 0:H.more)||void 0===f?void 0:f.icon)&&void 0!==v?v:null==H?void 0:H.moreIcon)&&void 0!==p?p:O)&&void 0!==m?m:a.createElement(c.Z,null),transitionName:"".concat(Y,"-slide-up")},Z),prefixCls:X,animated:J,indicator:tl,destroyInactiveTabPane:null!=N?N:M})))};tS.TabPane=()=>null;var tw=tS}}]);