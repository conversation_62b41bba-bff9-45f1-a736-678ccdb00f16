/**
 * Ant Design样式工具函数测试
 */

import { 
  styleHelpers, 
  tailwindToStyle, 
  mergeStyles, 
  conditionalStyle,
  type SpacingSize,
  type ShadowSize,
  type BorderRadiusSize 
} from '../antdHelpers'

describe('styleHelpers', () => {
  describe('spacing', () => {
    it('应该包含正确的间距值', () => {
      expect(styleHelpers.spacing.xs).toBe(4)
      expect(styleHelpers.spacing.sm).toBe(8)
      expect(styleHelpers.spacing.md).toBe(16)
      expect(styleHelpers.spacing.lg).toBe(24)
      expect(styleHelpers.spacing.xl).toBe(32)
      expect(styleHelpers.spacing.xxl).toBe(48)
    })
  })

  describe('shadows', () => {
    it('应该包含正确的阴影值', () => {
      expect(styleHelpers.shadows.sm).toBe('0 1px 2px 0 rgba(0, 0, 0, 0.05)')
      expect(styleHelpers.shadows.md).toBe('0 4px 6px -1px rgba(0, 0, 0, 0.1)')
      expect(styleHelpers.shadows.lg).toBe('0 10px 15px -3px rgba(0, 0, 0, 0.1)')
      expect(styleHelpers.shadows.xl).toBe('0 20px 25px -5px rgba(0, 0, 0, 0.1)')
    })
  })

  describe('borderRadius', () => {
    it('应该包含正确的圆角值', () => {
      expect(styleHelpers.borderRadius.sm).toBe(4)
      expect(styleHelpers.borderRadius.md).toBe(8)
      expect(styleHelpers.borderRadius.lg).toBe(12)
      expect(styleHelpers.borderRadius.xl).toBe(16)
    })
  })

  describe('colors', () => {
    it('应该包含正确的颜色值', () => {
      expect(styleHelpers.colors.primary[500]).toBe('#0ea5e9')
      expect(styleHelpers.colors.gray[50]).toBe('#f9fafb')
      expect(styleHelpers.colors.success).toBe('#10b981')
      expect(styleHelpers.colors.error).toBe('#ef4444')
    })
  })

  describe('createSpacing', () => {
    it('应该生成正确的padding样式', () => {
      const result = styleHelpers.createSpacing('md')
      expect(result).toEqual({ padding: 16 })
    })

    it('应该支持所有间距尺寸', () => {
      const sizes: SpacingSize[] = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl']
      sizes.forEach(size => {
        const result = styleHelpers.createSpacing(size)
        expect(result.padding).toBe(styleHelpers.spacing[size])
      })
    })
  })

  describe('createMargin', () => {
    it('应该生成正确的margin样式', () => {
      const result = styleHelpers.createMargin('lg')
      expect(result).toEqual({ margin: 24 })
    })
  })

  describe('createShadow', () => {
    it('应该生成正确的阴影样式', () => {
      const result = styleHelpers.createShadow('md')
      expect(result).toEqual({ boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' })
    })

    it('应该支持所有阴影尺寸', () => {
      const sizes: ShadowSize[] = ['sm', 'md', 'lg', 'xl']
      sizes.forEach(size => {
        const result = styleHelpers.createShadow(size)
        expect(result.boxShadow).toBe(styleHelpers.shadows[size])
      })
    })
  })

  describe('createBorderRadius', () => {
    it('应该生成正确的圆角样式', () => {
      const result = styleHelpers.createBorderRadius('lg')
      expect(result).toEqual({ borderRadius: 12 })
    })

    it('应该支持所有圆角尺寸', () => {
      const sizes: BorderRadiusSize[] = ['sm', 'md', 'lg', 'xl']
      sizes.forEach(size => {
        const result = styleHelpers.createBorderRadius(size)
        expect(result.borderRadius).toBe(styleHelpers.borderRadius[size])
      })
    })
  })

  describe('预定义样式', () => {
    it('cardStyle应该包含正确的样式', () => {
      expect(styleHelpers.cardStyle).toEqual({
        borderRadius: 12,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        background: '#ffffff',
        border: '1px solid #f0f0f0',
      })
    })

    it('buttonStyle应该包含正确的样式', () => {
      expect(styleHelpers.buttonStyle).toEqual({
        borderRadius: 8,
        transition: 'all 0.2s ease-in-out',
      })
    })

    it('layoutStyle应该包含正确的样式', () => {
      expect(styleHelpers.layoutStyle).toEqual({
        minHeight: '100vh',
        background: '#f9fafb',
      })
    })
  })
})

describe('tailwindToStyle', () => {
  it('应该正确转换间距类名', () => {
    expect(tailwindToStyle['p-1']).toEqual({ padding: 4 })
    expect(tailwindToStyle['p-2']).toEqual({ padding: 8 })
    expect(tailwindToStyle['p-4']).toEqual({ padding: 16 })
    expect(tailwindToStyle['m-1']).toEqual({ margin: 4 })
  })

  it('应该正确转换布局类名', () => {
    expect(tailwindToStyle['min-h-screen']).toEqual({ minHeight: '100vh' })
    expect(tailwindToStyle['h-full']).toEqual({ height: '100%' })
    expect(tailwindToStyle['w-full']).toEqual({ width: '100%' })
  })

  it('应该正确转换背景色类名', () => {
    expect(tailwindToStyle['bg-white']).toEqual({ background: '#ffffff' })
    expect(tailwindToStyle['bg-gray-50']).toEqual({ background: '#f9fafb' })
  })

  it('应该正确转换阴影类名', () => {
    expect(tailwindToStyle['shadow-sm']).toEqual({ boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)' })
    expect(tailwindToStyle['shadow-md']).toEqual({ boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)' })
  })

  it('应该正确转换圆角类名', () => {
    expect(tailwindToStyle['rounded']).toEqual({ borderRadius: 4 })
    expect(tailwindToStyle['rounded-md']).toEqual({ borderRadius: 8 })
    expect(tailwindToStyle['rounded-lg']).toEqual({ borderRadius: 12 })
  })
})

describe('mergeStyles', () => {
  it('应该合并多个样式对象', () => {
    const style1 = { color: 'red', fontSize: 16 }
    const style2 = { backgroundColor: 'blue', fontSize: 18 }
    const result = mergeStyles(style1, style2)
    
    expect(result).toEqual({
      color: 'red',
      fontSize: 18, // 后面的值应该覆盖前面的
      backgroundColor: 'blue',
    })
  })

  it('应该过滤掉undefined值', () => {
    const style1 = { color: 'red' }
    const style2 = undefined
    const style3 = { fontSize: 16 }
    const result = mergeStyles(style1, style2, style3)
    
    expect(result).toEqual({
      color: 'red',
      fontSize: 16,
    })
  })

  it('应该处理空参数', () => {
    const result = mergeStyles()
    expect(result).toEqual({})
  })
})

describe('conditionalStyle', () => {
  it('当条件为true时应该返回trueStyle', () => {
    const trueStyle = { color: 'red' }
    const falseStyle = { color: 'blue' }
    const result = conditionalStyle(true, trueStyle, falseStyle)
    
    expect(result).toEqual(trueStyle)
  })

  it('当条件为false时应该返回falseStyle', () => {
    const trueStyle = { color: 'red' }
    const falseStyle = { color: 'blue' }
    const result = conditionalStyle(false, trueStyle, falseStyle)
    
    expect(result).toEqual(falseStyle)
  })

  it('当条件为false且没有falseStyle时应该返回空对象', () => {
    const trueStyle = { color: 'red' }
    const result = conditionalStyle(false, trueStyle)
    
    expect(result).toEqual({})
  })

  it('应该处理复杂的样式对象', () => {
    const trueStyle = { 
      color: 'red', 
      fontSize: 16, 
      padding: 8,
      borderRadius: 4 
    }
    const result = conditionalStyle(true, trueStyle)
    
    expect(result).toEqual(trueStyle)
  })
})
