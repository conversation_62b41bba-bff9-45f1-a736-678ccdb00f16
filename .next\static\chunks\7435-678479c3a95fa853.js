"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7435],{58405:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(13428),a=n(2265),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},r=n(46614),i=a.forwardRef(function(e,t){return a.createElement(r.Z,(0,o.Z)({},e,{ref:t,icon:c}))})},26715:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(13428),a=n(2265),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M724 218.3V141c0-6.7-7.7-10.4-12.9-6.3L260.3 486.8a31.86 31.86 0 000 50.3l450.8 352.1c5.3 4.1 12.9.4 12.9-6.3v-77.3c0-4.9-2.3-9.6-6.1-12.6l-360-281 360-281.1c3.8-3 6.1-7.7 6.1-12.6z"}}]},name:"left",theme:"outlined"},r=n(46614),i=a.forwardRef(function(e,t){return a.createElement(r.Z,(0,o.Z)({},e,{ref:t,icon:c}))})},71773:function(e,t,n){n.d(t,{Z:function(){return i}});var o=n(13428),a=n(2265),c={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"}}]},name:"right",theme:"outlined"},r=n(46614),i=a.forwardRef(function(e,t){return a.createElement(r.Z,(0,o.Z)({},e,{ref:t,icon:c}))})},21467:function(e,t,n){n.d(t,{i:function(){return i}});var o=n(2265),a=n(73310),c=n(13292),r=n(57499);function i(e){return t=>o.createElement(c.ZP,{theme:{token:{motion:!1,zIndexPopupBase:0}}},o.createElement(e,Object.assign({},t)))}t.Z=(e,t,n,c,l)=>i(i=>{let{prefixCls:s,style:d}=i,u=o.useRef(null),[m,p]=o.useState(0),[g,b]=o.useState(0),[f,v]=(0,a.Z)(!1,{value:i.open}),{getPrefixCls:h}=o.useContext(r.E_),y=h(c||"select",s);o.useEffect(()=>{if(v(!0),"undefined"!=typeof ResizeObserver){let e=new ResizeObserver(e=>{let t=e[0].target;p(t.offsetHeight+8),b(t.offsetWidth)}),t=setInterval(()=>{var n;let o=l?".".concat(l(y)):".".concat(y,"-dropdown"),a=null===(n=u.current)||void 0===n?void 0:n.querySelector(o);a&&(clearInterval(t),e.observe(a))},10);return()=>{clearInterval(t),e.disconnect()}}},[]);let I=Object.assign(Object.assign({},i),{style:Object.assign(Object.assign({},d),{margin:0}),open:f,visible:f,getPopupContainer:()=>u.current});return n&&(I=n(I)),t&&Object.assign(I,{[t]:{overflow:{adjustX:!1,adjustY:!1}}}),o.createElement("div",{ref:u,style:{paddingBottom:m,position:"relative",minWidth:g}},o.createElement(e,Object.assign({},I)))})},45284:function(e,t,n){n.d(t,{Z:function(){return _}});var o=n(2265),a=n(26715),c=n(71773),r=n(42744),i=n.n(r),l=n(23803),s=n(28788),d=n(73310),u=n(54925),m=n(51761),p=e=>"object"!=typeof e&&"function"!=typeof e||null===e,g=n(67966),b=n(21467),f=n(65823),v=n(76564),h=n(86718),y=n(57499),I=n(92935),w=n(80311),x=n(81642),C=n(18987),O=n(58489),S=n(11303),B=n(202),E=n(25926),k=n(58854),j=n(89869),z=n(2638),H=n(78387),T=n(12711),N=e=>{let{componentCls:t,menuCls:n,colorError:o,colorTextLightSolid:a}=e,c="".concat(n,"-item");return{["".concat(t,", ").concat(t,"-menu-submenu")]:{["".concat(n," ").concat(c)]:{["&".concat(c,"-danger:not(").concat(c,"-disabled)")]:{color:o,"&:hover":{color:a,backgroundColor:o}}}}}};let R=e=>{let{componentCls:t,menuCls:n,zIndexPopup:o,dropdownArrowDistance:a,sizePopupArrow:c,antCls:r,iconCls:i,motionDurationMid:l,paddingBlock:s,fontSize:d,dropdownEdgeChildPadding:u,colorTextDisabled:m,fontSizeIcon:p,controlPaddingHorizontal:g,colorBgElevated:b}=e;return[{[t]:{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:o,display:"block","&::before":{position:"absolute",insetBlock:e.calc(c).div(2).sub(a).equal(),zIndex:-9999,opacity:1e-4,content:'""'},"&-menu-vertical":{maxHeight:"100vh",overflowY:"auto"},["&-trigger".concat(r,"-btn")]:{["& > ".concat(i,"-down, & > ").concat(r,"-btn-icon > ").concat(i,"-down")]:{fontSize:p}},["".concat(t,"-wrap")]:{position:"relative",["".concat(r,"-btn > ").concat(i,"-down")]:{fontSize:p},["".concat(i,"-down::before")]:{transition:"transform ".concat(l)}},["".concat(t,"-wrap-open")]:{["".concat(i,"-down::before")]:{transform:"rotate(180deg)"}},"\n        &-hidden,\n        &-menu-hidden,\n        &-menu-submenu-hidden\n      ":{display:"none"},["&".concat(r,"-slide-down-enter").concat(r,"-slide-down-enter-active").concat(t,"-placement-bottomLeft,\n          &").concat(r,"-slide-down-appear").concat(r,"-slide-down-appear-active").concat(t,"-placement-bottomLeft,\n          &").concat(r,"-slide-down-enter").concat(r,"-slide-down-enter-active").concat(t,"-placement-bottom,\n          &").concat(r,"-slide-down-appear").concat(r,"-slide-down-appear-active").concat(t,"-placement-bottom,\n          &").concat(r,"-slide-down-enter").concat(r,"-slide-down-enter-active").concat(t,"-placement-bottomRight,\n          &").concat(r,"-slide-down-appear").concat(r,"-slide-down-appear-active").concat(t,"-placement-bottomRight")]:{animationName:B.fJ},["&".concat(r,"-slide-up-enter").concat(r,"-slide-up-enter-active").concat(t,"-placement-topLeft,\n          &").concat(r,"-slide-up-appear").concat(r,"-slide-up-appear-active").concat(t,"-placement-topLeft,\n          &").concat(r,"-slide-up-enter").concat(r,"-slide-up-enter-active").concat(t,"-placement-top,\n          &").concat(r,"-slide-up-appear").concat(r,"-slide-up-appear-active").concat(t,"-placement-top,\n          &").concat(r,"-slide-up-enter").concat(r,"-slide-up-enter-active").concat(t,"-placement-topRight,\n          &").concat(r,"-slide-up-appear").concat(r,"-slide-up-appear-active").concat(t,"-placement-topRight")]:{animationName:B.Qt},["&".concat(r,"-slide-down-leave").concat(r,"-slide-down-leave-active").concat(t,"-placement-bottomLeft,\n          &").concat(r,"-slide-down-leave").concat(r,"-slide-down-leave-active").concat(t,"-placement-bottom,\n          &").concat(r,"-slide-down-leave").concat(r,"-slide-down-leave-active").concat(t,"-placement-bottomRight")]:{animationName:B.Uw},["&".concat(r,"-slide-up-leave").concat(r,"-slide-up-leave-active").concat(t,"-placement-topLeft,\n          &").concat(r,"-slide-up-leave").concat(r,"-slide-up-leave-active").concat(t,"-placement-top,\n          &").concat(r,"-slide-up-leave").concat(r,"-slide-up-leave-active").concat(t,"-placement-topRight")]:{animationName:B.ly}}},(0,j.ZP)(e,b,{arrowPlacement:{top:!0,bottom:!0}}),{["".concat(t," ").concat(n)]:{position:"relative",margin:0},["".concat(n,"-submenu-popup")]:{position:"absolute",zIndex:o,background:"transparent",boxShadow:"none",transformOrigin:"0 0","ul, li":{listStyle:"none",margin:0}},["".concat(t,", ").concat(t,"-menu-submenu")]:Object.assign(Object.assign({},(0,S.Wf)(e)),{[n]:Object.assign(Object.assign({padding:u,listStyleType:"none",backgroundColor:b,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary},(0,S.Qy)(e)),{"&:empty":{padding:0,boxShadow:"none"},["".concat(n,"-item-group-title")]:{padding:"".concat((0,O.bf)(s)," ").concat((0,O.bf)(g)),color:e.colorTextDescription,transition:"all ".concat(l)},["".concat(n,"-item")]:{position:"relative",display:"flex",alignItems:"center"},["".concat(n,"-item-icon")]:{minWidth:d,marginInlineEnd:e.marginXS,fontSize:e.fontSizeSM},["".concat(n,"-title-content")]:{flex:"auto","&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},"> a":{color:"inherit",transition:"all ".concat(l),"&:hover":{color:"inherit"},"&::after":{position:"absolute",inset:0,content:'""'}},["".concat(n,"-item-extra")]:{paddingInlineStart:e.padding,marginInlineStart:"auto",fontSize:e.fontSizeSM,color:e.colorTextDescription}},["".concat(n,"-item, ").concat(n,"-submenu-title")]:Object.assign(Object.assign({display:"flex",margin:0,padding:"".concat((0,O.bf)(s)," ").concat((0,O.bf)(g)),color:e.colorText,fontWeight:"normal",fontSize:d,lineHeight:e.lineHeight,cursor:"pointer",transition:"all ".concat(l),borderRadius:e.borderRadiusSM,"&:hover, &-active":{backgroundColor:e.controlItemBgHover}},(0,S.Qy)(e)),{"&-selected":{color:e.colorPrimary,backgroundColor:e.controlItemBgActive,"&:hover, &-active":{backgroundColor:e.controlItemBgActiveHover}},"&-disabled":{color:m,cursor:"not-allowed","&:hover":{color:m,backgroundColor:b,cursor:"not-allowed"},a:{pointerEvents:"none"}},"&-divider":{height:1,margin:"".concat((0,O.bf)(e.marginXXS)," 0"),overflow:"hidden",lineHeight:0,backgroundColor:e.colorSplit},["".concat(t,"-menu-submenu-expand-icon")]:{position:"absolute",insetInlineEnd:e.paddingXS,["".concat(t,"-menu-submenu-arrow-icon")]:{marginInlineEnd:"0 !important",color:e.colorIcon,fontSize:p,fontStyle:"normal"}}}),["".concat(n,"-item-group-list")]:{margin:"0 ".concat((0,O.bf)(e.marginXS)),padding:0,listStyle:"none"},["".concat(n,"-submenu-title")]:{paddingInlineEnd:e.calc(g).add(e.fontSizeSM).equal()},["".concat(n,"-submenu-vertical")]:{position:"relative"},["".concat(n,"-submenu").concat(n,"-submenu-disabled ").concat(t,"-menu-submenu-title")]:{["&, ".concat(t,"-menu-submenu-arrow-icon")]:{color:m,backgroundColor:b,cursor:"not-allowed"}},["".concat(n,"-submenu-selected ").concat(t,"-menu-submenu-title")]:{color:e.colorPrimary}})})},[(0,B.oN)(e,"slide-up"),(0,B.oN)(e,"slide-down"),(0,E.Fm)(e,"move-up"),(0,E.Fm)(e,"move-down"),(0,k._y)(e,"zoom-big")]]};var P=(0,H.I$)("Dropdown",e=>{let{marginXXS:t,sizePopupArrow:n,paddingXXS:o,componentCls:a}=e,c=(0,T.IX)(e,{menuCls:"".concat(a,"-menu"),dropdownArrowDistance:e.calc(n).div(2).add(t).equal(),dropdownEdgeChildPadding:o});return[R(c),N(c)]},e=>Object.assign(Object.assign({zIndexPopup:e.zIndexPopupBase+50,paddingBlock:(e.controlHeight-e.fontSize*e.lineHeight)/2},(0,j.wZ)({contentRadius:e.borderRadiusLG,limitVerticalRadius:!0})),(0,z.w)(e)),{resetStyle:!1});let Z=e=>{var t;let{menu:n,arrow:r,prefixCls:b,children:O,trigger:S,disabled:B,dropdownRender:E,popupRender:k,getPopupContainer:j,overlayClassName:z,rootClassName:H,overlayStyle:T,open:N,onOpenChange:R,visible:Z,onVisibleChange:M,mouseEnterDelay:D=.15,mouseLeaveDelay:A=.1,autoAdjustOverflow:W=!0,placement:L="",overlay:X,transitionName:q,destroyOnHidden:_,destroyPopupOnHide:F}=e,{getPopupContainer:Y,getPrefixCls:V,direction:G,dropdown:K}=o.useContext(y.E_),U=k||E;(0,v.ln)("Dropdown");let $=o.useMemo(()=>{let e=V();return void 0!==q?q:L.includes("top")?"".concat(e,"-slide-down"):"".concat(e,"-slide-up")},[V,L,q]),J=o.useMemo(()=>L?L.includes("Center")?L.slice(0,L.indexOf("Center")):L:"rtl"===G?"bottomRight":"bottomLeft",[L,G]),Q=V("dropdown",b),ee=(0,I.Z)(Q),[et,en,eo]=P(Q,ee),[,ea]=(0,C.ZP)(),ec=o.Children.only(p(O)?o.createElement("span",null,O):O),er=(0,f.Tm)(ec,{className:i()("".concat(Q,"-trigger"),{["".concat(Q,"-rtl")]:"rtl"===G},ec.props.className),disabled:null!==(t=ec.props.disabled)&&void 0!==t?t:B}),ei=B?[]:S,el=!!(null==ei?void 0:ei.includes("contextMenu")),[es,ed]=(0,d.Z)(!1,{value:null!=N?N:Z}),eu=(0,s.Z)(e=>{null==R||R(e,{source:"trigger"}),null==M||M(e),ed(e)}),em=i()(z,H,en,eo,ee,null==K?void 0:K.className,{["".concat(Q,"-rtl")]:"rtl"===G}),ep=(0,g.Z)({arrowPointAtCenter:"object"==typeof r&&r.pointAtCenter,autoAdjustOverflow:W,offset:ea.marginXXS,arrowWidth:r?ea.sizePopupArrow:0,borderRadius:ea.borderRadius}),eg=o.useCallback(()=>{null!=n&&n.selectable&&null!=n&&n.multiple||(null==R||R(!1,{source:"menu"}),ed(!1))},[null==n?void 0:n.selectable,null==n?void 0:n.multiple]),[eb,ef]=(0,m.Cn)("Dropdown",null==T?void 0:T.zIndex),ev=o.createElement(l.Z,Object.assign({alignPoint:el},(0,u.Z)(e,["rootClassName"]),{mouseEnterDelay:D,mouseLeaveDelay:A,visible:es,builtinPlacements:ep,arrow:!!r,overlayClassName:em,prefixCls:Q,getPopupContainer:j||Y,transitionName:$,trigger:ei,overlay:()=>{let e;return e=(null==n?void 0:n.items)?o.createElement(w.Z,Object.assign({},n)):"function"==typeof X?X():X,U&&(e=U(e)),e=o.Children.only("string"==typeof e?o.createElement("span",null,e):e),o.createElement(x.J,{prefixCls:"".concat(Q,"-menu"),rootClassName:i()(eo,ee),expandIcon:o.createElement("span",{className:"".concat(Q,"-menu-submenu-arrow")},"rtl"===G?o.createElement(a.Z,{className:"".concat(Q,"-menu-submenu-arrow-icon")}):o.createElement(c.Z,{className:"".concat(Q,"-menu-submenu-arrow-icon")})),mode:"vertical",selectable:!1,onClick:eg,validator:e=>{let{mode:t}=e}},e)},placement:J,onVisibleChange:eu,overlayStyle:Object.assign(Object.assign(Object.assign({},null==K?void 0:K.style),T),{zIndex:eb}),autoDestroy:null!=_?_:F}),er);return eb&&(ev=o.createElement(h.Z.Provider,{value:ef},ev)),et(ev)},M=(0,b.Z)(Z,"align",void 0,"dropdown",e=>e);Z._InternalPanelDoNotUseOrYouWillBeFired=e=>o.createElement(M,Object.assign({},e),o.createElement("span",null));var D=n(51842),A=n(94734),W=n(65270),L=n(92801),X=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let q=e=>{let{getPopupContainer:t,getPrefixCls:n,direction:a}=o.useContext(y.E_),{prefixCls:c,type:r="default",danger:l,disabled:s,loading:d,onClick:u,htmlType:m,children:p,className:g,menu:b,arrow:f,autoFocus:v,overlay:h,trigger:I,align:w,open:x,onOpenChange:C,placement:O,getPopupContainer:S,href:B,icon:E=o.createElement(D.Z,null),title:k,buttonsRender:j=e=>e,mouseEnterDelay:z,mouseLeaveDelay:H,overlayClassName:T,overlayStyle:N,destroyOnHidden:R,destroyPopupOnHide:P,dropdownRender:M,popupRender:q}=e,_=X(e,["prefixCls","type","danger","disabled","loading","onClick","htmlType","children","className","menu","arrow","autoFocus","overlay","trigger","align","open","onOpenChange","placement","getPopupContainer","href","icon","title","buttonsRender","mouseEnterDelay","mouseLeaveDelay","overlayClassName","overlayStyle","destroyOnHidden","destroyPopupOnHide","dropdownRender","popupRender"]),F=n("dropdown",c),Y={menu:b,arrow:f,autoFocus:v,align:w,disabled:s,trigger:s?[]:I,onOpenChange:C,getPopupContainer:S||t,mouseEnterDelay:z,mouseLeaveDelay:H,overlayClassName:T,overlayStyle:N,destroyOnHidden:R,popupRender:q||M},{compactSize:V,compactItemClassnames:G}=(0,L.ri)(F,a),K=i()("".concat(F,"-button"),G,g);"destroyPopupOnHide"in e&&(Y.destroyPopupOnHide=P),"overlay"in e&&(Y.overlay=h),"open"in e&&(Y.open=x),"placement"in e?Y.placement=O:Y.placement="rtl"===a?"bottomLeft":"bottomRight";let[U,$]=j([o.createElement(A.ZP,{type:r,danger:l,disabled:s,loading:d,onClick:u,htmlType:m,href:B,title:k},p),o.createElement(A.ZP,{type:r,danger:l,icon:E})]);return o.createElement(W.Z.Compact,Object.assign({className:K,size:V,block:!0},_),U,o.createElement(Z,Object.assign({},Y),$))};q.__ANT_BUTTON=!0,Z.Button=q;var _=Z},24118:function(e,t,n){let o;n.d(t,{D:function(){return S},Z:function(){return E}});var a=n(2265),c=n(13428),r={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M912 192H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 284H328c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h584c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM104 228a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0zm0 284a56 56 0 10112 0 56 56 0 10-112 0z"}}]},name:"bars",theme:"outlined"},i=n(46614),l=a.forwardRef(function(e,t){return a.createElement(i.Z,(0,c.Z)({},e,{ref:t,icon:r}))}),s=n(26715),d=n(71773),u=n(42744),m=n.n(u),p=n(54925),g=n(16060),b=n(57499),f=n(31747),v=n(58489),h=n(13191),y=n(78387);let I=e=>{let{componentCls:t,siderBg:n,motionDurationMid:o,motionDurationSlow:a,antCls:c,triggerHeight:r,triggerColor:i,triggerBg:l,headerHeight:s,zeroTriggerWidth:d,zeroTriggerHeight:u,borderRadiusLG:m,lightSiderBg:p,lightTriggerColor:g,lightTriggerBg:b,bodyBg:f}=e;return{[t]:{position:"relative",minWidth:0,background:n,transition:"all ".concat(o,", background 0s"),"&-has-trigger":{paddingBottom:r},"&-right":{order:1},["".concat(t,"-children")]:{height:"100%",marginTop:-.1,paddingTop:.1,["".concat(c,"-menu").concat(c,"-menu-inline-collapsed")]:{width:"auto"}},["&-zero-width ".concat(t,"-children")]:{overflow:"hidden"},["".concat(t,"-trigger")]:{position:"fixed",bottom:0,zIndex:1,height:r,color:i,lineHeight:(0,v.bf)(r),textAlign:"center",background:l,cursor:"pointer",transition:"all ".concat(o)},["".concat(t,"-zero-width-trigger")]:{position:"absolute",top:s,insetInlineEnd:e.calc(d).mul(-1).equal(),zIndex:1,width:d,height:u,color:i,fontSize:e.fontSizeXL,display:"flex",alignItems:"center",justifyContent:"center",background:n,borderRadius:"0 ".concat((0,v.bf)(m)," ").concat((0,v.bf)(m)," 0"),cursor:"pointer",transition:"background ".concat(a," ease"),"&::after":{position:"absolute",inset:0,background:"transparent",transition:"all ".concat(a),content:'""'},"&:hover::after":{background:"rgba(255, 255, 255, 0.2)"},"&-right":{insetInlineStart:e.calc(d).mul(-1).equal(),borderRadius:"".concat((0,v.bf)(m)," 0 0 ").concat((0,v.bf)(m))}},"&-light":{background:p,["".concat(t,"-trigger")]:{color:g,background:b},["".concat(t,"-zero-width-trigger")]:{color:g,background:b,border:"1px solid ".concat(f),borderInlineStart:0}}}}};var w=(0,y.I$)(["Layout","Sider"],e=>[I(e)],h.eh,{deprecatedTokens:h.jn}),x=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let C={xs:"479.98px",sm:"575.98px",md:"767.98px",lg:"991.98px",xl:"1199.98px",xxl:"1599.98px"},O=e=>!Number.isNaN(Number.parseFloat(e))&&isFinite(e),S=a.createContext({}),B=(o=0,function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return o+=1,"".concat(e).concat(o)});var E=a.forwardRef((e,t)=>{let{prefixCls:n,className:o,trigger:c,children:r,defaultCollapsed:i=!1,theme:u="dark",style:v={},collapsible:h=!1,reverseArrow:y=!1,width:I=200,collapsedWidth:E=80,zeroWidthTriggerStyle:k,breakpoint:j,onCollapse:z,onBreakpoint:H}=e,T=x(e,["prefixCls","className","trigger","children","defaultCollapsed","theme","style","collapsible","reverseArrow","width","collapsedWidth","zeroWidthTriggerStyle","breakpoint","onCollapse","onBreakpoint"]),{siderHook:N}=(0,a.useContext)(f.V),[R,P]=(0,a.useState)("collapsed"in e?e.collapsed:i),[Z,M]=(0,a.useState)(!1);(0,a.useEffect)(()=>{"collapsed"in e&&P(e.collapsed)},[e.collapsed]);let D=(t,n)=>{"collapsed"in e||P(t),null==z||z(t,n)},{getPrefixCls:A,direction:W}=(0,a.useContext)(b.E_),L=A("layout-sider",n),[X,q,_]=w(L),F=(0,a.useRef)(null);F.current=e=>{M(e.matches),null==H||H(e.matches),R!==e.matches&&D(e.matches,"responsive")},(0,a.useEffect)(()=>{let e;function t(e){var t;return null===(t=F.current)||void 0===t?void 0:t.call(F,e)}return void 0!==(null==window?void 0:window.matchMedia)&&j&&j in C&&(e=window.matchMedia("screen and (max-width: ".concat(C[j],")")),(0,g.x)(e,t),t(e)),()=>{(0,g.h)(e,t)}},[j]),(0,a.useEffect)(()=>{let e=B("ant-sider-");return N.addSider(e),()=>N.removeSider(e)},[]);let Y=()=>{D(!R,"clickTrigger")},V=(0,p.Z)(T,["collapsed"]),G=R?E:I,K=O(G)?"".concat(G,"px"):String(G),U=0===parseFloat(String(E||0))?a.createElement("span",{onClick:Y,className:m()("".concat(L,"-zero-width-trigger"),"".concat(L,"-zero-width-trigger-").concat(y?"right":"left")),style:k},c||a.createElement(l,null)):null,$="rtl"===W==!y,J={expanded:$?a.createElement(d.Z,null):a.createElement(s.Z,null),collapsed:$?a.createElement(s.Z,null):a.createElement(d.Z,null)}[R?"collapsed":"expanded"],Q=null!==c?U||a.createElement("div",{className:"".concat(L,"-trigger"),onClick:Y,style:{width:K}},c||J):null,ee=Object.assign(Object.assign({},v),{flex:"0 0 ".concat(K),maxWidth:K,minWidth:K,width:K}),et=m()(L,"".concat(L,"-").concat(u),{["".concat(L,"-collapsed")]:!!R,["".concat(L,"-has-trigger")]:h&&null!==c&&!U,["".concat(L,"-below")]:!!Z,["".concat(L,"-zero-width")]:0===parseFloat(K)},o,q,_),en=a.useMemo(()=>({siderCollapsed:R}),[R]);return X(a.createElement(S.Provider,{value:en},a.createElement("aside",Object.assign({className:et},V,{style:ee,ref:t}),a.createElement("div",{className:"".concat(L,"-children")},r),h||Z&&U?Q:null)))})},31747:function(e,t,n){n.d(t,{V:function(){return o}});let o=n(2265).createContext({siderHook:{addSider:()=>null,removeSider:()=>null}})},13191:function(e,t,n){n.d(t,{eh:function(){return r},jn:function(){return i}});var o=n(58489),a=n(78387);let c=e=>{let{antCls:t,componentCls:n,colorText:a,footerBg:c,headerHeight:r,headerPadding:i,headerColor:l,footerPadding:s,fontSize:d,bodyBg:u,headerBg:m}=e;return{[n]:{display:"flex",flex:"auto",flexDirection:"column",minHeight:0,background:u,"&, *":{boxSizing:"border-box"},["&".concat(n,"-has-sider")]:{flexDirection:"row",["> ".concat(n,", > ").concat(n,"-content")]:{width:0}},["".concat(n,"-header, &").concat(n,"-footer")]:{flex:"0 0 auto"},"&-rtl":{direction:"rtl"}},["".concat(n,"-header")]:{height:r,padding:i,color:l,lineHeight:(0,o.bf)(r),background:m,["".concat(t,"-menu")]:{lineHeight:"inherit"}},["".concat(n,"-footer")]:{padding:s,color:a,fontSize:d,background:c},["".concat(n,"-content")]:{flex:"auto",color:a,minHeight:0}}},r=e=>{let{colorBgLayout:t,controlHeight:n,controlHeightLG:o,colorText:a,controlHeightSM:c,marginXXS:r,colorTextLightSolid:i,colorBgContainer:l}=e,s=1.25*o;return{colorBgHeader:"#001529",colorBgBody:t,colorBgTrigger:"#002140",bodyBg:t,headerBg:"#001529",headerHeight:2*n,headerPadding:"0 ".concat(s,"px"),headerColor:a,footerPadding:"".concat(c,"px ").concat(s,"px"),footerBg:t,siderBg:"#001529",triggerHeight:o+2*r,triggerBg:"#002140",triggerColor:i,zeroTriggerWidth:o,zeroTriggerHeight:o,lightSiderBg:l,lightTriggerBg:l,lightTriggerColor:a}},i=[["colorBgBody","bodyBg"],["colorBgHeader","headerBg"],["colorBgTrigger","triggerBg"]];t.ZP=(0,a.I$)("Layout",e=>[c(e)],r,{deprecatedTokens:i})},81642:function(e,t,n){n.d(t,{J:function(){return l}});var o=n(2265),a=n(17146),c=n(59888),r=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};let i=o.createContext(null),l=o.forwardRef((e,t)=>{let{children:n}=e,l=r(e,["children"]),s=o.useContext(i),d=o.useMemo(()=>Object.assign(Object.assign({},s),l),[s,l.prefixCls,l.mode,l.selectable,l.rootClassName]),u=(0,a.t4)(n),m=(0,a.x1)(t,u?(0,a.C4)(n):null);return o.createElement(i.Provider,{value:d},o.createElement(c.Z,{space:!0},u?o.cloneElement(n,{ref:m}):n))});t.Z=i},80311:function(e,t,n){n.d(t,{Z:function(){return V}});var o=n(2265),a=n(93706),c=n(24118),r=n(51842),i=n(42744),l=n.n(i),s=n(28788),d=n(54925),u=n(47387),m=n(65823),p=n(57499),g=n(92935);let b=(0,o.createContext)({prefixCls:"",firstLevel:!0,inlineCollapsed:!1});var f=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n},v=e=>{let{prefixCls:t,className:n,dashed:c}=e,r=f(e,["prefixCls","className","dashed"]),{getPrefixCls:i}=o.useContext(p.E_),s=i("menu",t),d=l()({["".concat(s,"-item-divider-dashed")]:!!c},n);return o.createElement(a.iz,Object.assign({className:d},r))},h=n(79173),y=n(78634),I=e=>{var t;let{className:n,children:r,icon:i,title:s,danger:u,extra:p}=e,{prefixCls:g,firstLevel:f,direction:v,disableMenuItemTitleTooltip:I,inlineCollapsed:w}=o.useContext(b),{siderCollapsed:x}=o.useContext(c.D),C=s;void 0===s?C=f?r:"":!1===s&&(C="");let O={title:C};x||w||(O.title=null,O.open=!1);let S=(0,h.Z)(r).length,B=o.createElement(a.ck,Object.assign({},(0,d.Z)(e,["title","icon","danger"]),{className:l()({["".concat(g,"-item-danger")]:u,["".concat(g,"-item-only-child")]:(i?S+1:S)===1},n),title:"string"==typeof s?s:void 0}),(0,m.Tm)(i,{className:l()(o.isValidElement(i)?null===(t=i.props)||void 0===t?void 0:t.className:"","".concat(g,"-item-icon"))}),(e=>{let t=null==r?void 0:r[0],n=o.createElement("span",{className:l()("".concat(g,"-title-content"),{["".concat(g,"-title-content-with-extra")]:!!p||0===p})},r);return(!i||o.isValidElement(r)&&"span"===r.type)&&r&&e&&f&&"string"==typeof t?o.createElement("div",{className:"".concat(g,"-inline-collapsed-noicon")},t.charAt(0)):n})(w));return I||(B=o.createElement(y.Z,Object.assign({},O,{placement:"rtl"===v?"left":"right",classNames:{root:"".concat(g,"-inline-collapsed-tooltip")}}),B)),B},w=n(81642),x=n(58489),C=n(47861),O=n(11303),S=n(46154),B=n(202),E=n(58854),k=n(78387),j=n(12711),z=e=>{let{componentCls:t,motionDurationSlow:n,horizontalLineHeight:o,colorSplit:a,lineWidth:c,lineType:r,itemPaddingInline:i}=e;return{["".concat(t,"-horizontal")]:{lineHeight:o,border:0,borderBottom:"".concat((0,x.bf)(c)," ").concat(r," ").concat(a),boxShadow:"none","&::after":{display:"block",clear:"both",height:0,content:'"\\20"'},["".concat(t,"-item, ").concat(t,"-submenu")]:{position:"relative",display:"inline-block",verticalAlign:"bottom",paddingInline:i},["> ".concat(t,"-item:hover,\n        > ").concat(t,"-item-active,\n        > ").concat(t,"-submenu ").concat(t,"-submenu-title:hover")]:{backgroundColor:"transparent"},["".concat(t,"-item, ").concat(t,"-submenu-title")]:{transition:["border-color ".concat(n),"background ".concat(n)].join(",")},["".concat(t,"-submenu-arrow")]:{display:"none"}}}},H=e=>{let{componentCls:t,menuArrowOffset:n,calc:o}=e;return{["".concat(t,"-rtl")]:{direction:"rtl"},["".concat(t,"-submenu-rtl")]:{transformOrigin:"100% 0"},["".concat(t,"-rtl").concat(t,"-vertical,\n    ").concat(t,"-submenu-rtl ").concat(t,"-vertical")]:{["".concat(t,"-submenu-arrow")]:{"&::before":{transform:"rotate(-45deg) translateY(".concat((0,x.bf)(o(n).mul(-1).equal()),")")},"&::after":{transform:"rotate(45deg) translateY(".concat((0,x.bf)(n),")")}}}}};let T=e=>Object.assign({},(0,O.oN)(e));var N=(e,t)=>{let{componentCls:n,itemColor:o,itemSelectedColor:a,subMenuItemSelectedColor:c,groupTitleColor:r,itemBg:i,subMenuItemBg:l,itemSelectedBg:s,activeBarHeight:d,activeBarWidth:u,activeBarBorderWidth:m,motionDurationSlow:p,motionEaseInOut:g,motionEaseOut:b,itemPaddingInline:f,motionDurationMid:v,itemHoverColor:h,lineType:y,colorSplit:I,itemDisabledColor:w,dangerItemColor:C,dangerItemHoverColor:O,dangerItemSelectedColor:S,dangerItemActiveBg:B,dangerItemSelectedBg:E,popupBg:k,itemHoverBg:j,itemActiveBg:z,menuSubMenuBg:H,horizontalItemSelectedColor:N,horizontalItemSelectedBg:R,horizontalItemBorderRadius:P,horizontalItemHoverBg:Z}=e;return{["".concat(n,"-").concat(t,", ").concat(n,"-").concat(t," > ").concat(n)]:{color:o,background:i,["&".concat(n,"-root:focus-visible")]:Object.assign({},T(e)),["".concat(n,"-item")]:{"&-group-title, &-extra":{color:r}},["".concat(n,"-submenu-selected > ").concat(n,"-submenu-title")]:{color:c},["".concat(n,"-item, ").concat(n,"-submenu-title")]:{color:o,["&:not(".concat(n,"-item-disabled):focus-visible")]:Object.assign({},T(e))},["".concat(n,"-item-disabled, ").concat(n,"-submenu-disabled")]:{color:"".concat(w," !important")},["".concat(n,"-item:not(").concat(n,"-item-selected):not(").concat(n,"-submenu-selected)")]:{["&:hover, > ".concat(n,"-submenu-title:hover")]:{color:h}},["&:not(".concat(n,"-horizontal)")]:{["".concat(n,"-item:not(").concat(n,"-item-selected)")]:{"&:hover":{backgroundColor:j},"&:active":{backgroundColor:z}},["".concat(n,"-submenu-title")]:{"&:hover":{backgroundColor:j},"&:active":{backgroundColor:z}}},["".concat(n,"-item-danger")]:{color:C,["&".concat(n,"-item:hover")]:{["&:not(".concat(n,"-item-selected):not(").concat(n,"-submenu-selected)")]:{color:O}},["&".concat(n,"-item:active")]:{background:B}},["".concat(n,"-item a")]:{"&, &:hover":{color:"inherit"}},["".concat(n,"-item-selected")]:{color:a,["&".concat(n,"-item-danger")]:{color:S},"a, a:hover":{color:"inherit"}},["& ".concat(n,"-item-selected")]:{backgroundColor:s,["&".concat(n,"-item-danger")]:{backgroundColor:E}},["&".concat(n,"-submenu > ").concat(n)]:{backgroundColor:H},["&".concat(n,"-popup > ").concat(n)]:{backgroundColor:k},["&".concat(n,"-submenu-popup > ").concat(n)]:{backgroundColor:k},["&".concat(n,"-horizontal")]:Object.assign(Object.assign({},"dark"===t?{borderBottom:0}:{}),{["> ".concat(n,"-item, > ").concat(n,"-submenu")]:{top:m,marginTop:e.calc(m).mul(-1).equal(),marginBottom:0,borderRadius:P,"&::after":{position:"absolute",insetInline:f,bottom:0,borderBottom:"".concat((0,x.bf)(d)," solid transparent"),transition:"border-color ".concat(p," ").concat(g),content:'""'},"&:hover, &-active, &-open":{background:Z,"&::after":{borderBottomWidth:d,borderBottomColor:N}},"&-selected":{color:N,backgroundColor:R,"&:hover":{backgroundColor:R},"&::after":{borderBottomWidth:d,borderBottomColor:N}}}}),["&".concat(n,"-root")]:{["&".concat(n,"-inline, &").concat(n,"-vertical")]:{borderInlineEnd:"".concat((0,x.bf)(m)," ").concat(y," ").concat(I)}},["&".concat(n,"-inline")]:{["".concat(n,"-sub").concat(n,"-inline")]:{background:l},["".concat(n,"-item")]:{position:"relative","&::after":{position:"absolute",insetBlock:0,insetInlineEnd:0,borderInlineEnd:"".concat((0,x.bf)(u)," solid ").concat(a),transform:"scaleY(0.0001)",opacity:0,transition:["transform ".concat(v," ").concat(b),"opacity ".concat(v," ").concat(b)].join(","),content:'""'},["&".concat(n,"-item-danger")]:{"&::after":{borderInlineEndColor:S}}},["".concat(n,"-selected, ").concat(n,"-item-selected")]:{"&::after":{transform:"scaleY(1)",opacity:1,transition:["transform ".concat(v," ").concat(g),"opacity ".concat(v," ").concat(g)].join(",")}}}}}};let R=e=>{let{componentCls:t,itemHeight:n,itemMarginInline:o,padding:a,menuArrowSize:c,marginXS:r,itemMarginBlock:i,itemWidth:l,itemPaddingInline:s}=e,d=e.calc(c).add(a).add(r).equal();return{["".concat(t,"-item")]:{position:"relative",overflow:"hidden"},["".concat(t,"-item, ").concat(t,"-submenu-title")]:{height:n,lineHeight:(0,x.bf)(n),paddingInline:s,overflow:"hidden",textOverflow:"ellipsis",marginInline:o,marginBlock:i,width:l},["> ".concat(t,"-item,\n            > ").concat(t,"-submenu > ").concat(t,"-submenu-title")]:{height:n,lineHeight:(0,x.bf)(n)},["".concat(t,"-item-group-list ").concat(t,"-submenu-title,\n            ").concat(t,"-submenu-title")]:{paddingInlineEnd:d}}};var P=e=>{let{componentCls:t,iconCls:n,itemHeight:o,colorTextLightSolid:a,dropdownWidth:c,controlHeightLG:r,motionEaseOut:i,paddingXL:l,itemMarginInline:s,fontSizeLG:d,motionDurationFast:u,motionDurationSlow:m,paddingXS:p,boxShadowSecondary:g,collapsedWidth:b,collapsedIconSize:f}=e,v={height:o,lineHeight:(0,x.bf)(o),listStylePosition:"inside",listStyleType:"disc"};return[{[t]:{"&-inline, &-vertical":Object.assign({["&".concat(t,"-root")]:{boxShadow:"none"}},R(e))},["".concat(t,"-submenu-popup")]:{["".concat(t,"-vertical")]:Object.assign(Object.assign({},R(e)),{boxShadow:g})}},{["".concat(t,"-submenu-popup ").concat(t,"-vertical").concat(t,"-sub")]:{minWidth:c,maxHeight:"calc(100vh - ".concat((0,x.bf)(e.calc(r).mul(2.5).equal()),")"),padding:"0",overflow:"hidden",borderInlineEnd:0,"&:not([class*='-active'])":{overflowX:"hidden",overflowY:"auto"}}},{["".concat(t,"-inline")]:{width:"100%",["&".concat(t,"-root")]:{["".concat(t,"-item, ").concat(t,"-submenu-title")]:{display:"flex",alignItems:"center",transition:["border-color ".concat(m),"background ".concat(m),"padding ".concat(u," ").concat(i)].join(","),["> ".concat(t,"-title-content")]:{flex:"auto",minWidth:0,overflow:"hidden",textOverflow:"ellipsis"},"> *":{flex:"none"}}},["".concat(t,"-sub").concat(t,"-inline")]:{padding:0,border:0,borderRadius:0,boxShadow:"none",["& > ".concat(t,"-submenu > ").concat(t,"-submenu-title")]:v,["& ".concat(t,"-item-group-title")]:{paddingInlineStart:l}},["".concat(t,"-item")]:v}},{["".concat(t,"-inline-collapsed")]:{width:b,["&".concat(t,"-root")]:{["".concat(t,"-item, ").concat(t,"-submenu ").concat(t,"-submenu-title")]:{["> ".concat(t,"-inline-collapsed-noicon")]:{fontSize:d,textAlign:"center"}}},["> ".concat(t,"-item,\n          > ").concat(t,"-item-group > ").concat(t,"-item-group-list > ").concat(t,"-item,\n          > ").concat(t,"-item-group > ").concat(t,"-item-group-list > ").concat(t,"-submenu > ").concat(t,"-submenu-title,\n          > ").concat(t,"-submenu > ").concat(t,"-submenu-title")]:{insetInlineStart:0,paddingInline:"calc(50% - ".concat((0,x.bf)(e.calc(f).div(2).equal())," - ").concat((0,x.bf)(s),")"),textOverflow:"clip",["\n            ".concat(t,"-submenu-arrow,\n            ").concat(t,"-submenu-expand-icon\n          ")]:{opacity:0},["".concat(t,"-item-icon, ").concat(n)]:{margin:0,fontSize:f,lineHeight:(0,x.bf)(o),"+ span":{display:"inline-block",opacity:0}}},["".concat(t,"-item-icon, ").concat(n)]:{display:"inline-block"},"&-tooltip":{pointerEvents:"none",["".concat(t,"-item-icon, ").concat(n)]:{display:"none"},"a, a:hover":{color:a}},["".concat(t,"-item-group-title")]:Object.assign(Object.assign({},O.vS),{paddingInline:p})}}]};let Z=e=>{let{componentCls:t,motionDurationSlow:n,motionDurationMid:o,motionEaseInOut:a,motionEaseOut:c,iconCls:r,iconSize:i,iconMarginInlineEnd:l}=e;return{["".concat(t,"-item, ").concat(t,"-submenu-title")]:{position:"relative",display:"block",margin:0,whiteSpace:"nowrap",cursor:"pointer",transition:["border-color ".concat(n),"background ".concat(n),"padding calc(".concat(n," + 0.1s) ").concat(a)].join(","),["".concat(t,"-item-icon, ").concat(r)]:{minWidth:i,fontSize:i,transition:["font-size ".concat(o," ").concat(c),"margin ".concat(n," ").concat(a),"color ".concat(n)].join(","),"+ span":{marginInlineStart:l,opacity:1,transition:["opacity ".concat(n," ").concat(a),"margin ".concat(n),"color ".concat(n)].join(",")}},["".concat(t,"-item-icon")]:Object.assign({},(0,O.Ro)()),["&".concat(t,"-item-only-child")]:{["> ".concat(r,", > ").concat(t,"-item-icon")]:{marginInlineEnd:0}}},["".concat(t,"-item-disabled, ").concat(t,"-submenu-disabled")]:{background:"none !important",cursor:"not-allowed","&::after":{borderColor:"transparent !important"},a:{color:"inherit !important",cursor:"not-allowed",pointerEvents:"none"},["> ".concat(t,"-submenu-title")]:{color:"inherit !important",cursor:"not-allowed"}}}},M=e=>{let{componentCls:t,motionDurationSlow:n,motionEaseInOut:o,borderRadius:a,menuArrowSize:c,menuArrowOffset:r}=e;return{["".concat(t,"-submenu")]:{"&-expand-icon, &-arrow":{position:"absolute",top:"50%",insetInlineEnd:e.margin,width:c,color:"currentcolor",transform:"translateY(-50%)",transition:"transform ".concat(n," ").concat(o,", opacity ").concat(n)},"&-arrow":{"&::before, &::after":{position:"absolute",width:e.calc(c).mul(.6).equal(),height:e.calc(c).mul(.15).equal(),backgroundColor:"currentcolor",borderRadius:a,transition:["background ".concat(n," ").concat(o),"transform ".concat(n," ").concat(o),"top ".concat(n," ").concat(o),"color ".concat(n," ").concat(o)].join(","),content:'""'},"&::before":{transform:"rotate(45deg) translateY(".concat((0,x.bf)(e.calc(r).mul(-1).equal()),")")},"&::after":{transform:"rotate(-45deg) translateY(".concat((0,x.bf)(r),")")}}}}},D=e=>{let{antCls:t,componentCls:n,fontSize:o,motionDurationSlow:a,motionDurationMid:c,motionEaseInOut:r,paddingXS:i,padding:l,colorSplit:s,lineWidth:d,zIndexPopup:u,borderRadiusLG:m,subMenuItemBorderRadius:p,menuArrowSize:g,menuArrowOffset:b,lineType:f,groupTitleLineHeight:v,groupTitleFontSize:h}=e;return[{"":{[n]:Object.assign(Object.assign({},(0,O.dF)()),{"&-hidden":{display:"none"}})},["".concat(n,"-submenu-hidden")]:{display:"none"}},{[n]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(0,O.Wf)(e)),(0,O.dF)()),{marginBottom:0,paddingInlineStart:0,fontSize:o,lineHeight:0,listStyle:"none",outline:"none",transition:"width ".concat(a," cubic-bezier(0.2, 0, 0, 1) 0s"),"ul, ol":{margin:0,padding:0,listStyle:"none"},"&-overflow":{display:"flex",["".concat(n,"-item")]:{flex:"none"}},["".concat(n,"-item, ").concat(n,"-submenu, ").concat(n,"-submenu-title")]:{borderRadius:e.itemBorderRadius},["".concat(n,"-item-group-title")]:{padding:"".concat((0,x.bf)(i)," ").concat((0,x.bf)(l)),fontSize:h,lineHeight:v,transition:"all ".concat(a)},["&-horizontal ".concat(n,"-submenu")]:{transition:["border-color ".concat(a," ").concat(r),"background ".concat(a," ").concat(r)].join(",")},["".concat(n,"-submenu, ").concat(n,"-submenu-inline")]:{transition:["border-color ".concat(a," ").concat(r),"background ".concat(a," ").concat(r),"padding ".concat(c," ").concat(r)].join(",")},["".concat(n,"-submenu ").concat(n,"-sub")]:{cursor:"initial",transition:["background ".concat(a," ").concat(r),"padding ".concat(a," ").concat(r)].join(",")},["".concat(n,"-title-content")]:{transition:"color ".concat(a),"&-with-extra":{display:"inline-flex",alignItems:"center",width:"100%"},["> ".concat(t,"-typography-ellipsis-single-line")]:{display:"inline",verticalAlign:"unset"},["".concat(n,"-item-extra")]:{marginInlineStart:"auto",paddingInlineStart:e.padding}},["".concat(n,"-item a")]:{"&::before":{position:"absolute",inset:0,backgroundColor:"transparent",content:'""'}},["".concat(n,"-item-divider")]:{overflow:"hidden",lineHeight:0,borderColor:s,borderStyle:f,borderWidth:0,borderTopWidth:d,marginBlock:d,padding:0,"&-dashed":{borderStyle:"dashed"}}}),Z(e)),{["".concat(n,"-item-group")]:{["".concat(n,"-item-group-list")]:{margin:0,padding:0,["".concat(n,"-item, ").concat(n,"-submenu-title")]:{paddingInline:"".concat((0,x.bf)(e.calc(o).mul(2).equal())," ").concat((0,x.bf)(l))}}},"&-submenu":{"&-popup":{position:"absolute",zIndex:u,borderRadius:m,boxShadow:"none",transformOrigin:"0 0",["&".concat(n,"-submenu")]:{background:"transparent"},"&::before":{position:"absolute",inset:0,zIndex:-1,width:"100%",height:"100%",opacity:0,content:'""'},["> ".concat(n)]:Object.assign(Object.assign(Object.assign({borderRadius:m},Z(e)),M(e)),{["".concat(n,"-item, ").concat(n,"-submenu > ").concat(n,"-submenu-title")]:{borderRadius:p},["".concat(n,"-submenu-title::after")]:{transition:"transform ".concat(a," ").concat(r)}})},"\n          &-placement-leftTop,\n          &-placement-bottomRight,\n          ":{transformOrigin:"100% 0"},"\n          &-placement-leftBottom,\n          &-placement-topRight,\n          ":{transformOrigin:"100% 100%"},"\n          &-placement-rightBottom,\n          &-placement-topLeft,\n          ":{transformOrigin:"0 100%"},"\n          &-placement-bottomLeft,\n          &-placement-rightTop,\n          ":{transformOrigin:"0 0"},"\n          &-placement-leftTop,\n          &-placement-leftBottom\n          ":{paddingInlineEnd:e.paddingXS},"\n          &-placement-rightTop,\n          &-placement-rightBottom\n          ":{paddingInlineStart:e.paddingXS},"\n          &-placement-topRight,\n          &-placement-topLeft\n          ":{paddingBottom:e.paddingXS},"\n          &-placement-bottomRight,\n          &-placement-bottomLeft\n          ":{paddingTop:e.paddingXS}}}),M(e)),{["&-inline-collapsed ".concat(n,"-submenu-arrow,\n        &-inline ").concat(n,"-submenu-arrow")]:{"&::before":{transform:"rotate(-45deg) translateX(".concat((0,x.bf)(b),")")},"&::after":{transform:"rotate(45deg) translateX(".concat((0,x.bf)(e.calc(b).mul(-1).equal()),")")}},["".concat(n,"-submenu-open").concat(n,"-submenu-inline > ").concat(n,"-submenu-title > ").concat(n,"-submenu-arrow")]:{transform:"translateY(".concat((0,x.bf)(e.calc(g).mul(.2).mul(-1).equal()),")"),"&::after":{transform:"rotate(-45deg) translateX(".concat((0,x.bf)(e.calc(b).mul(-1).equal()),")")},"&::before":{transform:"rotate(45deg) translateX(".concat((0,x.bf)(b),")")}}})},{["".concat(t,"-layout-header")]:{[n]:{lineHeight:"inherit"}}}]},A=e=>{var t,n,o;let{colorPrimary:a,colorError:c,colorTextDisabled:r,colorErrorBg:i,colorText:l,colorTextDescription:s,colorBgContainer:d,colorFillAlter:u,colorFillContent:m,lineWidth:p,lineWidthBold:g,controlItemBgActive:b,colorBgTextHover:f,controlHeightLG:v,lineHeight:h,colorBgElevated:y,marginXXS:I,padding:w,fontSize:x,controlHeightSM:O,fontSizeLG:S,colorTextLightSolid:B,colorErrorHover:E}=e,k=null!==(t=e.activeBarWidth)&&void 0!==t?t:0,j=null!==(n=e.activeBarBorderWidth)&&void 0!==n?n:p,z=null!==(o=e.itemMarginInline)&&void 0!==o?o:e.marginXXS,H=new C.t(B).setA(.65).toRgbString();return{dropdownWidth:160,zIndexPopup:e.zIndexPopupBase+50,radiusItem:e.borderRadiusLG,itemBorderRadius:e.borderRadiusLG,radiusSubMenuItem:e.borderRadiusSM,subMenuItemBorderRadius:e.borderRadiusSM,colorItemText:l,itemColor:l,colorItemTextHover:l,itemHoverColor:l,colorItemTextHoverHorizontal:a,horizontalItemHoverColor:a,colorGroupTitle:s,groupTitleColor:s,colorItemTextSelected:a,itemSelectedColor:a,subMenuItemSelectedColor:a,colorItemTextSelectedHorizontal:a,horizontalItemSelectedColor:a,colorItemBg:d,itemBg:d,colorItemBgHover:f,itemHoverBg:f,colorItemBgActive:m,itemActiveBg:b,colorSubItemBg:u,subMenuItemBg:u,colorItemBgSelected:b,itemSelectedBg:b,colorItemBgSelectedHorizontal:"transparent",horizontalItemSelectedBg:"transparent",colorActiveBarWidth:0,activeBarWidth:k,colorActiveBarHeight:g,activeBarHeight:g,colorActiveBarBorderSize:p,activeBarBorderWidth:j,colorItemTextDisabled:r,itemDisabledColor:r,colorDangerItemText:c,dangerItemColor:c,colorDangerItemTextHover:c,dangerItemHoverColor:c,colorDangerItemTextSelected:c,dangerItemSelectedColor:c,colorDangerItemBgActive:i,dangerItemActiveBg:i,colorDangerItemBgSelected:i,dangerItemSelectedBg:i,itemMarginInline:z,horizontalItemBorderRadius:0,horizontalItemHoverBg:"transparent",itemHeight:v,groupTitleLineHeight:h,collapsedWidth:2*v,popupBg:y,itemMarginBlock:I,itemPaddingInline:w,horizontalLineHeight:"".concat(1.15*v,"px"),iconSize:x,iconMarginInlineEnd:O-x,collapsedIconSize:S,groupTitleFontSize:x,darkItemDisabledColor:new C.t(B).setA(.25).toRgbString(),darkItemColor:H,darkDangerItemColor:c,darkItemBg:"#001529",darkPopupBg:"#001529",darkSubMenuItemBg:"#000c17",darkItemSelectedColor:B,darkItemSelectedBg:a,darkDangerItemSelectedBg:c,darkItemHoverBg:"transparent",darkGroupTitleColor:H,darkItemHoverColor:B,darkDangerItemHoverColor:E,darkDangerItemSelectedColor:B,darkDangerItemActiveBg:c,itemWidth:k?"calc(100% + ".concat(j,"px)"):"calc(100% - ".concat(2*z,"px)")}};var W=n(51761),L=e=>{var t;let n;let{popupClassName:c,icon:r,title:i,theme:s}=e,u=o.useContext(b),{prefixCls:p,inlineCollapsed:g,theme:f}=u,v=(0,a.Xl)();if(r){let e=o.isValidElement(i)&&"span"===i.type;n=o.createElement(o.Fragment,null,(0,m.Tm)(r,{className:l()(o.isValidElement(r)?null===(t=r.props)||void 0===t?void 0:t.className:"","".concat(p,"-item-icon"))}),e?i:o.createElement("span",{className:"".concat(p,"-title-content")},i))}else n=g&&!v.length&&i&&"string"==typeof i?o.createElement("div",{className:"".concat(p,"-inline-collapsed-noicon")},i.charAt(0)):o.createElement("span",{className:"".concat(p,"-title-content")},i);let h=o.useMemo(()=>Object.assign(Object.assign({},u),{firstLevel:!1}),[u]),[y]=(0,W.Cn)("Menu");return o.createElement(b.Provider,{value:h},o.createElement(a.Wd,Object.assign({},(0,d.Z)(e,["icon"]),{title:n,popupClassName:l()(p,c,"".concat(p,"-").concat(s||f)),popupStyle:Object.assign({zIndex:y},e.popupStyle)})))},X=function(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&0>t.indexOf(o)&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)0>t.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(n[o[a]]=e[o[a]]);return n};function q(e){return null===e||!1===e}let _={item:I,submenu:L,divider:v},F=(0,o.forwardRef)((e,t)=>{var n;let c=o.useContext(w.Z),i=c||{},{getPrefixCls:f,getPopupContainer:v,direction:h,menu:y}=o.useContext(p.E_),I=f(),{prefixCls:x,className:C,style:O,theme:T="light",expandIcon:R,_internalDisableMenuItemTitleTooltip:Z,inlineCollapsed:M,siderCollapsed:W,rootClassName:L,mode:F,selectable:Y,onClick:V,overflowedIndicatorPopupClassName:G}=e,K=X(e,["prefixCls","className","style","theme","expandIcon","_internalDisableMenuItemTitleTooltip","inlineCollapsed","siderCollapsed","rootClassName","mode","selectable","onClick","overflowedIndicatorPopupClassName"]),U=(0,d.Z)(K,["collapsedWidth"]);null===(n=i.validator)||void 0===n||n.call(i,{mode:F});let $=(0,s.Z)(function(){for(var e,t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];null==V||V.apply(void 0,n),null===(e=i.onClick)||void 0===e||e.call(i)}),J=i.mode||F,Q=null!=Y?Y:i.selectable,ee=null!=M?M:W,et={horizontal:{motionName:"".concat(I,"-slide-up")},inline:(0,u.Z)(I),other:{motionName:"".concat(I,"-zoom-big")}},en=f("menu",x||i.prefixCls),eo=(0,g.Z)(en),[ea,ec,er]=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return(0,k.I$)("Menu",e=>{let{colorBgElevated:t,controlHeightLG:n,fontSize:o,darkItemColor:a,darkDangerItemColor:c,darkItemBg:r,darkSubMenuItemBg:i,darkItemSelectedColor:l,darkItemSelectedBg:s,darkDangerItemSelectedBg:d,darkItemHoverBg:u,darkGroupTitleColor:m,darkItemHoverColor:p,darkItemDisabledColor:g,darkDangerItemHoverColor:b,darkDangerItemSelectedColor:f,darkDangerItemActiveBg:v,popupBg:h,darkPopupBg:y}=e,I=e.calc(o).div(7).mul(5).equal(),w=(0,j.IX)(e,{menuArrowSize:I,menuHorizontalHeight:e.calc(n).mul(1.15).equal(),menuArrowOffset:e.calc(I).mul(.25).equal(),menuSubMenuBg:t,calc:e.calc,popupBg:h}),x=(0,j.IX)(w,{itemColor:a,itemHoverColor:p,groupTitleColor:m,itemSelectedColor:l,subMenuItemSelectedColor:l,itemBg:r,popupBg:y,subMenuItemBg:i,itemActiveBg:"transparent",itemSelectedBg:s,activeBarHeight:0,activeBarBorderWidth:0,itemHoverBg:u,itemDisabledColor:g,dangerItemColor:c,dangerItemHoverColor:b,dangerItemSelectedColor:f,dangerItemActiveBg:v,dangerItemSelectedBg:d,menuSubMenuBg:i,horizontalItemSelectedColor:l,horizontalItemSelectedBg:s});return[D(w),z(w),P(w),N(w,"light"),N(x,"dark"),H(w),(0,S.Z)(w),(0,B.oN)(w,"slide-up"),(0,B.oN)(w,"slide-down"),(0,E._y)(w,"zoom-big")]},A,{deprecatedTokens:[["colorGroupTitle","groupTitleColor"],["radiusItem","itemBorderRadius"],["radiusSubMenuItem","subMenuItemBorderRadius"],["colorItemText","itemColor"],["colorItemTextHover","itemHoverColor"],["colorItemTextHoverHorizontal","horizontalItemHoverColor"],["colorItemTextSelected","itemSelectedColor"],["colorItemTextSelectedHorizontal","horizontalItemSelectedColor"],["colorItemTextDisabled","itemDisabledColor"],["colorDangerItemText","dangerItemColor"],["colorDangerItemTextHover","dangerItemHoverColor"],["colorDangerItemTextSelected","dangerItemSelectedColor"],["colorDangerItemBgActive","dangerItemActiveBg"],["colorDangerItemBgSelected","dangerItemSelectedBg"],["colorItemBg","itemBg"],["colorItemBgHover","itemHoverBg"],["colorSubItemBg","subMenuItemBg"],["colorItemBgActive","itemActiveBg"],["colorItemBgSelectedHorizontal","horizontalItemSelectedBg"],["colorActiveBarWidth","activeBarWidth"],["colorActiveBarHeight","activeBarHeight"],["colorActiveBarBorderSize","activeBarBorderWidth"],["colorItemBgSelected","itemSelectedBg"]],injectStyle:n,unitless:{groupTitleLineHeight:!0}})(e,t)}(en,eo,!c),ei=l()("".concat(en,"-").concat(T),null==y?void 0:y.className,C),el=o.useMemo(()=>{var e,t;if("function"==typeof R||q(R))return R||null;if("function"==typeof i.expandIcon||q(i.expandIcon))return i.expandIcon||null;if("function"==typeof(null==y?void 0:y.expandIcon)||q(null==y?void 0:y.expandIcon))return(null==y?void 0:y.expandIcon)||null;let n=null!==(e=null!=R?R:null==i?void 0:i.expandIcon)&&void 0!==e?e:null==y?void 0:y.expandIcon;return(0,m.Tm)(n,{className:l()("".concat(en,"-submenu-expand-icon"),o.isValidElement(n)?null===(t=n.props)||void 0===t?void 0:t.className:void 0)})},[R,null==i?void 0:i.expandIcon,null==y?void 0:y.expandIcon,en]),es=o.useMemo(()=>({prefixCls:en,inlineCollapsed:ee||!1,direction:h,firstLevel:!0,theme:T,mode:J,disableMenuItemTitleTooltip:Z}),[en,ee,h,Z,T]);return ea(o.createElement(w.Z.Provider,{value:null},o.createElement(b.Provider,{value:es},o.createElement(a.ZP,Object.assign({getPopupContainer:v,overflowedIndicator:o.createElement(r.Z,null),overflowedIndicatorPopupClassName:l()(en,"".concat(en,"-").concat(T),G),mode:J,selectable:Q,onClick:$},U,{inlineCollapsed:ee,style:Object.assign(Object.assign({},null==y?void 0:y.style),O),className:ei,prefixCls:en,direction:h,defaultMotions:et,expandIcon:el,ref:t,rootClassName:l()(L,ec,i.rootClassName,er,eo),_internalComponents:_})))))}),Y=(0,o.forwardRef)((e,t)=>{let n=(0,o.useRef)(null),a=o.useContext(c.D);return(0,o.useImperativeHandle)(t,()=>({menu:n.current,focus:e=>{var t;null===(t=n.current)||void 0===t||t.focus(e)}})),o.createElement(F,Object.assign({ref:n},e,a))});Y.Item=I,Y.SubMenu=L,Y.Divider=v,Y.ItemGroup=a.BW;var V=Y},46154:function(e,t){t.Z=e=>({[e.componentCls]:{["".concat(e.antCls,"-motion-collapse-legacy")]:{overflow:"hidden","&-active":{transition:"height ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut,",\n        opacity ").concat(e.motionDurationMid," ").concat(e.motionEaseInOut," !important")}},["".concat(e.antCls,"-motion-collapse")]:{overflow:"hidden",transition:"height ".concat(e.motionDurationMid," ").concat(e.motionEaseInOut,",\n        opacity ").concat(e.motionDurationMid," ").concat(e.motionEaseInOut," !important")}}})},25926:function(e,t,n){n.d(t,{Fm:function(){return m}});var o=n(58489),a=n(59353);let c=new o.E4("antMoveDownIn",{"0%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),r=new o.E4("antMoveDownOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, 100%, 0)",transformOrigin:"0 0",opacity:0}}),i=new o.E4("antMoveLeftIn",{"0%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),l=new o.E4("antMoveLeftOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(-100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),s=new o.E4("antMoveRightIn",{"0%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),d=new o.E4("antMoveRightOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(100%, 0, 0)",transformOrigin:"0 0",opacity:0}}),u={"move-up":{inKeyframes:new o.E4("antMoveUpIn",{"0%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0},"100%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1}}),outKeyframes:new o.E4("antMoveUpOut",{"0%":{transform:"translate3d(0, 0, 0)",transformOrigin:"0 0",opacity:1},"100%":{transform:"translate3d(0, -100%, 0)",transformOrigin:"0 0",opacity:0}})},"move-down":{inKeyframes:c,outKeyframes:r},"move-left":{inKeyframes:i,outKeyframes:l},"move-right":{inKeyframes:s,outKeyframes:d}},m=(e,t)=>{let{antCls:n}=e,o="".concat(n,"-").concat(t),{inKeyframes:c,outKeyframes:r}=u[t];return[(0,a.R)(o,c,r,e.motionDurationMid),{["\n        ".concat(o,"-enter,\n        ").concat(o,"-appear\n      ")]:{opacity:0,animationTimingFunction:e.motionEaseOutCirc},["".concat(o,"-leave")]:{animationTimingFunction:e.motionEaseInOutCirc}}]}}}]);