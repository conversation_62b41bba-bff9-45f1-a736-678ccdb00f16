(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5993],{30315:function(e,t,r){Promise.resolve().then(r.bind(r,34907))},34907:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return t$}});var s=r(57437),n=r(2265),a=r(57416),o=r(63424),i=r(89511),l=r(38302),c=r(28683),d=r(65270),u=r(6053),h=r(94734),m=r(45284),x=r(86155),g=r(2012),p=r(9427),f=r(59160),y=r(40960),j=r(27203),w=r(82765),v=r(58405),k=r(13924),S=r(68826),Z=r(51554),b=r(8399),C=r(17046),D=r(78634),T=r(27296),M=r(50030),I=r(28807),N=r(74548),O=r.n(N),E=r(29255),W=r(32779);class _{addEventListener(e,t,r,s){let n={...s,signal:this.abortController.signal};e.addEventListener(t,r,n),this.listeners.push({target:e,type:t,listener:r})}removeEventListener(e,t,r){e.removeEventListener(t,r),this.listeners=this.listeners.filter(s=>!(s.target===e&&s.type===t&&s.listener===r))}cleanup(){this.abortController.abort(),this.listeners=[]}getActiveListenersCount(){return this.listeners.length}isCleanedUp(){return this.abortController.signal.aborted}constructor(){this.abortController=new AbortController,this.listeners=[]}}let A={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"};function R(e){return null==e?"":"string"!=typeof e?String(e):e.replace(/[&<>"'`=\/]/g,e=>A[e]||e)}function z(e){return e?R(e.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"").replace(/javascript:/gi,"").replace(/on\w+\s*=/gi,"").trim()):"未知产品"}function B(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200;return e?R((e.length>t?e.substring(0,t)+"...":e).trim()):""}let P=[{label:"7天",value:7},{label:"14天",value:14},{label:"30天",value:30}],L=e=>{if(!e||"string"!=typeof e)throw Error("Invalid time string: must be a non-empty string");let t=e.split(":");if(2!==t.length)throw Error("Invalid time format: must be HH:MM");let r=parseInt(t[0],10),s=parseInt(t[1],10);if(isNaN(r)||isNaN(s)||r<0||r>23||s<0||s>59)throw Error("Invalid time values: hour=".concat(r,", minute=").concat(s));return 60*r+s},H=e=>"".concat(Math.floor(e/60).toString().padStart(2,"0"),":").concat((e%60).toString().padStart(2,"0")),V=e=>{let t=new Map;return e.forEach(e=>{t.get(e.time)&&"work"!==e.type||t.set(e.time,e)}),Array.from(t.values()).sort((e,t)=>L(e.time)-L(t.time))},F=(e,t,r)=>{let s=L(e),{startMinutes:n,endMinutes:a}=t,o=s;return s<n&&(o+=1440),20+(o-n)/(a-n)*(r-40)},Q=e=>{let t=L(e.startTime),r=L(e.endTime)-t,s=[];if(r>=180){let n=t+Math.floor(r/2);s.push({time:H(n),type:"work",label:H(n),isMiddle:!0,slotName:e.name})}return s},U=e=>{if(!e)return[];let t=e.workTimeSlots.filter(e=>e.isActive);if(0===t.length)return[];let r=[];return t.forEach(e=>{r.push({time:e.startTime,type:"work",label:e.startTime,isStart:!0,slotName:e.name}),r.push({time:e.endTime,type:"work",label:e.endTime,isEnd:!0,slotName:e.name});let t=Q(e);r.push(...t)}),V(r)},Y=e=>{if(!e)return{startMinutes:480,endMinutes:1020};let t=e.workTimeSlots.filter(e=>e.isActive);if(0===t.length)return{startMinutes:480,endMinutes:1020};let r=1/0,s=-1/0;return t.forEach(e=>{let t=L(e.startTime),n=L(e.endTime);r=Math.min(r,t),s=Math.max(s,n)}),s<r&&(s+=1440),{startMinutes:r,endMinutes:s}},q=e=>{var t;let{task:r,startDate:n,dayWidth:a,timeRange:o,actualDays:i,onTaskClick:l}=e,c=new Date(r.plannedStartTime),d=new Date(r.plannedEndTime),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=new Date(e);r.setHours(0,0,0,0);let s=new Date(n);s.setHours(0,0,0,0);let l=(r.getTime()-s.getTime())/864e5,c=i-1;if(l<0){if(t){let e=F(H(o.startMinutes),o,a);return 0*a+e}{let e=F(H(o.startMinutes),o,a);return 0*a+e}}if(l>c){if(t){let e=F(H(o.endMinutes),o,a);return c*a+e}{let e=F(H(o.endMinutes),o,a);return c*a+e}}{let t=F("".concat(e.getHours().toString().padStart(2,"0"),":").concat(e.getMinutes().toString().padStart(2,"0")),o,a);return Math.floor(l)*a+t}},h=u(c,!0),m=Math.max(u(d,!1)-h,20),x=r.plannedMoldCount>0?r.completedMoldCount/r.plannedMoldCount*100:0,g=x/100*m,p="completed"===r.status?{completed:"#52c41a",remaining:"#52c41a"}:"in_progress"===r.status?{completed:"#52c41a",remaining:"#f0f0f0"}:{completed:"#1890ff",remaining:"#e6f7ff"};return(0,s.jsx)(D.Z,{title:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:(0,s.jsx)("strong",{children:z(r.productName)})}),(0,s.jsxs)("div",{children:["批次号: ",B(r.batchNumber)]}),(0,s.jsxs)("div",{children:["开始: ",O()(r.plannedStartTime).format("MM-DD HH:mm")]}),(0,s.jsxs)("div",{children:["结束: ",O()(r.plannedEndTime).format("MM-DD HH:mm")]}),(0,s.jsxs)("div",{children:["计划模数: ",r.plannedMoldCount,"模"]}),(0,s.jsxs)("div",{children:["完成模数: ",r.completedMoldCount,"模"]}),(0,s.jsxs)("div",{children:["完成率: ",x.toFixed(1),"%"]}),(0,s.jsxs)("div",{children:["状态: ",(null===(t=E.ux[r.status])||void 0===t?void 0:t.text)||r.status]})]}),children:(0,s.jsxs)("div",{style:{position:"absolute",left:"".concat(h,"px"),width:"".concat(m,"px"),height:"28px",top:"6px",borderRadius:"4px",cursor:"pointer",border:"1px solid rgba(0,0,0,0.1)",overflow:"hidden",background:p.remaining},onClick:()=>null==l?void 0:l(r),children:[(0,s.jsx)("div",{style:{position:"absolute",left:0,top:0,width:"".concat(g,"px"),height:"100%",background:p.completed,transition:"width 0.3s ease"}}),(0,s.jsxs)("div",{style:{position:"absolute",left:"8px",top:"50%",transform:"translateY(-50%)",color:g>.5*m?"white":"#333",fontSize:"12px",fontWeight:500,whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",maxWidth:"".concat(m-16,"px"),zIndex:10},children:[z(r.productName)," (",x.toFixed(0),"%)"]})]})})},X=e=>{let{tasks:t,startDate:r,dayWidth:n,timeRange:a,actualDays:o,onTaskClick:i}=e;return(0,s.jsx)("div",{style:{minHeight:"50px",borderBottom:"1px solid #f0f0f0",position:"relative"},children:t.map(e=>(0,s.jsx)(q,{task:e,startDate:r,dayWidth:n,timeRange:a,actualDays:o,onTaskClick:i},e.id))})},G=e=>{let{startDate:t,days:r,dayWidth:n,workTimeConfig:a}=e,o=Array.from({length:r},(e,r)=>{let s=new Date(t);return s.setDate(s.getDate()+r),s}),i=U(a||null),l=Y(a||null);return(0,s.jsx)("div",{style:{height:"100px",display:"flex",borderBottom:"2px solid #e8e8e8",background:"#fafafa"},children:o.map((e,t)=>(0,s.jsxs)("div",{style:{width:"".concat(n,"px"),borderRight:t<o.length-1?"1px solid #f0f0f0":"none",display:"flex",flexDirection:"column"},children:[(0,s.jsxs)("div",{style:{height:"40px",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",borderBottom:"1px solid #f0f0f0",padding:"4px"},children:[(0,s.jsx)("div",{style:{fontWeight:600,fontSize:"14px"},children:O()(e).format("MM-DD")}),(0,s.jsx)("div",{style:{color:"#666",fontSize:"12px"},children:O()(e).format("ddd")})]}),(0,s.jsx)("div",{style:{height:"60px",position:"relative",background:"#fafafa",overflow:"visible"},children:i.map((e,t)=>{let r=F(e.time,l,n);return(0,s.jsxs)("div",{style:{position:"absolute",left:"".concat(r,"px"),top:"0px",height:"50px",display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,s.jsx)("div",{style:{width:"2px",height:"25px",background:"#1890ff",position:"absolute",top:"5px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsx)("div",{style:{fontSize:"12px",color:"#666",fontWeight:500,position:"absolute",top:"35px",left:"50%",transform:"translateX(-50%)",whiteSpace:"nowrap"},children:e.label})]},t)})})]},t))})};var K=e=>{var t,r;let{tasks:a=[],workstations:l=[],onTaskClick:c,onRefresh:m,loading:x=!1}=e,[g,p]=(0,n.useState)(7),[f,y]=(0,n.useState)([null,null]),[j,v]=(0,n.useState)(null),k=(0,n.useMemo)(()=>Y(j),[j]),[S,Z]=(0,n.useState)(!1),b=(0,n.useCallback)(async()=>{try{let e=await W.dataAccessManager.workTime.getDefault();"success"===e.status&&e.data&&v(e.data)}catch(e){console.error("加载工作时间配置失败:",e)}},[]),D=(0,n.useCallback)(async()=>{try{Z(!0),W.dataAccessManager.clearDataTypeCache("workstations"),W.dataAccessManager.clearDataTypeCache("productionWorkOrders"),await b(),"function"==typeof m&&await m()}catch(e){console.error("数据刷新失败:",e)}finally{Z(!1)}},[b,m]);(0,n.useEffect)(()=>{b()},[b]),t="work-time-configurations",r=(0,n.useCallback)(()=>{b()},[b]),n.useEffect(()=>{let e=new _;return e.addEventListener(window,"storage",e=>{e.key===t&&r(e.newValue,e.oldValue)}),()=>{e.cleanup()}},[t,r]);let{startDate:N,endDate:E}=(0,n.useMemo)(()=>{if(f[0]&&f[1]){let e=new Date(f[0]);e.setHours(0,0,0,0);let t=new Date(f[1]);return t.setHours(23,59,59,999),{startDate:e,endDate:t}}let e=new Date;e.setHours(0,0,0,0);let t=new Date(e);return t.setDate(t.getDate()+g),{startDate:e,endDate:t}},[g,f]),A=(0,n.useMemo)(()=>Math.ceil((E.getTime()-N.getTime())/864e5),[N,E]),R=(0,n.useMemo)(()=>a.filter(e=>e.plannedStartTime&&e.plannedEndTime&&("scheduled"===e.status||"in_progress"===e.status||"completed"===e.status)&&new Date(e.plannedStartTime)<E&&new Date(e.plannedEndTime)>N),[a,N,E]),z=(0,n.useMemo)(()=>{let e=new Map;return R.forEach(t=>{t.workstationCode&&(e.has(t.workstationCode)||e.set(t.workstationCode,[]),e.get(t.workstationCode).push(t))}),e.forEach(e=>{e.sort((e,t)=>new Date(e.plannedStartTime).getTime()-new Date(t.plannedStartTime).getTime())}),e},[R]),B=(0,n.useMemo)(()=>{let e=[],t=[];return l.forEach(r=>{z.has(r.code)?e.push(r):t.push(r.code)}),e},[l,z]);return x?(0,s.jsxs)("div",{style:{padding:"24px",textAlign:"center"},children:[(0,s.jsx)(o.Z,{size:"large"}),(0,s.jsx)("div",{style:{marginTop:"16px",color:"#666"},children:"正在加载排产看板数据..."})]}):0===R.length?(0,s.jsx)("div",{style:{padding:"24px"},children:(0,s.jsx)(i.Z,{title:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,s.jsx)(C.Z,{}),(0,s.jsx)("span",{children:"排产看板"})]}),extra:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(T.default,{value:g,onChange:e=>{p(e),y([null,null])},options:P,style:{width:80}}),(0,s.jsx)(M.default.RangePicker,{value:f[0]&&f[1]?[O()(f[0]),O()(f[1])]:[null,null],onChange:e=>{e&&e[0]&&e[1]?y([e[0].toDate(),e[1].toDate()]):y([null,null])},placeholder:["开始日期","结束日期"],style:{width:240}}),(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(w.Z,{}),onClick:D,loading:S,children:"刷新"})]}),children:(0,s.jsx)(I.Z,{description:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:"暂无排产数据"}),(0,s.jsx)("div",{style:{color:"#999",fontSize:"12px",marginTop:"4px"},children:"当前时间范围内没有已排程的工单"})]}),image:I.Z.PRESENTED_IMAGE_SIMPLE})})}):(0,s.jsx)("div",{style:{padding:"24px"},children:(0,s.jsxs)(i.Z,{title:(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,s.jsx)(C.Z,{}),(0,s.jsx)("span",{children:"排产看板"}),(0,s.jsxs)(u.Z,{color:"blue",children:[R.length," 个工单"]})]}),extra:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(T.default,{value:g,onChange:e=>{p(e),y([null,null])},options:P,style:{width:80}}),(0,s.jsx)(M.default.RangePicker,{value:f[0]&&f[1]?[O()(f[0]),O()(f[1])]:[null,null],onChange:e=>{e&&e[0]&&e[1]?y([e[0].toDate(),e[1].toDate()]):y([null,null])},placeholder:["开始日期","结束日期"],style:{width:240}}),(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(w.Z,{}),onClick:D,loading:S,children:"刷新"})]}),children:[(0,s.jsx)("div",{style:{border:"1px solid #f0f0f0",borderRadius:"6px",overflow:"hidden",display:"flex",flexDirection:"column"},children:(0,s.jsxs)("div",{style:{display:"flex",height:"600px"},children:[(0,s.jsxs)("div",{style:{width:"50px",flexShrink:0,display:"flex",flexDirection:"column",background:"#fafafa",borderRight:"2px solid #e8e8e8",zIndex:10},children:[(0,s.jsx)("div",{style:{height:"100px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:600,fontSize:"10px",color:"#666",borderBottom:"2px solid #e8e8e8",padding:"4px 2px",boxSizing:"border-box",background:"#fafafa",writingMode:"vertical-rl",textOrientation:"mixed"},children:"工位"}),(0,s.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:B.map(e=>(0,s.jsx)("div",{title:"".concat(e.name," (").concat(e.code,")"),style:{minHeight:"50px",padding:"2px",borderBottom:"1px solid #f0f0f0",display:"flex",alignItems:"center",justifyContent:"center",background:"#fafafa",cursor:"help"},children:(0,s.jsx)("div",{style:{fontWeight:600,fontSize:"9px",color:"#333",textAlign:"center",lineHeight:"10px",display:"flex",alignItems:"center",justifyContent:"center",width:"100%",height:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:e.name})},e.id))})]}),(0,s.jsx)("div",{style:{flex:1,display:"flex",flexDirection:"column",overflow:"hidden"},children:(0,s.jsx)("div",{style:{width:"100%",height:"100%",overflowX:"auto",overflowY:"hidden"},children:(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",minWidth:"".concat(320*A,"px"),height:"100%"},children:[(0,s.jsx)("div",{style:{position:"sticky",top:0,zIndex:5,flexShrink:0},children:(0,s.jsx)(G,{startDate:N,days:A,dayWidth:320,workTimeConfig:j})}),(0,s.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:B.map(e=>(0,s.jsx)(X,{tasks:z.get(e.code)||[],startDate:N,dayWidth:320,timeRange:k,actualDays:A,onTaskClick:c},e.id))})]})})})]})}),(0,s.jsxs)("div",{style:{marginTop:"16px",display:"flex",gap:"24px",flexWrap:"wrap",alignItems:"center"},children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[(0,s.jsx)("span",{style:{fontSize:"12px",color:"#666"},children:"状态说明："}),(0,s.jsx)(u.Z,{color:"blue",children:"已排程"}),(0,s.jsx)(u.Z,{color:"green",children:"进行中"}),(0,s.jsx)(u.Z,{color:"cyan",children:"已完成"})]}),(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[(0,s.jsx)("span",{style:{fontSize:"12px",color:"#666"},children:"进度说明："}),(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,s.jsx)("div",{style:{width:"20px",height:"8px",background:"#52c41a",borderRadius:"2px"}}),(0,s.jsx)("span",{style:{fontSize:"12px"},children:"已完成"})]}),(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[(0,s.jsx)("div",{style:{width:"20px",height:"8px",background:"#f0f0f0",borderRadius:"2px"}}),(0,s.jsx)("span",{style:{fontSize:"12px"},children:"未完成"})]})]})]})]})})},J=r(23656),$=r(89198),ee=r(47628),et=r(75123),er=r(50574),es=r(75216);class en{static canEditOrder(e){return!!e&&"in_plan"===e.status}static canDeleteOrder(e){return!!e&&["in_plan","cancelled"].includes(e.status)}static canCreateWorkOrder(e){return!!e&&"in_plan"===e.status&&(e.quantity||0)>0}static canCancelOrder(e){return!!e&&["in_plan","in_progress"].includes(e.status)}static canCompleteOrder(e){return!!e&&"in_progress"===e.status}static validateOrderStatus(e){if(!e)return{isValid:!1,canEdit:!1,canDelete:!1,canCreateWorkOrder:!1,canCancel:!1,canComplete:!1,message:"订单数据无效"};let t=this.canEditOrder(e),r=this.canDeleteOrder(e);return{isValid:!0,canEdit:t,canDelete:r,canCreateWorkOrder:this.canCreateWorkOrder(e),canCancel:this.canCancelOrder(e),canComplete:this.canCompleteOrder(e),message:this.getStatusMessage(e.status)}}static getStatusMessage(e){return({in_plan:"订单在计划中，可以编辑、删除或创建工单",planned:"订单已规划，准备进入生产",in_progress:"订单正在生产中，可以取消或完成",completed:"订单已完成，无法修改",cancelled:"订单已取消，只能删除"})[e]||"未知状态"}static batchValidateOrderStatus(e){let t=new Map;return e.forEach(e=>{e.id&&t.set(e.id,this.validateOrderStatus(e))}),t}static getOperableOrders(e,t){return e.filter(e=>{switch(t){case"edit":return this.canEditOrder(e);case"delete":return this.canDeleteOrder(e);case"createWorkOrder":return this.canCreateWorkOrder(e);case"cancel":return this.canCancelOrder(e);case"complete":return this.canCompleteOrder(e);default:return!1}})}}var ea=r(87304),eo=r(57084),ei=r(80782),el=r(13412),ec=r(928),ed=r(43043),eu=r(57098);let eh={production_order:{in_plan:{color:"blue",icon:(0,s.jsx)(S.Z,{}),text:"计划中",antdColor:"blue"},planned:{color:"cyan",icon:(0,s.jsx)(ea.Z,{}),text:"已计划",antdColor:"cyan"},in_production:{color:"orange",icon:(0,s.jsx)(eo.Z,{}),text:"生产中",antdColor:"orange"},completed:{color:"green",icon:(0,s.jsx)(ea.Z,{}),text:"已完成",antdColor:"green"},cancelled:{color:"red",icon:(0,s.jsx)(ei.Z,{}),text:"已取消",antdColor:"red"}},work_order:{pending:{color:"default",icon:(0,s.jsx)(S.Z,{}),text:"待开始",antdColor:"default"},in_progress:{color:"processing",icon:(0,s.jsx)(eo.Z,{}),text:"进行中",antdColor:"processing"},paused:{color:"warning",icon:(0,s.jsx)(el.Z,{}),text:"已暂停",antdColor:"warning"},completed:{color:"success",icon:(0,s.jsx)(ea.Z,{}),text:"已完成",antdColor:"success"},cancelled:{color:"error",icon:(0,s.jsx)(ei.Z,{}),text:"已取消",antdColor:"error"}},workstation:{active:{color:"success",icon:(0,s.jsx)(ea.Z,{}),text:"启用",antdColor:"success"},inactive:{color:"default",icon:(0,s.jsx)(ec.Z,{}),text:"停用",antdColor:"default"},maintenance:{color:"warning",icon:(0,s.jsx)(b.Z,{}),text:"维护中",antdColor:"warning"},error:{color:"error",icon:(0,s.jsx)(ed.Z,{}),text:"故障",antdColor:"error"}},credit_level:{A:{color:"green",text:"A级",antdColor:"green"},B:{color:"blue",text:"B级",antdColor:"blue"},C:{color:"orange",text:"C级",antdColor:"orange"},D:{color:"red",text:"D级",antdColor:"red"},E:{color:"volcano",text:"E级",antdColor:"volcano"}},priority:{low:{color:"default",text:"低",antdColor:"default"},medium:{color:"blue",text:"中",antdColor:"blue"},high:{color:"orange",text:"高",antdColor:"orange"},urgent:{color:"red",text:"紧急",antdColor:"red"}},work_time:{active:{color:"success",icon:(0,s.jsx)(ea.Z,{}),text:"启用",antdColor:"success"},inactive:{color:"default",icon:(0,s.jsx)(ec.Z,{}),text:"停用",antdColor:"default"},configured:{color:"processing",icon:(0,s.jsx)(S.Z,{}),text:"已配置",antdColor:"processing"},pending:{color:"warning",icon:(0,s.jsx)(ed.Z,{}),text:"待配置",antdColor:"warning"},updating:{color:"blue",icon:(0,s.jsx)(S.Z,{}),text:"更新中",antdColor:"blue"}}},em=n.memo(e=>{let{type:t,status:r,showIcon:n=!0,style:a,className:o,onClick:i}=e,l=((e,t)=>{let r=eh[e];return r&&r[t]||{color:"default",icon:(0,s.jsx)(eu.Z,{}),text:t,antdColor:"default"}})(t,r);return(0,s.jsx)(u.Z,{color:l.antdColor||l.color,icon:n?l.icon:void 0,style:a,className:o,onClick:i,children:l.text})});em.displayName="UnifiedTagRenderer";let ex=n.memo(e=>{let{status:t,showIcon:r=!0,onClick:n}=e;return(0,s.jsx)(em,{type:"production_order",status:t,showIcon:r,onClick:n})});ex.displayName="ProductionOrderStatusTag";let eg=n.memo(e=>{let{status:t,showIcon:r=!0,onClick:n}=e;return(0,s.jsx)(em,{type:"work_order",status:t,showIcon:r,onClick:n})});eg.displayName="WorkOrderStatusTag",n.memo(e=>{let{status:t,showIcon:r=!0,onClick:n}=e;return(0,s.jsx)(em,{type:"workstation",status:t,showIcon:r,onClick:n})}).displayName="WorkstationStatusTag";let ep=n.memo(e=>{let{level:t,onClick:r}=e;return(0,s.jsx)(em,{type:"credit_level",status:t,showIcon:!1,onClick:r})});ep.displayName="CreditLevelTag",n.memo(e=>{let{priority:t,onClick:r}=e;return(0,s.jsx)(em,{type:"priority",status:t,showIcon:!1,onClick:r})}).displayName="PriorityTag";let ef=n.memo(e=>{let{status:t,showIcon:r=!0,onClick:n,style:a,className:o}=e;return(0,s.jsx)(em,{type:"work_time",status:t,showIcon:r,onClick:n,style:a,className:o})});ef.displayName="WorkTimeStatusTag",n.memo(e=>{let{tags:t,direction:r="horizontal",size:n="small"}=e;return(0,s.jsx)(d.Z,{direction:r,size:n,children:t.map((e,t)=>(0,s.jsx)(em,{type:e.type,status:e.status,showIcon:e.showIcon},"".concat(e.type,"-").concat(e.status,"-").concat(t)))})}).displayName="CombinedTags";let ey=e=>{let t=eh[e];return t?Object.entries(t).map(e=>{let[t,r]=e;return{text:r.text,value:t}}):[]},{Text:ej}=J.default,ew=n.memo(e=>{let{open:t,loading:r,selectedOrders:a,onConfirm:o,onCancel:i}=e,[l]=$.Z.useForm();(0,n.useEffect)(()=>{t&&l.setFieldsValue({hourlyCapacity:130})},[t,l]);let c=async()=>{try{let e=await l.validateFields();await o(e),l.resetFields()}catch(e){}};return(0,s.jsxs)(ee.Z,{title:"创建生产工单",open:t,onOk:c,onCancel:()=>{l.resetFields(),i()},confirmLoading:r,width:600,destroyOnHidden:!0,children:[(0,s.jsxs)("div",{style:{marginBottom:16},children:[(0,s.jsx)(ej,{strong:!0,children:"已选择的生产订单："}),(0,s.jsx)("div",{style:{marginTop:8,maxHeight:200,overflowY:"auto"},children:a.map(e=>(0,s.jsx)("div",{style:{padding:"8px 12px",border:"1px solid #d9d9d9",borderRadius:6,marginBottom:8,backgroundColor:"#fafafa"},children:(0,s.jsxs)(d.Z,{direction:"vertical",size:4,children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(ej,{strong:!0,children:"订单号："}),(0,s.jsx)(ej,{children:e.orderNumber})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ej,{strong:!0,children:"产品名称："}),(0,s.jsx)(ej,{children:e.productName})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ej,{strong:!0,children:"计划数量："}),(0,s.jsx)(ej,{children:e.plannedQuantity})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ej,{strong:!0,children:"客户："}),(0,s.jsx)(ej,{children:e.customerName}),e.customerCreditLevel&&(0,s.jsx)("div",{style:{marginLeft:8},children:(0,s.jsx)(ep,{level:e.customerCreditLevel})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(ej,{strong:!0,children:"信用等级："}),e.customerCreditLevel?(0,s.jsx)(ep,{level:e.customerCreditLevel}):(0,s.jsx)(u.Z,{color:"default",children:"未设置"})]})]})},e.id))})]}),(0,s.jsx)($.Z,{form:l,layout:"vertical",children:(0,s.jsx)($.Z.Item,{name:"hourlyCapacity",label:"小时产能",rules:[{required:!0,message:"请输入小时产能"},{type:"number",min:1,message:"产能必须大于0"}],children:(0,s.jsx)(et.Z,{style:{width:"100%"},placeholder:"请输入小时产能",min:1,max:1e3,precision:0,addonAfter:"模/小时"})})})]})}),ev=n.memo(e=>{let{orders:t,loading:r=!1,onRefresh:o,onOrderDetail:l,onStatusChange:c,onOrderSelectionChange:m,onCreateWorkOrders:x,onTabChange:g,workstations:p=[],currentSplitConfig:f,selectedOrdersCount:y=0}=e,{message:j}=a.Z.useApp(),[v,k]=(0,n.useState)([]),[S,Z]=(0,n.useState)([]),[C,D]=(0,n.useState)(!1),[T,M]=(0,n.useState)(!1),N=(0,n.useCallback)(()=>{null==o||o()},[o]),E=(0,n.useCallback)(e=>{null==m||m(e)},[m]);(0,n.useCallback)(()=>{D(!0)},[]);let W=(0,n.useCallback)(()=>{D(!1),M(!1)},[]),_=(0,n.useCallback)(async e=>{try{M(!0);let t=S.map(e=>e.id).filter(Boolean);await (null==x?void 0:x(t,e.hourlyCapacity)),D(!1),k([]),Z([]),N()}catch(e){console.error("创建工单失败:",e)}finally{M(!1)}},[S,x,N]),A=(0,n.useMemo)(()=>[{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>(0,s.jsx)(ex,{status:e})},{title:"生产订单号",dataIndex:"orderNumber",key:"orderNumber",width:150},{title:"产品名称",dataIndex:"productName",key:"productName",width:200,render:(e,t)=>t.isSharedMold&&t.productItems&&t.productItems.length>0?(0,s.jsx)("div",{style:{lineHeight:"1.4"},children:t.productItems.map((e,r)=>(0,s.jsx)("div",{style:{marginBottom:r<t.productItems.length-1?"2px":"0"},children:(0,s.jsx)(ej,{style:{fontSize:"13px",display:"block",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"180px"},title:z(e.productName),children:z(e.productName)})},r))}):(0,s.jsx)(ej,{children:z(e)})},{title:"产品编码",dataIndex:"productCode",key:"productCode",width:120,render:(e,t)=>t.isSharedMold&&t.productItems&&t.productItems.length>0?(0,s.jsx)("div",{style:{lineHeight:"1.4"},children:t.productItems.map((e,r)=>(0,s.jsx)("div",{style:{marginBottom:r<t.productItems.length-1?"2px":"0"},children:(0,s.jsx)(ej,{style:{fontSize:"13px"},children:e.productCode})},r))}):(0,s.jsx)(ej,{children:e})},{title:"计划数量",dataIndex:"plannedQuantity",key:"plannedQuantity",width:100,render:e=>(0,s.jsx)(ej,{strong:!0,children:e})},{title:"客户信息",key:"customerInfo",width:180,render:(e,t)=>(0,s.jsxs)(d.Z,{direction:"vertical",size:2,children:[(0,s.jsx)(ej,{strong:!0,children:t.customerName}),t.customerCreditLevel&&(0,s.jsx)(ep,{level:t.customerCreditLevel})]})},{title:"信用等级",dataIndex:"customerCreditLevel",key:"customerCreditLevel",width:120,render:(e,t)=>e?(0,s.jsxs)(d.Z,{direction:"vertical",size:2,children:[(0,s.jsx)(ep,{level:e}),"manual"===t.prioritySource&&(0,s.jsx)(u.Z,{color:"blue",style:{fontSize:"12px"},children:"手动"})]}):(0,s.jsx)(u.Z,{color:"default",children:"未设置"})},{title:"交付日期",dataIndex:"deliveryDate",key:"deliveryDate",width:120,render:e=>O()(e).format("YYYY-MM-DD"),sorter:(e,t)=>O()(e.deliveryDate).unix()-O()(t.deliveryDate).unix()},{title:"成型模具编号",dataIndex:"formingMoldNumber",key:"formingMoldNumber",width:140,render:e=>e||"-"},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:140,render:e=>O()(e).format("YYYY-MM-DD HH:mm"),sorter:(e,t)=>O()(e.createdAt).unix()-O()(t.createdAt).unix(),defaultSortOrder:"descend"},{title:"操作",key:"action",width:150,render:(e,t)=>(0,s.jsx)(d.Z,{size:"small",children:(0,s.jsx)(h.ZP,{type:"link",size:"small",icon:(0,s.jsx)(es.Z,{}),onClick:()=>null==l?void 0:l(t),children:"详情"})})}],[l,c]),R=(0,n.useMemo)(()=>({selectedRowKeys:v,onChange:(e,t)=>{k(e),Z(t),E(t)},getCheckboxProps:e=>({disabled:!en.canCreateWorkOrder(e)})}),[v,E]),B=(0,n.useCallback)(()=>0===S.length?(j.warning("请选择要创建工单的生产订单"),!1):!(S.filter(e=>!en.canCreateWorkOrder(e)).length>0)||(j.error('只能为"计划中"状态的订单创建工单'),!1),[S,j]),P=(0,n.useCallback)(()=>{B()&&D(!0)},[B]);return(0,s.jsxs)(i.Z,{title:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)("span",{children:"生产订单列表"}),(0,s.jsxs)(ej,{type:"secondary",children:["(",t.length," 个订单)"]})]}),extra:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(h.ZP,{type:"primary",icon:(0,s.jsx)(b.Z,{}),onClick:P,disabled:0===S.length,children:"创建工单"}),(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(w.Z,{}),onClick:N,loading:r,children:"刷新"})]}),children:[0===t.length?(0,s.jsx)(I.Z,{description:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{children:"暂无生产订单"}),(0,s.jsx)("div",{style:{fontSize:"12px",color:"#666",marginTop:"8px"},children:"生产订单只能通过MRP流程创建，请前往销售订单管理页面启动MRP"})]}),image:I.Z.PRESENTED_IMAGE_SIMPLE}):(0,s.jsx)(er.Z,{rowSelection:R,columns:A,dataSource:t,rowKey:"id",loading:r,pagination:{total:t.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条")},scroll:{x:1140}}),(0,s.jsx)(ew,{open:C,loading:T,selectedOrders:S,onConfirm:_,onCancel:W})]})});var ek=r(66326),eS=r(99617),eZ=r(74424);let{Text:eb,Title:eC}=J.default,eD={title:"生产订单详情",statusConfig:E.Fi,getStatus:e=>e.status,width:800,sections:[{title:"基本信息",columns:2,bordered:!0,size:"small",fields:[{label:"生产订单号",key:"orderNumber"},{label:"状态",key:"status",render:e=>eZ.Xe.status(e,E.Fi)},{label:"订单类型",key:"isSharedMold",render:(e,t)=>e&&t.productItems&&t.productItems.length>1?(0,s.jsx)(u.Z,{color:"blue",children:"\uD83D\uDD27 共享模具生产"}):(0,s.jsx)(u.Z,{color:"default",children:"单产品生产"})},{label:"成型模具",key:"formingMoldNumber",render:e=>e||eZ.Xe.empty("未指定")},{label:"产品名称",key:"productName",hidden:e=>e.isSharedMold&&e.productItems&&e.productItems.length>1},{label:"产品编码",key:"productCode"},{label:"客户信用等级",key:"customerCreditLevel",render:e=>e?(0,s.jsx)(u.Z,{color:{A:"red",B:"orange",C:"green"}[e]||"default",children:e}):eZ.Xe.empty("未设置")}]},{title:"产品明细",columns:1,bordered:!1,hidden:e=>!(e.isSharedMold&&e.productItems&&e.productItems.length>1),fields:[],customContent:e=>e.isSharedMold&&e.productItems&&!(e.productItems.length<=1)?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{style:{marginBottom:16},children:(0,s.jsxs)(eb,{type:"secondary",children:["此生产订单包含 ",e.productItems.length," 个产品，使用共享模具 ",e.formingMoldNumber," 同时生产"]})}),e.productItems.map((e,t)=>(0,s.jsx)("div",{style:{marginBottom:12,padding:12,border:"1px solid #f0f0f0",borderRadius:4},children:(0,s.jsxs)(eS.Z,{column:3,size:"small",children:[(0,s.jsx)(eS.Z.Item,{label:"产品编码",children:e.productCode}),(0,s.jsx)(eS.Z.Item,{label:"产品名称",children:e.productName}),(0,s.jsx)(eS.Z.Item,{label:"需求数量",children:(0,s.jsx)(eb,{type:"secondary",children:e.requiredQuantity})}),(0,s.jsx)(eS.Z.Item,{label:"生产数量",children:(0,s.jsx)(eb,{strong:!0,children:e.plannedQuantity})}),(0,s.jsx)(eS.Z.Item,{label:"超产数量",children:(0,s.jsx)(eb,{type:e.plannedQuantity>e.requiredQuantity?"warning":"secondary",children:e.plannedQuantity-e.requiredQuantity})})]})},e.id))]}):null},{title:"生产信息",columns:2,bordered:!0,size:"small",fields:[{label:"计划数量",key:"plannedQuantity",render:e=>(0,s.jsx)(eb,{strong:!0,children:e})},{label:"分配工位",key:"workstation",render:e=>e||eZ.Xe.empty("未分配")},{label:"交付日期",key:"deliveryDate",render:e=>eZ.Xe.date(e)}]},{title:"关联信息",columns:2,bordered:!0,size:"small",fields:[{label:"销售订单号",key:"salesOrderNumber",render:(e,t)=>e||(t.sourceOrderNumbers&&t.sourceOrderNumbers.length>0?t.sourceOrderNumbers[0]:eZ.Xe.empty("无"))},{label:"客户名称",key:"customerName",render:e=>e||eZ.Xe.empty("无")},{label:"创建时间",key:"createdAt",render:e=>eZ.Xe.datetime(e)},{label:"更新时间",key:"updatedAt",render:e=>eZ.Xe.datetime(e)}]}]};var eT=e=>{let{open:t,order:r,onClose:n,onStatusChange:a}=e;return(0,s.jsx)(ek.ZP,{open:t,order:r,onClose:n,config:eD})},eM=r(39992),eI=r(57927),eN=r(92503),eO=r(59189),eE=r(65362),eW=r(34021),e_=r(64370),eA=r(74898),eR=r(74408),ez=r(8349),eB=r(5815),eP=r(67883);class eL{static getOrderStatusConfig(e){return this.ORDER_STATUS_MAP[e]||{color:"default",icon:(0,s.jsx)(eu.Z,{}),text:"未知状态"}}static getWorkstationStatusConfig(e){return this.WORKSTATION_STATUS_MAP[e]||{color:"default",icon:(0,s.jsx)(eu.Z,{}),text:"未知状态"}}static getProductionOrderStatusConfig(e){return this.PRODUCTION_ORDER_STATUS_MAP[e]||{color:"default",icon:(0,s.jsx)(eu.Z,{}),text:"未知状态"}}static getWorkOrderStatusConfig(e){return this.WORK_ORDER_STATUS_MAP[e]||{color:"default",icon:(0,s.jsx)(eu.Z,{}),text:"未知状态"}}static renderOrderStatusTag(e){let t=this.getOrderStatusConfig(e);return(0,s.jsx)(u.Z,{color:t.color,icon:t.icon,children:t.text})}static renderWorkstationStatusTag(e){let t=this.getWorkstationStatusConfig(e);return(0,s.jsx)(u.Z,{color:t.color,icon:t.icon,children:t.text})}static renderProductionOrderStatusTag(e){let t=this.getProductionOrderStatusConfig(e);return(0,s.jsx)(u.Z,{color:t.color,icon:t.icon,children:t.text})}static renderWorkOrderStatusTag(e){let t=this.getWorkOrderStatusConfig(e);return(0,s.jsx)(u.Z,{color:t.color,icon:t.icon,children:t.text})}static renderStatusTag(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"order";switch(t){case"workstation":return this.renderWorkstationStatusTag(e);case"production_order":return this.renderProductionOrderStatusTag(e);case"work_order":return this.renderWorkOrderStatusTag(e);default:return this.renderOrderStatusTag(e)}}static getStatusColor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"order";switch(t){case"workstation":return this.getWorkstationStatusConfig(e).color;case"production_order":return this.getProductionOrderStatusConfig(e).color;case"work_order":return this.getWorkOrderStatusConfig(e).color;default:return this.getOrderStatusConfig(e).color}}static getStatusIcon(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"order";switch(t){case"workstation":return this.getWorkstationStatusConfig(e).icon;case"production_order":return this.getProductionOrderStatusConfig(e).icon;case"work_order":return this.getWorkOrderStatusConfig(e).icon;default:return this.getOrderStatusConfig(e).icon}}static getStatusText(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"order";switch(t){case"workstation":return this.getWorkstationStatusConfig(e).text;case"production_order":return this.getProductionOrderStatusConfig(e).text;case"work_order":return this.getWorkOrderStatusConfig(e).text;default:return this.getOrderStatusConfig(e).text}}}eL.ORDER_STATUS_MAP={pending:{color:"orange",icon:(0,s.jsx)(S.Z,{}),text:"未审核"},confirmed:{color:"green",icon:(0,s.jsx)(ea.Z,{}),text:"已审核"},completed:{color:"default",icon:(0,s.jsx)(eP.Z,{}),text:"完成"},cancelled:{color:"red",icon:(0,s.jsx)(ei.Z,{}),text:"已取消"}},eL.WORKSTATION_STATUS_MAP={active:{color:"success",icon:(0,s.jsx)(ea.Z,{}),text:"启用"},inactive:{color:"default",icon:(0,s.jsx)(ec.Z,{}),text:"停用"}},eL.PRODUCTION_ORDER_STATUS_MAP={planned:{color:"blue",icon:(0,s.jsx)(S.Z,{}),text:"计划中"},in_progress:{color:"processing",icon:(0,s.jsx)(f.Z,{spin:!0}),text:"生产中"},completed:{color:"success",icon:(0,s.jsx)(ea.Z,{}),text:"已完成"},cancelled:{color:"error",icon:(0,s.jsx)(ei.Z,{}),text:"已取消"}},eL.WORK_ORDER_STATUS_MAP={pending:{color:"default",icon:(0,s.jsx)(S.Z,{}),text:"待开始"},in_progress:{color:"processing",icon:(0,s.jsx)(f.Z,{spin:!0}),text:"进行中"},completed:{color:"success",icon:(0,s.jsx)(ea.Z,{}),text:"已完成"},paused:{color:"warning",icon:(0,s.jsx)(ed.Z,{}),text:"已暂停"}};let{renderOrderStatusTag:eH,renderWorkstationStatusTag:eV,renderProductionOrderStatusTag:eF,renderWorkOrderStatusTag:eQ,renderStatusTag:eU,getStatusColor:eY,getStatusIcon:eq,getStatusText:eX,getOrderStatusConfig:eG,getWorkstationStatusConfig:eK,getProductionOrderStatusConfig:eJ,getWorkOrderStatusConfig:e$}=eL;var e0=r(93011),e1=e=>{let{workstation:t,onEdit:r,onDelete:n,onStatusToggle:a}=e;return(0,s.jsx)(i.Z,{style:{height:"100%",transition:e0.ZB.transitions.normal,borderColor:"active"===t.status?"#bbf7d0":e0.ZB.colors.gray[200],...e0.ZB.createShadow("md")},styles:{body:{padding:e0.ZB.spacing.md},header:{borderBottom:"1px solid ".concat(e0.ZB.colors.gray[200])}},onMouseEnter:e=>{e.currentTarget.style.boxShadow=e0.ZB.shadows.lg},onMouseLeave:e=>{e.currentTarget.style.boxShadow=e0.ZB.shadows.md},actions:[(0,s.jsx)(D.Z,{title:"编辑",children:(0,s.jsx)(h.ZP,{type:"text",icon:(0,s.jsx)(eE.Z,{}),onClick:()=>r(t)})},"edit"),(0,s.jsx)(D.Z,{title:"active"===t.status?"停用":"启用",children:(0,s.jsx)(h.ZP,{type:"text",icon:eL.getStatusIcon(t.status,"workstation"),onClick:()=>a(t)})},"toggle"),(0,s.jsx)(eN.Z,{title:"确定要删除这个工位吗？",description:"删除后将无法恢复，请谨慎操作。",onConfirm:()=>n(t.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(D.Z,{title:"删除",children:(0,s.jsx)(h.ZP,{type:"text",danger:!0,icon:(0,s.jsx)(eW.Z,{})})})},"delete")],children:(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:e0.ZB.spacing.md},children:[(0,s.jsx)("div",{style:{display:"flex",alignItems:"flex-start",justifyContent:"space-between"},children:(0,s.jsxs)("div",{style:{flex:1},children:[(0,s.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:e0.ZB.spacing.sm},children:[(0,s.jsx)("span",{style:{fontFamily:"monospace",fontSize:"18px",fontWeight:"bold",color:e0.ZB.colors.primary[600]},children:B(t.code)}),eL.renderWorkstationStatusTag(t.status)]}),(0,s.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",marginTop:e0.ZB.spacing.xs,color:e0.ZB.colors.gray[800],margin:"".concat(e0.ZB.spacing.xs,"px 0 0 0")},children:B(t.name)}),t.description&&(0,s.jsx)("p",{style:{fontSize:"14px",color:e0.ZB.colors.gray[500],marginTop:e0.ZB.spacing.xs,display:"-webkit-box",WebkitLineClamp:2,WebkitBoxOrient:"vertical",overflow:"hidden",margin:"".concat(e0.ZB.spacing.xs,"px 0 0 0")},children:B(t.description)})]})}),(0,s.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:e0.ZB.spacing.sm},children:(0,s.jsxs)("div",{style:{background:e0.ZB.colors.primary[50],borderRadius:e0.ZB.borderRadius.sm,padding:e0.ZB.spacing.sm,marginTop:e0.ZB.spacing.sm},children:[(0,s.jsxs)("div",{style:{fontSize:"12px",color:e0.ZB.colors.primary[600],marginBottom:e0.ZB.spacing.xs},children:[(0,s.jsx)(y.Z,{style:{marginRight:e0.ZB.spacing.xs}}),"高级功能"]}),(0,s.jsx)("div",{style:{fontSize:"12px",color:e0.ZB.colors.primary[500]},children:"工位状态监控、产能统计等功能正在开发中"})]})}),(0,s.jsxs)("div",{style:{fontSize:"12px",color:e0.ZB.colors.gray[400],borderTop:"1px solid ".concat(e0.ZB.colors.gray[200]),paddingTop:e0.ZB.spacing.sm},children:[(0,s.jsxs)("div",{children:["创建: ",new Date(t.createdAt).toLocaleDateString()]}),(0,s.jsxs)("div",{children:["更新: ",new Date(t.updatedAt).toLocaleDateString()]})]})]})})},e2=r(50538);class e4{static getInstance(){return e4.instance||(e4.instance=new e4),e4.instance}async validateWorkstation(e){let t=[],r=[];try{let s=await W.dataAccessManager.productionWorkOrders.getAll();if("success"!==s.status||!s.data)return t.push({type:"missing_work_order",severity:"error",description:"无法获取生产工单数据进行验证"}),{isValid:!1,workstationId:e.id,workstationCode:e.code,issues:t,recommendations:["请检查生产工单数据访问服务"]};let n=s.data.items||[];if(!(e.currentMoldNumber||e.currentBatchNumber||e.batchNumberQueue&&e.batchNumberQueue.length>0))return{isValid:!0,workstationId:e.id,workstationCode:e.code,issues:[],recommendations:[]};if(e.currentBatchNumber){let s=n.find(t=>t.batchNumber===e.currentBatchNumber&&t.workstationCode===e.code);s?e.currentMoldNumber&&s.formingMoldNumber!==e.currentMoldNumber&&(t.push({type:"mold_mismatch",severity:"warning",description:"工位模具编号与工单不匹配",currentValue:e.currentMoldNumber,expectedValue:s.formingMoldNumber}),r.push("同步工位模具编号与工单数据")):(t.push({type:"missing_work_order",severity:"error",description:"工位当前批次号 ".concat(e.currentBatchNumber," 在生产工单中不存在"),currentValue:e.currentBatchNumber}),r.push("检查生产工单数据或重置工位状态"))}if(e.batchNumberQueue&&e.batchNumberQueue.length>0)for(let s of e.batchNumberQueue)n.find(t=>t.batchNumber===s&&(t.workstationCode===e.code||"pending"===t.status))||(t.push({type:"queue_inconsistency",severity:"warning",description:"队列中的批次号 ".concat(s," 在生产工单中不存在"),currentValue:s}),r.push("清理工位队列中的无效批次号"));return e.currentMoldNumber&&!e.currentBatchNumber&&(t.push({type:"orphaned_production_data",severity:"warning",description:"工位有模具编号但无对应批次号",currentValue:e.currentMoldNumber}),r.push("重置工位状态或补充批次号信息")),{isValid:0===t.filter(e=>"error"===e.severity).length,workstationId:e.id,workstationCode:e.code,issues:t,recommendations:r}}catch(t){return console.error("验证工位 ".concat(e.code," 数据一致性时发生错误:"),t),{isValid:!1,workstationId:e.id,workstationCode:e.code,issues:[{type:"missing_work_order",severity:"error",description:"验证过程中发生系统错误"}],recommendations:["请联系系统管理员检查"]}}}async validateAllWorkstations(){try{console.log("\uD83D\uDD0D 开始批量验证工位数据一致性");let e=await W.dataAccessManager.workstations.getAll();if("success"!==e.status||!e.data)throw Error("无法获取工位数据");let t=e.data.items||[],r=[];for(let e of t){let t=await this.validateWorkstation(e);r.push(t)}let s=r.filter(e=>e.isValid).length,n=r.length-s,a=r.filter(e=>!e.isValid),o={missingWorkOrders:0,batchMismatches:0,moldMismatches:0,orphanedData:0,queueInconsistencies:0};a.forEach(e=>{e.issues.forEach(e=>{switch(e.type){case"missing_work_order":o.missingWorkOrders++;break;case"batch_mismatch":o.batchMismatches++;break;case"mold_mismatch":o.moldMismatches++;break;case"orphaned_production_data":o.orphanedData++;break;case"queue_inconsistency":o.queueInconsistencies++}})});let i={totalWorkstations:t.length,validWorkstations:s,invalidWorkstations:n,issues:a,summary:o,generatedAt:new Date().toISOString()};return console.log("✅ 工位数据一致性验证完成:"),console.log("   总工位数: ".concat(i.totalWorkstations)),console.log("   有效工位: ".concat(i.validWorkstations)),console.log("   问题工位: ".concat(i.invalidWorkstations)),i}catch(e){throw console.error("批量验证工位数据一致性失败:",e),e}}async autoFixInconsistencies(e){let t=[],r=0;for(let s of(console.log("\uD83D\uDD27 开始自动修复 ".concat(e.length," 个工位的数据一致性问题")),e))try{s.issues.some(e=>"orphaned_production_data"===e.type||"missing_work_order"===e.type)?t.push({workstationId:s.workstationId,success:!1,message:"工位 ".concat(s.workstationCode," 有孤立数据，需要手动处理（单个工位重置功能暂时不可用）")}):t.push({workstationId:s.workstationId,success:!1,message:"工位 ".concat(s.workstationCode," 的问题需要手动处理")}),r++}catch(e){r++,t.push({workstationId:s.workstationId,success:!1,message:"修复工位 ".concat(s.workstationCode," 时发生错误: ").concat(e)})}return console.log("✅ 自动修复完成: 成功 ".concat(0," 个，失败 ").concat(r," 个")),{fixed:0,failed:r,results:t}}}let e6=e4.getInstance();var e8=r(3587);class e3{static getInstance(){return e3.instance||(e3.instance=new e3),e3.instance}connect(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/api/workstation/events";this.eventSource&&(console.warn("[WorkstationRealtimeService] 已存在连接，先断开现有连接"),this.disconnect()),console.log("\uD83D\uDD0C [WorkstationRealtimeService] 连接到实时事件流: ".concat(e)),this.setConnectionStatus("connecting");try{this.eventSource=new EventSource(e),this.setupEventHandlers()}catch(t){console.error("[WorkstationRealtimeService] 连接失败:",t),this.setConnectionStatus("error",t),this.scheduleReconnect(e)}}disconnect(){console.log("\uD83D\uDD0C [WorkstationRealtimeService] 断开实时事件流连接"),this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.setConnectionStatus("disconnected"),this.reconnectAttempts=0}addEventListener(e,t){this.listeners.has(e)||this.listeners.set(e,new Set),this.listeners.get(e).add(t),console.log("\uD83D\uDCDD [WorkstationRealtimeService] 添加事件监听器: ".concat(e))}removeEventListener(e,t){let r=this.listeners.get(e);r&&(r.delete(t),0===r.size&&this.listeners.delete(e)),console.log("\uD83D\uDDD1️ [WorkstationRealtimeService] 移除事件监听器: ".concat(e))}addConnectionStatusListener(e){this.statusListeners.add(e)}removeConnectionStatusListener(e){this.statusListeners.delete(e)}getConnectionStatus(){return this.connectionStatus}reconnect(){if(this.eventSource){let e=this.eventSource.url;this.disconnect(),this.connect(e)}}setupEventHandlers(){this.eventSource&&(this.eventSource.onopen=()=>{console.log("✅ [WorkstationRealtimeService] 实时事件流连接成功"),this.setConnectionStatus("connected"),this.reconnectAttempts=0,this.reconnectDelay=1e3},this.eventSource.onerror=e=>{var t;console.error("❌ [WorkstationRealtimeService] 实时事件流连接错误:",e),this.setConnectionStatus("error",Error("EventSource connection error")),(null===(t=this.eventSource)||void 0===t?void 0:t.readyState)===EventSource.CLOSED&&this.scheduleReconnect(this.eventSource.url)},this.eventSource.onmessage=e=>{try{let t=JSON.parse(e.data);this.handleWorkstationEvent(t)}catch(t){console.error("[WorkstationRealtimeService] 解析事件数据失败:",t,e.data)}},["workstation_updated","workstation_created","workstation_deleted","workstation_status_changed","workstation_production_assigned","workstation_reset","batch_update_completed"].forEach(e=>{this.eventSource.addEventListener(e,t=>{try{let e=JSON.parse(t.data);this.handleWorkstationEvent(e)}catch(t){console.error("[WorkstationRealtimeService] 解析 ".concat(e," 事件失败:"),t)}})}))}handleWorkstationEvent(e){console.log("\uD83D\uDCE1 [WorkstationRealtimeService] 收到工位事件:",{type:e.type,workstationId:e.workstationId,timestamp:e.timestamp});let t=this.listeners.get(e.type);t&&t.forEach(t=>{try{t(e)}catch(e){console.error("[WorkstationRealtimeService] 事件监听器执行失败:",e)}}),this.logEventHandling(e)}setConnectionStatus(e,t){this.connectionStatus!==e&&(this.connectionStatus=e,console.log("\uD83D\uDD04 [WorkstationRealtimeService] 连接状态变更: ".concat(e)),this.statusListeners.forEach(r=>{try{r(e,t)}catch(e){console.error("[WorkstationRealtimeService] 状态监听器执行失败:",e)}}))}scheduleReconnect(e){if(this.reconnectAttempts>=this.maxReconnectAttempts){console.error("[WorkstationRealtimeService] 达到最大重连次数 (".concat(this.maxReconnectAttempts,")，停止重连"));return}this.reconnectAttempts++;let t=Math.min(this.reconnectDelay*Math.pow(2,this.reconnectAttempts-1),this.maxReconnectDelay);console.log("\uD83D\uDD04 [WorkstationRealtimeService] 计划在 ".concat(t,"ms 后进行第 ").concat(this.reconnectAttempts," 次重连")),this.reconnectTimer=setTimeout(()=>{console.log("\uD83D\uDD04 [WorkstationRealtimeService] 执行第 ".concat(this.reconnectAttempts," 次重连")),this.connect(e)},t)}logEventHandling(e){var t,r,s;console.log("\uD83D\uDCDD [WorkstationRealtimeService] 事件处理日志:",{timestamp:new Date().toISOString(),eventType:e.type,workstationId:e.workstationId,source:null===(t=e.metadata)||void 0===t?void 0:t.source,userId:null===(r=e.metadata)||void 0===r?void 0:r.userId,reason:null===(s=e.metadata)||void 0===s?void 0:s.reason})}constructor(){this.eventSource=null,this.listeners=new Map,this.statusListeners=new Set,this.connectionStatus="disconnected",this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectDelay=1e3,this.maxReconnectDelay=3e4,this.reconnectTimer=null}}let e5=e3.getInstance(),{Search:e9}=eM.default,{Option:e7}=T.default,{Text:te}=J.default;var tt=e=>{let{loading:t=!1}=e,{message:r}=a.Z.useApp(),[o,m]=(0,n.useState)(!1),[p,f]=(0,n.useState)(null),[j]=$.Z.useForm(),[v,k]=(0,n.useState)(""),[S,C]=(0,n.useState)(""),[M,I]=(0,n.useState)("table"),[N,E]=(0,n.useState)("list"),[W,_]=(0,n.useState)([]),[A,R]=(0,n.useState)(!1),[z,B]=(0,n.useState)(!1),[P,L]=(0,n.useState)(null),[H,V]=(0,n.useState)(!1),{connectionStatus:F,isConnected:Q}=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{onAnyWorkstationEvent:s,...a}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{autoConnect:t=!0,endpoint:r="/api/workstation/events",disconnectOnUnmount:s=!0,onConnectionError:a,onConnectionStatusChange:o}=e,[i,l]=(0,n.useState)(e5.getConnectionStatus()),c=(0,n.useRef)(new Map),d=(0,n.useRef)(null),u=(0,n.useCallback)((e,t)=>{l(e),o&&o(e),t&&a&&a(t)},[a,o]),h=(0,n.useCallback)(()=>{e5.connect(r)},[r]),m=(0,n.useCallback)(()=>{e5.disconnect()},[]),x=(0,n.useCallback)(()=>{e5.reconnect()},[]),g=(0,n.useCallback)(e=>t=>(e5.addEventListener(e,t),c.current.has(e)||c.current.set(e,new Set),c.current.get(e).add(t),()=>{e5.removeEventListener(e,t);let r=c.current.get(e);r&&(r.delete(t),0===r.size&&c.current.delete(e))}),[]),p=(0,n.useCallback)(e=>{let t=["workstation_updated","workstation_created","workstation_deleted","workstation_status_changed","workstation_production_assigned","workstation_reset","batch_update_completed"].map(t=>g(t)(e));return()=>{t.forEach(e=>e())}},[g]);(0,n.useEffect)(()=>(d.current=u,e5.addConnectionStatusListener(u),t&&"disconnected"===i&&h(),()=>{d.current&&(e5.removeConnectionStatusListener(d.current),d.current=null),c.current.forEach((e,t)=>{e.forEach(e=>{e5.removeEventListener(t,e)})}),c.current.clear(),s&&m()}),[t,h,m,s,u,i]);let f="connected"===i,y="connecting"===i,j="error"===i;return{connectionStatus:i,isConnected:f,isConnecting:y,hasError:j,connect:h,disconnect:m,reconnect:x,onWorkstationUpdate:g("workstation_updated"),onWorkstationCreate:g("workstation_created"),onWorkstationDelete:g("workstation_deleted"),onWorkstationStatusChange:g("workstation_status_changed"),onWorkstationProductionAssign:g("workstation_production_assigned"),onWorkstationReset:g("workstation_reset"),onBatchUpdateComplete:g("batch_update_completed"),onAnyWorkstationEvent:p}}(r);return(0,n.useEffect)(()=>s(r=>{switch(console.log("\uD83D\uDD04 [useWorkstationSync] 处理工位事件: ".concat(r.type),r.workstationId),r.type){case"workstation_updated":case"workstation_status_changed":case"workstation_production_assigned":case"workstation_reset":r.workstation&&t(e.map(e=>e.id===r.workstationId?r.workstation:e));break;case"workstation_created":r.workstation&&t([...e,r.workstation]);break;case"workstation_deleted":t(e.filter(e=>e.id!==r.workstationId));break;case"batch_update_completed":console.log("\uD83D\uDD04 [useWorkstationSync] 批量更新完成，建议重新加载数据")}}),[s,t,e]),a}(W,_,{autoConnect:!0,onConnectionError:e=>{console.error("工位实时同步连接错误:",e),r.warning("实时同步连接异常，数据可能不是最新的")},onConnectionStatusChange:e=>{console.log("工位实时同步状态: ".concat(e)),"connected"===e?r.success("实时同步已连接",2):"disconnected"===e&&r.warning("实时同步已断开",2)}}),U=(0,n.useCallback)(async()=>{R(!0);try{let e=await dataAccessManager.workstations.getAll();"success"===e.status&&e.data&&e.data.items?(_(e.data.items),console.log({count:e.data.items.length,workstations:e.data.items.map(e=>({id:e.id,code:e.code,name:e.name}))})):r.error(e.message||"加载工位数据失败")}catch(e){r.error("系统错误，请稍后重试")}finally{R(!1)}},[r]);(0,n.useEffect)(()=>{U()},[U]);let Y=()=>{let e=W.map(e=>e.code);for(let t of["A","B","C","D","E","F","G","H"])for(let r=1;r<=99;r++){let s="".concat(t).concat(r);if(!e.includes(s))return s}return"A".concat(e.length+1)},q=e=>{f(e),j.setFieldsValue({...e}),m(!0)},X=async e=>{await (0,e2.Ro)(()=>dataAccessManager.workstations.delete(e),"删除工位")&&(r.success("工位删除成功"),dataAccessManager.clearServiceCache("WorkstationService"),await U())},G=async e=>{try{let t="active"===e.status?"inactive":"active";console.log("\uD83D\uDD04 开始切换工位 ".concat(e.code," 状态: ").concat(e.status," → ").concat(t));let s=await e8.r.updateWorkstation(e.id,{status:t},{source:"user",operation:"status_change",userId:"current_user",reason:"用户手动".concat("active"===t?"启用":"停用","工位")},e.version);s.success?(r.success("工位已".concat("active"===t?"启用":"停用")),dataAccessManager.clearServiceCache("WorkstationService"),await U(),console.log("✅ 工位 ".concat(e.code," 状态切换成功"))):(s.conflictInfo?(r.error("状态切换失败：数据已被其他用户修改，请刷新后重试"),dataAccessManager.clearServiceCache("WorkstationService"),await U()):r.error("状态切换失败：".concat(s.error)),console.error("❌ 工位 ".concat(e.code," 状态切换失败:"),s.error)),s.warnings&&s.warnings.length>0&&s.warnings.forEach(e=>{r.warning(e)})}catch(e){console.error("❌ 工位状态切换异常:",e),r.error("系统错误，请稍后重试")}},K=async e=>{try{console.log("\uD83D\uDD04 开始重置工位 ".concat(e.code," 状态"));let t=await e8.r.updateWorkstation(e.id,{currentMoldNumber:null,currentBatchNumber:null,batchNumberQueue:[],lastEndTime:null},{source:"user",operation:"reset",userId:"current_user",reason:"用户手动重置工位为空闲状态"},e.version);t.success?(r.success("工位 ".concat(e.code," 已重置为空闲状态")),dataAccessManager.clearServiceCache("WorkstationService"),await U(),console.log("✅ 工位 ".concat(e.code," 重置完成"))):(t.conflictInfo?(r.error("重置失败：数据已被其他用户修改，请刷新后重试"),dataAccessManager.clearServiceCache("WorkstationService"),await U()):r.error("重置失败：".concat(t.error)),console.error("❌ 工位 ".concat(e.code," 重置失败:"),t.error)),t.warnings&&t.warnings.length>0&&t.warnings.forEach(e=>{r.warning(e)})}catch(t){console.error("❌ 重置工位 ".concat(e.code," 状态异常:"),t),r.error("系统错误，请稍后重试")}},J=async()=>{try{B(!0),console.log("\uD83D\uDD0D 开始工位数据一致性验证");let e=await e6.validateAllWorkstations();L(e),V(!0),0===e.invalidWorkstations?r.success("所有工位数据一致性验证通过"):r.warning("发现 ".concat(e.invalidWorkstations," 个工位存在数据一致性问题")),console.log("✅ 数据一致性验证完成:",e)}catch(e){console.error("❌ 数据一致性验证失败:",e),r.error("数据一致性验证失败，请稍后重试")}finally{B(!1)}},et=async()=>{if(!P||0===P.issues.length){r.info("没有需要修复的问题");return}try{console.log("\uD83D\uDD27 开始自动修复数据一致性问题");let e=await e6.autoFixInconsistencies(P.issues);if(e.fixed>0){r.success("成功修复 ".concat(e.fixed," 个工位的数据问题")),await U();let t=await e6.validateAllWorkstations();L(t)}e.failed>0&&r.warning("".concat(e.failed," 个工位的问题需要手动处理")),console.log("✅ 自动修复完成:",e)}catch(e){console.error("❌ 自动修复失败:",e),r.error("自动修复失败，请稍后重试")}},es=async()=>{try{let e=await j.validateFields();if(p){console.log("\uD83D\uDD04 开始更新工位 ".concat(p.code));let t=await e8.r.updateWorkstation(p.id,e,{source:"user",operation:"general",userId:"current_user",reason:"用户编辑工位信息"},p.version);if(t.success)r.success("工位更新成功"),dataAccessManager.clearServiceCache("WorkstationService"),await U(),console.log("✅ 工位 ".concat(p.code," 更新成功"));else{t.conflictInfo?(r.error("更新失败：数据已被其他用户修改，请刷新后重试"),dataAccessManager.clearServiceCache("WorkstationService"),await U()):r.error("更新失败：".concat(t.error)),console.error("❌ 工位 ".concat(p.code," 更新失败:"),t.error);return}t.warnings&&t.warnings.length>0&&t.warnings.forEach(e=>{r.warning(e)})}else{let t={id:"ws_".concat(Date.now()),...e,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};if(!await (0,e2.Ro)(()=>dataAccessManager.workstations.create(t),"创建工位"))return;r.success("工位创建成功"),dataAccessManager.clearServiceCache("WorkstationService"),await U(),console.log("✅ 工位 ".concat(e.code," 创建成功"))}m(!1),j.resetFields(),f(null)}catch(e){console.error("工位操作失败:",e),r.error("操作失败，请重试")}},en=W.filter(e=>{let t=!v||e.name.toLowerCase().includes(v.toLowerCase())||e.code.toLowerCase().includes(v.toLowerCase())||e.description&&e.description.toLowerCase().includes(v.toLowerCase()),r=!S||e.status===S;return t&&r}),ea={total:W.length,active:W.filter(e=>"active"===e.status).length,inactive:W.filter(e=>"inactive"===e.status).length},eo=[{title:"工位编码",dataIndex:"code",key:"code",width:100,fixed:"left",sorter:(e,t)=>e.code.localeCompare(t.code)},{title:"工位名称",dataIndex:"name",key:"name",width:80,ellipsis:!0},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>(0,s.jsx)(u.Z,{color:"active"===e?"success":"default",children:"active"===e?"启用":"停用"}),filters:[{text:"启用",value:"active"},{text:"停用",value:"inactive"}],onFilter:(e,t)=>t.status===e},{title:"当前模具",dataIndex:"currentMoldNumber",key:"currentMoldNumber",width:120,render:(e,t)=>e?(0,s.jsx)(D.Z,{title:"当前使用模具: ".concat(e),children:(0,s.jsx)(u.Z,{color:"blue",children:e})}):(0,s.jsx)(D.Z,{title:"工位空闲，无当前模具",children:(0,s.jsx)(u.Z,{color:"default",style:{color:"#999"},children:"空闲"})})},{title:"当前批次",dataIndex:"currentBatchNumber",key:"currentBatchNumber",width:140,render:(e,t)=>e?(0,s.jsx)(D.Z,{title:"当前生产批次: ".concat(e),children:(0,s.jsx)(u.Z,{color:"green",children:e})}):(0,s.jsx)(D.Z,{title:"工位空闲，无生产批次",children:(0,s.jsx)(u.Z,{color:"default",style:{color:"#999"},children:"无任务"})})},{title:"队列任务",dataIndex:"batchNumberQueue",key:"batchNumberQueue",width:120,render:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=e&&e.length>0;return t.currentMoldNumber||t.currentBatchNumber||r?(0,s.jsx)(D.Z,{title:r?"排队任务: ".concat(e.join(", ")):"无排队任务",children:(0,s.jsxs)(u.Z,{color:r?"orange":"default",children:[e.length," 个任务"]})}):(0,s.jsx)(D.Z,{title:"工位空闲，无排队任务",children:(0,s.jsx)(u.Z,{color:"default",style:{color:"#999"},children:"空闲中"})})}},{title:"预计结束",dataIndex:"lastEndTime",key:"lastEndTime",width:140,render:(e,t)=>{if(!e){let e=!t.currentMoldNumber&&!t.currentBatchNumber;return(0,s.jsx)(D.Z,{title:e?"工位空闲，无预计结束时间":"暂无预计结束时间",children:(0,s.jsx)(te,{type:"secondary",style:{color:"#ccc"},children:e?"空闲中":"-"})})}return(0,s.jsx)(D.Z,{title:"预计结束时间: ".concat(e),children:(0,s.jsx)(te,{type:"secondary",children:O()(e).format("MM-DD HH:mm")})})}},{title:"操作",key:"action",width:200,fixed:"right",render:(e,t)=>(0,s.jsxs)(d.Z,{size:"small",children:[(0,s.jsx)(D.Z,{title:"编辑",children:(0,s.jsx)(h.ZP,{type:"text",icon:(0,s.jsx)(eE.Z,{}),onClick:()=>q(t),size:"small"})}),(0,s.jsx)(D.Z,{title:"active"===t.status?"停用":"启用",children:(0,s.jsx)(eI.Z,{size:"small",checked:"active"===t.status,onChange:()=>G(t)})}),(t.currentMoldNumber||t.currentBatchNumber||t.batchNumberQueue&&t.batchNumberQueue.length>0)&&(0,s.jsx)(eN.Z,{title:"重置工位状态",description:"确定要将此工位重置为空闲状态吗？这将清空当前模具、批次和队列信息。",onConfirm:()=>K(t),okText:"确定重置",cancelText:"取消",children:(0,s.jsx)(D.Z,{title:"重置为空闲状态",children:(0,s.jsx)(h.ZP,{type:"text",icon:(0,s.jsx)(w.Z,{}),size:"small",style:{color:"#faad14"}})})}),(0,s.jsx)(eN.Z,{title:"确定要删除这个工位吗？",description:"删除后将无法恢复，请谨慎操作。",onConfirm:()=>X(t.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(D.Z,{title:"删除",children:(0,s.jsx)(h.ZP,{type:"text",icon:(0,s.jsx)(eW.Z,{}),danger:!0,size:"small"})})})]})}];return(0,s.jsxs)("div",{style:{padding:0},children:[(0,s.jsx)(i.Z,{size:"small",style:{marginBottom:"16px"},children:(0,s.jsxs)(l.Z,{justify:"space-between",align:"middle",children:[(0,s.jsx)(c.Z,{children:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(e9,{placeholder:"搜索工位编码、名称或描述",value:v,onChange:e=>k(e.target.value),style:{width:250},allowClear:!0}),(0,s.jsxs)(T.default,{placeholder:"筛选状态",value:S,onChange:C,style:{width:120},allowClear:!0,children:[(0,s.jsx)(e7,{value:"active",children:"启用"}),(0,s.jsx)(e7,{value:"inactive",children:"停用"})]}),(0,s.jsx)(D.Z,{title:Q?"实时同步已连接，数据将自动更新":"实时同步状态: ".concat(F),children:(0,s.jsx)(u.Z,{color:Q?"green":"connecting"===F?"orange":"red",style:{cursor:"help"},children:Q?"\uD83D\uDFE2 实时同步":"connecting"===F?"\uD83D\uDFE1 连接中":"\uD83D\uDD34 离线"})})]})}),(0,s.jsx)(c.Z,{children:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(w.Z,{}),onClick:U,loading:A,children:"刷新"}),(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(e_.Z,{}),onClick:()=>{try{let e=["工位编码,工位名称,描述,状态,创建时间,更新时间",...en.map(e=>[e.code,e.name,e.description||"","active"===e.status?"启用":"停用",new Date(e.createdAt).toLocaleDateString(),new Date(e.updatedAt).toLocaleDateString()].join(","))].join("\n"),t=new Blob([e],{type:"text/csv;charset=utf-8;"}),s=document.createElement("a"),n=URL.createObjectURL(t);s.setAttribute("href",n),s.setAttribute("download","工位数据_".concat(new Date().toISOString().split("T")[0],".csv")),s.style.visibility="hidden",document.body.appendChild(s),s.click(),document.body.removeChild(s),r.success("工位数据导出成功")}catch(e){r.error("导出失败，请稍后重试")}},children:"导出"}),(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(Z.Z,{}),onClick:J,loading:z,children:"数据验证"}),(0,s.jsx)(h.ZP,{type:"primary",icon:(0,s.jsx)(eA.Z,{}),onClick:()=>{f(null),j.resetFields(),j.setFieldsValue({code:Y(),status:"active"}),m(!0)},children:"新增工位"})]})})]})}),(0,s.jsxs)(l.Z,{gutter:16,style:{marginBottom:"16px"},children:[(0,s.jsx)(c.Z,{xs:24,sm:12,md:8,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"工位总数",value:ea.total,prefix:(0,s.jsx)(y.Z,{}),valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(c.Z,{xs:24,sm:12,md:8,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"启用工位",value:ea.active,prefix:(0,s.jsx)(b.Z,{}),valueStyle:{color:"#52c41a"}})})}),(0,s.jsx)(c.Z,{xs:24,sm:12,md:8,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"停用工位",value:ea.inactive,prefix:(0,s.jsx)(b.Z,{}),valueStyle:{color:"#ff4d4f"}})})})]}),(0,s.jsx)(i.Z,{children:(0,s.jsx)(g.default,{activeKey:N,onChange:E,tabBarExtraContent:"list"===N&&(0,s.jsxs)(d.Z.Compact,{size:"small",children:[(0,s.jsx)(h.ZP,{type:"table"===M?"primary":"default",icon:(0,s.jsx)(eR.Z,{}),onClick:()=>I("table"),size:"small",children:"表格"}),(0,s.jsx)(h.ZP,{type:"card"===M?"primary":"default",icon:(0,s.jsx)(ez.Z,{}),onClick:()=>I("card"),size:"small",children:"卡片"})]}),items:[{key:"list",label:(0,s.jsxs)("span",{children:[(0,s.jsx)(y.Z,{}),"工位列表 (",en.length,")"]}),children:"table"===M?(0,s.jsx)(er.Z,{columns:eo,dataSource:en,rowKey:"id",loading:A,pagination:{total:en.length,pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条/共 ").concat(e," 条")},scroll:{x:1200}}):(0,s.jsx)(l.Z,{gutter:[16,16],children:en.map(e=>(0,s.jsx)(c.Z,{xs:24,sm:12,lg:8,xl:6,children:(0,s.jsx)(e1,{workstation:e,onEdit:q,onDelete:X,onStatusToggle:G})},e.id))})},{key:"report",label:"统计报告",icon:(0,s.jsx)(Z.Z,{}),children:(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[(0,s.jsx)(i.Z,{title:"总体统计",size:"small",children:(0,s.jsxs)(l.Z,{gutter:[16,16],children:[(0,s.jsx)(c.Z,{xs:24,sm:12,md:8,children:(0,s.jsx)(x.Z,{title:"工位利用率",value:ea.total>0?Math.round(ea.active/ea.total*100):0,suffix:"%",valueStyle:{color:"#52c41a"}})}),(0,s.jsx)(c.Z,{xs:24,sm:12,md:8,children:(0,s.jsx)(eO.Z,{message:"功能开发中",description:"产能统计功能正在开发中，敬请期待",type:"info",showIcon:!0,style:{height:"100%"}})})]})}),(0,s.jsx)(i.Z,{title:"改进建议",size:"small",children:(0,s.jsx)(eO.Z,{message:"系统建议",description:(0,s.jsxs)("ul",{style:{listStyleType:"disc",listStylePosition:"inside",display:"flex",flexDirection:"column",gap:"4px",margin:0,padding:0},children:[ea.inactive>0&&(0,s.jsxs)("li",{children:["有 ",ea.inactive," 个工位处于停用状态，建议检查设备状况"]}),ea.total<5&&(0,s.jsx)("li",{children:"工位数量偏少，建议适当增加工位配置"}),ea.active===ea.total&&ea.total>0&&(0,s.jsx)("li",{children:"所有工位均已启用，工位状态管理良好"})]}),type:"info",showIcon:!0})})]})}]})}),(0,s.jsx)(ee.Z,{title:p?"编辑工位":"新增工位",open:o,onOk:es,onCancel:()=>{m(!1),j.resetFields()},width:600,okText:"保存",cancelText:"取消",children:(0,s.jsxs)($.Z,{form:j,layout:"vertical",initialValues:{status:"active"},children:[(0,s.jsxs)(l.Z,{gutter:16,children:[(0,s.jsx)(c.Z,{span:12,children:(0,s.jsx)($.Z.Item,{label:"工位编码",name:"code",rules:(0,eB.zX)("code"),children:(0,s.jsx)(eM.default,{placeholder:"如：A1, B2",disabled:!!p})})}),(0,s.jsx)(c.Z,{span:12,children:(0,s.jsx)($.Z.Item,{label:"工位名称",name:"name",rules:(0,eB.zX)("name"),children:(0,s.jsx)(eM.default,{placeholder:"请输入工位名称"})})})]}),(0,s.jsx)($.Z.Item,{label:"工位描述",name:"description",rules:(0,eB.zX)("description"),children:(0,s.jsx)(eM.default,{placeholder:"请输入工位描述"})}),(0,s.jsx)($.Z.Item,{label:"状态",name:"status",rules:[{required:!0,message:"请选择状态"}],children:(0,s.jsxs)(T.default,{children:[(0,s.jsx)(e7,{value:"active",children:"启用"}),(0,s.jsx)(e7,{value:"inactive",children:"停用"})]})})]})}),(0,s.jsx)(ee.Z,{title:"工位数据一致性验证报告",open:H,onCancel:()=>V(!1),width:800,footer:[(0,s.jsx)(h.ZP,{onClick:()=>V(!1),children:"关闭"},"close"),P&&P.invalidWorkstations>0&&(0,s.jsx)(h.ZP,{type:"primary",onClick:et,children:"自动修复"},"autofix")],children:P&&(0,s.jsxs)("div",{children:[(0,s.jsxs)(l.Z,{gutter:16,style:{marginBottom:16},children:[(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(x.Z,{title:"总工位数",value:P.totalWorkstations,valueStyle:{color:"#1890ff"}})}),(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(x.Z,{title:"验证通过",value:P.validWorkstations,valueStyle:{color:"#52c41a"}})}),(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(x.Z,{title:"存在问题",value:P.invalidWorkstations,valueStyle:{color:"#ff4d4f"}})}),(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(x.Z,{title:"验证时间",value:O()(P.generatedAt).format("HH:mm:ss"),valueStyle:{fontSize:14}})})]}),P.invalidWorkstations>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eO.Z,{message:"发现数据一致性问题",description:"共发现 ".concat(P.invalidWorkstations," 个工位存在数据一致性问题，建议及时处理。"),type:"warning",showIcon:!0,style:{marginBottom:16}}),(0,s.jsx)("div",{style:{maxHeight:400,overflowY:"auto"},children:P.issues.map((e,t)=>(0,s.jsx)(i.Z,{size:"small",style:{marginBottom:8},children:(0,s.jsxs)("div",{children:[(0,s.jsxs)(te,{strong:!0,children:["工位 ",e.workstationCode]}),(0,s.jsx)("div",{style:{marginTop:8},children:e.issues.map((e,t)=>(0,s.jsxs)("div",{style:{marginBottom:4},children:[(0,s.jsx)(u.Z,{color:"error"===e.severity?"red":"warning"===e.severity?"orange":"blue",children:"error"===e.severity?"错误":"warning"===e.severity?"警告":"信息"}),(0,s.jsx)(te,{children:e.description})]},t))}),e.recommendations.length>0&&(0,s.jsxs)("div",{style:{marginTop:8},children:[(0,s.jsx)(te,{type:"secondary",children:"建议："}),(0,s.jsx)("ul",{style:{margin:"4px 0",paddingLeft:20},children:e.recommendations.map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsx)(te,{type:"secondary",style:{fontSize:12},children:e})},t))})]})]})},t))})]}),0===P.invalidWorkstations&&(0,s.jsx)(eO.Z,{message:"数据一致性验证通过",description:"所有工位的数据都与生产工单保持一致，无需处理。",type:"success",showIcon:!0})]})})]})},tr=r(78649),ts=r(28116),tn=r(63260),ta=r(91705),to=r(92104);let{Title:ti,Text:tl}=J.default;var tc=e=>{var t;let{loading:r=!1}=e,{message:o,modal:u}=a.Z.useApp(),[m]=$.Z.useForm(),[g,p]=(0,n.useState)([]),[f,y]=(0,n.useState)(null),[j,v]=(0,n.useState)(!1),k=(0,n.useCallback)(async()=>{v(!0);try{let e=await W.dataAccessManager.workTime.getConfigurations();if("success"===e.status&&e.data){if(p(e.data),!f&&e.data.length>0){let t=e.data.find(e=>e.isDefault)||e.data[0];y(t)}}else o.error(e.message||"获取工作时间配置失败")}catch(e){o.error("获取工作时间配置失败")}finally{v(!1)}},[f,o]);(0,n.useEffect)(()=>{k()},[k]),(0,n.useEffect)(()=>{if(f){let e={...(f.workTimeSlots||[]).reduce((e,t)=>({...e,["work_start_".concat(t.id)]:O()(t.startTime,"HH:mm"),["work_end_".concat(t.id)]:O()(t.endTime,"HH:mm"),["work_active_".concat(t.id)]:t.isActive}),{})};m.setFieldsValue(e)}},[f,m]);let Z=async e=>{if(f)try{let t=(f.workTimeSlots||[]).map(t=>{var r,s,n;return{...t,startTime:(null===(r=e["work_start_".concat(t.id)])||void 0===r?void 0:r.format("HH:mm"))||t.startTime,endTime:(null===(s=e["work_end_".concat(t.id)])||void 0===s?void 0:s.format("HH:mm"))||t.endTime,isActive:null!==(n=e["work_active_".concat(t.id)])&&void 0!==n?n:t.isActive}}),r=await W.dataAccessManager.workTime.calculateWorkingMinutes(t),s={...f,workTimeSlots:t,breakTimeSlots:[],...r,updatedAt:new Date().toISOString()},n=await W.dataAccessManager.workTime.update(f.id,{workTimeSlots:t,breakTimeSlots:[],...r});"success"===n.status?(y(s),to.dataChangeNotifier.notifyDataChange({type:"work_time_configuration",action:"update",data:s,affectedIds:[f.id]}),o.success("工作时间配置已保存")):o.error(n.message||"保存工作时间配置失败")}catch(e){o.error("保存工作时间配置失败")}},b=async()=>{if(!f)return;let e=f.workTimeSlots||[],t="工作时间段",r="09:00",s="17:00",n="";0===e.length?(t="上午工作时间段",r="06:30",s="11:00",n="上午工作时间"):1===e.length?(t="下午工作时间段",r="11:30",s="17:00",n="下午工作时间"):(t="工作时间段".concat(e.length+1),n="第".concat(e.length+1,"个工作时间段"));let a=[...e,{id:"work_slot_".concat(Date.now()),name:t,startTime:r,endTime:s,isActive:!0,description:n}],i=await W.dataAccessManager.workTime.calculateWorkingMinutes(a);await (0,e2.Ro)(()=>W.dataAccessManager.workTime.update(f.id,{workTimeSlots:a,...i}),"添加工作时间段")&&(y({...f,workTimeSlots:a,...i}),o.success("已添加".concat(t)))},C=async e=>{if(!f)return;let t=f.workTimeSlots.filter(t=>t.id!==e),r=await W.dataAccessManager.workTime.calculateWorkingMinutes(t);await (0,e2.Ro)(()=>W.dataAccessManager.workTime.update(f.id,{workTimeSlots:t,...r}),"删除工作时间段")&&(y({...f,workTimeSlots:t,...r}),o.success("工作时间段已删除"))};return(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:[(0,s.jsx)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:(0,s.jsxs)("div",{children:[(0,s.jsxs)(ti,{level:4,style:{marginBottom:"8px"},children:[(0,s.jsx)(S.Z,{style:{marginRight:"8px"}}),"工作时间配置"]}),(0,s.jsx)(tl,{type:"secondary",children:"配置生产工位的工作时间安排"})]})}),f&&(0,s.jsx)(eO.Z,{description:(0,s.jsxs)("div",{style:{fontSize:"14px",color:"#666"},children:[f.workTimeSlots&&f.workTimeSlots.length>0&&(0,s.jsxs)("span",{children:["工作时间: ",f.workTimeSlots.filter(e=>e.isActive).map(e=>"".concat(e.startTime,"-").concat(e.endTime)).join(", ")]}),f.effectiveWorkingMinutes&&(0,s.jsxs)("span",{style:{marginLeft:"16px"},children:["总工作时间: ",Math.floor(f.effectiveWorkingMinutes/60),"小时",f.effectiveWorkingMinutes%60,"分钟"]})]}),type:"info",showIcon:!0,icon:(0,s.jsx)(tn.Z,{})}),(0,s.jsx)(i.Z,{children:(0,s.jsx)("div",{style:{display:"flex",flexDirection:"column",gap:"24px"},children:f&&(0,s.jsx)($.Z,{form:m,layout:"vertical",onFinish:Z,initialValues:{...((null==f?void 0:f.workTimeSlots)||[]).reduce((e,t)=>({...e,["work_start_".concat(t.id)]:O()(t.startTime,"HH:mm"),["work_end_".concat(t.id)]:O()(t.endTime,"HH:mm"),["work_active_".concat(t.id)]:t.isActive}),{})},children:(0,s.jsx)(i.Z,{size:"small",title:"工作时间配置",children:(0,s.jsxs)("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"},children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(ti,{level:5,children:"工作时间段（上午/下午）"}),(0,s.jsx)(tl,{type:"secondary",style:{fontSize:"12px"},children:"建议设置上午和下午两个工作时间段"})]}),(0,s.jsx)(h.ZP,{type:"dashed",size:"small",icon:(0,s.jsx)(eA.Z,{}),onClick:b,children:"添加时间段"})]}),(f.workTimeSlots||[]).map(e=>(0,s.jsx)(i.Z,{size:"small",style:{marginBottom:"8px"},children:(0,s.jsxs)(l.Z,{gutter:16,align:"middle",children:[(0,s.jsx)(c.Z,{span:4,children:(0,s.jsx)($.Z.Item,{label:"开始时间",name:"work_start_".concat(e.id),rules:[{required:!0,message:"请选择开始时间"},{validator:(t,r)=>{if(!r)return Promise.resolve();let s=m.getFieldValue("work_end_".concat(e.id));if(s&&r.isAfter(s))return Promise.reject(Error("开始时间不能晚于结束时间"));let n=r.hour();return n<6||n>23?Promise.reject(Error("工作时间应在6:00-23:59之间")):Promise.resolve()}}],children:(0,s.jsx)(tr.Z,{format:"HH:mm",placeholder:"开始时间",size:"small",onChange:()=>{m.validateFields(["work_end_".concat(e.id)])}})})}),(0,s.jsx)(c.Z,{span:4,children:(0,s.jsx)($.Z.Item,{label:"结束时间",name:"work_end_".concat(e.id),rules:[{required:!0,message:"请选择结束时间"},{validator:(t,r)=>{if(!r)return Promise.resolve();let s=m.getFieldValue("work_start_".concat(e.id));if(s&&r.isBefore(s))return Promise.reject(Error("结束时间不能早于开始时间"));let n=r.hour();return n<6||n>23?Promise.reject(Error("工作时间应在6:00-23:59之间")):s&&30>r.diff(s,"minutes")?Promise.reject(Error("工作时长不能少于30分钟")):Promise.resolve()}}],children:(0,s.jsx)(tr.Z,{format:"HH:mm",placeholder:"结束时间",size:"small",onChange:()=>{m.validateFields(["work_start_".concat(e.id)])}})})}),(0,s.jsx)(c.Z,{span:3,children:(0,s.jsx)($.Z.Item,{label:"启用",name:"work_active_".concat(e.id),valuePropName:"checked",children:(0,s.jsx)(eI.Z,{size:"small"})})}),(0,s.jsx)(c.Z,{span:8,children:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(tl,{type:"secondary",children:e.name}),(0,s.jsx)(ef,{status:e.isActive?"configured":"inactive",showIcon:!0})]})}),(0,s.jsx)(c.Z,{span:5,children:(0,s.jsx)(d.Z,{children:(0,s.jsx)(D.Z,{title:"删除时间段",children:(0,s.jsx)(eN.Z,{title:"确定删除此工作时间段吗？",onConfirm:()=>C(e.id),okText:"确定",cancelText:"取消",children:(0,s.jsx)(h.ZP,{type:"text",danger:!0,size:"small",icon:(0,s.jsx)(eW.Z,{})})})})})})]})},e.id))]}),(0,s.jsx)(ts.Z,{}),(0,s.jsxs)(l.Z,{gutter:16,children:[(0,s.jsx)(c.Z,{span:12,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"总工作时间",value:Math.round(((null==f?void 0:f.totalWorkingMinutes)||0)/60*10)/10,suffix:"小时",precision:1,valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(c.Z,{span:12,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"工作时间段数量",value:(null==f?void 0:null===(t=f.workTimeSlots)||void 0===t?void 0:t.filter(e=>e.isActive).length)||0,suffix:"个",valueStyle:{color:"#52c41a"}})})})]}),(0,s.jsx)("div",{style:{textAlign:"center"},children:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(h.ZP,{type:"primary",htmlType:"submit",icon:(0,s.jsx)(ta.Z,{}),loading:r,children:"保存配置"}),(0,s.jsx)(h.ZP,{onClick:()=>{if(!f){o.warning("没有可重置的配置");return}let e={...(f.workTimeSlots||[]).reduce((e,t)=>({...e,["work_start_".concat(t.id)]:O()(t.startTime,"HH:mm"),["work_end_".concat(t.id)]:O()(t.endTime,"HH:mm"),["work_active_".concat(t.id)]:t.isActive}),{})};m.setFieldsValue(e),o.success("表单已重置到当前配置")},icon:(0,s.jsx)(w.Z,{}),children:"重置"})]})})]})})})})})]})},td=r(34863),tu=r(88849),th=r(22315),tm=r(51769);let tx=n.memo(e=>{let{productCode:t,format:r="simple",style:a,className:i,loadingText:l="加载中...",emptyText:c="暂无重量",unit:d="g"}=e,[u,h]=(0,n.useState)(null),[m,x]=(0,n.useState)(!1),[g,p]=(0,n.useState)(null);return(0,n.useEffect)(()=>{let e=!1;return(async()=>{if(!t){h(null);return}x(!0),p(null);try{let r=await (0,e2.Ro)(()=>W.dataAccessManager.products.getByCode(t),"获取产品重量");if(e)return;if("success"===r.status&&r.data){let e=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{min:r=0,max:s=1e6,allowDecimal:n=!0,maxDecimals:a=3}=t,o=[];if(null==e||""===e)return{isValid:!0,errors:[],sanitizedValue:null};let i=Number(e);return isNaN(i)?(o.push("产品重量必须是有效数字"),{isValid:!1,errors:o}):(i<0&&o.push("产品重量不能为负数"),i<r&&o.push("产品重量不能小于".concat(r)),i>s&&o.push("产品重量不能大于".concat(s)),n||i%1==0?n&&a>0&&(i.toString().split(".")[1]||"").length>a&&o.push("产品重量小数位数不能超过".concat(a,"位")):o.push("产品重量必须是整数"),{isValid:0===o.length,errors:o,sanitizedValue:0===o.length?i:null})}(r.data.productWeight,{min:0,max:1e6,allowDecimal:!0,maxDecimals:3});e.isValid&&null!==e.sanitizedValue?h(e.sanitizedValue):(h(null),e.errors.length>0&&(console.warn("产品重量验证失败 (".concat(t,"):"),e.errors),p("重量数据异常")))}else h(null)}catch(r){if(e)return;console.error("获取产品重量失败 (".concat(t,"):"),r),h(null),p("获取重量失败")}finally{e||x(!1)}})(),()=>{e=!0}},[t]),m?(0,s.jsxs)("span",{className:i,style:a,children:[(0,s.jsx)(o.Z,{size:"small"})," ",l]}):g?(0,s.jsx)("span",{className:i,style:{color:"#ff4d4f",...a},title:g,children:c}):null===u?(0,s.jsx)("span",{className:i,style:{color:"#999",...a},children:c}):(0,s.jsx)("span",{className:i,style:a,title:"产品编码: ".concat(t,", 重量: ").concat(u," ").concat(d),children:"detailed"===r?"重量: ".concat(u.toLocaleString()," ").concat(d):"".concat(u.toLocaleString()," ").concat(d)})});tx.displayName="ProductWeightDisplay";let tg=n.memo(e=>{let{productCode:t,style:r,className:n}=e;return(0,s.jsx)(tx,{productCode:t,format:"simple",style:r,className:n,emptyText:"-",loadingText:"..."})});tg.displayName="SimpleProductWeight",n.memo(e=>{let{productCode:t,style:r,className:n}=e;return(0,s.jsx)(tx,{productCode:t,format:"detailed",style:r,className:n})}).displayName="DetailedProductWeight",n.memo(e=>{let{productCodes:t,direction:r="vertical",separator:a=", ",maxDisplay:o=5}=e,i=t.slice(0,o),l=t.length>o;return"horizontal"===r?(0,s.jsxs)("span",{children:[i.map((e,t)=>(0,s.jsxs)(n.Fragment,{children:[t>0&&a,(0,s.jsx)(tg,{productCode:e})]},e)),l&&(0,s.jsxs)("span",{style:{color:"#999"},children:[a,"等",t.length,"个产品"]})]}):(0,s.jsxs)("div",{children:[i.map(e=>(0,s.jsx)("div",{style:{marginBottom:4},children:(0,s.jsx)(tg,{productCode:e})},e)),l&&(0,s.jsxs)("div",{style:{color:"#999",fontSize:"12px"},children:["等",t.length,"个产品..."]})]})}).displayName="BatchProductWeightDisplay";let{Search:tp}=eM.default,{Option:tf}=T.default,{RangePicker:ty}=M.default,{Text:tj}=J.default;var tw=e=>{let{workOrders:t=[],loading:r=!1,onRefresh:o,onWorkOrderDetail:g,onStatusChange:f,onEdit:y,onDelete:j,onExport:v,onStartScheduling:k}=e,{modal:S}=a.Z.useApp(),[Z,b]=(0,n.useState)(""),[C,D]=(0,n.useState)(""),[M,I]=(0,n.useState)(""),[N,E]=(0,n.useState)(""),[W,_]=(0,n.useState)(null),[A,P]=(0,n.useState)([]),L=(0,n.useCallback)(e=>{b(B(e.target.value,100))},[]),H=(0,n.useCallback)(e=>{var t;E((t=e.target.value)?R(t.replace(/[^A-Za-z0-9\-]/g,"")):"")},[]),V=(0,n.useMemo)(()=>{let e=[...t];if(Z){let t=Z.toLowerCase();e=e.filter(e=>e.batchNumber.toLowerCase().includes(t)||e.productName.toLowerCase().includes(t)||e.productCode.toLowerCase().includes(t)||e.customerName.toLowerCase().includes(t))}if(C&&(e=e.filter(e=>e.status===C)),M&&(e=e.filter(e=>e.customerCreditLevel===M)),N&&(e=e.filter(e=>e.workstation.includes(N))),W){let[t,r]=W;e=e.filter(e=>{let s=O()(e.plannedStartTime);return s.isAfter(t.startOf("day"))&&s.isBefore(r.endOf("day"))})}return e},[t,Z,C,M,N,W]),F=(0,n.useMemo)(()=>({total:V.length,inProgress:V.filter(e=>"in_progress"===e.status).length,completed:V.filter(e=>"completed"===e.status).length,exception:V.filter(e=>"exception"===e.status||e.exceptionCount>0).length}),[V]),Q=e=>({items:[{key:"detail",icon:(0,s.jsx)(es.Z,{}),label:"查看详情",onClick:()=>null==g?void 0:g(e)},{key:"edit",icon:(0,s.jsx)(eE.Z,{}),label:"编辑",onClick:()=>null==y?void 0:y(e)},{type:"divider"},{key:"delete",icon:(0,s.jsx)(eW.Z,{}),label:"删除",danger:!0,onClick:()=>{S.confirm({title:"确认删除",content:"确定要删除工单 ".concat(e.batchNumber," 吗？"),onOk:()=>null==j?void 0:j(e.id)})}}]}),U=[{title:"状态",dataIndex:"status",key:"status",width:80,render:e=>(0,s.jsx)(eg,{status:e}),filters:ey("work_order"),onFilter:(e,t)=>t.status===e},{title:"生产工单ID",dataIndex:"id",key:"id",width:160,render:e=>(0,s.jsx)(tj,{code:!0,style:{fontSize:"12px"},children:e})},{title:"批次号",dataIndex:"batchNumber",key:"batchNumber",width:120,render:(e,t)=>(0,s.jsx)(h.ZP,{type:"link",onClick:()=>null==g?void 0:g(t),style:{padding:0,height:"auto"},children:e})},{title:"产品名称",dataIndex:"productName",key:"productName",width:200,render:(e,t)=>t.isSharedMold&&t.productItems&&t.productItems.length>0?(0,s.jsx)("div",{style:{lineHeight:"1.4"},children:t.productItems.map((e,r)=>(0,s.jsx)("div",{style:{marginBottom:r<t.productItems.length-1?"2px":"0"},children:(0,s.jsx)(tj,{style:{fontSize:"13px",display:"block",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",maxWidth:"180px"},title:z(e.productName),children:z(e.productName)})},r))}):(0,s.jsx)(tj,{ellipsis:{tooltip:z(e)},children:z(e)})},{title:"产品编码",dataIndex:"productCode",key:"productCode",width:120,render:(e,t)=>t.isSharedMold&&t.productItems&&t.productItems.length>0?(0,s.jsx)("div",{style:{lineHeight:"1.4"},children:t.productItems.map((e,r)=>(0,s.jsx)("div",{style:{marginBottom:r<t.productItems.length-1?"2px":"0"},children:(0,s.jsx)(tj,{style:{fontSize:"13px"},children:z(e.productCode)})},r))}):(0,s.jsx)(tj,{children:z(e)})},{title:"产品重量(g)",dataIndex:"productCode",key:"weight",width:120,align:"right",render:(e,t)=>t.isSharedMold&&t.productItems&&t.productItems.length>0?(0,s.jsx)("div",{style:{lineHeight:"1.4"},children:t.productItems.map((e,r)=>(0,s.jsx)("div",{style:{marginBottom:r<t.productItems.length-1?"2px":"0"},children:(0,s.jsx)(tj,{style:{fontSize:"13px"},children:(0,s.jsx)(tg,{productCode:e.productCode})})},r))}):(0,s.jsx)(tg,{productCode:e}),sorter:!1},{title:"信用等级",dataIndex:"customerCreditLevel",key:"customerCreditLevel",width:100,render:e=>e?(0,s.jsx)(ep,{level:e}):(0,s.jsx)(u.Z,{color:"default",children:"未设置"}),filters:[{text:"A级",value:"A"},{text:"B级",value:"B"},{text:"C级",value:"C"},{text:"D级",value:"D"},{text:"E级",value:"E"}],onFilter:(e,t)=>t.customerCreditLevel===e},{title:"计划模数",dataIndex:"plannedMoldCount",key:"plannedMoldCount",width:100,align:"right",sorter:(e,t)=>e.plannedMoldCount-t.plannedMoldCount},{title:"工位",dataIndex:"workstation",key:"workstation",width:100},{title:"计划开始",dataIndex:"plannedStartTime",key:"plannedStartTime",width:140,render:e=>e?O()(e).format("YYYY-MM-DD HH:mm"):(0,s.jsx)("span",{style:{color:"#999"},children:"未设置"}),sorter:(e,t)=>e.plannedStartTime||t.plannedStartTime?e.plannedStartTime?t.plannedStartTime?O()(e.plannedStartTime).unix()-O()(t.plannedStartTime).unix():-1:1:0},{title:"计划结束",dataIndex:"plannedEndTime",key:"plannedEndTime",width:140,render:e=>e?O()(e).format("YYYY-MM-DD HH:mm"):(0,s.jsx)("span",{style:{color:"#999"},children:"未设置"}),sorter:(e,t)=>e.plannedEndTime||t.plannedEndTime?e.plannedEndTime?t.plannedEndTime?O()(e.plannedEndTime).unix()-O()(t.plannedEndTime).unix():-1:1:0},{title:"交货日期",dataIndex:"deliveryDate",key:"deliveryDate",width:110,render:e=>O()(e).format("YYYY-MM-DD"),sorter:(e,t)=>O()(e.deliveryDate).unix()-O()(t.deliveryDate).unix()},{title:"小时产能",dataIndex:"hourlyCapacity",key:"hourlyCapacity",width:120,align:"right",render:e=>"".concat(e,"模/小时"),sorter:(e,t)=>e.hourlyCapacity-t.hourlyCapacity},{title:"完成模数",dataIndex:"completedMoldCount",key:"completedMoldCount",width:100,align:"right",sorter:(e,t)=>e.completedMoldCount-t.completedMoldCount},{title:"执行比(%)",dataIndex:"executionRate",key:"executionRate",width:120,render:e=>{let t="normal";return e>=100?t="success":e<50&&(t="exception"),(0,s.jsx)(td.Z,{percent:e,size:"small",status:t,format:e=>"".concat(null==e?void 0:e.toFixed(1),"%")})},sorter:(e,t)=>e.executionRate-t.executionRate},{title:"创建时间",dataIndex:"createdAt",key:"createdAt",width:140,render:e=>O()(e).format("YYYY-MM-DD HH:mm"),sorter:(e,t)=>O()(e.createdAt).unix()-O()(t.createdAt).unix(),defaultSortOrder:"descend"},{title:"成型模具编号",dataIndex:"formingMoldNumber",key:"formingMoldNumber",width:140,render:e=>e||"-"},{title:"异常数量",dataIndex:"exceptionCount",key:"exceptionCount",width:100,align:"right",render:e=>e>0?(0,s.jsx)("span",{style:{color:"#ff4d4f",fontWeight:"bold"},children:e}):(0,s.jsx)("span",{children:e}),sorter:(e,t)=>e.exceptionCount-t.exceptionCount},{title:"操作",key:"action",width:80,fixed:"right",render:(e,t)=>(0,s.jsx)(m.Z,{menu:Q(t),trigger:["click"],children:(0,s.jsx)(h.ZP,{type:"text",icon:(0,s.jsx)(tu.Z,{})})})}],Y={selectedRowKeys:A,onChange:P,getCheckboxProps:e=>({disabled:"pending"!==e.status,name:e.batchNumber}),selections:[{key:"select-pending",text:"选择待开始工单",onSelect:e=>{P(e.filter(e=>{let t=V.find(t=>t.id===e);return(null==t?void 0:t.status)==="pending"}))}},er.Z.SELECTION_NONE]};return(0,s.jsxs)("div",{className:"production-work-orders-list",children:[(0,s.jsx)(i.Z,{size:"small",style:{marginBottom:"16px"},children:(0,s.jsxs)(l.Z,{gutter:16,align:"middle",children:[(0,s.jsx)(c.Z,{flex:"auto",children:(0,s.jsxs)(d.Z,{wrap:!0,children:[(0,s.jsx)(tp,{placeholder:"搜索批次号、产品名称、产品编码、客户名称",value:Z,onChange:L,style:{width:300},allowClear:!0}),(0,s.jsxs)(T.default,{placeholder:"状态",value:C,onChange:D,style:{width:120},allowClear:!0,children:[(0,s.jsx)(tf,{value:"pending",children:"待开始"}),(0,s.jsx)(tf,{value:"in_progress",children:"进行中"}),(0,s.jsx)(tf,{value:"completed",children:"已完成"}),(0,s.jsx)(tf,{value:"paused",children:"暂停"}),(0,s.jsx)(tf,{value:"cancelled",children:"已取消"}),(0,s.jsx)(tf,{value:"exception",children:"异常"})]}),(0,s.jsxs)(T.default,{placeholder:"信用等级",value:M,onChange:I,style:{width:100},allowClear:!0,children:[(0,s.jsx)(tf,{value:"A",children:"A级"}),(0,s.jsx)(tf,{value:"B",children:"B级"}),(0,s.jsx)(tf,{value:"C",children:"C级"}),(0,s.jsx)(tf,{value:"D",children:"D级"}),(0,s.jsx)(tf,{value:"E",children:"E级"})]}),(0,s.jsx)(eM.default,{placeholder:"工位",value:N,onChange:H,style:{width:120},allowClear:!0}),(0,s.jsx)(ty,{value:W,onChange:e=>_(e),placeholder:["开始日期","结束日期"]}),(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(th.Z,{}),onClick:()=>{b(""),D(""),I(""),E(""),_(null)},children:"清空筛选"})]})}),(0,s.jsx)(c.Z,{children:(0,s.jsxs)(d.Z,{children:[(0,s.jsxs)(h.ZP,{type:"primary",onClick:()=>null==k?void 0:k(A),disabled:0===A.length,children:["开始排单 ",A.length>0&&"(".concat(A.length,")")]}),(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(tm.Z,{}),onClick:v,children:"导出"}),(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(w.Z,{}),onClick:o,loading:r,children:"刷新"})]})})]})}),(0,s.jsxs)(l.Z,{gutter:16,style:{marginBottom:"16px"},children:[(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"总工单数",value:F.total,prefix:(0,s.jsx)(p.Z,{status:"default"})})})}),(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"进行中",value:F.inProgress,prefix:(0,s.jsx)(p.Z,{status:"processing"}),valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"已完成",value:F.completed,prefix:(0,s.jsx)(p.Z,{status:"success"}),valueStyle:{color:"#52c41a"}})})}),(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"异常工单",value:F.exception,prefix:(0,s.jsx)(p.Z,{status:"error"}),valueStyle:{color:"#ff4d4f"}})})})]}),(0,s.jsx)(i.Z,{children:(0,s.jsx)(er.Z,{columns:U,dataSource:V,rowKey:"id",loading:r,rowSelection:Y,scroll:{x:1900},pagination:{total:V.length,pageSize:20,showSizeChanger:!0,showQuickJumper:!0,showTotal:(e,t)=>"第 ".concat(t[0],"-").concat(t[1]," 条，共 ").concat(e," 条")},size:"small"})})]})},tv=r(55175),tk=r(61773),tS=r(23455);let{Title:tZ,Text:tb,Paragraph:tC}=J.default;var tD=e=>{let{open:t,onCancel:r,onConfirm:a,selectedWorkOrders:o,loading:u=!1}=e,[h]=$.Z.useForm(),[m,g]=(0,n.useState)("same_mold_priority"),p=n.useMemo(()=>{let e=o.length;return{totalWorkOrders:e,totalMoldCount:o.reduce((e,t)=>e+t.plannedMoldCount,0),uniqueMolds:new Set(o.map(e=>e.formingMoldNumber)).size,avgHourlyCapacity:o.length>0?Math.round(o.reduce((e,t)=>e+t.hourlyCapacity,0)/o.length):0}},[o]),f=async()=>{try{let e=await h.validateFields(),t={mode:m,changeoverTime:20,allowOvertime:e.allowOvertime||!1,maxOvertimeHours:e.maxOvertimeHours||4,sameMoldDeliveryToleranceDays:e.sameMoldDeliveryToleranceDays||30};a(t)}catch(e){tv.ZP.error("请完善排程配置信息")}},y=(e=>{switch(e){case"same_mold_priority":return{title:"相同模具优先",description:"优先将工单分配到已装载相同模具的工位，减少换模时间，提高生产效率",advantages:["减少换模次数，节省换模时间","提高工位利用率","降低生产成本","适合批量生产场景"],icon:(0,s.jsx)(b.Z,{style:{color:"#1890ff"}})};case"delivery_priority":return{title:"交期优先",description:"严格按照交货期排序，优先安排紧急订单，确保按时交付",advantages:["确保交货期准时性","提高客户满意度","降低延期风险","适合多品种小批量场景"],icon:(0,s.jsx)(C.Z,{style:{color:"#52c41a"}})};default:return{title:"",description:"",advantages:[],icon:null}}})(m);return(0,s.jsx)(ee.Z,{title:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(S.Z,{}),(0,s.jsx)("span",{children:"生产排程配置"})]}),open:t,onCancel:r,onOk:f,confirmLoading:u,width:800,okText:"开始排程",cancelText:"取消",destroyOnHidden:!0,children:(0,s.jsxs)($.Z,{form:h,layout:"vertical",initialValues:{mode:"same_mold_priority",allowOvertime:!1,maxOvertimeHours:4,sameMoldDeliveryToleranceDays:30},children:[(0,s.jsxs)(i.Z,{size:"small",style:{marginBottom:16},children:[(0,s.jsx)(tZ,{level:5,style:{margin:0,marginBottom:12},children:"待排程工单统计"}),(0,s.jsxs)(l.Z,{gutter:16,children:[(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(x.Z,{title:"工单数量",value:p.totalWorkOrders,suffix:"个",valueStyle:{color:"#1890ff"}})}),(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(x.Z,{title:"总计划模数",value:p.totalMoldCount,suffix:"模",valueStyle:{color:"#52c41a"}})}),(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(x.Z,{title:"涉及模具",value:p.uniqueMolds,suffix:"套",valueStyle:{color:"#faad14"}})}),(0,s.jsx)(c.Z,{span:6,children:(0,s.jsx)(x.Z,{title:"平均产能",value:p.avgHourlyCapacity,suffix:"模/时",valueStyle:{color:"#722ed1"}})})]})]}),(0,s.jsx)($.Z.Item,{name:"mode",label:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(b.Z,{}),(0,s.jsx)("span",{children:"排程模式"})]}),rules:[{required:!0,message:"请选择排程模式"}],children:(0,s.jsx)(tk.ZP.Group,{value:m,onChange:e=>g(e.target.value),style:{width:"100%"},children:(0,s.jsxs)(d.Z,{direction:"vertical",style:{width:"100%"},children:[(0,s.jsx)(tk.ZP,{value:"same_mold_priority",children:(0,s.jsx)(i.Z,{size:"small",hoverable:!0,style:{width:"100%",border:"same_mold_priority"===m?"2px solid #1890ff":"1px solid #d9d9d9"},children:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(b.Z,{style:{color:"#1890ff",fontSize:20}}),(0,s.jsxs)("div",{children:[(0,s.jsx)(tb,{strong:!0,children:"相同模具优先"}),(0,s.jsx)("br",{}),(0,s.jsx)(tb,{type:"secondary",style:{fontSize:12},children:"优先分配到相同模具工位，减少换模时间"})]})]})})}),(0,s.jsx)(tk.ZP,{value:"delivery_priority",children:(0,s.jsx)(i.Z,{size:"small",hoverable:!0,style:{width:"100%",border:"delivery_priority"===m?"2px solid #1890ff":"1px solid #d9d9d9"},children:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(C.Z,{style:{color:"#52c41a",fontSize:20}}),(0,s.jsxs)("div",{children:[(0,s.jsx)(tb,{strong:!0,children:"交期优先"}),(0,s.jsx)("br",{}),(0,s.jsx)(tb,{type:"secondary",style:{fontSize:12},children:"严格按照交货期排序，确保按时交付"})]})]})})})]})})}),(0,s.jsxs)(i.Z,{size:"small",style:{marginBottom:16},children:[(0,s.jsxs)(d.Z,{children:[y.icon,(0,s.jsx)(tZ,{level:5,style:{margin:0},children:y.title})]}),(0,s.jsx)(tC,{style:{marginTop:8,marginBottom:12},children:y.description}),(0,s.jsx)(tb,{strong:!0,children:"主要优势："}),(0,s.jsx)("ul",{style:{marginTop:4,marginBottom:0},children:y.advantages.map((e,t)=>(0,s.jsx)("li",{children:(0,s.jsx)(tb,{children:e})},t))})]}),(0,s.jsx)(i.Z,{size:"small",title:"排程参数",children:(0,s.jsxs)(l.Z,{gutter:16,children:[(0,s.jsx)(c.Z,{span:8,children:(0,s.jsx)($.Z.Item,{label:"换模时间",children:(0,s.jsx)(tb,{children:"20 分钟（固定）"})})}),(0,s.jsx)(c.Z,{span:8,children:(0,s.jsx)($.Z.Item,{label:"工作时间配置",children:(0,s.jsx)(tb,{children:"使用系统默认配置"})})}),(0,s.jsx)(c.Z,{span:8,children:(0,s.jsx)($.Z.Item,{label:"相同模具交货期容忍天数",name:"sameMoldDeliveryToleranceDays",tooltip:"交货期相差在此天数内的相同模具工单将被分配到同一工位，减少换模次数",rules:[{required:!0,message:"请输入容忍天数"},{type:"number",min:1,max:90,message:"容忍天数必须在1-90天之间"}],children:(0,s.jsx)(et.Z,{min:1,max:90,suffix:"天",placeholder:"请输入天数",style:{width:"100%"}})})})]})}),(0,s.jsx)(eO.Z,{message:"排程注意事项",description:(0,s.jsxs)("ul",{style:{margin:0,paddingLeft:16},children:[(0,s.jsx)("li",{children:"排程将自动分配工位和时间，请确认工单信息准确"}),(0,s.jsx)("li",{children:'排程完成后，工单状态将变更为"已排程"'}),(0,s.jsx)("li",{children:"如有冲突或风险，系统将提供详细的警告信息"}),(0,s.jsx)("li",{children:"排程结果可在排程完成后进行调整"})]}),type:"info",showIcon:!0,icon:(0,s.jsx)(tS.Z,{}),style:{marginTop:16}})]})})};let{Title:tT,Text:tM}=J.default,{TabPane:tI}=g.default;var tN=e=>{let{open:t,onCancel:r,onConfirm:a,result:o,loading:h=!1}=e,[m,f]=(0,n.useState)("results");if(!o)return null;let y=[{title:"工单ID",dataIndex:"workOrderId",key:"workOrderId",width:120,render:e=>(0,s.jsx)(tM,{code:!0,children:e.slice(-8)})},{title:"分配工位",dataIndex:"workstation",key:"workstation",width:150,render:(e,t)=>(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(u.Z,{color:t.isSameMold?"green":"blue",children:t.workstationCode}),(0,s.jsx)(tM,{children:e})]})},{title:"模具匹配",dataIndex:"isSameMold",key:"isSameMold",width:100,render:e=>(0,s.jsx)(u.Z,{color:e?"green":"orange",icon:(0,s.jsx)(b.Z,{}),children:e?"相同模具":"换模"})},{title:"预计开始时间",dataIndex:"plannedStartTime",key:"plannedStartTime",width:150,render:e=>(0,s.jsxs)(d.Z,{direction:"vertical",size:0,children:[(0,s.jsx)(tM,{children:new Date(e).toLocaleDateString()}),(0,s.jsx)(tM,{type:"secondary",style:{fontSize:12},children:new Date(e).toLocaleTimeString()})]})},{title:"预计结束时间",dataIndex:"plannedEndTime",key:"plannedEndTime",width:150,render:e=>(0,s.jsxs)(d.Z,{direction:"vertical",size:0,children:[(0,s.jsx)(tM,{children:new Date(e).toLocaleDateString()}),(0,s.jsx)(tM,{type:"secondary",style:{fontSize:12},children:new Date(e).toLocaleTimeString()})]})},{title:"生产时长",dataIndex:"productionTimeMinutes",key:"productionTimeMinutes",width:100,render:e=>(0,s.jsxs)(tM,{children:[Math.round(e/60*10)/10," 小时"]})},{title:"换模时间",dataIndex:"changeoverTimeMinutes",key:"changeoverTimeMinutes",width:100,render:e=>(0,s.jsxs)(tM,{type:e>0?"warning":"secondary",children:[e," 分钟"]})}],j=[{title:"工单ID",dataIndex:"workOrderId",key:"workOrderId",width:120,render:e=>(0,s.jsx)(tM,{code:!0,children:e.slice(-8)})},{title:"风险等级",dataIndex:"riskLevel",key:"riskLevel",width:100,render:e=>{let t={normal:{text:"正常",color:"green",icon:(0,s.jsx)(ea.Z,{})},medium:{text:"中风险",color:"orange",icon:(0,s.jsx)(tS.Z,{})},high:{text:"高风险",color:"red",icon:(0,s.jsx)(ed.Z,{})}}[e]||{text:e,color:"default",icon:null};return(0,s.jsx)(u.Z,{color:t.color,icon:t.icon,children:t.text})}},{title:"延误/提前",key:"timeDiff",width:120,render:(e,t)=>t.delayDays?(0,s.jsxs)(tM,{type:"danger",children:["延误 ",t.delayDays," 天"]}):t.advanceDays?(0,s.jsxs)(tM,{type:"success",children:["提前 ",t.advanceDays," 天"]}):(0,s.jsx)(tM,{children:"按时"})},{title:"预计完成日期",dataIndex:"expectedCompletionDate",key:"expectedCompletionDate",width:120,render:e=>new Date(e).toLocaleDateString()},{title:"建议",dataIndex:"suggestion",key:"suggestion",ellipsis:!0,render:e=>e||"-"}],w={normal:o.riskAssessments.filter(e=>"normal"===e.riskLevel).length,medium:o.riskAssessments.filter(e=>"medium"===e.riskLevel).length,high:o.riskAssessments.filter(e=>"high"===e.riskLevel).length};return(0,s.jsxs)(ee.Z,{title:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(S.Z,{style:{color:"#1890ff"}}),(0,s.jsx)("span",{children:"排程结果"}),(0,s.jsx)(u.Z,{color:"blue",icon:(0,s.jsx)(S.Z,{}),children:"预览模式"})]}),open:t,onCancel:r,onOk:a,confirmLoading:h,width:1200,okText:"确认应用排程结果",cancelText:"取消",style:{top:20},children:[(0,s.jsx)(eO.Z,{message:"\uD83D\uDCCB 排程预览模式",description:(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:["当前显示的是排程计算结果预览，",(0,s.jsx)("strong",{children:"工位状态和工单状态尚未实际更新"}),"。"]}),(0,s.jsx)("p",{children:"• 工位队列任务：未添加"}),(0,s.jsx)("p",{children:'• 工单状态：仍为"待开始"'}),(0,s.jsx)("p",{children:"• 工位状态：保持原有状态"}),(0,s.jsx)("p",{style:{marginBottom:0,marginTop:8},children:(0,s.jsx)("strong",{children:'点击"确认应用排程结果"后才会正式应用所有更改。'})})]}),type:"info",showIcon:!0,style:{marginBottom:16}}),(0,s.jsx)(i.Z,{size:"small",style:{marginBottom:16},children:(0,s.jsxs)(l.Z,{gutter:16,children:[(0,s.jsx)(c.Z,{span:4,children:(0,s.jsx)(x.Z,{title:"总工单数",value:o.statistics.totalWorkOrders,suffix:"个",valueStyle:{color:"#1890ff"}})}),(0,s.jsx)(c.Z,{span:4,children:(0,s.jsx)(x.Z,{title:"成功排程",value:o.statistics.successfullyScheduled,suffix:"个",valueStyle:{color:"#52c41a"}})}),(0,s.jsx)(c.Z,{span:4,children:(0,s.jsx)(x.Z,{title:"相同模具",value:o.statistics.sameMoldAssignments,suffix:"个",valueStyle:{color:"#722ed1"}})}),(0,s.jsx)(c.Z,{span:4,children:(0,s.jsx)(x.Z,{title:"涉及工位",value:o.statistics.involvedWorkstations,suffix:"个",valueStyle:{color:"#faad14"}})}),(0,s.jsx)(c.Z,{span:4,children:(0,s.jsx)(x.Z,{title:"正常风险",value:w.normal,suffix:"个",valueStyle:{color:"#52c41a"}})}),(0,s.jsx)(c.Z,{span:4,children:(0,s.jsx)(x.Z,{title:"风险工单",value:w.medium+w.high,suffix:"个",valueStyle:{color:"#ff4d4f"}})})]})}),(0,s.jsxs)(g.default,{activeKey:m,onChange:f,children:[(0,s.jsx)(tI,{tab:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(S.Z,{}),(0,s.jsx)("span",{children:"排程结果"}),(0,s.jsx)(p.Z,{count:o.schedulingResults.length,showZero:!0})]}),children:(0,s.jsx)(er.Z,{columns:y,dataSource:o.schedulingResults,rowKey:"workOrderId",size:"small",pagination:{pageSize:10},scroll:{y:400}})},"results"),(0,s.jsx)(tI,{tab:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(tS.Z,{}),(0,s.jsx)("span",{children:"冲突警告"}),(0,s.jsx)(p.Z,{count:o.conflicts.length,showZero:!0})]}),children:o.conflicts.length>0?(0,s.jsx)(er.Z,{columns:[{title:"冲突类型",dataIndex:"type",key:"type",width:120,render:e=>{let t={time_overlap:{text:"时间重叠",color:"red"},workstation_conflict:{text:"工位冲突",color:"orange"},mold_conflict:{text:"模具冲突",color:"yellow"}}[e]||{text:e,color:"default"};return(0,s.jsx)(u.Z,{color:t.color,children:t.text})}},{title:"涉及工位",dataIndex:"workstation",key:"workstation",width:150},{title:"冲突工单",dataIndex:"conflictingTasks",key:"conflictingTasks",width:200,render:e=>(0,s.jsx)(d.Z,{wrap:!0,children:e.map(e=>(0,s.jsx)(u.Z,{color:"red",children:e.slice(-8)},e))})},{title:"重叠时间",dataIndex:"overlapTime",key:"overlapTime",width:100,render:e=>e?"".concat(e," 分钟"):"-"},{title:"描述",dataIndex:"description",key:"description",ellipsis:!0}],dataSource:o.conflicts,rowKey:(e,t)=>"conflict-".concat(t),size:"small",pagination:{pageSize:10},scroll:{y:400}}):(0,s.jsx)(eO.Z,{message:"无冲突",description:"排程结果无时间冲突或工位冲突",type:"success",showIcon:!0})},"conflicts"),(0,s.jsx)(tI,{tab:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(C.Z,{}),(0,s.jsx)("span",{children:"风险评估"}),(0,s.jsx)(p.Z,{count:w.medium+w.high,showZero:!0})]}),children:(0,s.jsx)(er.Z,{columns:j,dataSource:o.riskAssessments,rowKey:"workOrderId",size:"small",pagination:{pageSize:10},scroll:{y:400}})},"risks")]}),o.conflicts.length>0&&(0,s.jsx)(eO.Z,{message:"发现排程冲突",description:"系统检测到时间冲突，建议检查冲突详情并考虑调整排程参数",type:"warning",showIcon:!0,style:{marginTop:16}}),(0,s.jsx)(eO.Z,{message:"\uD83D\uDCA1 温馨提示",description:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{children:"这是排程计算的预览结果，您可以："}),(0,s.jsxs)("ul",{style:{marginBottom:0,paddingLeft:20},children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"确认应用"}),"：将排程结果应用到实际系统，更新工单和工位状态"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"取消"}),"：放弃此次排程，不做任何更改"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"查看详情"}),"：检查排程结果、冲突和风险评估"]})]})]}),type:"success",showIcon:!0,style:{marginTop:16}})]})},tO=r(83539),tE=r(36476),tW=()=>{let{message:e}=a.Z.useApp(),[t,r]=(0,n.useState)({queueExists:!1,handlersRegistered:!1,messagesInQueue:0,pendingOrdersCount:0,errors:[]}),[o,l]=(0,n.useState)(!1),[c,m]=(0,n.useState)(!1),x=async()=>{l(!0);let e=[];try{throw Error("消息队列服务已删除")}catch(t){e.push("调试信息收集失败: ".concat(t instanceof Error?t.message:"未知错误")),r(t=>({...t,errors:e}))}finally{l(!1)}},g=async()=>{try{throw Error("消息队列服务已删除")}catch(t){e.error("发送测试消息失败: ".concat(t instanceof Error?t.message:"未知错误"))}},p=async()=>{try{throw Error("消息队列服务已删除")}catch(t){e.error("清空队列失败: ".concat(t instanceof Error?t.message:"未知错误"))}},f=()=>t.errors.length>0?"red":t.queueExists&&t.handlersRegistered?"green":"orange";return(0,n.useEffect)(()=>{c&&x()},[c,0]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(tO.Z,{}),onClick:()=>m(!0),title:"消息队列调试器",children:"调试器"}),(0,s.jsx)(ee.Z,{title:"消息队列调试器",open:c,onCancel:()=>m(!1),width:800,footer:[(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(w.Z,{}),onClick:x,loading:o,children:"刷新"},"refresh"),(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(tE.Z,{}),onClick:g,type:"primary",children:"发送测试消息"},"test"),(0,s.jsx)(h.ZP,{onClick:p,danger:!0,children:"清空队列"},"clear"),(0,s.jsx)(h.ZP,{onClick:()=>m(!1),children:"关闭"},"close")],children:(0,s.jsxs)(d.Z,{direction:"vertical",style:{width:"100%"},size:"large",children:[(0,s.jsx)(eO.Z,{message:"消息队列状态: ".concat(t.errors.length>0?"有错误":t.queueExists&&t.handlersRegistered?"正常":"配置不完整"),type:"green"===f()?"success":"orange"===f()?"warning":"error",showIcon:!0}),(0,s.jsx)(i.Z,{title:"系统状态",size:"small",children:(0,s.jsxs)(eS.Z,{column:2,size:"small",children:[(0,s.jsx)(eS.Z.Item,{label:"生产队列",children:(0,s.jsx)(u.Z,{color:t.queueExists?"green":"red",children:t.queueExists?"已创建":"未创建"})}),(0,s.jsx)(eS.Z.Item,{label:"消息处理器",children:(0,s.jsx)(u.Z,{color:t.handlersRegistered?"green":"red",children:t.handlersRegistered?"已注册":"未注册"})}),(0,s.jsx)(eS.Z.Item,{label:"队列中消息数",children:(0,s.jsx)(u.Z,{color:t.messagesInQueue>0?"blue":"default",children:t.messagesInQueue})}),(0,s.jsx)(eS.Z.Item,{label:"待排产订单数",children:(0,s.jsx)(u.Z,{color:t.pendingOrdersCount>0?"green":"default",children:t.pendingOrdersCount})}),t.lastMessageTime&&(0,s.jsx)(eS.Z.Item,{label:"最后消息时间",span:2,children:t.lastMessageTime})]})}),t.errors.length>0&&(0,s.jsx)(i.Z,{title:"错误信息",size:"small",children:(0,s.jsx)("ul",{style:{margin:0,paddingLeft:"20px"},children:t.errors.map((e,t)=>(0,s.jsx)("li",{style:{color:"#ff4d4f",marginBottom:"4px"},children:e},t))})}),(0,s.jsx)(i.Z,{title:"诊断建议",size:"small",children:(0,s.jsxs)("div",{style:{fontSize:"14px",lineHeight:"1.6"},children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"如果消息无法传输，请检查："})}),(0,s.jsxs)("ol",{style:{paddingLeft:"20px"},children:[(0,s.jsx)("li",{children:"确保销售订单页面和自动排单页面都已访问过（初始化消息队列）"}),(0,s.jsx)("li",{children:"检查浏览器控制台是否有错误信息"}),(0,s.jsx)("li",{children:"确认MRP执行成功并生成了待排产订单"}),(0,s.jsx)("li",{children:"检查消息处理器是否正确注册"}),(0,s.jsx)("li",{children:"尝试发送测试消息验证队列功能"})]}),(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"预期流程："})}),(0,s.jsx)("p",{children:"MRP执行 → 发送消息到队列 → 消息处理器接收 → 添加到自动排单模块 → 在待排产订单列表中显示"})]})})]})})]})};class t_{static async calculateSchedulingResults(e,t){try{try{let e=W.dataAccessManager.clearServiceCache("WorkstationService","pre_calculation"),t=W.dataAccessManager.clearDataTypeCache("workstations");console.log("\uD83E\uDDF9 [排程计算前缓存清理] 清除工位服务缓存: ".concat(e," 个缓存项")),console.log("\uD83E\uDDF9 [排程计算前缓存清理] 清除工位数据缓存: ".concat(t," 个缓存项"))}catch(e){console.warn("⚠️ [排程计算前缓存清理] 清除缓存时发生错误:",e)}(null==t?void 0:t.sameMoldDeliveryToleranceDays)!==void 0&&(this.currentSchedulingConfig.sameMoldDeliveryToleranceDays=t.sameMoldDeliveryToleranceDays);let r=await this.getPendingWorkOrders(e),s=await this.getAvailableWorkstations(),n=await this.getWorkTimeConfiguration();console.log("\uD83D\uDD0D [排程调试] 获取到的工位状态:"),s.forEach(e=>{console.log("   工位 ".concat(e.code,": 当前模具=").concat(e.currentMoldNumber||"无",", 最后结束时间=").concat(e.lastEndTime||"无",", 当前批次=").concat(e.currentBatchNumber||"无"))});let a=this.createVirtualWorkstationStates(s);return await this.performSchedulingCalculation(r,a,n)}catch(e){throw Error("排程计算失败: ".concat(e instanceof Error?e.message:"未知错误"))}}static async executeSameMoldPriorityScheduling(e){try{return await this.calculateSchedulingResults(e)}catch(e){throw Error("排程执行失败: ".concat(e instanceof Error?e.message:"未知错误"))}}static async performSchedulingCalculation(e,t,r){try{let s=[],n=[],a=[];for(let n of e){console.log("\uD83D\uDD0D [相同模具检测] 工单 ".concat(n.batchNumber," 模具 ").concat(n.formingMoldNumber));let e=this.findSameMoldWorkstation(n.formingMoldNumber,t);if(e){console.log("✅ [相同模具匹配] 工单 ".concat(n.batchNumber," 找到相同模具工位: ").concat(e.code));let t=await this.assignWorkOrderToWorkstation(n,e,!0,r);s.push(t),this.updateVirtualWorkstationStatus(e,t)}else console.log("❌ [相同模具检测] 工单 ".concat(n.batchNumber," 未找到相同模具工位")),a.push(n)}let o=await this.processRemainingWorkOrdersWithMoldGrouping(a,t,r);n.push(...o);let i=[...s,...n],l=this.detectSchedulingConflicts(i),c=this.assessDeliveryRisks(i,e),d=this.calculateStatistics(e,i,s.length,n.length,t.map(e=>e.originalState)),u=i.reduce((e,t)=>{let r=t.workstationCode;return e[r]=(e[r]||0)+1,e},{});return Object.entries(u).forEach(e=>{let[t,r]=e}),{schedulingResults:i,conflicts:l,riskAssessments:c,statistics:d}}catch(e){throw Error("排程执行失败: ".concat(e instanceof Error?e.message:"未知错误"))}}static async getPendingWorkOrders(e){try{let t=await W.dataAccessManager.productionWorkOrders.getAll();if("success"!==t.status||!t.data||!t.data.items||0===t.data.items.length)throw Error("获取生产工单失败：数据为空");let r=t.data.items,s=r.filter(t=>e.includes(t.id)&&"pending"===t.status);if(0===s.length)throw r.forEach(t=>{e.includes(t.id)}),Error("没有找到待开始状态的工单");let n=[];s.forEach(e=>{let t=[];e.formingMoldNumber||t.push("formingMoldNumber"),(!e.plannedMoldCount||e.plannedMoldCount<=0)&&t.push("plannedMoldCount"),(!e.hourlyCapacity||e.hourlyCapacity<=0)&&t.push("hourlyCapacity"),e.deliveryDate||t.push("deliveryDate"),n.push({workOrder:e,isValid:0===t.length,missingFields:t})}),n.forEach(e=>{e.isValid});let a=n.filter(e=>e.isValid).map(e=>e.workOrder);if(0===a.length)throw n.filter(e=>!e.isValid).length,n.forEach(e=>{e.isValid}),Error("没有找到有效的待排程工单：所有工单都存在数据完整性问题");return a}catch(e){throw e}}static async getAvailableWorkstations(){try{let e=await W.dataAccessManager.workstations.getWorkstations();if("success"!==e.status||!e.data||!e.data.items)throw Error("获取工位信息失败");let t=e.data.items.filter(e=>"active"===e.status);if(0===t.length)throw Error("没有可用的工位");return t}catch(e){throw e}}static async getWorkTimeConfiguration(){try{let e=await (0,e2.Ro)(()=>W.dataAccessManager.workTime.getDefault(),"获取工作时间配置");if(!e)throw Error("获取工作时间配置失败");return e}catch(e){throw e}}static createVirtualWorkstationStates(e){return e.map(e=>({...e,isVirtual:!0,originalState:{...e},code:e.code,name:e.name,lastEndTime:e.lastEndTime||null,currentMoldNumber:e.currentMoldNumber||null,currentBatchNumber:e.currentBatchNumber||null,batchNumberQueue:e.batchNumberQueue||[]}))}static updateVirtualWorkstationStatus(e,t){e.lastEndTime=t.plannedEndTime,e.currentMoldNumber=t.formingMoldNumber,e.currentBatchNumber=t.batchNumber,e.batchNumberQueue||(e.batchNumberQueue=[]),e.batchNumberQueue.includes(t.batchNumber)||e.batchNumberQueue.push(t.batchNumber)}static hasTimeOverlap(e,t){let r=new Date(e.plannedStartTime).getTime(),s=new Date(e.plannedEndTime).getTime(),n=new Date(t.plannedStartTime).getTime();return r<new Date(t.plannedEndTime).getTime()&&n<s}static async calculateWorkstationUpdates(e){try{let t=await W.dataAccessManager.workstations.getWorkstations();if("success"!==t.status||!t.data||!t.data.items)return[];let r=t.data.items,s=new Map,n=e.reduce((e,t)=>{let r=t.workstationCode;return e[r]||(e[r]=[]),e[r].push(t),e},{});return Object.entries(n).forEach(e=>{let[t,n]=e,a=n.sort((e,t)=>new Date(e.plannedEndTime).getTime()-new Date(t.plannedEndTime).getTime()),o=a[a.length-1],i=r.find(e=>e.code===t);i&&s.set(i.id,{workstationId:i.id,updates:{lastEndTime:o.plannedEndTime,currentMoldNumber:o.formingMoldNumber,currentBatchNumber:o.batchNumber},batchNumber:o.batchNumber})}),Array.from(s.values())}catch(e){return[]}}static findSameMoldWorkstation(e,t){let r=t.filter(t=>t.currentMoldNumber===e);return 0===r.length?null:r.reduce((e,t)=>{let r=e.lastEndTime?new Date(e.lastEndTime):new Date;return(t.lastEndTime?new Date(t.lastEndTime):new Date)<r?t:e})}static sortByDeliveryPriority(e){return e.sort((e,t)=>{let r=new Date(e.deliveryDate),s=new Date(t.deliveryDate);if(r.getTime()!==s.getTime())return r.getTime()-s.getTime();let n={A:5,B:4,C:3,D:2,E:1},a=n[e.customerCreditLevel]||0,o=n[t.customerCreditLevel]||0;return a!==o?o-a:t.plannedMoldCount-e.plannedMoldCount})}static selectOptimalWorkstation(e){return 0===e.length?null:e.reduce((e,t)=>{let r=e.lastEndTime?new Date(e.lastEndTime):new Date;return(t.lastEndTime?new Date(t.lastEndTime):new Date)<r?t:e})}static async assignWorkOrderToWorkstation(e,t,r,s){try{let n;let a=r?0:20;if(t.lastEndTime){n=new Date(t.lastEndTime);let e=new Date;n<e?n=this.calculateNextValidWorkTime(s):r||n.setMinutes(n.getMinutes()+a)}else n=this.calculateNextValidWorkTime(s);let o=Math.ceil(e.plannedMoldCount/e.hourlyCapacity*60),i=this.calculateContinuousWorkEndTime(n,o,s);return{workOrderId:e.id,batchNumber:e.batchNumber,formingMoldNumber:e.formingMoldNumber||"",workstation:t.name,workstationCode:t.code,plannedStartTime:n.toISOString(),plannedEndTime:i.toISOString(),productionTimeMinutes:o,isSameMold:r,changeoverTimeMinutes:a}}catch(e){throw Error("分配工单到工位失败: ".concat(e instanceof Error?e.message:"未知错误"))}}static calculateNextValidWorkTime(e){let t=new Date,r=t.toTimeString().substring(0,5),s=e.workTimeSlots.filter(e=>e.isActive).sort((e,t)=>e.startTime.localeCompare(t.startTime));for(let e of s)if(r>=e.startTime&&r<e.endTime)return t;for(let e of s)if(r<e.startTime){let r=new Date(t),[s,n]=e.startTime.split(":").map(Number);return r.setHours(s,n,0,0),r}let n=new Date(t);if(n.setDate(n.getDate()+1),s.length>0){let[e,t]=s[0].startTime.split(":").map(Number);return n.setHours(e,t,0,0),n}return t}static calculateContinuousWorkEndTime(e,t,r){let s=new Date(e),n=t,a=r.workTimeSlots.filter(e=>e.isActive).sort((e,t)=>e.startTime.localeCompare(t.startTime));for(;n>0;){s.toISOString().split("T")[0];let e=s.toTimeString().substring(0,5),t=this.calculateRemainingWorkTimeToday(e,a);if(t>=n)return this.advanceTimeWithinWorkDay(s,n,a);{n-=t;let e=new Date(s);e.setDate(e.getDate()+1),s=new Date("".concat(e.toISOString().split("T")[0],"T").concat(a[0].startTime,":00"))}}return s}static calculateRemainingWorkTimeToday(e,t){let r=0;for(let s of t)if(e<s.endTime){let t=e>s.startTime?e:s.startTime;r+=this.calculateTimeDifferenceMinutes(t,s.endTime)}return r}static advanceTimeWithinWorkDay(e,t,r){let s=new Date(e),n=t,a=s.toTimeString().substring(0,5);for(let e of r){if(a>=e.endTime)continue;let t=a>e.startTime?a:e.startTime,o=this.calculateTimeDifferenceMinutes(t,e.endTime);if(n<=o){let e=this.addMinutesToTime(t,n),r=new Date(s),[a,o]=e.split(":").map(Number);return r.setHours(a,o,0,0),r}if(n-=o,r.indexOf(e)<r.length-1){let[t,n]=r[r.indexOf(e)+1].startTime.split(":").map(Number);s.setHours(t,n,0,0)}}return s}static calculateTimeDifferenceMinutes(e,t){let[r,s]=e.split(":").map(Number),[n,a]=t.split(":").map(Number);return Math.max(0,60*n+a-(60*r+s))}static addMinutesToTime(e,t){let[r,s]=e.split(":").map(Number),n=60*r+s+t;return"".concat((Math.floor(n/60)%24).toString().padStart(2,"0"),":").concat((n%60).toString().padStart(2,"0"))}static async updateWorkstationStatus(e,t){try{var r;if("active"!==e.status){console.warn("工位 ".concat(e.code," 状态为 ").concat(e.status,"，无法分配任务"));return}if(!t.formingMoldNumber||!t.batchNumber){console.error("排程结果缺少必要信息：模具编号=".concat(t.formingMoldNumber,", 批次号=").concat(t.batchNumber));return}console.log("\uD83D\uDD04 工位状态转换: ".concat(e.code," 从空闲状态激活为生产状态")),console.log("   模具: ".concat(t.formingMoldNumber)),console.log("   批次: ".concat(t.batchNumber)),console.log("   计划结束时间: ".concat(t.plannedEndTime));let s={source:"scheduling",operation:"production_assignment",userId:"scheduling-system",reason:"自动排程分配任务 - 模具:".concat(t.formingMoldNumber,", 批次:").concat(t.batchNumber)},n=await this.workstationUpdateService.updateWorkstation(e.id,{lastEndTime:t.plannedEndTime,currentMoldNumber:t.formingMoldNumber,currentBatchNumber:t.batchNumber},s,e.version);if(!n.success){n.conflictInfo?console.error("❌ 工位 ".concat(e.code," 状态更新失败 - 版本冲突:"),n.conflictInfo):console.error("❌ 工位 ".concat(e.code," 状态更新失败:"),n.error);return}console.log("✅ 工位 ".concat(e.code," 状态更新成功，版本: ").concat(e.version," → ").concat(null===(r=n.workstation)||void 0===r?void 0:r.version)),e.lastEndTime=t.plannedEndTime,e.currentMoldNumber=t.formingMoldNumber,e.currentBatchNumber=t.batchNumber,await (0,e2.Ro)(()=>W.dataAccessManager.workstations.addToQueue(e.id,t.batchNumber),"更新工位 ".concat(e.code," 队列"))||console.warn("⚠️ 工位 ".concat(e.code," 队列更新失败，但状态更新成功")),e.batchNumberQueue||(e.batchNumberQueue=[]),e.batchNumberQueue.includes(t.batchNumber)||e.batchNumberQueue.push(t.batchNumber),console.log("✅ 工位 ".concat(e.code," 状态更新完成"))}catch(t){console.error("❌ 更新工位 ".concat(e.code," 状态时发生错误:"),t)}}static detectSchedulingConflicts(e){let t=[],r=new Map;for(let t of e)r.has(t.workstation)||r.set(t.workstation,[]),r.get(t.workstation).push(t);for(let[e,s]of Array.from(r.entries())){let r=s.sort((e,t)=>new Date(e.plannedStartTime).getTime()-new Date(t.plannedStartTime).getTime());for(let s=0;s<r.length-1;s++){let n=r[s],a=r[s+1],o=new Date(n.plannedEndTime),i=new Date(a.plannedStartTime);if(o>i){let r=Math.ceil((o.getTime()-i.getTime())/6e4);t.push({type:"time_overlap",workstation:e,conflictingTasks:[n.workOrderId,a.workOrderId],overlapTime:r,description:"工位 ".concat(e," 存在时间重叠，重叠时间 ").concat(r," 分钟")})}}}return t}static assessDeliveryRisks(e,t){let r=[];for(let s of e){let e=t.find(e=>e.id===s.workOrderId);if(!e)continue;let n=new Date(e.deliveryDate),a=new Date(s.plannedEndTime),o=Math.ceil((a.getTime()-n.getTime())/864e5);if(o>0){let e=o>3?"high":"medium";r.push({workOrderId:s.workOrderId,riskLevel:e,delayDays:o,suggestion:"考虑调整优先级或增加产能",expectedCompletionDate:a.toISOString().split("T")[0]})}else r.push({workOrderId:s.workOrderId,riskLevel:"normal",advanceDays:Math.abs(o),expectedCompletionDate:a.toISOString().split("T")[0]})}return r}static calculateStatistics(e,t,r,s,n){var a;let o=new Set(t.map(e=>e.workstation)).size,i=t.reduce((e,t)=>new Date(t.plannedEndTime)>new Date(e)?t.plannedEndTime:e,(null===(a=t[0])||void 0===a?void 0:a.plannedEndTime)||new Date().toISOString());return{totalWorkOrders:e.length,successfullyScheduled:t.length,sameMoldAssignments:r,deliveryDateAssignments:s,involvedWorkstations:o,latestCompletionTime:i}}static async applySchedulingResults(e){try{let t=e.map(e=>(0,e2.Ro)(()=>W.dataAccessManager.productionWorkOrders.update(e.workOrderId,{workstation:e.workstation,workstationCode:e.workstationCode,plannedStartTime:e.plannedStartTime,plannedEndTime:e.plannedEndTime,status:"scheduled",updatedAt:new Date().toISOString()}),"更新工单 ".concat(e.workOrderId," 状态"))),s=await Promise.allSettled(t);s.filter(e=>"fulfilled"===e.status&&null!==e.value).length,s.filter(e=>"rejected"===e.status||null===e.value).length>0&&s.forEach((e,t)=>{"rejected"===e.status||"fulfilled"===e.status&&e.value});let n=await this.calculateWorkstationUpdates(e);for(let t of n){let r=await W.dataAccessManager.workstations.getById(t.workstationId);if(!r||"success"!==r.status||!r.data){console.warn("⚠️ 无法获取工位 ".concat(t.workstationId," 信息，跳过更新"));continue}let s={source:"scheduling",operation:"production_assignment",userId:"scheduling-system",reason:"批量排程结果应用"},n=await this.workstationUpdateService.updateWorkstation(t.workstationId,t.updates,s,r.data.version);if(!n.success){console.error("❌ 工位 ".concat(t.workstationId," 状态更新失败:"),n.error);continue}let a=n.workstation;if(!a){console.error("❌ 工位 ".concat(t.workstationId," 更新响应为空"));continue}for(let r of e.filter(e=>e.workstationCode===a.code))await (0,e2.Ro)(()=>W.dataAccessManager.workstations.addToQueue(t.workstationId,r.batchNumber),"更新工位 ".concat(t.workstationId," 队列"))}console.log({操作类型:"自动排程",排程模式:"相同模具优先",影响工单数:e.length,影响工位数:n.length,操作时间:new Date().toISOString(),工位更新详情:n.map(e=>({工位ID:e.workstationId,最后结束时间:e.updates.lastEndTime,当前模具:e.updates.currentMoldNumber}))});try{let{dataSyncService:t}=await Promise.resolve().then(r.bind(r,15575));t.addChangeEvent({type:"update",module:"production",entityType:"production_work_order",entityId:"batch_update",newData:{count:e.length,workOrderIds:e.map(e=>e.workOrderId),workstationCodes:Array.from(new Set(e.map(e=>e.workstationCode))),timestamp:new Date().toISOString()}}),t.addChangeEvent({type:"create",module:"scheduling",entityType:"scheduling_result",entityId:"batch_update",newData:{count:e.length,timestamp:new Date().toISOString()}})}catch(e){}}catch(e){throw Error("应用排程结果失败: ".concat(e instanceof Error?e.message:"未知错误"))}}static async processRemainingWorkOrdersWithMoldGrouping(e,t,r){try{let s=[],n=this.groupWorkOrdersByMold(e),a=this.filterEligibleMoldGroups(n),o=new Set;for(let e of a){let n=await this.assignMoldGroupToSameWorkstation(e,t,r);s.push(...n),e.workOrders.forEach(e=>o.add(e.id))}let i=e.filter(e=>!o.has(e.id));for(let e of this.sortByDeliveryPriority(i)){let n=this.selectOptimalWorkstation(t);if(n){let t=await this.assignWorkOrderToWorkstation(e,n,!1,r);s.push(t),this.updateVirtualWorkstationStatus(n,t)}}return s}catch(e){throw console.error("处理剩余工单失败:",e),Error("处理剩余工单失败: ".concat(e instanceof Error?e.message:"未知错误"))}}static groupWorkOrdersByMold(e){let t=new Map;for(let r of e){let e=r.formingMoldNumber;t.has(e)||t.set(e,[]),t.get(e).push(r)}return t}static filterEligibleMoldGroups(e){let t=[];for(let[r,s]of e)if(s.length>=2){let e=s.map(e=>new Date(e.deliveryDate).getTime()),n=Math.min(...e),a=(Math.max(...e)-n)/864e5;if(a<=this.getSameMoldDeliveryToleranceDays()){let e=s.sort((e,t)=>new Date(e.deliveryDate).getTime()-new Date(t.deliveryDate).getTime());t.push({moldNumber:r,workOrders:e,daysDiff:a})}}return t.sort((e,t)=>new Date(e.workOrders[0].deliveryDate).getTime()-new Date(t.workOrders[0].deliveryDate).getTime())}static async assignMoldGroupToSameWorkstation(e,t,r){let s=[],n=this.selectOptimalWorkstation(t);if(!n)return s;console.log("\uD83D\uDD27 相同模具聚合: 模具 ".concat(e.moldNumber," 的 ").concat(e.workOrders.length," 个工单分配到工位 ").concat(n.code));for(let t=0;t<e.workOrders.length;t++){let a=e.workOrders[t],o=0===t,i=await this.assignWorkOrderToWorkstation(a,n,!o,r);s.push(i),this.updateVirtualWorkstationStatus(n,i)}return s}static getSameMoldDeliveryToleranceDays(){return this.currentSchedulingConfig.sameMoldDeliveryToleranceDays}}t_.workstationUpdateService=e8.J.getInstance(),t_.currentSchedulingConfig={sameMoldDeliveryToleranceDays:30};var tA=r(62416),tR=r(71910),tz=r(40810);let tB=async(e,t)=>{try{let r=await W.dataAccessManager.products.getByCode(t);if("success"!==r.status||!r.data)return console.warn("⚠️ 未找到产品编码 ".concat(t," 的产品数据，使用默认模数计算")),Math.ceil(e/100);let s=r.data;if(!s.formingMoldQuantity||s.formingMoldQuantity<=0)return console.warn("⚠️ 产品 ".concat(t," 的成型模具单模数量无效 (").concat(s.formingMoldQuantity,")，使用默认值")),Math.ceil(e/100);let n=Math.ceil(e/s.formingMoldQuantity);return console.log("✅ 模数计算: 产品=".concat(t,", 计划数量=").concat(e,", 单模数量=").concat(s.formingMoldQuantity,", 生产模数=").concat(n)),n}catch(t){return console.error("❌ 模数计算失败:",t),Math.ceil(e/100)}},tP=0,tL=()=>{let e=new Date,t=e.getFullYear(),r=(e.getMonth()+1).toString().padStart(2,"0"),s=e.getDate().toString().padStart(2,"0"),n="".concat(t).concat(r).concat(s);tP++;let a=(Date.now().toString().slice(-3)+tP.toString().padStart(1,"0")).slice(-4);return"PC".concat(n).concat(a)},tH=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:130;if(!e)throw Error("生产订单数据不能为空");if(!e.productCode||!e.productName)throw Error("生产订单 ".concat(e.orderNumber," 缺少必要的产品信息：productCode=").concat(e.productCode,", productName=").concat(e.productName));if(!e.plannedQuantity||e.plannedQuantity<=0)throw Error("生产订单 ".concat(e.orderNumber," 计划数量无效：").concat(e.plannedQuantity));if(!e.orderNumber)throw Error("生产订单号不能为空");console.log("\uD83D\uDD0D 工单生成验证: 订单=".concat(e.orderNumber,", 产品=").concat(e.productCode,"-").concat(e.productName,", 数量=").concat(e.plannedQuantity));let r=tL(),s=await tB(e.plannedQuantity,e.productCode),n="UNASSIGNED",a="未分配";if(e.workstation){let t=e.workstation.match(/(\d+)/);if(t){let r=t[1].padStart(3,"0");n="WS".concat(r),a=e.workstation;try{var o;let e=await W.dataAccessManager.workstations.getAll();"success"===e.status&&(null===(o=e.data)||void 0===o?void 0:o.items)&&(e.data.items.some(e=>e.code===n)?console.log("✅ 工位编码验证通过: ".concat(n)):(console.warn("⚠️ 工位编码 ".concat(n," 不存在，将使用UNASSIGNED")),n="UNASSIGNED",a="未分配"))}catch(e){console.warn("⚠️ 工位编码验证失败，使用默认值: ".concat(e)),n="UNASSIGNED",a="未分配"}}}let i={batchNumber:r,productCode:e.productCode,productName:e.productName,productModelCode:e.productCode,formingMoldNumber:e.formingMoldNumber,status:"pending",customerCode:e.customerId||"",customerCreditLevel:e.customerCreditLevel,prioritySource:e.prioritySource||"auto",workstation:a,workstationCode:n,createdBy:"system",plannedStartTime:void 0,plannedEndTime:void 0,deliveryDate:e.deliveryDate,plannedMoldCount:s,completedMoldCount:0,hourlyCapacity:t,executionRate:0,exceptionCount:0,sourceOrderId:e.id,sourceOrderNumber:e.orderNumber,customerName:e.customerName||"",isSharedMold:e.isSharedMold||!1};if(i.productCode!==e.productCode||i.productName!==e.productName)throw Error("工单数据映射错误：期望产品 ".concat(e.productCode,"-").concat(e.productName,"，实际得到 ").concat(i.productCode,"-").concat(i.productName));return console.log("✅ 工单生成成功: 批次=".concat(r,", 产品=").concat(i.productCode,"-").concat(i.productName)),i};var tV=r(30227),tF=r(15560);class tQ{static async handleWorkOrderStatusChange(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{showSuccessMessage:r=!0,showErrorMessage:s=!0,successMessage:n="工单状态更新成功",errorMessagePrefix:a="工单状态更新失败",silent:o=!1}=t;try{let t=await tF.Fc.handleWorkOrderStatusChange({workOrderId:e.workOrderId,oldStatus:e.oldStatus,newStatus:e.newStatus,sourceOrderId:e.sourceOrderId,operatorId:e.operatorId,reason:e.reason},{enableErrorLogging:!0,throwOnError:!1});return!o&&t.success&&r&&(t.orderStatusChanged?tv.ZP.success("".concat(n,"，订单状态已自动更新为：").concat(t.newOrderStatus)):tv.ZP.success(n)),{success:t.success,orderStatusChanged:t.orderStatusChanged,newOrderStatus:t.newOrderStatus,error:t.error,duration:t.duration}}catch(t){let e=t instanceof Error?t.message:"未知错误";return!o&&s&&tv.ZP.error("".concat(a,"：").concat(e)),{success:!1,orderStatusChanged:!1,error:e}}}static async handleWorkOrderCreation(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{showSuccessMessage:s=!1,showErrorMessage:n=!0,errorMessagePrefix:a="工单创建状态转换失败",silent:o=!1}=r;try{let r=await tF.Fc.handleWorkOrderCreation(e,t,{enableErrorLogging:!0,throwOnError:!1});return!o&&r.success&&r.orderStatusChanged&&s&&tv.ZP.success("订单状态已自动更新为：".concat(r.newOrderStatus)),{success:r.success,orderStatusChanged:r.orderStatusChanged,newOrderStatus:r.newOrderStatus,error:r.error,duration:r.duration}}catch(t){let e=t instanceof Error?t.message:"未知错误";return!o&&n&&tv.ZP.error("".concat(a,"：").concat(e)),{success:!1,orderStatusChanged:!1,error:e}}}static async handleBatchWorkOrderCreation(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{silent:r=!0}=t,s=[];for(let n of e){let e=await this.handleWorkOrderCreation(n.id,n.status,{...t,silent:r});s.push({orderId:n.id,orderNumber:n.orderNumber,result:e})}return s}static getTransitionStatistics(e){let t=e.length,r=e.filter(e=>e.success).length,s=e.filter(e=>e.orderStatusChanged).length,n=e.filter(e=>e.duration).map(e=>e.duration);return{total:t,successful:r,failed:t-r,orderStatusChanged:s,successRate:Math.round(100*(t>0?r/t*100:0))/100,averageDuration:Math.round(n.length>0?n.reduce((e,t)=>e+t,0)/n.length:0)}}}let tU={handleStatusChange:tQ.handleWorkOrderStatusChange,handleCreation:tQ.handleWorkOrderCreation,handleBatch:tQ.handleBatchWorkOrderCreation,getStatistics:tQ.getTransitionStatistics};var tY=r(63420),tq=r(78466),tX=r(33729),tG=r(27787),tK=e=>{let{metrics:t,cacheStats:r,isMonitoring:n,lastUpdateTime:a,error:o,onClearCache:u,formatMemorySize:m,formatPercentage:g}=e,f=()=>r&&r.enabled?r.hitRate>.7?"success":r.hitRate>.3?"warning":"error":"default";return(0,s.jsxs)(i.Z,{size:"small",title:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(tY.Z,{}),"DataAccessManager监控",(0,s.jsx)(p.Z,{status:n?"processing":"default",text:n?"运行中":"已停止"})]}),extra:(0,s.jsx)(d.Z,{children:(0,s.jsx)(D.Z,{title:"清理缓存",children:(0,s.jsx)(h.ZP,{type:"text",size:"small",icon:(0,s.jsx)(tq.Z,{}),onClick:u,disabled:!(null==r?void 0:r.enabled)})})}),style:{marginBottom:16},children:[o&&(0,s.jsx)(eO.Z,{message:"监控错误",description:o,type:"error",style:{marginBottom:12}}),t?(0,s.jsxs)(l.Z,{gutter:16,children:[(0,s.jsx)(c.Z,{span:12,children:(0,s.jsx)(x.Z,{title:"平均响应时间",value:"".concat(t.averageResponseTime.toFixed(1),"ms"),prefix:(0,s.jsx)(tX.Z,{}),valueStyle:{fontSize:"14px",color:t.averageResponseTime>1e3?"#ff4d4f":"#52c41a"}})}),(0,s.jsx)(c.Z,{span:12,children:(0,s.jsx)(x.Z,{title:"错误率",value:g(t.errorRate),valueStyle:{fontSize:"14px",color:t.errorRate>.05?"#ff4d4f":"#52c41a"}})})]}):(0,s.jsx)("div",{style:{textAlign:"center",color:"#999"},children:"性能指标不可用"}),r?(0,s.jsxs)("div",{style:{marginTop:16},children:[(0,s.jsxs)(l.Z,{gutter:16,children:[(0,s.jsx)(c.Z,{span:12,children:(0,s.jsx)(x.Z,{title:"缓存命中率",value:g(r.hitRate),prefix:(0,s.jsx)(tG.Z,{}),valueStyle:{fontSize:"14px",color:r.hitRate>.5?"#52c41a":"#ff4d4f"}})}),(0,s.jsx)(c.Z,{span:12,children:(0,s.jsx)(x.Z,{title:"缓存大小",value:r.size,valueStyle:{fontSize:"14px"}})})]}),(0,s.jsx)("div",{style:{marginTop:12},children:(0,s.jsx)(td.Z,{percent:Math.round(100*r.hitRate),status:"success"===f()?"success":"warning"===f()?"active":"exception",size:"small",format:e=>"命中率 ".concat(e,"%")})}),(0,s.jsxs)(l.Z,{gutter:16,style:{marginTop:12},children:[(0,s.jsx)(c.Z,{span:8,children:(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["命中: ",(0,s.jsx)("strong",{children:r.hits})]})}),(0,s.jsx)(c.Z,{span:8,children:(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["未命中: ",(0,s.jsx)("strong",{children:r.misses})]})}),(0,s.jsx)(c.Z,{span:8,children:(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["状态: ",(0,s.jsx)(p.Z,{status:r.enabled?"success":"default",text:r.enabled?"启用":"禁用"})]})})]})]}):(0,s.jsx)("div",{style:{textAlign:"center",color:"#999",marginTop:16},children:"缓存统计不可用"}),t&&(0,s.jsxs)(l.Z,{gutter:16,style:{marginTop:16},children:[(0,s.jsx)(c.Z,{span:12,children:(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["总请求: ",(0,s.jsx)("strong",{children:t.totalRequests})]})}),(0,s.jsx)(c.Z,{span:12,children:(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["成功请求: ",(0,s.jsx)("strong",{children:t.successfulRequests})]})})]}),a&&(0,s.jsxs)("div",{style:{fontSize:"12px",color:"#666",marginTop:12,textAlign:"center"},children:["最后更新: ",new Date(a).toLocaleTimeString()]}),(0,s.jsx)("div",{style:{fontSize:"11px",color:"#52c41a",marginTop:8,textAlign:"center",fontStyle:"italic"},children:"✅ 使用DataAccessManager统一监控"})]})};let tJ=()=>{let{message:e}=a.Z.useApp(),{metrics:t,cacheStats:D,isMonitoring:T,lastUpdateTime:M,error:I,clearCache:N,getPerformanceAlerts:O,formatMemorySize:E,formatPercentage:_,isHealthy:A,needsOptimization:R}=(0,tz.e)({interval:6e4,enabled:!0,showDetails:!1}),[z,B]=(0,n.useState)(!1),[P,L]=(0,n.useState)(null),[H,V]=(0,n.useState)(!1),[F,Q]=(0,n.useState)(!1),[U,Y]=(0,n.useState)(!1),[q,X]=(0,n.useState)([]),[G,J]=(0,n.useState)(null),[$,ee]=(0,n.useState)(!1),[et,er]=(0,n.useState)("pending-orders"),[es,ea]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=new URLSearchParams(window.location.search).get("tab"),t=window.location.hash.replace("#",""),r=e||t;r&&["pending-orders","production-work-orders","scheduling-board","workstation-management"].includes(r)&&er(r)},[]);let eo=e=>{er(e);let t=new URL(window.location.href);t.searchParams.set("tab",e),window.history.replaceState({},"",t.toString())},[ei,el]=(0,n.useState)([]),[ec,ed]=(0,n.useState)([]),[eu,eh]=(0,n.useState)([]),em=ei.filter(e=>"in_plan"===e.status),ex=ei.filter(e=>"planned"===e.status),eg=ei.filter(e=>"in_progress"===e.status),ep=ei.filter(e=>"completed"===e.status),[ef,ey]=(0,n.useState)({totalOrders:0,inPlanOrders:0,plannedOrders:0,inProgressOrders:0,completedOrders:0}),ej=(0,n.useMemo)(()=>({totalOrders:ei.length,inPlanOrders:em.length,plannedOrders:ex.length,inProgressOrders:eg.length,completedOrders:ep.length}),[ei.length,em.length,ex.length,eg.length,ep.length]);(0,n.useEffect)(()=>{ey(ej)},[ej]);let ew=async function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{t&&W.dataAccessManager.clearAllCache();let r=await W.dataAccessManager.productionOrders.getAll();"success"===r.status&&r.data&&r.data.items?(el(r.data.items),console.log("\uD83D\uDCCA 加载生产订单: ".concat(r.data.items.length," 个订单"))):(e.error(r.message||"加载生产订单失败"),console.error("加载生产订单失败:",r))}catch(t){e.error("系统错误，请稍后重试"),console.error("加载生产订单异常:",t)}},ek=async function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{let n;t&&(console.log("\uD83D\uDD27 开始彻底清除工单缓存..."),W.dataAccessManager.clearAllCache(),console.log("✅ 缓存已清除"),await new Promise(e=>setTimeout(e,100)),console.log("\uD83D\uDD27 所有工单相关缓存已彻底清除")),console.log("\uD83D\uDCCA 开始获取工单数据...");try{var r,s;n=await W.dataAccessManager.productionWorkOrders.getAll(),console.log("\uD83D\uDCCA 工单数据响应 (方式1-缓存):",{status:n.status,hasData:!!n.data,itemsLength:(null===(s=n.data)||void 0===s?void 0:null===(r=s.items)||void 0===r?void 0:r.length)||0})}catch(e){throw console.error("获取工单数据失败:",e),e}"success"===n.status&&n.data&&n.data.items?(ed(n.data.items),console.log("\uD83D\uDCCA 加载生产工单: ".concat(n.data.items.length," 个工单")),n.data.items.length>0&&console.log("\uD83D\uDCCB 工单详情:",n.data.items.map(e=>({id:e.id,batchNumber:e.batchNumber,productName:e.productName,status:e.status})))):(e.error(n.message||"加载生产工单失败"),console.error("加载生产工单失败:",n))}catch(t){e.error("系统错误，请稍后重试"),console.error("加载生产工单异常:",t)}},eS=async function(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{if(t){console.log("\uD83D\uDD04 重置所有工位状态到初始空闲状态...");let e=await W.dataAccessManager.workstations.resetAllWorkstationsToIdle();if((null==e?void 0:e.status)==="success"){var r,s,n;console.log("✅ 工位状态重置成功，成功重置了 ".concat((null===(r=e.data)||void 0===r?void 0:r.resetCount)||0," 个工位")),(null===(n=e.data)||void 0===n?void 0:null===(s=n.details)||void 0===s?void 0:s.length)>0&&console.log("\uD83D\uDCCB 重置详情:",e.data.details.map(e=>({工位ID:e.workstationId,状态:"成功",消息:"工位 ".concat(e.workstationCode," 已重置为空闲状态")})))}else console.warn("⚠️ 重置工位状态失败:",null==e?void 0:e.message)}let a=await W.dataAccessManager.workstations.getAll();"success"===a.status&&a.data&&a.data.items?(eh(a.data.items),console.log({count:a.data.items.length,workstations:a.data.items.map(e=>({id:e.id,code:e.code,name:e.name}))})):e.error(a.message||"加载工位数据失败")}catch(t){e.error("系统错误，请稍后重试")}},eZ=async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];console.log("\uD83D\uDD04 开始刷新数据...",{forceRefresh:e,isInitialLoad:t}),await Promise.all([ew(e),ek(e),eS(t)]),console.log("✅ 数据刷新完成")},eb=(0,n.useCallback)(()=>{let e=ei.length,t=ei.filter(e=>"in_plan"===e.status).length;ey({totalOrders:e,inPlanOrders:t,plannedOrders:ei.filter(e=>"planned"===e.status).length,inProgressOrders:ei.filter(e=>"in_progress"===e.status).length,completedOrders:ei.filter(e=>"completed"===e.status).length})},[ei]);(0,n.useEffect)(()=>{eb()},[eb]);let eC=(0,n.useCallback)(async()=>{if(0!==ei.length)try{let{dataConsistencyService:t}=await r.e(3395).then(r.bind(r,53395)),s=(await t.performFullConsistencyCheck()).results.reduce((e,t)=>e+t.inconsistentItems,0);s>0&&(e.warning("发现 ".concat(s," 个数据一致性问题，正在重新加载...")),await eZ(!0))}catch(e){console.error("数据一致性验证失败:",e)}},[ei]);(0,n.useEffect)(()=>{if(ei.length>0){let e=setTimeout(()=>{eC()},2e3);return()=>clearTimeout(e)}},[ei,eC]);let eD=(0,tA.y1)(()=>ew(),300,[ew]),eM=(0,tA.y1)(()=>ek(!1),300,[ek]);(0,tR.el)("production-orders-page",{onProductionOrderCreated:e=>{console.log("生产订单页面收到新订单创建事件:",e),eD()},onProductionOrderUpdated:e=>{console.log("生产订单页面收到订单更新事件:",e),eD()},onProductionWorkOrderCreated:e=>{console.log("生产订单页面收到新工单创建事件:",e),eM()},onProductionWorkOrderUpdated:e=>{console.log("生产订单页面收到工单更新事件:",e),eM()},onProductionWorkOrderDeleted:e=>{console.log("生产订单页面收到工单删除事件:",e),ek(!1)}}),(0,n.useEffect)(()=>{(async()=>{V(!0);try{await eZ(!0,!0),e.success("数据初始化完成")}catch(t){e.error("数据初始化失败")}finally{V(!1)}})()},[]);let eI=async()=>{V(!0);try{console.log("\uD83D\uDD04 用户手动刷新数据"),await eZ(!0),e.success("数据刷新完成")}catch(t){console.error("数据刷新失败:",t),e.error("数据刷新失败，请稍后重试")}finally{V(!1)}},eN=async(t,r)=>{await (0,e2.Ro)(()=>W.dataAccessManager.productionOrders.update(t,{status:r}),"更新订单状态")?(e.success("订单状态更新成功"),await eZ()):e.error("订单状态更新失败")},eO=async()=>{V(!0);try{await new Promise(e=>setTimeout(e,1e3)),e.success("数据同步完成")}catch(t){e.error("数据同步失败")}finally{V(!1)}},eE=async(t,r)=>{let s=await W.dataAccessManager.productionWorkOrders.getById(t);if("success"!==s.status||!s.data){e.error("获取工单信息失败");return}let n=s.data.status,a=await (0,e2.Ro)(()=>W.dataAccessManager.productionWorkOrders.update(t,{status:r}),"更新工单状态");a&&a.sourceOrderId?(await tU.handleStatusChange({workOrderId:t,oldStatus:n,newStatus:r,sourceOrderId:a.sourceOrderId},{showSuccessMessage:!0,successMessage:"工单状态更新成功"}),await eZ()):a?(e.success("工单状态更新成功"),await eZ()):e.error("工单状态更新失败")},eW=async t=>{await (0,e2.Ro)(()=>W.dataAccessManager.productionWorkOrders.delete(t),"删除工单")?(e.success("工单删除成功"),await eZ()):e.error("工单删除失败")},e_=async t=>{try{ee(!0);let r=q.map(e=>e.id),s=await t_.calculateSchedulingResults(r,{sameMoldDeliveryToleranceDays:t.sameMoldDeliveryToleranceDays});J(s),Q(!1),Y(!0),e.success("排程计算完成！生成 ".concat(s.schedulingResults.length," 个工单的排程预览")),e.info('当前为预览模式，工位状态和工单状态尚未实际更新，请确认后点击"确认应用"')}catch(t){e.error("排程计算失败: ".concat(t instanceof Error?t.message:"未知错误"))}finally{ee(!1)}},eA=async()=>{if(G)try{ee(!0),await t_.applySchedulingResults(G.schedulingResults),e.success("排程结果已完整应用！工单状态和工位状态已同步更新"),e.info('所有工单状态已更新为"已排程"，工位队列任务已同步'),Y(!1),J(null),X([]),await eZ(!0)}catch(t){e.error("应用排程结果失败: ".concat(t instanceof Error?t.message:"未知错误"))}finally{ee(!1)}},eR=async(t,r)=>{try{e.loading("正在批量创建工单...",0),e.info("正在验证订单数据...");let s=[],n=[];for(let e of t)try{let t=await (0,e2.Ro)(()=>W.dataAccessManager.productionOrders.getById(e),"验证生产订单 (".concat(e,")"));if(!t){s.push("订单ID ".concat(e," 不存在"));continue}if(!en.canCreateWorkOrder(t)){let e="planned"===t.status?"已计划":"in_progress"===t.status?"生产中":"completed"===t.status?"已完成":"cancelled"===t.status?"已取消":t.status;s.push("订单 ".concat(t.orderNumber,' 状态为"').concat(e,'"，只能为"计划中"状态的订单创建工单'));continue}if(!t.productCode||!t.productName||!t.plannedQuantity){s.push("订单 ".concat(t.orderNumber," 数据不完整，缺少必要字段"));continue}let r=await W.dataAccessManager.productionWorkOrders.getAll();if("success"===r.status&&r.data){let n=r.data.items.find(t=>t.sourceOrderId===e);if(n){s.push("订单 ".concat(t.orderNumber," 已存在工单 ").concat(n.batchNumber));continue}}n.push(t)}catch(t){s.push("验证订单 ".concat(e," 时发生错误: ").concat(t instanceof Error?t.message:"未知错误"))}if(s.length>0){e.destroy();let t="数据验证失败，无法创建工单：\n".concat(s.join("\n"));e.error(t),console.error("工单创建验证失败:",s);return}if(0===n.length){e.destroy(),e.warning("没有有效的订单可以创建工单");return}e.info("验证通过，开始创建 ".concat(n.length," 个工单..."));let a=n.map(e=>async()=>{let t=await tH(e,r),s=await (0,e2.Ro)(()=>W.dataAccessManager.productionWorkOrders.create(t),"创建生产工单 (".concat(e.orderNumber,")"));if(!s)throw Error("创建工单失败: ".concat(e.orderNumber,"，请检查数据格式"));let n=await tU.handleCreation(e.id,e.status,{silent:!0});return{workOrder:s,order:{...e,status:n.orderStatusChanged?n.newOrderStatus:e.status},orderNumber:e.orderNumber}}),o=n.map(e=>"创建工单-".concat(e.orderNumber)),i=await tV.AO.executeBatch(a,o);e.destroy();let{successful:l,failed:c,successRate:d,totalDuration:u}=i;if(l.length>0&&(e.success("成功创建 ".concat(l.length," 个工单").concat(c.length>0?"，".concat(c.length," 个失败"):""," (耗时: ").concat(u,"ms)")),await eZ()),c.length>0){let t=c.map(e=>"• ".concat(e.taskName||e.name||"未知任务",": ").concat(e.error)).join("\n"),r="".concat(c.length," 个工单创建失败：\n").concat(t);e.error({content:r,duration:8}),console.error("批量创建工单失败详情:",c)}if(0===l.length){let t="没有成功创建任何工单，请检查订单数据或联系系统管理员";throw e.error(t),Error(t)}console.log("批量创建工单完成:",{success:l.length,failed:c.length,successRate:d,totalDuration:u})}catch(r){e.destroy();let t=r instanceof Error?r.message:"批量创建工单失败";throw e.error(t),console.error("批量创建工单错误:",r),r}},ez=ec.filter(e=>"scheduled"===e.status||"in_progress"===e.status||"completed"===e.status),eB=ec.filter(e=>"pending"===e.status),eP={items:[{key:"sync",icon:(0,s.jsx)(f.Z,{}),label:"同步数据",onClick:eO},{key:"config",icon:(0,s.jsx)(y.Z,{}),label:"排单配置",onClick:()=>e.info("排单配置功能开发中")}]};return(0,s.jsx)("div",{style:{padding:0},children:(0,s.jsxs)(o.Z,{spinning:H,children:[(0,s.jsx)(i.Z,{size:"small",style:{marginBottom:"16px"},children:(0,s.jsxs)(l.Z,{justify:"space-between",align:"middle",children:[(0,s.jsx)(c.Z,{children:(0,s.jsx)(d.Z,{size:"large",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{style:{margin:0},children:[(0,s.jsx)(j.Z,{style:{marginRight:"8px"}}),"生产订单管理"]}),(0,s.jsxs)("div",{style:{marginTop:"4px"},children:[(0,s.jsx)(u.Z,{color:"blue",children:"当前策略: 默认智能拆单配置"}),(0,s.jsx)(u.Z,{color:"orange",style:{marginLeft:"8px"},children:"生产订单只能通过MRP流程创建"})]})]})})}),(0,s.jsx)(c.Z,{children:(0,s.jsxs)(d.Z,{children:[(0,s.jsx)(tW,{}),(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(w.Z,{}),onClick:eI,children:"刷新"}),(0,s.jsx)(m.Z,{menu:eP,trigger:["click"],children:(0,s.jsxs)(h.ZP,{children:["更多操作 ",(0,s.jsx)(v.Z,{})]})})]})})]})}),(0,s.jsxs)(l.Z,{gutter:16,style:{marginBottom:"16px"},children:[(0,s.jsx)(c.Z,{xs:24,sm:12,md:6,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"生产订单",value:(null==ef?void 0:ef.totalOrders)||0,prefix:(0,s.jsx)(k.Z,{}),valueStyle:{color:"#1890ff"}})})}),(0,s.jsx)(c.Z,{xs:24,sm:12,md:6,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"待分配任务",value:eB.length,prefix:(0,s.jsx)(S.Z,{}),valueStyle:{color:"#faad14"}})})}),(0,s.jsx)(c.Z,{xs:24,sm:12,md:6,children:(0,s.jsx)(i.Z,{size:"small",children:(0,s.jsx)(x.Z,{title:"工位利用率",value:75,suffix:"%",prefix:(0,s.jsx)(Z.Z,{}),valueStyle:{color:"#722ed1"}})})})]}),(0,s.jsx)(l.Z,{gutter:16,style:{marginBottom:"16px"},children:(0,s.jsx)(c.Z,{xs:24,sm:12,md:8,children:(0,s.jsx)(tK,{metrics:t?{averageResponseTime:t.averageResponseTime,totalRequests:t.totalCalls||0,successfulRequests:t.successCalls||0,errorRate:t.totalCalls>0?t.errorCalls/t.totalCalls:0}:null,cacheStats:D,isMonitoring:T,lastUpdateTime:M,error:I,onClearCache:N,formatMemorySize:E,formatPercentage:_})})}),(0,s.jsx)(i.Z,{extra:(0,s.jsx)(d.Z,{children:(0,s.jsx)(h.ZP,{icon:(0,s.jsx)(Z.Z,{}),onClick:()=>ea(!0),type:"text",size:"small",children:"DataAccessManager监控"})}),children:(0,s.jsx)(g.default,{defaultActiveKey:"pending-orders",size:"large",onChange:eo,activeKey:et,items:[{key:"pending-orders",label:(0,s.jsxs)("span",{children:[(0,s.jsx)(k.Z,{}),"生产订单",((null==ef?void 0:ef.totalOrders)||0)>0&&(0,s.jsx)(p.Z,{count:(null==ef?void 0:ef.totalOrders)||0,style:{marginLeft:"8px"},showZero:!0})]}),children:(0,s.jsx)(ev,{orders:ei,loading:H,onRefresh:eI,onOrderDetail:e=>{L(e),B(!0)},onStatusChange:eN,onCreateWorkOrders:eR,onTabChange:eo,workstations:eu,currentSplitConfig:null})},{key:"production-work-orders",label:(0,s.jsxs)("span",{children:[(0,s.jsx)(b.Z,{}),"生产工单",ec.length>0&&(0,s.jsx)(p.Z,{count:ec.length,style:{marginLeft:"8px"},showZero:!0})]}),children:(0,s.jsx)(tw,{workOrders:ec,loading:H,onRefresh:eI,onStatusChange:eE,onDelete:eW,onExport:()=>{e.info("导出工单数据")},onStartScheduling:t=>{if(0===t.length){e.warning("请先选择要排程的工单");return}let r=ec.filter(e=>t.includes(e.id)&&"pending"===e.status);if(0===r.length){e.warning('所选工单中没有"待开始"状态的工单');return}r.length!==t.length&&e.warning("已过滤掉 ".concat(t.length-r.length,' 个非"待开始"状态的工单')),Q(!0),X(r)}})},{key:"scheduling-board",label:(0,s.jsxs)("span",{children:[(0,s.jsx)(C.Z,{}),"排产看板",ez.length>0&&(0,s.jsx)(p.Z,{count:ez.length,style:{marginLeft:"8px"},showZero:!0})]}),children:(0,s.jsx)(K,{tasks:ez,workstations:eu,onTaskClick:t=>{e.info("查看任务详情: ".concat(t.id||"未知任务"))},loading:H})},{key:"workstation-management",label:(0,s.jsxs)("span",{children:[(0,s.jsx)(y.Z,{}),"工位管理",eu.length>0&&(0,s.jsx)(p.Z,{count:eu.length,style:{marginLeft:"8px"},showZero:!0})]}),children:(0,s.jsx)(tt,{loading:H})},{key:"work-time-management",label:(0,s.jsxs)("span",{children:[(0,s.jsx)(S.Z,{}),"工作时间"]}),children:(0,s.jsx)(tc,{loading:H})}]})}),(0,s.jsx)(eT,{open:z,order:P,onClose:()=>{B(!1),L(null)},onStatusChange:eN}),(0,s.jsx)(tD,{open:F,onCancel:()=>{Q(!1),X([])},onConfirm:e_,selectedWorkOrders:q,loading:$}),(0,s.jsx)(tN,{open:U,onCancel:()=>{e.info("已取消排程应用，工位状态和工单状态保持不变"),Y(!1),J(null),X([])},onConfirm:eA,result:G,loading:$}),es&&(0,s.jsx)(tK,{metrics:t?{averageResponseTime:t.averageResponseTime,totalRequests:t.totalCalls||0,successfulRequests:t.successCalls||0,errorRate:t.totalCalls>0?t.errorCalls/t.totalCalls:0}:null,cacheStats:D,isMonitoring:T,lastUpdateTime:M,error:I,onClearCache:N,formatMemorySize:E,formatPercentage:_})]})})};function t$(){return(0,s.jsx)(a.Z,{children:(0,s.jsx)(tJ,{})})}},29255:function(e,t,r){"use strict";r.d(t,{Fi:function(){return s},ux:function(){return n}});let s={in_plan:{color:"blue",text:"计划中"},planned:{color:"cyan",text:"已计划"},in_progress:{color:"orange",text:"生产中"},completed:{color:"green",text:"已完成"},cancelled:{color:"red",text:"已取消"}},n={pending:{color:"default",text:"待开始",antdColor:"default"},scheduled:{color:"blue",text:"已排程",antdColor:"blue"},in_progress:{color:"processing",text:"进行中",antdColor:"processing"},completed:{color:"success",text:"已完成",antdColor:"success"},paused:{color:"warning",text:"暂停",antdColor:"warning"},cancelled:{color:"error",text:"已取消",antdColor:"error"},exception:{color:"error",text:"异常",antdColor:"error"}}},3587:function(e,t,r){"use strict";r.d(t,{J:function(){return n},r:function(){return a}});var s=r(40518);class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}async updateWorkstation(e,t,r,s){let n=Date.now();try{var a;console.log("\uD83D\uDD04 [WorkstationUpdateService] 开始更新工位 ".concat(e),{source:r.source,operation:r.operation,expectedVersion:s,reason:r.reason});let o=await this.validatePermissions(e,r);if(!o.allowed)return{success:!1,error:"权限不足: ".concat(o.reason)};let i=await this.validateBusinessRules(e,t,r);if(!i.valid)return{success:!1,error:"业务规则验证失败: ".concat(i.reason),warnings:i.warnings};let l=this.getModifiedBy(r),c=await this.workstationService.update(e,t,s,l);if("success"!==c.status){if(c.code&&"VERSION_CONFLICT"===c.code.toString()){let e=c.data;return{success:!1,error:c.message||"版本冲突",conflictInfo:{expectedVersion:s||0,currentVersion:(null==e?void 0:e.currentVersion)||0,lastModifiedBy:"unknown",lastModifiedAt:new Date().toISOString()}}}return{success:!1,error:c.message||"更新失败"}}return c.data&&await this.logUpdateOperation(e,t,r,c.data,n),await this.clearWorkstationCaches(e,r.operation),await this.clearDataAccessManagerCaches(),c.data&&await this.triggerPostUpdateActions(e,t,r,c.data),console.log("✅ [WorkstationUpdateService] 工位 ".concat(e," 更新成功"),{duration:Date.now()-n,newVersion:null===(a=c.data)||void 0===a?void 0:a.version}),{success:!0,workstation:c.data,warnings:i.warnings}}catch(t){return console.error("❌ [WorkstationUpdateService] 工位 ".concat(e," 更新失败:"),t),{success:!1,error:t instanceof Error?t.message:"未知错误"}}}async batchUpdateWorkstations(e,t){console.log("\uD83D\uDD04 [WorkstationUpdateService] 开始批量更新 ".concat(e.length," 个工位"));let r=[],s=0,n=0,a=0,o=0,i=0;for(let l of e){let e=await this.updateWorkstation(l.workstationId,l.updates,t,l.expectedVersion);r.push({workstationId:l.workstationId,result:e}),e.success?s++:(n++,e.conflictInfo?a++:o++),e.warnings&&e.warnings.length>0&&i++}let l={totalCount:e.length,successCount:s,failureCount:n,results:r,summary:{conflicts:a,errors:o,warnings:i}};return console.log("✅ [WorkstationUpdateService] 批量更新完成",l.summary),l}async validatePermissions(e,t){switch(t.source){case"user":if(!t.userId)return{allowed:!1,reason:"用户操作必须提供用户ID"};break;case"system":case"scheduling":case"batch":break;default:return{allowed:!1,reason:"未知的更新来源"}}return{allowed:!0}}async validateBusinessRules(e,t,r){let s=[],n=await this.workstationService.getWorkstationById(e);if("success"!==n.status)return{valid:!1,reason:"无法获取当前工位状态"};let a=n.data;if(!a)return{valid:!1,reason:"工位数据不存在"};if(t.status&&t.status!==a.status){let e=this.validateStatusTransition(a.status,t.status,r);if(!e.valid)return{valid:!1,reason:e.reason};e.warnings&&s.push(...e.warnings)}if(this.hasProductionStateChanges(t)){let e=this.validateProductionStateChanges(a,t,r);if(!e.valid)return{valid:!1,reason:e.reason};e.warnings&&s.push(...e.warnings)}return{valid:!0,warnings:s.length>0?s:void 0}}validateStatusTransition(e,t,r){var s;return(null===(s=({active:["inactive"],inactive:["active"]})[e])||void 0===s?void 0:s.includes(t))?{valid:!0}:{valid:!1,reason:"不允许从状态 ".concat(e," 转换到 ").concat(t)}}hasProductionStateChanges(e){return!(void 0===e.currentMoldNumber&&void 0===e.currentBatchNumber&&void 0===e.batchNumberQueue&&void 0===e.lastEndTime)}validateProductionStateChanges(e,t,r){let s=[];return"scheduling"!==r.source&&"system"!==r.source?{valid:!1,reason:"只有排程系统可以修改工位的生产状态"}:(t.currentMoldNumber&&t.currentBatchNumber&&s.push("请确认模具编号与批次号匹配"),{valid:!0,warnings:s.length>0?s:void 0})}getModifiedBy(e){switch(e.source){case"user":return e.userId||"unknown_user";case"system":return"system";case"scheduling":return"scheduling_service";case"batch":return"batch_operation";default:return"unknown"}}async logUpdateOperation(e,t,r,s,n){console.log("\uD83D\uDCDD [WorkstationUpdateService] 审计日志:",{timestamp:new Date().toISOString(),workstationId:e,operation:r.operation,source:r.source,userId:r.userId,reason:r.reason,updates:Object.keys(t),newVersion:s.version,duration:Date.now()-n,metadata:r.metadata})}async clearWorkstationCaches(e,t){try{let{dataAccessManager:t}=r(32779);t.clearDataTypeCache("workstations",[e]),t.clearServiceCache("WorkstationService")}catch(t){console.error("[WorkstationUpdateService] 清除工位 ".concat(e," 缓存失败:"),t)}}async clearDataAccessManagerCaches(){try{let{dataAccessManager:e}=await Promise.resolve().then(r.bind(r,32779)),t=0;["WorkstationService:*","WorkstationService.*"].forEach(r=>{let s=e.clearServiceCache("WorkstationService");t+=s});let s=e.clearDataTypeCache("workstations");t+=s,console.log("[WorkstationUpdateService] 清理DataAccessManager缓存: ".concat(t," 个条目"))}catch(e){console.error("[WorkstationUpdateService] 清理DataAccessManager缓存失败:",e)}}async triggerPostUpdateActions(e,t,r,s){console.log("\uD83D\uDD14 [WorkstationUpdateService] 触发后置处理: ".concat(e))}constructor(){this.workstationService=s.D.getInstance()}}let a=n.getInstance()},93011:function(e,t,r){"use strict";r.d(t,{ZB:function(){return s}});let s={spacing:{xs:4,sm:8,md:16,lg:24,xl:32,xxl:48},shadows:{sm:"0 1px 2px 0 rgba(0, 0, 0, 0.05)",md:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",lg:"0 10px 15px -3px rgba(0, 0, 0, 0.1)",xl:"0 20px 25px -5px rgba(0, 0, 0, 0.1)"},borderRadius:{sm:4,md:8,lg:12,xl:16},colors:{primary:{50:"#f0f9ff",100:"#e0f2fe",500:"#0ea5e9",600:"#0284c7",700:"#0369a1"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827"},success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"},createSpacing:e=>({padding:s.spacing[e]}),createMargin:e=>({margin:s.spacing[e]}),createShadow:e=>({boxShadow:s.shadows[e]}),createBorderRadius:e=>({borderRadius:s.borderRadius[e]}),cardStyle:{borderRadius:12,boxShadow:"0 4px 6px -1px rgba(0, 0, 0, 0.1)",background:"#ffffff",border:"1px solid #f0f0f0"},buttonStyle:{borderRadius:8,transition:"all 0.2s ease-in-out"},layoutStyle:{minHeight:"100vh",background:"#f9fafb"},sidebarStyle:{background:"#ffffff",boxShadow:"2px 0 8px 0 rgba(29, 35, 41, 0.05)",borderRight:"1px solid #f0f0f0"},headerStyle:{background:"#ffffff",borderBottom:"1px solid #f0f0f0",boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.05)"},breakpoints:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px",xxl:"1536px"},transitions:{fast:"all 0.15s ease-in-out",normal:"all 0.2s ease-in-out",slow:"all 0.3s ease-in-out"}};s.spacing.xs,s.spacing.sm,s.spacing.md,s.spacing.lg,s.spacing.xl,s.spacing.xs,s.spacing.sm,s.spacing.md,s.spacing.lg,s.colors.gray[50],s.colors.gray[100],s.colors.gray[200],s.colors.gray[200],s.shadows.sm,s.shadows.md,s.shadows.lg,s.borderRadius.sm,s.borderRadius.md,s.borderRadius.lg,s.transitions.normal}},function(e){e.O(0,[9444,8454,6254,7661,7435,9511,5117,364,574,6245,128,2217,9198,9992,9427,1157,4863,7416,8236,30,9617,2897,5424,3656,3077,2779,1865,2971,4938,1744],function(){return e(e.s=30315)}),_N_E=e.O()}]);