(()=>{var e={};e.id=6475,e.ids=[6475],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},92462:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(50482),n=s(69108),i=s(62563),a=s.n(i),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["production",{children:["hot-press-board",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94931)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\hot-press-board\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,18223)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\hot-press-board\\page.tsx"],m="/production/hot-press-board/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/production/hot-press-board/page",pathname:"/production/hot-press-board",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},68483:(e,t,s)=>{Promise.resolve().then(s.bind(s,72295))},3745:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var r=s(65651),n=s(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"};var a=s(49809);let l=n.forwardRef(function(e,t){return n.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:i}))})},35329:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var r=s(65651),n=s(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"}}]},name:"clock-circle",theme:"outlined"};var a=s(49809);let l=n.forwardRef(function(e,t){return n.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:i}))})},98507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var r=s(65651),n=s(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"};var a=s(49809);let l=n.forwardRef(function(e,t){return n.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:i}))})},20316:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var r=s(65651),n=s(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M136 384h56c4.4 0 8-3.6 8-8V200h176c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H196c-37.6 0-68 30.4-68 68v180c0 4.4 3.6 8 8 8zm512-184h176v176c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V196c0-37.6-30.4-68-68-68H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zM376 824H200V648c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v180c0 37.6 30.4 68 68 68h180c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm512-184h-56c-4.4 0-8 3.6-8 8v176H648c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h180c37.6 0 68-30.4 68-68V648c0-4.4-3.6-8-8-8zm16-164H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8z"}}]},name:"scan",theme:"outlined"};var a=s(49809);let l=n.forwardRef(function(e,t){return n.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:i}))})},12391:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var r=s(65651),n=s(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"};var a=s(49809);let l=n.forwardRef(function(e,t){return n.createElement(a.Z,(0,r.Z)({},e,{ref:t,icon:i}))})},14223:(e,t,s)=>{"use strict";s.d(t,{Z:()=>A});var r=s(3729),n=s(33795),i=s(57629),a=s(32066),l=s(2523),o=s(29513),c=s(34132),d=s.n(c),m=s(27335),u=s(7305),p=s(67862),x=s(29545),h=s(84893),g=s(92959),f=s(22989),v=s(13165);let y=(e,t,s,r,n)=>({background:e,border:`${(0,g.bf)(r.lineWidth)} ${r.lineType} ${t}`,[`${n}-icon`]:{color:s}}),j=e=>{let{componentCls:t,motionDurationSlow:s,marginXS:r,marginSM:n,fontSize:i,fontSizeLG:a,lineHeight:l,borderRadiusLG:o,motionEaseInOutCirc:c,withDescriptionIconSize:d,colorText:m,colorTextHeading:u,withDescriptionPadding:p,defaultPadding:x}=e;return{[t]:Object.assign(Object.assign({},(0,f.Wf)(e)),{position:"relative",display:"flex",alignItems:"center",padding:x,wordWrap:"break-word",borderRadius:o,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:r,lineHeight:0},"&-description":{display:"none",fontSize:i,lineHeight:l},"&-message":{color:u},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${s} ${c}, opacity ${s} ${c},
        padding-top ${s} ${c}, padding-bottom ${s} ${c},
        margin-bottom ${s} ${c}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:p,[`${t}-icon`]:{marginInlineEnd:n,fontSize:d,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:r,color:u,fontSize:a},[`${t}-description`]:{display:"block",color:m}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},b=e=>{let{componentCls:t,colorSuccess:s,colorSuccessBorder:r,colorSuccessBg:n,colorWarning:i,colorWarningBorder:a,colorWarningBg:l,colorError:o,colorErrorBorder:c,colorErrorBg:d,colorInfo:m,colorInfoBorder:u,colorInfoBg:p}=e;return{[t]:{"&-success":y(n,r,s,e,t),"&-info":y(p,u,m,e,t),"&-warning":y(l,a,i,e,t),"&-error":Object.assign(Object.assign({},y(d,c,o,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},Z=e=>{let{componentCls:t,iconCls:s,motionDurationMid:r,marginXS:n,fontSizeIcon:i,colorIcon:a,colorIconHover:l}=e;return{[t]:{"&-action":{marginInlineStart:n},[`${t}-close-icon`]:{marginInlineStart:n,padding:0,overflow:"hidden",fontSize:i,lineHeight:(0,g.bf)(i),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${s}-close`]:{color:a,transition:`color ${r}`,"&:hover":{color:l}}},"&-close-text":{color:a,transition:`color ${r}`,"&:hover":{color:l}}}}},N=(0,v.I$)("Alert",e=>[j(e),b(e),Z(e)],e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}));var w=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)0>t.indexOf(r[n])&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(s[r[n]]=e[r[n]]);return s};let C={success:n.Z,info:o.Z,error:i.Z,warning:l.Z},E=e=>{let{icon:t,prefixCls:s,type:n}=e,i=C[n]||null;return t?(0,x.wm)(t,r.createElement("span",{className:`${s}-icon`},t),()=>({className:d()(`${s}-icon`,t.props.className)})):r.createElement(i,{className:`${s}-icon`})},k=e=>{let{isClosable:t,prefixCls:s,closeIcon:n,handleClose:i,ariaProps:l}=e,o=!0===n||void 0===n?r.createElement(a.Z,null):n;return t?r.createElement("button",Object.assign({type:"button",onClick:i,className:`${s}-close-icon`,tabIndex:0},l),o):null},$=r.forwardRef((e,t)=>{let{description:s,prefixCls:n,message:i,banner:a,className:l,rootClassName:o,style:c,onMouseEnter:x,onMouseLeave:g,onClick:f,afterClose:v,showIcon:y,closable:j,closeText:b,closeIcon:Z,action:C,id:$}=e,S=w(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[P,I]=r.useState(!1),M=r.useRef(null);r.useImperativeHandle(t,()=>({nativeElement:M.current}));let{getPrefixCls:T,direction:D,closable:O,closeIcon:A,className:_,style:z}=(0,h.dj)("alert"),q=T("alert",n),[B,H,V]=N(q),R=t=>{var s;I(!0),null===(s=e.onClose)||void 0===s||s.call(e,t)},L=r.useMemo(()=>void 0!==e.type?e.type:a?"warning":"info",[e.type,a]),Q=r.useMemo(()=>"object"==typeof j&&!!j.closeIcon||!!b||("boolean"==typeof j?j:!1!==Z&&null!=Z||!!O),[b,Z,j,O]),W=!!a&&void 0===y||y,G=d()(q,`${q}-${L}`,{[`${q}-with-description`]:!!s,[`${q}-no-icon`]:!W,[`${q}-banner`]:!!a,[`${q}-rtl`]:"rtl"===D},_,l,o,V,H),U=(0,u.Z)(S,{aria:!0,data:!0}),X=r.useMemo(()=>"object"==typeof j&&j.closeIcon?j.closeIcon:b||(void 0!==Z?Z:"object"==typeof O&&O.closeIcon?O.closeIcon:A),[Z,j,b,A]),F=r.useMemo(()=>{let e=null!=j?j:O;if("object"==typeof e){let{closeIcon:t}=e;return w(e,["closeIcon"])}return{}},[j,O]);return B(r.createElement(m.ZP,{visible:!P,motionName:`${q}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:e=>({maxHeight:e.offsetHeight}),onLeaveEnd:v},({className:t,style:n},a)=>r.createElement("div",Object.assign({id:$,ref:(0,p.sQ)(M,a),"data-show":!P,className:d()(G,t),style:Object.assign(Object.assign(Object.assign({},z),c),n),onMouseEnter:x,onMouseLeave:g,onClick:f,role:"alert"},U),W?r.createElement(E,{description:s,icon:e.icon,prefixCls:q,type:L}):null,r.createElement("div",{className:`${q}-content`},i?r.createElement("div",{className:`${q}-message`},i):null,s?r.createElement("div",{className:`${q}-description`},s):null),C?r.createElement("div",{className:`${q}-action`},C):null,r.createElement(k,{isClosable:Q,prefixCls:q,closeIcon:X,handleClose:R,ariaProps:F}))))});var S=s(31475),P=s(24142),I=s(61792),M=s(50804),T=s(6392),D=s(94977);let O=function(e){function t(){var e,s,r;return(0,S.Z)(this,t),s=t,r=arguments,s=(0,I.Z)(s),(e=(0,T.Z)(this,(0,M.Z)()?Reflect.construct(s,r||[],(0,I.Z)(this).constructor):s.apply(this,r))).state={error:void 0,info:{componentStack:""}},e}return(0,D.Z)(t,e),(0,P.Z)(t,[{key:"componentDidCatch",value:function(e,t){this.setState({error:e,info:t})}},{key:"render",value:function(){let{message:e,description:t,id:s,children:n}=this.props,{error:i,info:a}=this.state,l=(null==a?void 0:a.componentStack)||null,o=void 0===e?(i||"").toString():e;return i?r.createElement($,{id:s,type:"error",message:o,description:r.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},void 0===t?l:t)}):n}}])}(r.Component);$.ErrorBoundary=O;let A=$},16408:(e,t,s)=>{"use strict";s.d(t,{Z:()=>b});var r=s(35864),n=s(4176),i=s(51966),a=s(3729),l=s(34132),o=s.n(l),c=s(74393),d=s(9286),m=s(84893),u=s(13878),p=s(59604),x=s(93142),h=s(59239),g=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,r=Object.getOwnPropertySymbols(e);n<r.length;n++)0>t.indexOf(r[n])&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(s[r[n]]=e[r[n]]);return s};let f=(0,d.i)(e=>{let{prefixCls:t,className:s,closeIcon:r,closable:n,type:i,title:l,children:d,footer:f}=e,v=g(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:y}=a.useContext(m.E_),j=y(),b=t||y("modal"),Z=(0,u.Z)(j),[N,w,C]=(0,h.ZP)(b,Z),E=`${b}-confirm`,k={};return k=i?{closable:null!=n&&n,title:"",footer:"",children:a.createElement(p.O,Object.assign({},e,{prefixCls:b,confirmPrefixCls:E,rootPrefixCls:j,content:d}))}:{closable:null==n||n,title:l,footer:null!==f&&a.createElement(x.$,Object.assign({},e)),children:d},N(a.createElement(c.s,Object.assign({prefixCls:b,className:o()(w,`${b}-pure-panel`,i&&E,i&&`${E}-${i}`,s,C,Z)},v,{closeIcon:(0,x.b)(b,r),closable:n},k)))});var v=s(4531);function y(e){return(0,r.ZP)((0,r.uW)(e))}let j=i.Z;j.useModal=v.Z,j.info=function(e){return(0,r.ZP)((0,r.cw)(e))},j.success=function(e){return(0,r.ZP)((0,r.vq)(e))},j.error=function(e){return(0,r.ZP)((0,r.AQ)(e))},j.warning=y,j.warn=y,j.confirm=function(e){return(0,r.ZP)((0,r.Au)(e))},j.destroyAll=function(){for(;n.Z.length;){let e=n.Z.pop();e&&e()}},j.config=r.ai,j._InternalPanelDoNotUseOrYouWillBeFired=f;let b=j},72295:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>H});var r=s(95344),n=s(3729),i=s(97854),a=s(284),l=s(32979),o=s(87049),c=s(90377),d=s(39470),m=s(10707),u=s(11157),p=s(63724),x=s(27976),h=s(83984),g=s(43896),f=s(14223),v=s(36527),y=s(45850),j=s(52788),b=s(90185),Z=s(16408),N=s(35329),w=s(98507),C=s(3745),E=s(27224),k=s(27385),$=s(32535),S=s(44670),P=s(12391),I=s(65651);let M={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M632 888H392c-4.4 0-8 3.6-8 8v32c0 17.7 14.3 32 32 32h192c17.7 0 32-14.3 32-32v-32c0-4.4-3.6-8-8-8zM512 64c-181.1 0-328 146.9-328 328 0 121.4 66 227.4 164 284.1V792c0 17.7 14.3 32 32 32h264c17.7 0 32-14.3 32-32V676.1c98-56.7 164-162.7 164-284.1 0-181.1-146.9-328-328-328zm127.9 549.8L604 634.6V752H420V634.6l-35.9-20.8C305.4 568.3 256 484.5 256 392c0-141.4 114.6-256 256-256s256 114.6 256 256c0 92.5-49.4 176.3-128.1 221.8z"}}]},name:"bulb",theme:"outlined"};var T=s(49809),D=n.forwardRef(function(e,t){return n.createElement(T.Z,(0,I.Z)({},e,{ref:t,icon:M}))}),O=s(33537),A=s(20316);class _{static sortTasksByPriority(e){return[...e].sort((e,t)=>new Date(e.deliveryDate).getTime()-new Date(t.deliveryDate).getTime())}static validateEmployeeBinding(e,t,s){return s.find(s=>s.employeeId===e&&s.taskId===t&&s.isActive)?{isValid:!1,message:"该员工已绑定此任务",warningType:"duplicate"}:s.filter(e=>e.taskId===t&&e.isActive).length>=3?{isValid:!1,message:"该任务已达到最大员工绑定数量（3名）",warningType:"max_employees"}:s.filter(s=>s.employeeId===e&&s.isActive&&s.taskId!==t).length>0?{isValid:!0,message:"警告：该员工同时绑定了其他批次任务",warningType:"cross_batch"}:{isValid:!0,message:"绑定验证通过"}}static createEmployeeBinding(e,t,s,r,n,i,a="qr_code"){return{id:`binding-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,employeeId:e,employeeName:t,employeeCode:s,taskId:r,batchNumber:n,bindingTime:new Date().toISOString(),scanMethod:a,bindingMolds:i,isActive:!0,createdAt:new Date().toISOString()}}static calculateBasicStatistics(e,t){let s=new Date().toISOString().split("T")[0],r=e.length,n=e.filter(e=>"completed"===e.status).length,i=t.filter(e=>e.isActive&&e.bindingTime.startsWith(s));return{totalTasks:r,completedTasks:n,activeEmployees:new Set(i.map(e=>e.employeeId)).size,completionRate:Math.round(100*(r>0?n/r*100:0))/100}}static performBindingErrorCheck(e,t,s){return!e||e.length<3?{hasError:!0,errorType:"invalid_employee",errorMessage:"无效的员工工牌号",suggestions:["请检查工牌号是否正确","确认工牌是否可读"]}:s.find(s=>s.employeeCode===e&&s.taskId===t&&s.isActive)?{hasError:!0,errorType:"duplicate_binding",errorMessage:"该员工已绑定此批次",suggestions:["如需重新绑定，请先解除原绑定","确认是否为其他员工操作"]}:s.filter(e=>e.taskId===t&&e.isActive).length>=3?{hasError:!0,errorType:"max_binding_exceeded",errorMessage:"该批次绑定员工已达上限（3名）",suggestions:["请选择其他批次","或等待当前员工完成后解绑"]}:{hasError:!1}}static getPriorityTasks(e){let t=e.filter(e=>"waiting"===e.status);return this.sortTasksByPriority(t).slice(0,5)}}let{Option:z}=i.default,{Search:q}=a.default,B=()=>{let{message:e,modal:t}=l.Z.useApp(),[s,i]=(0,n.useState)(!1),[I,M]=(0,n.useState)(!1),[T,z]=(0,n.useState)(null),[q,B]=(0,n.useState)(""),[H,V]=(0,n.useState)(0),[R,L]=(0,n.useState)([{id:"task-001",taskNumber:"HP-2024-001",productModelCode:"CP-202",productName:"精密机械组件A",batchNumber:"BATCH-001",formingCompletionRate:85.5,hotPressQuantity:1200,remainingQuantity:800,deliveryDate:"2024-01-20",status:"waiting",assignedEmployees:[],createdAt:"2024-01-15T08:00:00Z",updatedAt:"2024-01-15T08:00:00Z"},{id:"task-002",taskNumber:"HP-2024-002",productModelCode:"CP-305",productName:"电子控制器B",batchNumber:"BATCH-002",formingCompletionRate:92,hotPressQuantity:800,remainingQuantity:600,deliveryDate:"2024-01-18",status:"in_progress",assignedEmployees:["EMP001","EMP002"],startTime:"2024-01-15T09:00:00Z",createdAt:"2024-01-15T09:00:00Z",updatedAt:"2024-01-15T09:00:00Z"}]),[Q,W]=(0,n.useState)([{id:"binding-001",employeeId:"EMP001",employeeName:"王师傅",employeeCode:"W001",taskId:"task-002",batchNumber:"BATCH-002",bindingTime:"2024-01-15T09:00:00Z",scanMethod:"qr_code",bindingMolds:400,isActive:!0,createdAt:"2024-01-15T09:00:00Z"},{id:"binding-002",employeeId:"EMP002",employeeName:"李师傅",employeeCode:"L002",taskId:"task-002",batchNumber:"BATCH-002",bindingTime:"2024-01-15T10:00:00Z",scanMethod:"qr_code",bindingMolds:400,isActive:!0,createdAt:"2024-01-15T10:00:00Z"}]),[G]=(0,n.useState)([{id:"eq-001",equipmentCode:"HP-001",equipmentName:"热压机A",equipmentType:"hot_press_machine",status:"running",currentLoad:80,maxCapacity:1e3,efficiency:95,lastMaintenanceDate:"2024-01-01",nextMaintenanceDate:"2024-02-01",operatorId:"EMP001",operatorName:"王师傅"},{id:"eq-002",equipmentCode:"HP-002",equipmentName:"热压机B",equipmentType:"hot_press_machine",status:"running",currentLoad:60,maxCapacity:1200,efficiency:88,lastMaintenanceDate:"2024-01-05",nextMaintenanceDate:"2024-02-05"}]),U=_.calculateBasicStatistics(R,Q),X=_.getPriorityTasks(R),F=async()=>{if(T&&q){i(!0);try{let e=_.performBindingErrorCheck(q,T.id,Q);if(e.hasError){t.error({title:"绑定失败",content:e.errorMessage,footer:(0,r.jsxs)("div",{children:[r.jsx("p",{children:"建议："}),r.jsx("ul",{children:e.suggestions?.map((e,t)=>r.jsxs("li",{children:["• ",e]},t))})]})});return}let s=_.validateEmployeeBinding(q,T.id,Q);if(!s.isValid){t.error({title:"绑定验证失败",content:s.message});return}let n=_.createEmployeeBinding(q,`员工${q}`,q,T.id,T.batchNumber,H||T.remainingQuantity);W(e=>[...e,n]),L(e=>e.map(e=>{if(e.id===T.id){let t={...e};return t.assignedEmployees.includes(q)||t.assignedEmployees.push(q),"waiting"===t.status&&(t.status="in_progress",t.startTime=new Date().toISOString()),t.updatedAt=new Date().toISOString(),t}return e})),M(!1),B(""),V(0),z(null)}catch(e){t.error({title:"绑定失败",content:"系统错误，请重试"})}finally{i(!1)}}},K=e=>{W(t=>t.map(t=>t.id===e?{...t,isActive:!1,unbindingTime:new Date().toISOString()}:t))};return(0,r.jsxs)("div",{className:"space-y-6",children:[r.jsx("div",{className:"page-header",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx($.Z,{className:"text-2xl text-orange-600 mr-3"}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"page-title",children:"热压机生产看板"}),r.jsx("p",{className:"page-description",children:"热压机生产任务看板和员工绑定系统"})]})]}),r.jsx(u.ZP,{icon:r.jsx(S.Z,{}),onClick:()=>window.location.reload(),children:"刷新看板"})]})}),(0,r.jsxs)(p.Z,{gutter:[16,16],children:[r.jsx(x.Z,{xs:24,sm:6,children:r.jsx(h.Z,{children:r.jsx(g.Z,{title:"任务完成率",value:U.completionRate,suffix:"%",valueStyle:{color:"#52c41a"},prefix:r.jsx(P.Z,{})})})}),r.jsx(x.Z,{xs:24,sm:6,children:r.jsx(h.Z,{children:r.jsx(g.Z,{title:"待处理任务",value:U.totalTasks-U.completedTasks,suffix:"个",valueStyle:{color:"#1890ff"},prefix:r.jsx(N.Z,{})})})}),r.jsx(x.Z,{xs:24,sm:6,children:r.jsx(h.Z,{children:r.jsx(g.Z,{title:"已完成任务",value:U.completedTasks,suffix:"个",valueStyle:{color:"#52c41a"},prefix:r.jsx($.Z,{})})})}),r.jsx(x.Z,{xs:24,sm:6,children:r.jsx(h.Z,{children:r.jsx(g.Z,{title:"活跃员工",value:U.activeEmployees,suffix:"人",valueStyle:{color:"#722ed1"},prefix:r.jsx(E.Z,{})})})})]}),X.length>0&&r.jsx(p.Z,{gutter:[16,16],children:r.jsx(x.Z,{xs:24,children:r.jsx(f.Z,{message:"优先任务提醒",description:(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{children:["当前有 ",X.length," 个优先任务需要处理："]}),(0,r.jsxs)("ul",{className:"mb-0",children:[X.slice(0,3).map((e,t)=>(0,r.jsxs)("li",{children:["• ",e.batchNumber," - ",e.productName," (交货日期: ",new Date(e.deliveryDate).toLocaleDateString(),")"]},t)),X.length>3&&(0,r.jsxs)("li",{children:["• 还有 ",X.length-3," 个任务..."]})]})]}),type:"info",showIcon:!0,icon:r.jsx(D,{})})})}),r.jsx(h.Z,{title:"设备状态",children:r.jsx(p.Z,{gutter:[16,16],children:G.map(e=>r.jsx(x.Z,{xs:24,sm:12,lg:8,children:r.jsx(h.Z,{size:"small",className:"border-l-4 border-l-blue-500",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h4",{className:"font-medium",children:e.equipmentName}),r.jsx(c.Z,{color:"running"===e.status?"green":"idle"===e.status?"default":"maintenance"===e.status?"orange":"red",children:"running"===e.status?"运行中":"idle"===e.status?"空闲":"maintenance"===e.status?"维护中":"故障"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsxs)("div",{children:["负荷: ",e.currentLoad,"%"]}),(0,r.jsxs)("div",{children:["效率: ",e.efficiency,"%"]}),(0,r.jsxs)("div",{children:["产能: ",e.maxCapacity," 模/天"]})]}),e.operatorName&&(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["操作员: ",e.operatorName]})]})})},e.id))})}),r.jsx(h.Z,{title:"生产任务看板",extra:r.jsx("div",{className:"text-sm text-gray-500",children:"按交货日期排序 | 自动更新"}),children:r.jsx(v.Z,{columns:[{title:"任务信息",key:"taskInfo",width:200,render:(e,t)=>(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:t.taskNumber}),r.jsx("div",{className:"text-sm text-gray-500",children:t.productName}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:["批次: ",t.batchNumber]})]})},{title:"成型完成率",dataIndex:"formingCompletionRate",key:"formingCompletionRate",width:120,render:e=>r.jsx("div",{children:r.jsx(o.Z,{percent:e,size:"small",format:()=>`${e}%`,strokeColor:e>=90?"#52c41a":e>=70?"#faad14":"#ff4d4f"})}),sorter:(e,t)=>t.formingCompletionRate-e.formingCompletionRate},{title:"数量信息",key:"quantity",width:120,render:(e,t)=>(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("div",{children:["已热压: ",t.hotPressQuantity]}),(0,r.jsxs)("div",{children:["剩余: ",t.remainingQuantity]})]})},{title:"交货日期",dataIndex:"deliveryDate",key:"deliveryDate",width:100,render:e=>{let t=new Date(e),s=new Date,n=Math.ceil((t.getTime()-s.getTime())/864e5);return(0,r.jsxs)("div",{children:[r.jsx("div",{children:e}),r.jsx("div",{className:`text-xs ${n<=1?"text-red-500":n<=3?"text-orange-500":"text-gray-500"}`,children:n<=0?"已逾期":`${n}天后`})]})},sorter:(e,t)=>new Date(e.deliveryDate).getTime()-new Date(t.deliveryDate).getTime()},{title:"状态",dataIndex:"status",key:"status",width:100,render:e=>{let t={waiting:{color:"default",text:"等待中",icon:r.jsx(N.Z,{})},in_progress:{color:"blue",text:"进行中",icon:r.jsx(w.Z,{})},completed:{color:"green",text:"已完成",icon:r.jsx(C.Z,{})}}[e];return r.jsx(c.Z,{color:t.color,icon:t.icon,children:t.text})}},{title:"绑定员工",dataIndex:"assignedEmployees",key:"assignedEmployees",width:120,render:e=>(0,r.jsxs)("div",{children:[r.jsx(d.Z,{count:e.length,showZero:!0,children:r.jsx(E.Z,{className:"text-lg"})}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[e.length,"/3 人"]})]})},{title:"操作",key:"action",width:120,render:(e,t)=>r.jsx(m.Z,{size:"small",children:r.jsx(u.ZP,{type:"primary",size:"small",icon:r.jsx(k.Z,{}),onClick:()=>{z(t),M(!0)},disabled:t.assignedEmployees.length>=3,children:"绑定"})})}],dataSource:_.sortTasksByPriority(R),rowKey:"id",pagination:{pageSize:10},scroll:{x:1e3},rowClassName:e=>{let t=new Date,s=Math.ceil((new Date(e.deliveryDate).getTime()-t.getTime())/864e5);return s<=3?"bg-red-50":s<=7?"bg-orange-50":""}})}),r.jsx(h.Z,{title:"员工绑定记录",children:r.jsx(y.Z,{dataSource:Q.filter(e=>e.isActive),renderItem:e=>{let t=R.find(t=>t.id===e.taskId);return r.jsx(y.Z.Item,{actions:[r.jsx(j.Z,{title:"确认解除绑定？",onConfirm:()=>K(e.id),okText:"确认",cancelText:"取消",children:r.jsx(u.ZP,{type:"text",danger:!0,size:"small",icon:r.jsx(O.Z,{}),children:"解绑"})})],children:r.jsx(y.Z.Item.Meta,{avatar:r.jsx(b.Z,{icon:r.jsx(k.Z,{})}),title:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{children:e.employeeName}),r.jsx(c.Z,{children:e.employeeCode}),r.jsx(d.Z,{status:"qr_code"===e.scanMethod?"success":"default",text:"qr_code"===e.scanMethod?"扫码绑定":"手动绑定"})]}),description:(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsxs)("div",{children:["任务: ",t?.taskNumber," | 批次: ",e.batchNumber]}),(0,r.jsxs)("div",{children:["绑定模数: ",e.bindingMolds," | 时间: ",new Date(e.bindingTime).toLocaleString()]})]})})})}})}),r.jsx(Z.Z,{title:"员工绑定",open:I,onCancel:()=>M(!1),onOk:F,confirmLoading:s,width:600,children:T&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(f.Z,{message:"员工绑定确认",description:"请扫描员工工牌或手动输入工牌号进行绑定",type:"info",showIcon:!0}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[r.jsx("h4",{className:"font-medium mb-2",children:"任务信息"}),(0,r.jsxs)(p.Z,{gutter:16,children:[(0,r.jsxs)(x.Z,{span:12,children:[(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"任务号:"})," ",T.taskNumber]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"产品:"})," ",T.productName]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"批次:"})," ",T.batchNumber]})]}),(0,r.jsxs)(x.Z,{span:12,children:[(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"剩余数量:"})," ",T.remainingQuantity," 模"]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"交货日期:"})," ",T.deliveryDate]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"已绑定员工:"})," ",T.assignedEmployees.length,"/3 人"]})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium mb-2",children:"员工工牌"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx(a.default,{value:q,onChange:e=>B(e.target.value),placeholder:"请输入或扫描员工工牌号",className:"flex-1"}),r.jsx(u.ZP,{icon:r.jsx(A.Z,{}),onClick:()=>{let e=["W001","L002","Z003","S004"];B(e[Math.floor(Math.random()*e.length)])},type:"primary",children:"扫码"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium mb-2",children:"绑定模数"}),r.jsx(a.default,{type:"number",value:H,onChange:e=>V(Number(e.target.value)),placeholder:"请输入绑定模数（可选，默认为剩余数量）",addonAfter:"模"}),(0,r.jsxs)("div",{className:"mt-1 text-sm text-gray-500",children:["留空将自动设置为剩余数量 (",T.remainingQuantity," 模)"]})]}),q&&(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded",children:[r.jsx("h4",{className:"font-medium mb-2 text-blue-800",children:"绑定预览"}),(0,r.jsxs)("div",{className:"text-sm text-blue-700",children:[(0,r.jsxs)("p",{children:["员工工牌: ",q]}),(0,r.jsxs)("p",{children:["绑定模数: ",H||T.remainingQuantity," 模"]}),(0,r.jsxs)("p",{children:["绑定时间: ",new Date().toLocaleString()]})]})]}),T.assignedEmployees.length>0&&(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium mb-2",children:"当前绑定员工"}),r.jsx("div",{className:"space-y-2",children:Q.filter(e=>e.taskId===T.id&&e.isActive).map(e=>(0,r.jsxs)("div",{className:"flex justify-between items-center p-2 bg-gray-50 rounded",children:[(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium",children:e.employeeName}),(0,r.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:["(",e.employeeCode,")"]})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[e.bindingMolds," 模"]})]},e.id))})]})]})})]})},H=()=>r.jsx(l.Z,{children:r.jsx(B,{})})},94931:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>n,default:()=>a});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\production\hot-press-board\page.tsx`),{__esModule:n,$$typeof:i}=r,a=r.default},18223:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(25036),n=s(38834);function i({children:e}){return r.jsx(n.Z,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,284,7049,4441,1248,6274,996,6133],()=>s(92462));module.exports=r})();