"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7661],{29810:function(t,e,o){o.d(e,{o2:function(){return i},yT:function(){return c}});var a=o(16141),n=o(95599);let r=n.i.map(t=>"".concat(t,"-inverse")),l=["success","processing","error","default","warning"];function i(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return e?[].concat((0,a.Z)(r),(0,a.Z)(n.i)).includes(t):n.i.includes(t)}function c(t){return l.includes(t)}},67966:function(t,e,o){o.d(e,{Z:function(){return i}});var a=o(89869);let n={left:{points:["cr","cl"]},right:{points:["cl","cr"]},top:{points:["bc","tc"]},bottom:{points:["tc","bc"]},topLeft:{points:["bl","tl"]},leftTop:{points:["tr","tl"]},topRight:{points:["br","tr"]},rightTop:{points:["tl","tr"]},bottomRight:{points:["tr","br"]},rightBottom:{points:["bl","br"]},bottomLeft:{points:["tl","bl"]},leftBottom:{points:["br","bl"]}},r={topLeft:{points:["bl","tc"]},leftTop:{points:["tr","cl"]},topRight:{points:["br","tc"]},rightTop:{points:["tl","cr"]},bottomRight:{points:["tr","bc"]},rightBottom:{points:["bl","cr"]},bottomLeft:{points:["tl","bc"]},leftBottom:{points:["br","cl"]}},l=new Set(["topLeft","topRight","bottomLeft","bottomRight","leftTop","leftBottom","rightTop","rightBottom"]);function i(t){let{arrowWidth:e,autoAdjustOverflow:o,arrowPointAtCenter:i,offset:c,borderRadius:s,visibleFirst:p}=t,f=e/2,u={};return Object.keys(n).forEach(t=>{let m=Object.assign(Object.assign({},i&&r[t]||n[t]),{offset:[0,0],dynamicInset:!0});switch(u[t]=m,l.has(t)&&(m.autoArrow=!1),t){case"top":case"topLeft":case"topRight":m.offset[1]=-f-c;break;case"bottom":case"bottomLeft":case"bottomRight":m.offset[1]=f+c;break;case"left":case"leftTop":case"leftBottom":m.offset[0]=-f-c;break;case"right":case"rightTop":case"rightBottom":m.offset[0]=f+c}let d=(0,a.wZ)({contentRadius:s,limitVerticalRadius:!0});if(i)switch(t){case"topLeft":case"bottomLeft":m.offset[0]=-d.arrowOffsetHorizontal-f;break;case"topRight":case"bottomRight":m.offset[0]=d.arrowOffsetHorizontal+f;break;case"leftTop":case"rightTop":m.offset[1]=-(2*d.arrowOffsetHorizontal)+f;break;case"leftBottom":case"rightBottom":m.offset[1]=2*d.arrowOffsetHorizontal-f}m.overflow=function(t,e,o,a){if(!1===a)return{adjustX:!1,adjustY:!1};let n={};switch(t){case"top":case"bottom":n.shiftX=2*e.arrowOffsetHorizontal+o,n.shiftY=!0,n.adjustY=!0;break;case"left":case"right":n.shiftY=2*e.arrowOffsetVertical+o,n.shiftX=!0,n.adjustX=!0}let r=Object.assign(Object.assign({},n),a&&"object"==typeof a?a:{});return r.shiftX||(r.adjustX=!0),r.shiftY||(r.adjustY=!0),r}(t,d,e,o),p&&(m.htmlRegion="visibleFirst")}),u}},65270:function(t,e,o){o.d(e,{Z:function(){return v}});var a=o(2265),n=o(42744),r=o.n(n),l=o(79173);function i(t){return["small","middle","large"].includes(t)}function c(t){return!!t&&"number"==typeof t&&!Number.isNaN(t)}var s=o(57499),p=o(92801);let f=a.createContext({latestIndex:0}),u=f.Provider;var m=t=>{let{className:e,index:o,children:n,split:r,style:l}=t,{latestIndex:i}=a.useContext(f);return null==n?null:a.createElement(a.Fragment,null,a.createElement("div",{className:e,style:l},n),o<i&&r&&a.createElement("span",{className:"".concat(e,"-split")},r))},d=o(86682),b=function(t,e){var o={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(o[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(t);n<a.length;n++)0>e.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(t,a[n])&&(o[a[n]]=t[a[n]]);return o};let g=a.forwardRef((t,e)=>{var o;let{getPrefixCls:n,direction:p,size:f,className:g,style:v,classNames:h,styles:y}=(0,s.dj)("space"),{size:w=null!=f?f:"small",align:O,className:j,rootClassName:C,children:N,direction:k="horizontal",prefixCls:x,split:_,style:E,wrap:R=!1,classNames:P,styles:Z}=t,T=b(t,["size","align","className","rootClassName","children","direction","prefixCls","split","style","wrap","classNames","styles"]),[z,I]=Array.isArray(w)?w:[w,w],L=i(I),S=i(z),A=c(I),B=c(z),V=(0,l.Z)(N,{keepEmpty:!0}),X=void 0===O&&"horizontal"===k?"center":O,M=n("space",x),[Y,q,D]=(0,d.Z)(M),H=r()(M,g,q,"".concat(M,"-").concat(k),{["".concat(M,"-rtl")]:"rtl"===p,["".concat(M,"-align-").concat(X)]:X,["".concat(M,"-gap-row-").concat(I)]:L,["".concat(M,"-gap-col-").concat(z)]:S},j,C,D),W=r()("".concat(M,"-item"),null!==(o=null==P?void 0:P.item)&&void 0!==o?o:h.item),F=0,G=V.map((t,e)=>{var o;null!=t&&(F=e);let n=(null==t?void 0:t.key)||"".concat(W,"-").concat(e);return a.createElement(m,{className:W,key:n,index:e,split:_,style:null!==(o=null==Z?void 0:Z.item)&&void 0!==o?o:y.item},t)}),U=a.useMemo(()=>({latestIndex:F}),[F]);if(0===V.length)return null;let $={};return R&&($.flexWrap="wrap"),!S&&B&&($.columnGap=z),!L&&A&&($.rowGap=I),Y(a.createElement("div",Object.assign({ref:e,className:H,style:Object.assign(Object.assign(Object.assign({},$),v),E)},T),a.createElement(u,{value:U},G)))});g.Compact=p.ZP;var v=g},89869:function(t,e,o){o.d(e,{ZP:function(){return i},qN:function(){return r},wZ:function(){return l}});var a=o(58489),n=o(2638);let r=8;function l(t){let{contentRadius:e,limitVerticalRadius:o}=t,a=e>12?e+2:12;return{arrowOffsetHorizontal:a,arrowOffsetVertical:o?r:a}}function i(t,e,o){var r,l,i,c,s,p,f,u;let{componentCls:m,boxShadowPopoverArrow:d,arrowOffsetVertical:b,arrowOffsetHorizontal:g}=t,{arrowDistance:v=0,arrowPlacement:h={left:!0,right:!0,top:!0,bottom:!0}}=o||{};return{[m]:Object.assign(Object.assign(Object.assign(Object.assign({["".concat(m,"-arrow")]:[Object.assign(Object.assign({position:"absolute",zIndex:1,display:"block"},(0,n.W)(t,e,d)),{"&:before":{background:e}})]},(r=!!h.top,l={[["&-placement-top > ".concat(m,"-arrow"),"&-placement-topLeft > ".concat(m,"-arrow"),"&-placement-topRight > ".concat(m,"-arrow")].join(",")]:{bottom:v,transform:"translateY(100%) rotate(180deg)"},["&-placement-top > ".concat(m,"-arrow")]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(100%) rotate(180deg)"},"&-placement-topLeft":{"--arrow-offset-horizontal":g,["> ".concat(m,"-arrow")]:{left:{_skip_check_:!0,value:g}}},"&-placement-topRight":{"--arrow-offset-horizontal":"calc(100% - ".concat((0,a.bf)(g),")"),["> ".concat(m,"-arrow")]:{right:{_skip_check_:!0,value:g}}}},r?l:{})),(i=!!h.bottom,c={[["&-placement-bottom > ".concat(m,"-arrow"),"&-placement-bottomLeft > ".concat(m,"-arrow"),"&-placement-bottomRight > ".concat(m,"-arrow")].join(",")]:{top:v,transform:"translateY(-100%)"},["&-placement-bottom > ".concat(m,"-arrow")]:{left:{_skip_check_:!0,value:"50%"},transform:"translateX(-50%) translateY(-100%)"},"&-placement-bottomLeft":{"--arrow-offset-horizontal":g,["> ".concat(m,"-arrow")]:{left:{_skip_check_:!0,value:g}}},"&-placement-bottomRight":{"--arrow-offset-horizontal":"calc(100% - ".concat((0,a.bf)(g),")"),["> ".concat(m,"-arrow")]:{right:{_skip_check_:!0,value:g}}}},i?c:{})),(s=!!h.left,p={[["&-placement-left > ".concat(m,"-arrow"),"&-placement-leftTop > ".concat(m,"-arrow"),"&-placement-leftBottom > ".concat(m,"-arrow")].join(",")]:{right:{_skip_check_:!0,value:v},transform:"translateX(100%) rotate(90deg)"},["&-placement-left > ".concat(m,"-arrow")]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(100%) rotate(90deg)"},["&-placement-leftTop > ".concat(m,"-arrow")]:{top:b},["&-placement-leftBottom > ".concat(m,"-arrow")]:{bottom:b}},s?p:{})),(f=!!h.right,u={[["&-placement-right > ".concat(m,"-arrow"),"&-placement-rightTop > ".concat(m,"-arrow"),"&-placement-rightBottom > ".concat(m,"-arrow")].join(",")]:{left:{_skip_check_:!0,value:v},transform:"translateX(-100%) rotate(-90deg)"},["&-placement-right > ".concat(m,"-arrow")]:{top:{_skip_check_:!0,value:"50%"},transform:"translateY(-50%) translateX(-100%) rotate(-90deg)"},["&-placement-rightTop > ".concat(m,"-arrow")]:{top:b},["&-placement-rightBottom > ".concat(m,"-arrow")]:{bottom:b}},f?u:{}))}}},2638:function(t,e,o){o.d(e,{W:function(){return r},w:function(){return n}});var a=o(58489);function n(t){let{sizePopupArrow:e,borderRadiusXS:o,borderRadiusOuter:a}=t,n=e/2,r=1*a/Math.sqrt(2),l=n-a*(1-1/Math.sqrt(2)),i=n-1/Math.sqrt(2)*o,c=a*(Math.sqrt(2)-1)+1/Math.sqrt(2)*o,s=2*n-i,p=2*n-r,f=2*n-0,u=n*Math.sqrt(2)+a*(Math.sqrt(2)-2),m=a*(Math.sqrt(2)-1),d="polygon(".concat(m,"px 100%, 50% ").concat(m,"px, ").concat(2*n-m,"px 100%, ").concat(m,"px 100%)");return{arrowShadowWidth:u,arrowPath:"path('M ".concat(0," ").concat(n," A ").concat(a," ").concat(a," 0 0 0 ").concat(r," ").concat(l," L ").concat(i," ").concat(c," A ").concat(o," ").concat(o," 0 0 1 ").concat(s," ").concat(c," L ").concat(p," ").concat(l," A ").concat(a," ").concat(a," 0 0 0 ").concat(f," ").concat(n," Z')"),arrowPolygon:d}}let r=(t,e,o)=>{let{sizePopupArrow:n,arrowPolygon:r,arrowPath:l,arrowShadowWidth:i,borderRadiusXS:c,calc:s}=t;return{pointerEvents:"none",width:n,height:n,overflow:"hidden","&::before":{position:"absolute",bottom:0,insetInlineStart:0,width:n,height:s(n).div(2).equal(),background:e,clipPath:{_multi_value_:!0,value:[r,l]},content:'""'},"&::after":{content:'""',position:"absolute",width:i,height:i,bottom:0,insetInline:0,margin:"auto",borderRadius:{_skip_check_:!0,value:"0 0 ".concat((0,a.bf)(c)," 0")},transform:"translateY(50%) rotate(-135deg)",boxShadow:o,zIndex:0,background:"transparent"}}}},82303:function(t,e,o){o.d(e,{Z:function(){return n}});var a=o(95599);function n(t,e){return a.i.reduce((o,a)=>{let n=t["".concat(a,"1")],r=t["".concat(a,"3")],l=t["".concat(a,"6")],i=t["".concat(a,"7")];return Object.assign(Object.assign({},o),e(a,{lightColor:n,lightBorderColor:r,darkColor:l,textColor:i}))},{})}},78634:function(t,e,o){o.d(e,{Z:function(){return T}});var a=o(2265),n=o(42744),r=o.n(n),l=o(36680),i=o(73310),c=o(59888),s=o(51761),p=o(47387),f=o(67966),u=o(65823),m=o(76564),d=o(86718),b=o(57499),g=o(18987),v=o(58489),h=o(11303),y=o(58854),w=o(89869),O=o(2638),j=o(82303),C=o(12711),N=o(78387);let k=t=>{let{calc:e,componentCls:o,tooltipMaxWidth:a,tooltipColor:n,tooltipBg:r,tooltipBorderRadius:l,zIndexPopup:i,controlHeight:c,boxShadowSecondary:s,paddingSM:p,paddingXS:f,arrowOffsetHorizontal:u,sizePopupArrow:m}=t,d=e(l).add(m).add(u).equal(),b=e(l).mul(2).add(m).equal();return[{[o]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,h.Wf)(t)),{position:"absolute",zIndex:i,display:"block",width:"max-content",maxWidth:a,visibility:"visible","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:"var(--valid-offset-x, 50%) var(--arrow-y, 50%)","&-hidden":{display:"none"},"--antd-arrow-background-color":r,["".concat(o,"-inner")]:{minWidth:b,minHeight:c,padding:"".concat((0,v.bf)(t.calc(p).div(2).equal())," ").concat((0,v.bf)(f)),color:n,textAlign:"start",textDecoration:"none",wordWrap:"break-word",backgroundColor:r,borderRadius:l,boxShadow:s,boxSizing:"border-box"},"&-placement-topLeft,&-placement-topRight,&-placement-bottomLeft,&-placement-bottomRight":{minWidth:d},"&-placement-left,&-placement-leftTop,&-placement-leftBottom,&-placement-right,&-placement-rightTop,&-placement-rightBottom":{["".concat(o,"-inner")]:{borderRadius:t.min(l,w.qN)}},["".concat(o,"-content")]:{position:"relative"}}),(0,j.Z)(t,(t,e)=>{let{darkColor:a}=e;return{["&".concat(o,"-").concat(t)]:{["".concat(o,"-inner")]:{backgroundColor:a},["".concat(o,"-arrow")]:{"--antd-arrow-background-color":a}}}})),{"&-rtl":{direction:"rtl"}})},(0,w.ZP)(t,"var(--antd-arrow-background-color)"),{["".concat(o,"-pure")]:{position:"relative",maxWidth:"none",margin:t.sizePopupArrow}}]},x=t=>Object.assign(Object.assign({zIndexPopup:t.zIndexPopupBase+70},(0,w.wZ)({contentRadius:t.borderRadius,limitVerticalRadius:!0})),(0,O.w)((0,C.IX)(t,{borderRadiusOuter:Math.min(t.borderRadiusOuter,4)})));function _(t){let e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return(0,N.I$)("Tooltip",t=>{let{borderRadius:e,colorTextLightSolid:o,colorBgSpotlight:a}=t;return[k((0,C.IX)(t,{tooltipMaxWidth:250,tooltipColor:o,tooltipBorderRadius:e,tooltipBg:a})),(0,y._y)(t,"zoom-big-fast")]},x,{resetStyle:!1,injectStyle:e})(t)}var E=o(29810);function R(t,e){let o=(0,E.o2)(e),a=r()({["".concat(t,"-").concat(e)]:e&&o}),n={},l={};return e&&!o&&(n.background=e,l["--antd-arrow-background-color"]=e),{className:a,overlayStyle:n,arrowStyle:l}}var P=function(t,e){var o={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&0>e.indexOf(a)&&(o[a]=t[a]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var n=0,a=Object.getOwnPropertySymbols(t);n<a.length;n++)0>e.indexOf(a[n])&&Object.prototype.propertyIsEnumerable.call(t,a[n])&&(o[a[n]]=t[a[n]]);return o};let Z=a.forwardRef((t,e)=>{var o,n;let{prefixCls:v,openClassName:h,getTooltipContainer:y,color:w,overlayInnerStyle:O,children:j,afterOpenChange:C,afterVisibleChange:N,destroyTooltipOnHide:k,destroyOnHidden:x,arrow:E=!0,title:Z,overlay:T,builtinPlacements:z,arrowPointAtCenter:I=!1,autoAdjustOverflow:L=!0,motion:S,getPopupContainer:A,placement:B="top",mouseEnterDelay:V=.1,mouseLeaveDelay:X=.1,overlayStyle:M,rootClassName:Y,overlayClassName:q,styles:D,classNames:H}=t,W=P(t,["prefixCls","openClassName","getTooltipContainer","color","overlayInnerStyle","children","afterOpenChange","afterVisibleChange","destroyTooltipOnHide","destroyOnHidden","arrow","title","overlay","builtinPlacements","arrowPointAtCenter","autoAdjustOverflow","motion","getPopupContainer","placement","mouseEnterDelay","mouseLeaveDelay","overlayStyle","rootClassName","overlayClassName","styles","classNames"]),F=!!E,[,G]=(0,g.ZP)(),{getPopupContainer:U,getPrefixCls:$,direction:J,className:K,style:Q,classNames:tt,styles:te}=(0,b.dj)("tooltip"),to=(0,m.ln)("Tooltip"),ta=a.useRef(null),tn=()=>{var t;null===(t=ta.current)||void 0===t||t.forceAlign()};a.useImperativeHandle(e,()=>{var t,e;return{forceAlign:tn,forcePopupAlign:()=>{to.deprecated(!1,"forcePopupAlign","forceAlign"),tn()},nativeElement:null===(t=ta.current)||void 0===t?void 0:t.nativeElement,popupElement:null===(e=ta.current)||void 0===e?void 0:e.popupElement}});let[tr,tl]=(0,i.Z)(!1,{value:null!==(o=t.open)&&void 0!==o?o:t.visible,defaultValue:null!==(n=t.defaultOpen)&&void 0!==n?n:t.defaultVisible}),ti=!Z&&!T&&0!==Z,tc=a.useMemo(()=>{var t,e;let o=I;return"object"==typeof E&&(o=null!==(e=null!==(t=E.pointAtCenter)&&void 0!==t?t:E.arrowPointAtCenter)&&void 0!==e?e:I),z||(0,f.Z)({arrowPointAtCenter:o,autoAdjustOverflow:L,arrowWidth:F?G.sizePopupArrow:0,borderRadius:G.borderRadius,offset:G.marginXXS,visibleFirst:!0})},[I,E,z,G]),ts=a.useMemo(()=>0===Z?Z:T||Z||"",[T,Z]),tp=a.createElement(c.Z,{space:!0},"function"==typeof ts?ts():ts),tf=$("tooltip",v),tu=$(),tm=t["data-popover-inject"],td=tr;"open"in t||"visible"in t||!ti||(td=!1);let tb=a.isValidElement(j)&&!(0,u.M2)(j)?j:a.createElement("span",null,j),tg=tb.props,tv=tg.className&&"string"!=typeof tg.className?tg.className:r()(tg.className,h||"".concat(tf,"-open")),[th,ty,tw]=_(tf,!tm),tO=R(tf,w),tj=tO.arrowStyle,tC=r()(q,{["".concat(tf,"-rtl")]:"rtl"===J},tO.className,Y,ty,tw,K,tt.root,null==H?void 0:H.root),tN=r()(tt.body,null==H?void 0:H.body),[tk,tx]=(0,s.Cn)("Tooltip",W.zIndex),t_=a.createElement(l.Z,Object.assign({},W,{zIndex:tk,showArrow:F,placement:B,mouseEnterDelay:V,mouseLeaveDelay:X,prefixCls:tf,classNames:{root:tC,body:tN},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},tj),te.root),Q),M),null==D?void 0:D.root),body:Object.assign(Object.assign(Object.assign(Object.assign({},te.body),O),null==D?void 0:D.body),tO.overlayStyle)},getTooltipContainer:A||y||U,ref:ta,builtinPlacements:tc,overlay:tp,visible:td,onVisibleChange:e=>{var o,a;tl(!ti&&e),ti||(null===(o=t.onOpenChange)||void 0===o||o.call(t,e),null===(a=t.onVisibleChange)||void 0===a||a.call(t,e))},afterVisibleChange:null!=C?C:N,arrowContent:a.createElement("span",{className:"".concat(tf,"-arrow-content")}),motion:{motionName:(0,p.m)(tu,"zoom-big-fast",t.transitionName),motionDeadline:1e3},destroyTooltipOnHide:null!=x?x:!!k}),td?(0,u.Tm)(tb,{className:tv}):tb);return th(a.createElement(d.Z.Provider,{value:tx},t_))});Z._InternalPanelDoNotUseOrYouWillBeFired=t=>{let{prefixCls:e,className:o,placement:n="top",title:i,color:c,overlayInnerStyle:s}=t,{getPrefixCls:p}=a.useContext(b.E_),f=p("tooltip",e),[u,m,d]=_(f),g=R(f,c),v=g.arrowStyle,h=Object.assign(Object.assign({},s),g.overlayStyle),y=r()(m,d,f,"".concat(f,"-pure"),"".concat(f,"-placement-").concat(n),o,g.className);return u(a.createElement("div",{className:y,style:v},a.createElement("div",{className:"".concat(f,"-arrow")}),a.createElement(l.G,Object.assign({},t,{className:m,prefixCls:f,overlayInnerStyle:h}),i)))};var T=Z},36680:function(t,e,o){o.d(e,{G:function(){return l},Z:function(){return v}});var a=o(42744),n=o.n(a),r=o(2265);function l(t){var e=t.children,o=t.prefixCls,a=t.id,l=t.overlayInnerStyle,i=t.bodyClassName,c=t.className,s=t.style;return r.createElement("div",{className:n()("".concat(o,"-content"),c),style:s},r.createElement("div",{className:n()("".concat(o,"-inner"),i),id:a,role:"tooltip",style:l},"function"==typeof e?e():e))}var i=o(13428),c=o(10870),s=o(82554),p=o(16758),f={shiftX:64,adjustY:1},u={adjustX:1,shiftY:!0},m=[0,0],d={left:{points:["cr","cl"],overflow:u,offset:[-4,0],targetOffset:m},right:{points:["cl","cr"],overflow:u,offset:[4,0],targetOffset:m},top:{points:["bc","tc"],overflow:f,offset:[0,-4],targetOffset:m},bottom:{points:["tc","bc"],overflow:f,offset:[0,4],targetOffset:m},topLeft:{points:["bl","tl"],overflow:f,offset:[0,-4],targetOffset:m},leftTop:{points:["tr","tl"],overflow:u,offset:[-4,0],targetOffset:m},topRight:{points:["br","tr"],overflow:f,offset:[0,-4],targetOffset:m},rightTop:{points:["tl","tr"],overflow:u,offset:[4,0],targetOffset:m},bottomRight:{points:["tr","br"],overflow:f,offset:[0,4],targetOffset:m},rightBottom:{points:["bl","br"],overflow:u,offset:[4,0],targetOffset:m},bottomLeft:{points:["tl","bl"],overflow:f,offset:[0,4],targetOffset:m},leftBottom:{points:["br","bl"],overflow:u,offset:[-4,0],targetOffset:m}},b=o(53079),g=["overlayClassName","trigger","mouseEnterDelay","mouseLeaveDelay","overlayStyle","prefixCls","children","onVisibleChange","afterVisibleChange","transitionName","animation","motion","placement","align","destroyTooltipOnHide","defaultVisible","getTooltipContainer","overlayInnerStyle","arrowContent","overlay","id","showArrow","classNames","styles"],v=(0,r.forwardRef)(function(t,e){var o,a,f,u=t.overlayClassName,m=t.trigger,v=t.mouseEnterDelay,h=t.mouseLeaveDelay,y=t.overlayStyle,w=t.prefixCls,O=void 0===w?"rc-tooltip":w,j=t.children,C=t.onVisibleChange,N=t.afterVisibleChange,k=t.transitionName,x=t.animation,_=t.motion,E=t.placement,R=t.align,P=t.destroyTooltipOnHide,Z=t.defaultVisible,T=t.getTooltipContainer,z=t.overlayInnerStyle,I=(t.arrowContent,t.overlay),L=t.id,S=t.showArrow,A=t.classNames,B=t.styles,V=(0,s.Z)(t,g),X=(0,b.Z)(L),M=(0,r.useRef)(null);(0,r.useImperativeHandle)(e,function(){return M.current});var Y=(0,c.Z)({},V);return"visible"in t&&(Y.popupVisible=t.visible),r.createElement(p.Z,(0,i.Z)({popupClassName:n()(u,null==A?void 0:A.root),prefixCls:O,popup:function(){return r.createElement(l,{key:"content",prefixCls:O,id:X,bodyClassName:null==A?void 0:A.body,overlayInnerStyle:(0,c.Z)((0,c.Z)({},z),null==B?void 0:B.body)},I)},action:void 0===m?["hover"]:m,builtinPlacements:d,popupPlacement:void 0===E?"right":E,ref:M,popupAlign:void 0===R?{}:R,getPopupContainer:T,onPopupVisibleChange:C,afterPopupVisibleChange:N,popupTransitionName:k,popupAnimation:x,popupMotion:_,defaultPopupVisible:Z,autoDestroy:void 0!==P&&P,mouseLeaveDelay:void 0===h?.1:h,popupStyle:(0,c.Z)((0,c.Z)({},y),null==B?void 0:B.root),mouseEnterDelay:void 0===v?0:v,arrow:void 0===S||S},Y),(a=(null==(o=r.Children.only(j))?void 0:o.props)||{},f=(0,c.Z)((0,c.Z)({},a),{},{"aria-describedby":I?X:null}),r.cloneElement(j,f)))})}}]);