"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3395],{53395:function(e,t,s){s.d(t,{dataConsistencyService:function(){return i}});var a=s(32779),c=s(59841);class n{static getInstance(){return n.instance||(n.instance=new n),n.instance}async performFullConsistencyCheck(){let e=[];return e.push(await this.checkProductInventoryConsistency()),e.push(await this.checkOrderCustomerConsistency()),e.push(await this.checkOrderEmployeeConsistency()),{overall:{totalIssues:e.reduce((e,t)=>e+t.inconsistentItems,0),criticalIssues:e.reduce((e,t)=>e+t.issues.filter(e=>"critical"===e.severity).length,0),timestamp:c._l.now()},results:e}}async checkProductInventoryConsistency(){let e=[],t=0;try{let s=await a.dataAccessManager.inventory.getAll();if("success"===s.status&&s.data){let c=s.data.items;for(let s of(t=c.length,c)){let t=await a.dataAccessManager.products.getByCode(s.productCode);if("success"!==t.status||!t.data){e.push({type:"missing_reference",severity:"high",entityId:s.id,description:"库存记录引用的产品编码 ".concat(s.productCode," 不存在"),suggestedAction:"删除孤立的库存记录或创建对应的产品记录"});continue}let c=t.data;s.productName!==c.modelName&&e.push({type:"data_mismatch",severity:"medium",entityId:s.id,description:"库存记录中的产品名称与产品数据不一致",expectedValue:c.modelName,actualValue:s.productName,suggestedAction:"同步产品名称到库存记录"}),"active"!==c.status&&e.push({type:"invalid_status",severity:"low",entityId:s.id,description:"库存记录对应的产品状态为非活跃状态",actualValue:c.status,suggestedAction:"考虑清理非活跃产品的库存记录"})}}}catch(t){e.push({type:"missing_reference",severity:"critical",entityId:"system",description:"检查产品-库存一致性时发生错误: ".concat(t),suggestedAction:"检查系统配置和网络连接"})}return{module:"inventory",entityType:"product_inventory",totalChecked:t,inconsistentItems:e.length,issues:e,timestamp:c._l.now()}}async checkOrderCustomerConsistency(){let e=[];return{module:"sales",entityType:"order_customer",totalChecked:0,inconsistentItems:e.length,issues:e,timestamp:c._l.now()}}async checkOrderEmployeeConsistency(){let e=[];return{module:"sales",entityType:"order_employee",totalChecked:0,inconsistentItems:e.length,issues:e,timestamp:c._l.now()}}async autoRepairConsistencyIssues(e){let t=0,s=0,a=[];for(let c of e)try{switch(c.type){case"data_mismatch":c.entityId&&c.expectedValue&&(await this.repairDataMismatch(c),t++);break;case"orphaned_record":await this.removeOrphanedRecord(c),t++}}catch(e){s++,a.push("修复问题 ".concat(c.entityId," 失败: ").concat(e))}return{success:0===s,repairedItems:t,failedItems:s,errors:a,timestamp:c._l.now()}}async repairDataMismatch(e){if(e.description.includes("产品名称")){let t=await a.dataAccessManager.inventory.getByProductCode(e.entityId);"success"===t.status&&t.data&&await a.dataAccessManager.inventory.update(e.entityId,{productName:e.expectedValue})}}async removeOrphanedRecord(e){}async validateEntityConsistency(e,t,s){let a=[];try{if("inventory.product_inventory"==="".concat(e,".").concat(t)){let e=await this.validateInventoryItemConsistency(s);a.push(...e)}}catch(e){a.push({type:"missing_reference",severity:"critical",entityId:s,description:"验证实体一致性时发生错误: ".concat(e),suggestedAction:"检查系统配置"})}return a}async validateInventoryItemConsistency(e){let t=[];try{let s=await a.dataAccessManager.inventory.getByProductCode(e);if("success"!==s.status||!s.data)return t.push({type:"missing_reference",severity:"high",entityId:e,description:"库存记录不存在",suggestedAction:"检查库存记录ID是否正确"}),t;let c=s.data,n=await a.dataAccessManager.products.getByCode(c.productCode);if("success"===n.status&&n.data){let s=n.data;c.productName!==s.modelName&&t.push({type:"data_mismatch",severity:"medium",entityId:e,description:"产品名称不一致",expectedValue:s.modelName,actualValue:c.productName,suggestedAction:"同步产品名称"})}else t.push({type:"missing_reference",severity:"high",entityId:e,description:"库存记录引用的产品编码 ".concat(c.productCode," 不存在"),suggestedAction:"创建对应的产品记录或删除库存记录"})}catch(e){}return t}constructor(){}}let i=n.getInstance()}}]);