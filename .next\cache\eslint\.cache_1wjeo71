[{"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\admin\\data-management\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\admin\\integration-test\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\api\\products\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\dashboard\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\data-status\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\finance\\layout.tsx": "7", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\finance\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\employees\\components\\DepartmentManagement.tsx": "10", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\employees\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\layout.tsx": "12", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\materials\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\product-models\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\procurement\\layout.tsx": "17", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\procurement\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\cost-calculation\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\cost-calculation\\utils\\costCalculationEngine.ts": "20", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\hot-press-board\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\hot-press-board\\utils\\hotPressEngine.ts": "22", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\layout.tsx": "23", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionOrderDetailModal.tsx": "24", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionOrdersList.tsx": "25", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionSchedulingBoard.tsx": "26", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionWorkOrdersList.tsx": "27", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\SchedulingConfigModal.tsx": "28", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\SchedulingResultModal.tsx": "29", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkstationCard.tsx": "30", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkstationManagementTab.tsx": "31", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkTimeManagementTab.tsx": "32", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\page.tsx": "33", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\page.tsx": "34", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\queue-cleaner\\page.tsx": "35", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\services\\eventDrivenIntegration.ts": "36", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\services\\improvedIntegrationService.ts": "37", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\work-report\\page.tsx": "38", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\after-sales\\page.tsx": "39", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\analytics\\page.tsx": "40", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\credit\\page.tsx": "41", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\customers\\page.tsx": "42", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\delivery\\page.tsx": "43", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\invoices\\page.tsx": "44", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\layout.tsx": "45", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\components\\AddProductModal.tsx": "46", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\components\\ProductionOrderTable.tsx": "47", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\hooks\\useProductionOrders.ts": "48", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\page.tsx": "49", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\payments\\page.tsx": "50", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\layout.tsx": "51", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\materials\\page.tsx": "52", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\page.tsx": "53", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\products\\page.tsx": "54", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\BaseOrderDetailModal.tsx": "55", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\configs\\productionOrderConfig.tsx": "56", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\configs\\salesOrderConfig.tsx": "57", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\fieldRenderers.tsx": "58", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\index.ts": "59", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\types.ts": "60", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\ProductWeightDisplay.tsx": "61", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\SyncStatusIndicator.tsx": "62", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\UnifiedTagRenderer.tsx": "63", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\VirtualizedTable.tsx": "64", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\EventCommunicationMonitor.tsx": "65", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\MemoryMonitorCard.tsx": "66", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\MoldNumberDebugger.tsx": "67", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\FormSelect.tsx": "68", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\Header.tsx": "69", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\MainLayout.tsx": "70", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\Sidebar.tsx": "71", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\MessageQueueDebugger.tsx": "72", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\production\\RealTimeSyncStatus.tsx": "73", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\providers\\AntdProvider.tsx": "74", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\providers\\DataInitializationProvider.tsx": "75", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\SafeSelect.tsx": "76", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\AddOrderModal.tsx": "77", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductCreateModal.tsx": "78", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductInfoSection.tsx": "79", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductSelectModal.tsx": "80", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\__tests__\\OrderChangeModal.test.tsx": "81", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\shared\\ProductForm.tsx": "82", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\config\\dataAccessConfig.ts": "83", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\config\\validationRules.ts": "84", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\constants\\productionConstants.ts": "85", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\data\\seedData.ts": "86", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useDataAccessMonitor.ts": "87", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useDebouncedCallback.ts": "88", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useEventListener.ts": "89", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useFormValueFixer.ts": "90", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useOptimizedProductionData.ts": "91", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useOrdersData.ts": "92", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionDataAdapter.ts": "93", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionDataSync.ts": "94", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionErrorHandler.ts": "95", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionOperations.ts": "96", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionRealTimeSync.ts": "97", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionStatistics.ts": "98", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useTimer.ts": "99", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useWarehousePermissions.ts": "100", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useWorkstationRealtime.ts": "101", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\__tests__\\productionHooks.test.ts": "102", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\__tests__\\useOptimizedProductionData.test.ts": "103", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\BusinessIdGenerator.ts": "104", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\CacheDebugger.ts": "105", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\strategies.ts": "106", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\types.ts": "107", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\CostCalculationDataAccessService.ts": "108", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\CustomerDataAccessService.ts": "109", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessLayer.ts": "110", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessManager.ts": "111", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessPerformanceMonitor.ts": "112", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataChangeNotifier.ts": "113", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataConsistencyService.ts": "114", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataSyncService.ts": "115", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\EmployeeDataAccessService.ts": "116", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\InventoryDataAccessService.ts": "117", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\OrderDataAccessService.ts": "118", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\PerformanceOptimizer.ts": "119", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductDataAccessService.ts": "120", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductionOrderDataAccessService.ts": "121", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductionWorkOrderDataAccessService.ts": "122", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkstationDataAccessService.ts": "123", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkstationStorage.ts": "124", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkTimeDataAccessService.ts": "125", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\__tests__\\DataAccessManager.test.ts": "126", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataInitializationService.ts": "127", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataModelSyncService.ts": "128", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventCommunicationDebugger.ts": "129", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventErrorHandler.ts": "130", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventListenerManager.ts": "131", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\strategies.ts": "132", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\types.ts": "133", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\inventoryValueCalculationService.ts": "134", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\mrpService.ts": "135", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderCancellationService.ts": "136", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderDeliveryDateChangeService.ts": "137", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderQuantityChangeService.ts": "138", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\productInventorySyncService.ts": "139", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\productTypeBindingService.ts": "140", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\IncrementalSyncService.ts": "141", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\OfflineDataCache.ts": "142", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\RealtimeDataSyncService.ts": "143", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\RealtimeSyncManager.ts": "144", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\SyncErrorHandler.ts": "145", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\WorkstationRealtimeService.ts": "146", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\clearSchedulingData.ts": "147", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\SameMoldPrioritySchedulingService.ts": "148", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\SchedulingStateManager.ts": "149", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testSameMoldGrouping.ts": "150", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testSchedulingAlgorithm.ts": "151", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testWorkstationDistribution.ts": "152", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testWorkTimeCalculation.ts": "153", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\verifySchedulingFix.ts": "154", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\__tests__\\SameMoldPrioritySchedulingService.test.ts": "155", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\validation\\OrderValidationService.ts": "156", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\validation\\WorkstationDataConsistencyValidator.ts": "157", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\workstation\\WorkstationUpdateService.ts": "158", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\employeeStore.ts": "159", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\masterDataStore.ts": "160", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\productionStore.ts": "161", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\productionUIStore.ts": "162", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\sales\\useCustomerStore.ts": "163", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\sales\\useSalesCompositeStore.ts": "164", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\useProductionUIStore.ts": "165", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\warehouseStore.ts": "166", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\workTimeStore.ts": "167", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\__tests__\\useProductionUIStore.test.ts": "168", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\useAppStore.ts": "169", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\index.ts": "170", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\standardStoreInterface.ts": "171", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\architectureCompliance.ts": "172", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\batchNumberMigration.ts": "173", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\businessRuleValidator.ts": "174", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\dataTransformers.ts": "175", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\errorHandlers.ts": "176", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\index.ts": "177", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\productionOrderRules.ts": "178", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\statusTransitionManager.ts": "179", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\timestampGenerator.ts": "180", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\transactionManager.ts": "181", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\calculations\\amountCalculator.ts": "182", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\calculations\\index.ts": "183", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\concurrencyControl.ts": "184", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\DataAccessComplianceChecker.ts": "185", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataAccessErrorHandler.ts": "186", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataModelConsistency.ts": "187", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataSyncVerification.ts": "188", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dateUtils.ts": "189", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\duplicateExecutionPrevention.ts": "190", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formatters\\index.ts": "191", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formatters\\statusFormatter.tsx": "192", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formUtils.ts": "193", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\index.ts": "194", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\orderStatusTransitions.ts": "195", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\htmlSanitizer.ts": "196", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\inputValidator.ts": "197", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\safeEventManager.ts": "198", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\testSchedulingBoardData.ts": "199", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\UnifiedLogger.ts": "200", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validation\\workstationValidation.ts": "201", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validationUtils.ts": "202", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validators\\formValidators.ts": "203", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validators\\index.ts": "204", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\workOrderGenerator.ts": "205", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\workstationQueueCleaner.ts": "206", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\basic.test.ts": "207", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\businessRuleValidator.test.ts": "208", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\dataTransformers.test.ts": "209", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\errorHandlers.test.ts": "210", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\index.test.ts": "211", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\simple.test.ts": "212", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\statusTransitionManager.test.ts": "213", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\timestampGenerator.test.ts": "214", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\transactionManager.test.ts": "215", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\orderNumberValidation.test.ts": "216", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\batch\\BatchOperations.test.ts": "217", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\configuration\\SystemConfig.test.ts": "218", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\dataAccess\\DataAccessMonitoring.test.ts": "219", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\hooks\\useProductionDataAdapter.test.ts": "220", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\CrossModule.integration.test.ts": "221", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\DataAccess.integration.test.ts": "222", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\HookIntegration.test.tsx": "223", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\OrderManagement.e2e.test.ts": "224", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\Phase4EndToEndTest.test.ts": "225", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\StateManagementIntegration.test.tsx": "226", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\CachePerformanceComparison.test.ts": "227", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\DataAccess.performance.test.ts": "228", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\StateManagementPerformance.test.ts": "229", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\production\\ProductionReadinessTest.test.ts": "230", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\CacheIntegration.test.ts": "231", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\DataAccessManager.test.ts": "232", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\PerformanceOptimizer.test.ts": "233", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\ProductionOrderDataAccessService.test.ts": "234", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\WorkstationDataAccessService.test.ts": "235", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\events\\EventCommunicationOptimization.test.ts": "236", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\setup.ts": "237", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\DataAccessCompliantStateManager.ts": "238", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\LegacyCompatibilityAdapter.ts": "239", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\UnifiedStateTransitionService.ts": "240", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\unified\\UnifiedOrderStatusManager.ts": "241", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\performance.d.ts": "242", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\stateTransitionTypes.ts": "243", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\statusTransitionManagerDemo.ts": "244", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedCalculation.ts": "245", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedIdManager.ts": "246", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedStateTransition.ts": "247", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\styles\\antdHelpers.ts": "248"}, {"size": 8317, "mtime": 1751472082571, "results": "249", "hashOfConfig": "250"}, {"size": 17066, "mtime": 1753110188437, "results": "251", "hashOfConfig": "250"}, {"size": 10910, "mtime": 1753110281265, "results": "252", "hashOfConfig": "250"}, {"size": 200, "mtime": 1750663972000, "results": "253", "hashOfConfig": "250"}, {"size": 6285, "mtime": 1751471013201, "results": "254", "hashOfConfig": "250"}, {"size": 7679, "mtime": 1753110359903, "results": "255", "hashOfConfig": "250"}, {"size": 198, "mtime": 1750664248000, "results": "256", "hashOfConfig": "250"}, {"size": 13303, "mtime": 1752636568070, "results": "257", "hashOfConfig": "250"}, {"size": 687, "mtime": 1753462581535, "results": "258", "hashOfConfig": "250"}, {"size": 11936, "mtime": 1752636568072, "results": "259", "hashOfConfig": "250"}, {"size": 32672, "mtime": 1753596544526, "results": "260", "hashOfConfig": "250"}, {"size": 201, "mtime": 1750676474000, "results": "261", "hashOfConfig": "250"}, {"size": 14901, "mtime": 1753597860953, "results": "262", "hashOfConfig": "250"}, {"size": 5389, "mtime": 1750676500000, "results": "263", "hashOfConfig": "250"}, {"size": 31923, "mtime": 1753597668284, "results": "264", "hashOfConfig": "250"}, {"size": 135, "mtime": 1750663842000, "results": "265", "hashOfConfig": "250"}, {"size": 202, "mtime": 1750664068000, "results": "266", "hashOfConfig": "250"}, {"size": 9130, "mtime": 1752636568076, "results": "267", "hashOfConfig": "250"}, {"size": 26876, "mtime": 1752676456709, "results": "268", "hashOfConfig": "250"}, {"size": 11598, "mtime": 1750783880000, "results": "269", "hashOfConfig": "250"}, {"size": 22365, "mtime": 1752676485529, "results": "270", "hashOfConfig": "250"}, {"size": 6532, "mtime": 1752636638340, "results": "271", "hashOfConfig": "250"}, {"size": 201, "mtime": 1750664192000, "results": "272", "hashOfConfig": "250"}, {"size": 839, "mtime": 1753589546202, "results": "273", "hashOfConfig": "250"}, {"size": 14643, "mtime": 1753673540477, "results": "274", "hashOfConfig": "250"}, {"size": 31861, "mtime": 1753767020331, "results": "275", "hashOfConfig": "250"}, {"size": 21107, "mtime": 1753686849223, "results": "276", "hashOfConfig": "250"}, {"size": 10580, "mtime": 1753251806457, "results": "277", "hashOfConfig": "250"}, {"size": 13053, "mtime": 1752683170218, "results": "278", "hashOfConfig": "250"}, {"size": 3641, "mtime": 1753685967058, "results": "279", "hashOfConfig": "250"}, {"size": 36669, "mtime": 1753686021991, "results": "280", "hashOfConfig": "250"}, {"size": 19817, "mtime": 1753688153596, "results": "281", "hashOfConfig": "250"}, {"size": 41952, "mtime": 1753898252102, "results": "282", "hashOfConfig": "250"}, {"size": 207, "mtime": 1753110003286, "results": "283", "hashOfConfig": "250"}, {"size": 9122, "mtime": 1753111302759, "results": "284", "hashOfConfig": "250"}, {"size": 7409, "mtime": 1753111563102, "results": "285", "hashOfConfig": "250"}, {"size": 7355, "mtime": 1753111627741, "results": "286", "hashOfConfig": "250"}, {"size": 13186, "mtime": 1753112108584, "results": "287", "hashOfConfig": "250"}, {"size": 32932, "mtime": 1751471092404, "results": "288", "hashOfConfig": "250"}, {"size": 19721, "mtime": 1751519299779, "results": "289", "hashOfConfig": "250"}, {"size": 27895, "mtime": 1751907540694, "results": "290", "hashOfConfig": "250"}, {"size": 29281, "mtime": 1753596711203, "results": "291", "hashOfConfig": "250"}, {"size": 34635, "mtime": 1751624415640, "results": "292", "hashOfConfig": "250"}, {"size": 32378, "mtime": 1751520259608, "results": "293", "hashOfConfig": "250"}, {"size": 196, "mtime": 1750664022000, "results": "294", "hashOfConfig": "250"}, {"size": 6499, "mtime": 1752683868290, "results": "295", "hashOfConfig": "250"}, {"size": 9615, "mtime": 1752130586035, "results": "296", "hashOfConfig": "250"}, {"size": 5360, "mtime": 1752636568003, "results": "297", "hashOfConfig": "250"}, {"size": 49968, "mtime": 1753632898681, "results": "298", "hashOfConfig": "250"}, {"size": 23839, "mtime": 1751947848231, "results": "299", "hashOfConfig": "250"}, {"size": 200, "mtime": 1750664130000, "results": "300", "hashOfConfig": "250"}, {"size": 30710, "mtime": 1751471092422, "results": "301", "hashOfConfig": "250"}, {"size": 12125, "mtime": 1751523982521, "results": "302", "hashOfConfig": "250"}, {"size": 25330, "mtime": 1753595901403, "results": "303", "hashOfConfig": "250"}, {"size": 6953, "mtime": 1753425443052, "results": "304", "hashOfConfig": "250"}, {"size": 6420, "mtime": 1753425502395, "results": "305", "hashOfConfig": "250"}, {"size": 10388, "mtime": 1753425659050, "results": "306", "hashOfConfig": "250"}, {"size": 5689, "mtime": 1753425387183, "results": "307", "hashOfConfig": "250"}, {"size": 610, "mtime": 1753425459068, "results": "308", "hashOfConfig": "250"}, {"size": 4056, "mtime": 1753590154028, "results": "309", "hashOfConfig": "250"}, {"size": 6634, "mtime": 1753683040589, "results": "310", "hashOfConfig": "250"}, {"size": 5985, "mtime": 1752636638473, "results": "311", "hashOfConfig": "250"}, {"size": 10019, "mtime": 1753687909984, "results": "312", "hashOfConfig": "250"}, {"size": 12022, "mtime": 1753590236232, "results": "313", "hashOfConfig": "250"}, {"size": 11262, "mtime": 1753590395478, "results": "314", "hashOfConfig": "250"}, {"size": 7352, "mtime": 1753590431356, "results": "315", "hashOfConfig": "250"}, {"size": 8051, "mtime": 1751960054721, "results": "316", "hashOfConfig": "250"}, {"size": 4042, "mtime": 1753110084804, "results": "317", "hashOfConfig": "250"}, {"size": 3270, "mtime": 1750664664000, "results": "318", "hashOfConfig": "250"}, {"size": 1166, "mtime": 1750664448000, "results": "319", "hashOfConfig": "250"}, {"size": 7657, "mtime": 1753095592333, "results": "320", "hashOfConfig": "250"}, {"size": 8979, "mtime": 1752124680580, "results": "321", "hashOfConfig": "250"}, {"size": 10356, "mtime": 1753116519437, "results": "322", "hashOfConfig": "250"}, {"size": 1809, "mtime": 1752934163849, "results": "323", "hashOfConfig": "250"}, {"size": 4054, "mtime": 1752931470909, "results": "324", "hashOfConfig": "250"}, {"size": 2815, "mtime": 1753116837300, "results": "325", "hashOfConfig": "250"}, {"size": 15798, "mtime": 1753597147530, "results": "326", "hashOfConfig": "250"}, {"size": 18626, "mtime": 1752683812925, "results": "327", "hashOfConfig": "250"}, {"size": 11005, "mtime": 1753270264271, "results": "328", "hashOfConfig": "250"}, {"size": 11506, "mtime": 1752687579634, "results": "329", "hashOfConfig": "250"}, {"size": 5510, "mtime": 1753078696827, "results": "330", "hashOfConfig": "250"}, {"size": 13582, "mtime": 1752202638541, "results": "331", "hashOfConfig": "250"}, {"size": 4739, "mtime": 1753590910292, "results": "332", "hashOfConfig": "250"}, {"size": 7685, "mtime": 1753591071270, "results": "333", "hashOfConfig": "250"}, {"size": 1496, "mtime": 1753334284832, "results": "334", "hashOfConfig": "250"}, {"size": 22430, "mtime": 1752636638344, "results": "335", "hashOfConfig": "250"}, {"size": 5566, "mtime": 1753591186472, "results": "336", "hashOfConfig": "250"}, {"size": 4555, "mtime": 1753591512966, "results": "337", "hashOfConfig": "250"}, {"size": 12669, "mtime": 1753591597013, "results": "338", "hashOfConfig": "250"}, {"size": 2548, "mtime": 1752636568007, "results": "339", "hashOfConfig": "250"}, {"size": 16200, "mtime": 1753591898082, "results": "340", "hashOfConfig": "250"}, {"size": 7274, "mtime": 1753462713838, "results": "341", "hashOfConfig": "250"}, {"size": 17272, "mtime": 1753183526475, "results": "342", "hashOfConfig": "250"}, {"size": 9257, "mtime": 1753119081974, "results": "343", "hashOfConfig": "250"}, {"size": 8438, "mtime": 1753119255181, "results": "344", "hashOfConfig": "250"}, {"size": 14458, "mtime": 1753119734439, "results": "345", "hashOfConfig": "250"}, {"size": 12749, "mtime": 1753346402967, "results": "346", "hashOfConfig": "250"}, {"size": 9417, "mtime": 1753120263369, "results": "347", "hashOfConfig": "250"}, {"size": 10202, "mtime": 1753592123980, "results": "348", "hashOfConfig": "250"}, {"size": 4411, "mtime": 1751526108817, "results": "349", "hashOfConfig": "250"}, {"size": 10015, "mtime": 1753120524778, "results": "350", "hashOfConfig": "250"}, {"size": 10810, "mtime": 1753183656800, "results": "351", "hashOfConfig": "250"}, {"size": 12052, "mtime": 1752598511992, "results": "352", "hashOfConfig": "250"}, {"size": 9026, "mtime": 1753025744693, "results": "353", "hashOfConfig": "250"}, {"size": 9903, "mtime": 1753592257121, "results": "354", "hashOfConfig": "250"}, {"size": 8607, "mtime": 1753426737026, "results": "355", "hashOfConfig": "250"}, {"size": 4874, "mtime": 1753447681518, "results": "356", "hashOfConfig": "250"}, {"size": 16889, "mtime": 1753294654982, "results": "357", "hashOfConfig": "250"}, {"size": 10679, "mtime": 1753343790701, "results": "358", "hashOfConfig": "250"}, {"size": 9946, "mtime": 1753332732442, "results": "359", "hashOfConfig": "250"}, {"size": 80655, "mtime": 1753936993496, "results": "360", "hashOfConfig": "250"}, {"size": 17353, "mtime": 1753592493225, "results": "361", "hashOfConfig": "250"}, {"size": 20235, "mtime": 1753376754066, "results": "362", "hashOfConfig": "250"}, {"size": 11053, "mtime": 1753295847419, "results": "363", "hashOfConfig": "250"}, {"size": 15293, "mtime": 1753295924820, "results": "364", "hashOfConfig": "250"}, {"size": 12086, "mtime": 1753344120473, "results": "365", "hashOfConfig": "250"}, {"size": 14671, "mtime": 1753592558658, "results": "366", "hashOfConfig": "250"}, {"size": 17319, "mtime": 1753592637902, "results": "367", "hashOfConfig": "250"}, {"size": 11970, "mtime": 1753592806709, "results": "368", "hashOfConfig": "250"}, {"size": 13288, "mtime": 1753343385089, "results": "369", "hashOfConfig": "250"}, {"size": 29284, "mtime": 1753344449529, "results": "370", "hashOfConfig": "250"}, {"size": 30723, "mtime": 1753637955696, "results": "371", "hashOfConfig": "250"}, {"size": 31941, "mtime": 1753687446307, "results": "372", "hashOfConfig": "250"}, {"size": 12356, "mtime": 1753184660310, "results": "373", "hashOfConfig": "250"}, {"size": 17699, "mtime": 1753296145425, "results": "374", "hashOfConfig": "250"}, {"size": 11145, "mtime": 1753346287058, "results": "375", "hashOfConfig": "250"}, {"size": 10920, "mtime": 1752932955093, "results": "376", "hashOfConfig": "250"}, {"size": 9026, "mtime": 1753129802023, "results": "377", "hashOfConfig": "250"}, {"size": 13279, "mtime": 1753432845142, "results": "378", "hashOfConfig": "250"}, {"size": 21084, "mtime": 1753593098613, "results": "379", "hashOfConfig": "250"}, {"size": 16488, "mtime": 1753432480423, "results": "380", "hashOfConfig": "250"}, {"size": 11492, "mtime": 1753432218773, "results": "381", "hashOfConfig": "250"}, {"size": 7537, "mtime": 1753448215808, "results": "382", "hashOfConfig": "250"}, {"size": 10992, "mtime": 1753129887123, "results": "383", "hashOfConfig": "250"}, {"size": 17637, "mtime": 1753182767916, "results": "384", "hashOfConfig": "250"}, {"size": 9842, "mtime": 1753278708677, "results": "385", "hashOfConfig": "250"}, {"size": 17349, "mtime": 1753163754987, "results": "386", "hashOfConfig": "250"}, {"size": 9966, "mtime": 1753279011742, "results": "387", "hashOfConfig": "250"}, {"size": 14078, "mtime": 1753164171559, "results": "388", "hashOfConfig": "250"}, {"size": 14251, "mtime": 1752636568038, "results": "389", "hashOfConfig": "250"}, {"size": 9863, "mtime": 1752636568040, "results": "390", "hashOfConfig": "250"}, {"size": 8213, "mtime": 1752931368309, "results": "391", "hashOfConfig": "250"}, {"size": 10570, "mtime": 1753258490583, "results": "392", "hashOfConfig": "250"}, {"size": 8784, "mtime": 1752636638375, "results": "393", "hashOfConfig": "250"}, {"size": 7448, "mtime": 1752645059013, "results": "394", "hashOfConfig": "250"}, {"size": 9898, "mtime": 1752940477480, "results": "395", "hashOfConfig": "250"}, {"size": 7314, "mtime": 1753203286040, "results": "396", "hashOfConfig": "250"}, {"size": 45354, "mtime": 1753637091920, "results": "397", "hashOfConfig": "250"}, {"size": 7074, "mtime": 1753088327988, "results": "398", "hashOfConfig": "250"}, {"size": 5554, "mtime": 1753252196090, "results": "399", "hashOfConfig": "250"}, {"size": 6964, "mtime": 1753172024370, "results": "400", "hashOfConfig": "250"}, {"size": 4988, "mtime": 1753172863387, "results": "401", "hashOfConfig": "250"}, {"size": 3659, "mtime": 1753174191502, "results": "402", "hashOfConfig": "250"}, {"size": 4697, "mtime": 1753089147119, "results": "403", "hashOfConfig": "250"}, {"size": 13434, "mtime": 1752266933158, "results": "404", "hashOfConfig": "250"}, {"size": 20714, "mtime": 1753593288555, "results": "405", "hashOfConfig": "250"}, {"size": 11259, "mtime": 1753203314465, "results": "406", "hashOfConfig": "250"}, {"size": 16808, "mtime": 1753370080981, "results": "407", "hashOfConfig": "250"}, {"size": 7123, "mtime": 1753348031690, "results": "408", "hashOfConfig": "250"}, {"size": 9559, "mtime": 1753347069671, "results": "409", "hashOfConfig": "250"}, {"size": 13815, "mtime": 1753347205084, "results": "410", "hashOfConfig": "250"}, {"size": 12010, "mtime": 1752599799373, "results": "411", "hashOfConfig": "250"}, {"size": 10587, "mtime": 1753593348653, "results": "412", "hashOfConfig": "250"}, {"size": 14473, "mtime": 1753169826753, "results": "413", "hashOfConfig": "250"}, {"size": 15378, "mtime": 1753347175520, "results": "414", "hashOfConfig": "250"}, {"size": 19819, "mtime": 1753162490093, "results": "415", "hashOfConfig": "250"}, {"size": 9205, "mtime": 1753207611478, "results": "416", "hashOfConfig": "250"}, {"size": 13502, "mtime": 1752598565747, "results": "417", "hashOfConfig": "250"}, {"size": 1449, "mtime": 1750663890000, "results": "418", "hashOfConfig": "250"}, {"size": 77504, "mtime": 1753934336265, "results": "419", "hashOfConfig": "250"}, {"size": 5331, "mtime": 1752199782988, "results": "420", "hashOfConfig": "250"}, {"size": 9878, "mtime": 1753593489808, "results": "421", "hashOfConfig": "250"}, {"size": 5751, "mtime": 1753162734635, "results": "422", "hashOfConfig": "250"}, {"size": 14988, "mtime": 1753276559106, "results": "423", "hashOfConfig": "250"}, {"size": 12587, "mtime": 1753942428136, "results": "424", "hashOfConfig": "250"}, {"size": 13280, "mtime": 1753943513983, "results": "425", "hashOfConfig": "250"}, {"size": 2768, "mtime": 1753672005552, "results": "426", "hashOfConfig": "250"}, {"size": 8938, "mtime": 1753895390153, "results": "427", "hashOfConfig": "250"}, {"size": 26595, "mtime": 1753942231246, "results": "428", "hashOfConfig": "250"}, {"size": 5191, "mtime": 1753593586880, "results": "429", "hashOfConfig": "250"}, {"size": 13532, "mtime": 1753943017638, "results": "430", "hashOfConfig": "250"}, {"size": 8060, "mtime": 1753270177859, "results": "431", "hashOfConfig": "250"}, {"size": 535, "mtime": 1753270727671, "results": "432", "hashOfConfig": "250"}, {"size": 9532, "mtime": 1752931063725, "results": "433", "hashOfConfig": "250"}, {"size": 13488, "mtime": 1753203664315, "results": "434", "hashOfConfig": "250"}, {"size": 4263, "mtime": 1753895901895, "results": "435", "hashOfConfig": "250"}, {"size": 9982, "mtime": 1752636568061, "results": "436", "hashOfConfig": "250"}, {"size": 8127, "mtime": 1753175935226, "results": "437", "hashOfConfig": "250"}, {"size": 3699, "mtime": 1753120607827, "results": "438", "hashOfConfig": "250"}, {"size": 4198, "mtime": 1753125293677, "results": "439", "hashOfConfig": "250"}, {"size": 641, "mtime": 1753270744908, "results": "440", "hashOfConfig": "250"}, {"size": 7413, "mtime": 1753270353937, "results": "441", "hashOfConfig": "250"}, {"size": 9796, "mtime": 1753025804174, "results": "442", "hashOfConfig": "250"}, {"size": 1131, "mtime": 1753593920067, "results": "443", "hashOfConfig": "250"}, {"size": 6012, "mtime": 1753326520640, "results": "444", "hashOfConfig": "250"}, {"size": 2794, "mtime": 1753671480334, "results": "445", "hashOfConfig": "250"}, {"size": 8431, "mtime": 1753671684327, "results": "446", "hashOfConfig": "250"}, {"size": 5209, "mtime": 1753896163704, "results": "447", "hashOfConfig": "250"}, {"size": 3832, "mtime": 1753180301276, "results": "448", "hashOfConfig": "250"}, {"size": 3071, "mtime": 1753334272827, "results": "449", "hashOfConfig": "250"}, {"size": 3025, "mtime": 1753685242417, "results": "450", "hashOfConfig": "250"}, {"size": 7986, "mtime": 1753332776460, "results": "451", "hashOfConfig": "250"}, {"size": 8296, "mtime": 1753276644867, "results": "452", "hashOfConfig": "250"}, {"size": 402, "mtime": 1753270759925, "results": "453", "hashOfConfig": "250"}, {"size": 9081, "mtime": 1753180636246, "results": "454", "hashOfConfig": "250"}, {"size": 12936, "mtime": 1753180811711, "results": "455", "hashOfConfig": "250"}, {"size": 7464, "mtime": 1753287450857, "results": "456", "hashOfConfig": "250"}, {"size": 8711, "mtime": 1753946105560, "results": "457", "hashOfConfig": "250"}, {"size": 10410, "mtime": 1753286896501, "results": "458", "hashOfConfig": "250"}, {"size": 7648, "mtime": 1753287410272, "results": "459", "hashOfConfig": "250"}, {"size": 7837, "mtime": 1753286932569, "results": "460", "hashOfConfig": "250"}, {"size": 8256, "mtime": 1753287547287, "results": "461", "hashOfConfig": "250"}, {"size": 13028, "mtime": 1753934091360, "results": "462", "hashOfConfig": "250"}, {"size": 9646, "mtime": 1753294747577, "results": "463", "hashOfConfig": "250"}, {"size": 8743, "mtime": 1753896126729, "results": "464", "hashOfConfig": "250"}, {"size": 6977, "mtime": 1753025884170, "results": "465", "hashOfConfig": "250"}, {"size": 18950, "mtime": 1753944066315, "results": "466", "hashOfConfig": "250"}, {"size": 11602, "mtime": 1753944640062, "results": "467", "hashOfConfig": "250"}, {"size": 11618, "mtime": 1753944723095, "results": "468", "hashOfConfig": "250"}, {"size": 9901, "mtime": 1753945480206, "results": "469", "hashOfConfig": "250"}, {"size": 14763, "mtime": 1753945563874, "results": "470", "hashOfConfig": "250"}, {"size": 13425, "mtime": 1753375633015, "results": "471", "hashOfConfig": "250"}, {"size": 9358, "mtime": 1752640831372, "results": "472", "hashOfConfig": "250"}, {"size": 12869, "mtime": 1753375569895, "results": "473", "hashOfConfig": "250"}, {"size": 14171, "mtime": 1753370765605, "results": "474", "hashOfConfig": "250"}, {"size": 12423, "mtime": 1752598743592, "results": "475", "hashOfConfig": "250"}, {"size": 10509, "mtime": 1753370835593, "results": "476", "hashOfConfig": "250"}, {"size": 11164, "mtime": 1753375676930, "results": "477", "hashOfConfig": "250"}, {"size": 13436, "mtime": 1752598798485, "results": "478", "hashOfConfig": "250"}, {"size": 11996, "mtime": 1753370963443, "results": "479", "hashOfConfig": "250"}, {"size": 6140, "mtime": 1753345001176, "results": "480", "hashOfConfig": "250"}, {"size": 8589, "mtime": 1752640486549, "results": "481", "hashOfConfig": "250"}, {"size": 9905, "mtime": 1752640593542, "results": "482", "hashOfConfig": "250"}, {"size": 10899, "mtime": 1752745552656, "results": "483", "hashOfConfig": "250"}, {"size": 11027, "mtime": 1752640428765, "results": "484", "hashOfConfig": "250"}, {"size": 13147, "mtime": 1753594438226, "results": "485", "hashOfConfig": "250"}, {"size": 5049, "mtime": 1753286990271, "results": "486", "hashOfConfig": "250"}, {"size": 22951, "mtime": 1753898090263, "results": "487", "hashOfConfig": "250"}, {"size": 15777, "mtime": 1753898174617, "results": "488", "hashOfConfig": "250"}, {"size": 23667, "mtime": 1753897895005, "results": "489", "hashOfConfig": "250"}, {"size": 16391, "mtime": 1753900331284, "results": "490", "hashOfConfig": "250"}, {"size": 638, "mtime": 1753944332686, "results": "491", "hashOfConfig": "250"}, {"size": 11541, "mtime": 1753897973245, "results": "492", "hashOfConfig": "250"}, {"size": 5306, "mtime": 1753897234473, "results": "493", "hashOfConfig": "250"}, {"size": 10946, "mtime": 1753897535458, "results": "494", "hashOfConfig": "250"}, {"size": 9749, "mtime": 1753897483917, "results": "495", "hashOfConfig": "250"}, {"size": 8308, "mtime": 1753897402187, "results": "496", "hashOfConfig": "250"}, {"size": 5453, "mtime": 1753951908812, "results": "497", "hashOfConfig": "250"}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tpykxl", {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\admin\\data-management\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\admin\\integration-test\\page.tsx", ["1242", "1243", "1244", "1245"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\api\\products\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\data-status\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\finance\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\finance\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\employees\\components\\DepartmentManagement.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\employees\\page.tsx", ["1246"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\materials\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\product-models\\page.tsx", ["1247"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\procurement\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\procurement\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\cost-calculation\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\cost-calculation\\utils\\costCalculationEngine.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\hot-press-board\\page.tsx", ["1248"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\hot-press-board\\utils\\hotPressEngine.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionOrderDetailModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionOrdersList.tsx", ["1249", "1250", "1251"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionSchedulingBoard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionWorkOrdersList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\SchedulingConfigModal.tsx", ["1252", "1253"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\SchedulingResultModal.tsx", ["1254", "1255", "1256", "1257"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkstationCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkstationManagementTab.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkTimeManagementTab.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\page.tsx", ["1258", "1259"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\queue-cleaner\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\services\\eventDrivenIntegration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\services\\improvedIntegrationService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\work-report\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\after-sales\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\credit\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\customers\\page.tsx", ["1260"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\delivery\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\invoices\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\components\\AddProductModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\components\\ProductionOrderTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\hooks\\useProductionOrders.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\payments\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\materials\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\products\\page.tsx", ["1261"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\BaseOrderDetailModal.tsx", ["1262"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\configs\\productionOrderConfig.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\configs\\salesOrderConfig.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\fieldRenderers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\ProductWeightDisplay.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\SyncStatusIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\UnifiedTagRenderer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\VirtualizedTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\EventCommunicationMonitor.tsx", ["1263"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\MemoryMonitorCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\MoldNumberDebugger.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\FormSelect.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\MainLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\MessageQueueDebugger.tsx", ["1264"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\production\\RealTimeSyncStatus.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\providers\\AntdProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\providers\\DataInitializationProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\SafeSelect.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\AddOrderModal.tsx", ["1265"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductCreateModal.tsx", ["1266"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductInfoSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductSelectModal.tsx", ["1267", "1268"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\__tests__\\OrderChangeModal.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\shared\\ProductForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\config\\dataAccessConfig.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\config\\validationRules.ts", ["1269"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\constants\\productionConstants.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\data\\seedData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useDataAccessMonitor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useDebouncedCallback.ts", ["1270"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useEventListener.ts", ["1271", "1272", "1273", "1274", "1275"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useFormValueFixer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useOptimizedProductionData.ts", ["1276", "1277"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useOrdersData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionDataAdapter.ts", ["1278"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionDataSync.ts", ["1279", "1280"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionErrorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionRealTimeSync.ts", ["1281", "1282"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionStatistics.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useTimer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useWarehousePermissions.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useWorkstationRealtime.ts", ["1283"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\__tests__\\productionHooks.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\__tests__\\useOptimizedProductionData.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\BusinessIdGenerator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\CacheDebugger.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\strategies.ts", ["1284"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\types.ts", ["1285"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\CostCalculationDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\CustomerDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessLayer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessPerformanceMonitor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataChangeNotifier.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataConsistencyService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataSyncService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\EmployeeDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\InventoryDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\OrderDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\PerformanceOptimizer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductionOrderDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductionWorkOrderDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkstationDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkstationStorage.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkTimeDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\__tests__\\DataAccessManager.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataInitializationService.ts", ["1286"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataModelSyncService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventCommunicationDebugger.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventErrorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventListenerManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\strategies.ts", ["1287"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\types.ts", ["1288"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\inventoryValueCalculationService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\mrpService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderCancellationService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderDeliveryDateChangeService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderQuantityChangeService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\productInventorySyncService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\productTypeBindingService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\IncrementalSyncService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\OfflineDataCache.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\RealtimeDataSyncService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\RealtimeSyncManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\SyncErrorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\WorkstationRealtimeService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\clearSchedulingData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\SameMoldPrioritySchedulingService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\SchedulingStateManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testSameMoldGrouping.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testSchedulingAlgorithm.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testWorkstationDistribution.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testWorkTimeCalculation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\verifySchedulingFix.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\__tests__\\SameMoldPrioritySchedulingService.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\validation\\OrderValidationService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\validation\\WorkstationDataConsistencyValidator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\workstation\\WorkstationUpdateService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\employeeStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\masterDataStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\productionStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\productionUIStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\sales\\useCustomerStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\sales\\useSalesCompositeStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\useProductionUIStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\warehouseStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\workTimeStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\__tests__\\useProductionUIStore.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\useAppStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\standardStoreInterface.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\architectureCompliance.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\batchNumberMigration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\businessRuleValidator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\dataTransformers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\errorHandlers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\productionOrderRules.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\statusTransitionManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\timestampGenerator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\transactionManager.ts", ["1289"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\calculations\\amountCalculator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\calculations\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\concurrencyControl.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\DataAccessComplianceChecker.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataAccessErrorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataModelConsistency.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataSyncVerification.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dateUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\duplicateExecutionPrevention.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formatters\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formatters\\statusFormatter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\orderStatusTransitions.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\htmlSanitizer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\inputValidator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\safeEventManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\testSchedulingBoardData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\UnifiedLogger.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validation\\workstationValidation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validationUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validators\\formValidators.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validators\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\workOrderGenerator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\workstationQueueCleaner.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\basic.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\businessRuleValidator.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\dataTransformers.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\errorHandlers.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\index.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\simple.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\statusTransitionManager.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\timestampGenerator.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\transactionManager.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\orderNumberValidation.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\batch\\BatchOperations.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\configuration\\SystemConfig.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\dataAccess\\DataAccessMonitoring.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\hooks\\useProductionDataAdapter.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\CrossModule.integration.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\DataAccess.integration.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\HookIntegration.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\OrderManagement.e2e.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\Phase4EndToEndTest.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\StateManagementIntegration.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\CachePerformanceComparison.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\DataAccess.performance.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\StateManagementPerformance.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\production\\ProductionReadinessTest.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\CacheIntegration.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\DataAccessManager.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\PerformanceOptimizer.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\ProductionOrderDataAccessService.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\WorkstationDataAccessService.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\events\\EventCommunicationOptimization.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\setup.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\DataAccessCompliantStateManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\LegacyCompatibilityAdapter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\UnifiedStateTransitionService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\unified\\UnifiedOrderStatusManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\performance.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\stateTransitionTypes.ts", ["1290"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\statusTransitionManagerDemo.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedCalculation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedIdManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedStateTransition.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\styles\\antdHelpers.ts", [], [], {"ruleId": "1291", "severity": 2, "message": "1292", "line": 482, "column": 32, "nodeType": "1293", "messageId": "1294", "suggestions": "1295"}, {"ruleId": "1291", "severity": 2, "message": "1292", "line": 482, "column": 36, "nodeType": "1293", "messageId": "1294", "suggestions": "1296"}, {"ruleId": "1291", "severity": 2, "message": "1292", "line": 482, "column": 40, "nodeType": "1293", "messageId": "1294", "suggestions": "1297"}, {"ruleId": "1291", "severity": 2, "message": "1292", "line": 482, "column": 44, "nodeType": "1293", "messageId": "1294", "suggestions": "1298"}, {"ruleId": "1299", "severity": 1, "message": "1300", "line": 133, "column": 6, "nodeType": "1301", "endLine": 133, "endColumn": 8, "suggestions": "1302"}, {"ruleId": "1299", "severity": 1, "message": "1303", "line": 84, "column": 6, "nodeType": "1301", "endLine": 84, "endColumn": 8, "suggestions": "1304"}, {"ruleId": "1305", "severity": 2, "message": "1306", "line": 526, "column": 19, "nodeType": "1307", "messageId": "1308", "endLine": 540, "endColumn": 32}, {"ruleId": "1309", "severity": 2, "message": "1310", "line": 37, "column": 6, "nodeType": "1311", "messageId": "1312", "endLine": 142, "endColumn": 3}, {"ruleId": "1309", "severity": 2, "message": "1310", "line": 159, "column": 67, "nodeType": "1311", "messageId": "1312", "endLine": 476, "endColumn": 3}, {"ruleId": "1299", "severity": 1, "message": "1313", "line": 366, "column": 6, "nodeType": "1301", "endLine": 366, "endColumn": 37, "suggestions": "1314"}, {"ruleId": "1291", "severity": 2, "message": "1292", "line": 313, "column": 33, "nodeType": "1293", "messageId": "1294", "suggestions": "1315"}, {"ruleId": "1291", "severity": 2, "message": "1292", "line": 313, "column": 37, "nodeType": "1293", "messageId": "1294", "suggestions": "1316"}, {"ruleId": "1291", "severity": 2, "message": "1292", "line": 284, "column": 27, "nodeType": "1293", "messageId": "1294", "suggestions": "1317"}, {"ruleId": "1291", "severity": 2, "message": "1292", "line": 284, "column": 31, "nodeType": "1293", "messageId": "1294", "suggestions": "1318"}, {"ruleId": "1291", "severity": 2, "message": "1292", "line": 287, "column": 27, "nodeType": "1293", "messageId": "1294", "suggestions": "1319"}, {"ruleId": "1291", "severity": 2, "message": "1292", "line": 287, "column": 36, "nodeType": "1293", "messageId": "1294", "suggestions": "1320"}, {"ruleId": "1299", "severity": 1, "message": "1321", "line": 341, "column": 6, "nodeType": "1301", "endLine": 341, "endColumn": 24, "suggestions": "1322"}, {"ruleId": "1299", "severity": 1, "message": "1323", "line": 413, "column": 6, "nodeType": "1301", "endLine": 413, "endColumn": 8, "suggestions": "1324"}, {"ruleId": "1299", "severity": 1, "message": "1325", "line": 314, "column": 6, "nodeType": "1301", "endLine": 314, "endColumn": 8, "suggestions": "1326"}, {"ruleId": "1299", "severity": 1, "message": "1327", "line": 151, "column": 6, "nodeType": "1301", "endLine": 151, "endColumn": 8, "suggestions": "1328"}, {"ruleId": "1299", "severity": 1, "message": "1329", "line": 166, "column": 6, "nodeType": "1301", "endLine": 166, "endColumn": 58, "suggestions": "1330"}, {"ruleId": "1299", "severity": 1, "message": "1331", "line": 57, "column": 6, "nodeType": "1301", "endLine": 57, "endColumn": 24, "suggestions": "1332"}, {"ruleId": "1299", "severity": 1, "message": "1333", "line": 157, "column": 6, "nodeType": "1301", "endLine": 157, "endColumn": 37, "suggestions": "1334"}, {"ruleId": "1299", "severity": 1, "message": "1335", "line": 330, "column": 6, "nodeType": "1301", "endLine": 330, "endColumn": 12, "suggestions": "1336"}, {"ruleId": "1299", "severity": 1, "message": "1337", "line": 185, "column": 6, "nodeType": "1301", "endLine": 185, "endColumn": 12, "suggestions": "1338"}, {"ruleId": "1299", "severity": 1, "message": "1339", "line": 228, "column": 6, "nodeType": "1301", "endLine": 228, "endColumn": 12, "suggestions": "1340"}, {"ruleId": "1299", "severity": 1, "message": "1341", "line": 235, "column": 6, "nodeType": "1301", "endLine": 235, "endColumn": 42, "suggestions": "1342"}, {"ruleId": "1343", "severity": 1, "message": "1344", "line": 278, "column": 1, "nodeType": "1345", "endLine": 289, "endColumn": 2}, {"ruleId": "1299", "severity": 1, "message": "1346", "line": 37, "column": 29, "nodeType": "1347", "endLine": 37, "endColumn": 40}, {"ruleId": "1299", "severity": 1, "message": "1348", "line": 138, "column": 6, "nodeType": "1301", "endLine": 138, "endColumn": 117, "suggestions": "1349"}, {"ruleId": "1299", "severity": 1, "message": "1350", "line": 233, "column": 6, "nodeType": "1301", "endLine": 233, "endColumn": 67, "suggestions": "1351"}, {"ruleId": "1352", "severity": 2, "message": "1353", "line": 317, "column": 5, "nodeType": "1347", "endLine": 317, "endColumn": 27}, {"ruleId": "1352", "severity": 2, "message": "1354", "line": 319, "column": 5, "nodeType": "1347", "endLine": 319, "endColumn": 29}, {"ruleId": "1352", "severity": 2, "message": "1355", "line": 321, "column": 5, "nodeType": "1347", "endLine": 321, "endColumn": 24}, {"ruleId": "1299", "severity": 1, "message": "1356", "line": 168, "column": 8, "nodeType": "1301", "endLine": 168, "endColumn": 22, "suggestions": "1357"}, {"ruleId": "1299", "severity": 1, "message": "1358", "line": 479, "column": 6, "nodeType": "1301", "endLine": 479, "endColumn": 27, "suggestions": "1359"}, {"ruleId": "1299", "severity": 1, "message": "1360", "line": 169, "column": 8, "nodeType": "1301", "endLine": 169, "endColumn": 30, "suggestions": "1361"}, {"ruleId": "1299", "severity": 1, "message": "1362", "line": 210, "column": 6, "nodeType": "1301", "endLine": 210, "endColumn": 50, "suggestions": "1363"}, {"ruleId": "1299", "severity": 1, "message": "1364", "line": 253, "column": 6, "nodeType": "1301", "endLine": 253, "endColumn": 62, "suggestions": "1365"}, {"ruleId": "1299", "severity": 1, "message": "1366", "line": 222, "column": 6, "nodeType": "1301", "endLine": 222, "endColumn": 115, "suggestions": "1367"}, {"ruleId": "1299", "severity": 1, "message": "1368", "line": 406, "column": 25, "nodeType": "1347", "endLine": 406, "endColumn": 32}, {"ruleId": "1299", "severity": 1, "message": "1369", "line": 218, "column": 20, "nodeType": "1347", "endLine": 218, "endColumn": 27}, {"ruleId": "1343", "severity": 1, "message": "1344", "line": 305, "column": 1, "nodeType": "1345", "endLine": 310, "endColumn": 2}, {"ruleId": "1343", "severity": 1, "message": "1344", "line": 236, "column": 1, "nodeType": "1345", "endLine": 238, "endColumn": 2}, {"ruleId": "1343", "severity": 1, "message": "1344", "line": 348, "column": 1, "nodeType": "1345", "endLine": 358, "endColumn": 2}, {"ruleId": "1343", "severity": 1, "message": "1344", "line": 486, "column": 1, "nodeType": "1345", "endLine": 492, "endColumn": 2}, {"ruleId": "1343", "severity": 1, "message": "1344", "line": 350, "column": 1, "nodeType": "1345", "endLine": 355, "endColumn": 2}, {"ruleId": "1370", "severity": 2, "message": "1371", "line": 386, "column": 10, "nodeType": "1372", "endLine": 386, "endColumn": 22}, {"ruleId": "1343", "severity": 1, "message": "1344", "line": 446, "column": 1, "nodeType": "1345", "endLine": 459, "endColumn": 2}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["1373", "1374", "1375", "1376"], ["1377", "1378", "1379", "1380"], ["1381", "1382", "1383", "1384"], ["1385", "1386", "1387", "1388"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadDepartments' and 'loadEmployees'. Either include them or remove the dependency array.", "ArrayExpression", ["1389"], "React Hook useEffect has a missing dependency: 'refreshProductModels'. Either include it or remove the dependency array.", ["1390"], "react/jsx-key", "Missing \"key\" prop for element in array", "JSXElement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "react/display-name", "Component definition is missing display name", "CallExpression", "noDisplayName", "React Hook useMemo has an unnecessary dependency: 'onStatusChange'. Either exclude it or remove the dependency array.", ["1391"], ["1392", "1393", "1394", "1395"], ["1396", "1397", "1398", "1399"], ["1400", "1401", "1402", "1403"], ["1404", "1405", "1406", "1407"], ["1408", "1409", "1410", "1411"], ["1412", "1413", "1414", "1415"], "React Hook useCallback has missing dependencies: 'message' and 'refreshData'. Either include them or remove the dependency array.", ["1416"], "React Hook useEffect has missing dependencies: 'message' and 'refreshData'. Either include them or remove the dependency array.", ["1417"], "React Hook useEffect has a missing dependency: 'refreshCustomers'. Either include it or remove the dependency array.", ["1418"], "React Hook useEffect has a missing dependency: 'loadInventoryData'. Either include it or remove the dependency array.", ["1419"], "React Hook useMemo has a missing dependency: 'config'. Either include it or remove the dependency array.", ["1420"], "React Hook useEffect has a missing dependency: 'updateDebugInfo'. Either include it or remove the dependency array.", ["1421"], "React Hook useEffect has a missing dependency: 'collectDebugInfo'. Either include it or remove the dependency array.", ["1422"], "React Hook useEffect has missing dependencies: 'form', 'generateOrderNumber', and 'loadInitialData'. Either include them or remove the dependency array.", ["1423"], "React Hook useEffect has a missing dependency: 'initializeForm'. Either include it or remove the dependency array.", ["1424"], "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["1425"], "React Hook useEffect has a missing dependency: 'filterProducts'. Either include it or remove the dependency array.", ["1426"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "Identifier", "React Hook useEffect has a missing dependency: 'listener'. Either include it or remove the dependency array.", ["1427"], "React Hook useEffect has a missing dependency: 'options'. Either include it or remove the dependency array.", ["1428"], "react-hooks/rules-of-hooks", "React Hook \"useWindowEventListener\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "React Hook \"useDocumentEventListener\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "React Hook \"useDOMEventListener\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "React Hook useCallback has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["1429"], "React Hook useEffect has missing dependencies: 'performance.dataLoadTime' and 'performance.renderTime'. Either include them or remove the dependency array.", ["1430"], "React Hook useCallback has missing dependencies: 'refreshOrders' and 'refreshWorkOrders'. Either include them or remove the dependency array.", ["1431"], "React Hook useCallback has a missing dependency: 'checkForChanges'. Either include it or remove the dependency array.", ["1432"], "React Hook useCallback has a missing dependency: 'disconnect'. Either include it or remove the dependency array.", ["1433"], "React Hook useCallback has missing dependencies: 'handleCacheInvalidation' and 'showDataChangeMessage'. Either include them or remove the dependency array.", ["1434"], "The ref value 'eventListenersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'eventListenersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "The ref value 'listenersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'listenersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "@next/next/no-assign-module-variable", "Do not assign to the variable `module`. See: https://nextjs.org/docs/messages/no-assign-module-variable", "VariableDeclaration", {"messageId": "1435", "data": "1436", "fix": "1437", "desc": "1438"}, {"messageId": "1435", "data": "1439", "fix": "1440", "desc": "1441"}, {"messageId": "1435", "data": "1442", "fix": "1443", "desc": "1444"}, {"messageId": "1435", "data": "1445", "fix": "1446", "desc": "1447"}, {"messageId": "1435", "data": "1448", "fix": "1449", "desc": "1438"}, {"messageId": "1435", "data": "1450", "fix": "1451", "desc": "1441"}, {"messageId": "1435", "data": "1452", "fix": "1453", "desc": "1444"}, {"messageId": "1435", "data": "1454", "fix": "1455", "desc": "1447"}, {"messageId": "1435", "data": "1456", "fix": "1457", "desc": "1438"}, {"messageId": "1435", "data": "1458", "fix": "1459", "desc": "1441"}, {"messageId": "1435", "data": "1460", "fix": "1461", "desc": "1444"}, {"messageId": "1435", "data": "1462", "fix": "1463", "desc": "1447"}, {"messageId": "1435", "data": "1464", "fix": "1465", "desc": "1438"}, {"messageId": "1435", "data": "1466", "fix": "1467", "desc": "1441"}, {"messageId": "1435", "data": "1468", "fix": "1469", "desc": "1444"}, {"messageId": "1435", "data": "1470", "fix": "1471", "desc": "1447"}, {"desc": "1472", "fix": "1473"}, {"desc": "1474", "fix": "1475"}, {"desc": "1476", "fix": "1477"}, {"messageId": "1435", "data": "1478", "fix": "1479", "desc": "1438"}, {"messageId": "1435", "data": "1480", "fix": "1481", "desc": "1441"}, {"messageId": "1435", "data": "1482", "fix": "1483", "desc": "1444"}, {"messageId": "1435", "data": "1484", "fix": "1485", "desc": "1447"}, {"messageId": "1435", "data": "1486", "fix": "1487", "desc": "1438"}, {"messageId": "1435", "data": "1488", "fix": "1489", "desc": "1441"}, {"messageId": "1435", "data": "1490", "fix": "1491", "desc": "1444"}, {"messageId": "1435", "data": "1492", "fix": "1493", "desc": "1447"}, {"messageId": "1435", "data": "1494", "fix": "1495", "desc": "1438"}, {"messageId": "1435", "data": "1496", "fix": "1497", "desc": "1441"}, {"messageId": "1435", "data": "1498", "fix": "1499", "desc": "1444"}, {"messageId": "1435", "data": "1500", "fix": "1501", "desc": "1447"}, {"messageId": "1435", "data": "1502", "fix": "1503", "desc": "1438"}, {"messageId": "1435", "data": "1504", "fix": "1505", "desc": "1441"}, {"messageId": "1435", "data": "1506", "fix": "1507", "desc": "1444"}, {"messageId": "1435", "data": "1508", "fix": "1509", "desc": "1447"}, {"messageId": "1435", "data": "1510", "fix": "1511", "desc": "1438"}, {"messageId": "1435", "data": "1512", "fix": "1513", "desc": "1441"}, {"messageId": "1435", "data": "1514", "fix": "1515", "desc": "1444"}, {"messageId": "1435", "data": "1516", "fix": "1517", "desc": "1447"}, {"messageId": "1435", "data": "1518", "fix": "1519", "desc": "1438"}, {"messageId": "1435", "data": "1520", "fix": "1521", "desc": "1441"}, {"messageId": "1435", "data": "1522", "fix": "1523", "desc": "1444"}, {"messageId": "1435", "data": "1524", "fix": "1525", "desc": "1447"}, {"desc": "1526", "fix": "1527"}, {"desc": "1528", "fix": "1529"}, {"desc": "1530", "fix": "1531"}, {"desc": "1532", "fix": "1533"}, {"desc": "1534", "fix": "1535"}, {"desc": "1536", "fix": "1537"}, {"desc": "1538", "fix": "1539"}, {"desc": "1540", "fix": "1541"}, {"desc": "1542", "fix": "1543"}, {"desc": "1544", "fix": "1545"}, {"desc": "1546", "fix": "1547"}, {"desc": "1548", "fix": "1549"}, {"desc": "1550", "fix": "1551"}, {"desc": "1552", "fix": "1553"}, {"desc": "1554", "fix": "1555"}, {"desc": "1556", "fix": "1557"}, {"desc": "1558", "fix": "1559"}, {"desc": "1560", "fix": "1561"}, {"desc": "1562", "fix": "1563"}, "replaceWithAlt", {"alt": "1564"}, {"range": "1565", "text": "1566"}, "Replace with `&quot;`.", {"alt": "1567"}, {"range": "1568", "text": "1569"}, "Replace with `&ldquo;`.", {"alt": "1570"}, {"range": "1571", "text": "1572"}, "Replace with `&#34;`.", {"alt": "1573"}, {"range": "1574", "text": "1575"}, "Replace with `&rdquo;`.", {"alt": "1564"}, {"range": "1576", "text": "1577"}, {"alt": "1567"}, {"range": "1578", "text": "1579"}, {"alt": "1570"}, {"range": "1580", "text": "1581"}, {"alt": "1573"}, {"range": "1582", "text": "1583"}, {"alt": "1564"}, {"range": "1584", "text": "1585"}, {"alt": "1567"}, {"range": "1586", "text": "1587"}, {"alt": "1570"}, {"range": "1588", "text": "1589"}, {"alt": "1573"}, {"range": "1590", "text": "1591"}, {"alt": "1564"}, {"range": "1592", "text": "1593"}, {"alt": "1567"}, {"range": "1594", "text": "1595"}, {"alt": "1570"}, {"range": "1596", "text": "1597"}, {"alt": "1573"}, {"range": "1598", "text": "1599"}, "Update the dependencies array to be: [loadDepartments, loadEmployees]", {"range": "1600", "text": "1601"}, "Update the dependencies array to be: [refreshProductModels]", {"range": "1602", "text": "1603"}, "Update the dependencies array to be: [onOrderDetail]", {"range": "1604", "text": "1605"}, {"alt": "1564"}, {"range": "1606", "text": "1607"}, {"alt": "1567"}, {"range": "1608", "text": "1609"}, {"alt": "1570"}, {"range": "1610", "text": "1611"}, {"alt": "1573"}, {"range": "1612", "text": "1613"}, {"alt": "1564"}, {"range": "1614", "text": "1615"}, {"alt": "1567"}, {"range": "1616", "text": "1617"}, {"alt": "1570"}, {"range": "1618", "text": "1619"}, {"alt": "1573"}, {"range": "1620", "text": "1621"}, {"alt": "1564"}, {"range": "1622", "text": "1623"}, {"alt": "1567"}, {"range": "1624", "text": "1625"}, {"alt": "1570"}, {"range": "1626", "text": "1627"}, {"alt": "1573"}, {"range": "1628", "text": "1629"}, {"alt": "1564"}, {"range": "1630", "text": "1631"}, {"alt": "1567"}, {"range": "1632", "text": "1633"}, {"alt": "1570"}, {"range": "1634", "text": "1635"}, {"alt": "1573"}, {"range": "1636", "text": "1637"}, {"alt": "1564"}, {"range": "1638", "text": "1639"}, {"alt": "1567"}, {"range": "1640", "text": "1641"}, {"alt": "1570"}, {"range": "1642", "text": "1643"}, {"alt": "1573"}, {"range": "1644", "text": "1645"}, {"alt": "1564"}, {"range": "1646", "text": "1647"}, {"alt": "1567"}, {"range": "1648", "text": "1649"}, {"alt": "1570"}, {"range": "1650", "text": "1651"}, {"alt": "1573"}, {"range": "1652", "text": "1653"}, "Update the dependencies array to be: [message, productionOrders.length, refreshData]", {"range": "1654", "text": "1655"}, "Update the dependencies array to be: [message, refreshData]", {"range": "1656", "text": "1657"}, "Update the dependencies array to be: [refreshCustomers]", {"range": "1658", "text": "1659"}, "Update the dependencies array to be: [loadInventoryData]", {"range": "1660", "text": "1661"}, "Update the dependencies array to be: [actionButtons, onClose, config, order]", {"range": "1662", "text": "1663"}, "Update the dependencies array to be: [debuggerInstance, updateDebugInfo]", {"range": "1664", "text": "1665"}, "Update the dependencies array to be: [visible, pendingOrders.length, collectDebugInfo]", {"range": "1666", "text": "1667"}, "Update the dependencies array to be: [form, generateOrderNumber, loadInitialData, open]", {"range": "1668", "text": "1669"}, "Update the dependencies array to be: [initializeForm, open]", {"range": "1670", "text": "1671"}, "Update the dependencies array to be: [loadProducts, open]", {"range": "1672", "text": "1673"}, "Update the dependencies array to be: [searchText, statusFilter, products, filterProducts]", {"range": "1674", "text": "1675"}, "Update the dependencies array to be: [listenerId, opts.autoCleanup, opts.trackMemory, opts.useNewManager, opts.priority, opts.timeout, isMonitoring, listener]", {"range": "1676", "text": "1677"}, "Update the dependencies array to be: [element, eventType, handler, opts.trackMemory, isMonitoring, options]", {"range": "1678", "text": "1679"}, "Update the dependencies array to be: [onDataChange, refresh]", {"range": "1680", "text": "1681"}, "Update the dependencies array to be: [onPerformanceUpdate, performance.dataLoadTime, performance.renderTime]", {"range": "1682", "text": "1683"}, "Update the dependencies array to be: [onRealTimeDataChange, refreshOrders, refreshWorkOrders]", {"range": "1684", "text": "1685"}, "Update the dependencies array to be: [enabled, pollingInterval, checkForChanges, handleDataChange]", {"range": "1686", "text": "1687"}, "Update the dependencies array to be: [disconnect, syncMethod, connectWebSocket, connectSSE, startPolling]", {"range": "1688", "text": "1689"}, "Update the dependencies array to be: [isInitialized, eventTypes, onConnectionChange, autoInvalidateCache, onDataChange, showSyncMessages, handleCacheInvalidation, showDataChangeMessage, onError]", {"range": "1690", "text": "1691"}, "&quot;", [15030, 15054], "2. 审核订单，状态从&quot;未审核\"变更为\"已审核\"", "&ldquo;", [15030, 15054], "2. 审核订单，状态从&ldquo;未审核\"变更为\"已审核\"", "&#34;", [15030, 15054], "2. 审核订单，状态从&#34;未审核\"变更为\"已审核\"", "&rdquo;", [15030, 15054], "2. 审核订单，状态从&rdquo;未审核\"变更为\"已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核&quot;变更为\"已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核&ldquo;变更为\"已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核&#34;变更为\"已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核&rdquo;变更为\"已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为&quot;已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为&ldquo;已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为&#34;已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为&rdquo;已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为\"已审核&quot;", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为\"已审核&ldquo;", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为\"已审核&#34;", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为\"已审核&rdquo;", [4120, 4122], "[loadDepartments, loadEmployees]", [2177, 2179], "[refreshProductModels]", [10831, 10862], "[onOrderDetail]", [9234, 9253], "排程完成后，工单状态将变更为&quot;已排程\"", [9234, 9253], "排程完成后，工单状态将变更为&ldquo;已排程\"", [9234, 9253], "排程完成后，工单状态将变更为&#34;已排程\"", [9234, 9253], "排程完成后，工单状态将变更为&rdquo;已排程\"", [9234, 9253], "排程完成后，工单状态将变更为\"已排程&quot;", [9234, 9253], "排程完成后，工单状态将变更为\"已排程&ldquo;", [9234, 9253], "排程完成后，工单状态将变更为\"已排程&#34;", [9234, 9253], "排程完成后，工单状态将变更为\"已排程&rdquo;", [7406, 7420], "• 工单状态：仍为&quot;待开始\"", [7406, 7420], "• 工单状态：仍为&ldquo;待开始\"", [7406, 7420], "• 工单状态：仍为&#34;待开始\"", [7406, 7420], "• 工单状态：仍为&rdquo;待开始\"", [7406, 7420], "• 工单状态：仍为\"待开始&quot;", [7406, 7420], "• 工单状态：仍为\"待开始&ldquo;", [7406, 7420], "• 工单状态：仍为\"待开始&#34;", [7406, 7420], "• 工单状态：仍为\"待开始&rdquo;", [7544, 7568], "点击&quot;确认应用排程结果\"后才会正式应用所有更改。", [7544, 7568], "点击&ldquo;确认应用排程结果\"后才会正式应用所有更改。", [7544, 7568], "点击&#34;确认应用排程结果\"后才会正式应用所有更改。", [7544, 7568], "点击&rdquo;确认应用排程结果\"后才会正式应用所有更改。", [7544, 7568], "点击\"确认应用排程结果&quot;后才会正式应用所有更改。", [7544, 7568], "点击\"确认应用排程结果&ldquo;后才会正式应用所有更改。", [7544, 7568], "点击\"确认应用排程结果&#34;后才会正式应用所有更改。", [7544, 7568], "点击\"确认应用排程结果&rdquo;后才会正式应用所有更改。", [12205, 12223], "[message, productionOrders.length, refreshData]", [14085, 14087], "[message, refreshData]", [8109, 8111], "[refreshCustomers]", [4702, 4704], "[loadInventoryData]", [4482, 4534], "[actionButtons, onClose, config, order]", [1790, 1808], "[debuggerInstance, updateDebugInfo]", [4428, 4459], "[visible, pendingOrders.length, collectDebugInfo]", [9130, 9136], "[form, generateOrderNumber, loadInitialData, open]", [5325, 5331], "[initializeForm, open]", [5920, 5926], "[loadProducts, open]", [6005, 6041], "[searchText, statusFilter, products, filterProducts]", [4477, 4588], "[listenerId, opts.autoCleanup, opts.trackMemory, opts.useNewManager, opts.priority, opts.timeout, isMonitoring, listener]", [6859, 6920], "[element, eventType, handler, opts.trackMemory, isMonitoring, options]", [5170, 5184], "[on<PERSON><PERSON><PERSON><PERSON><PERSON>, refresh]", [14707, 14728], "[onPerformanceUpdate, performance.dataLoadTime, performance.renderTime]", [5634, 5656], "[onRealTimeDataChange, refreshOrders, refreshWorkOrders]", [5279, 5323], "[enabled, pollingInterval, checkForChanges, handleDataChange]", [6229, 6285], "[disconnect, syncMethod, connectWebSocket, connectSSE, startPolling]", [6034, 6143], "[isInitialized, eventTypes, onConnectionChange, autoInvalidateCache, onDataChange, showSyncMessages, handleCacheInvalidation, showDataChangeMessage, onError]"]