[{"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\admin\\data-management\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\admin\\integration-test\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\api\\products\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\dashboard\\layout.tsx": "4", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\dashboard\\page.tsx": "5", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\data-status\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\finance\\layout.tsx": "7", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\finance\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\employees\\components\\DepartmentManagement.tsx": "10", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\employees\\page.tsx": "11", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\layout.tsx": "12", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\materials\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\product-models\\page.tsx": "15", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\page.tsx": "16", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\procurement\\layout.tsx": "17", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\procurement\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\cost-calculation\\page.tsx": "19", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\cost-calculation\\utils\\costCalculationEngine.ts": "20", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\hot-press-board\\page.tsx": "21", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\hot-press-board\\utils\\hotPressEngine.ts": "22", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\layout.tsx": "23", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionOrderDetailModal.tsx": "24", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionOrdersList.tsx": "25", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionSchedulingBoard.tsx": "26", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionWorkOrdersList.tsx": "27", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\SchedulingConfigModal.tsx": "28", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\SchedulingResultModal.tsx": "29", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkstationCard.tsx": "30", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkstationManagementTab.tsx": "31", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkTimeManagementTab.tsx": "32", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\page.tsx": "33", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\page.tsx": "34", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\queue-cleaner\\page.tsx": "35", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\services\\eventDrivenIntegration.ts": "36", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\services\\improvedIntegrationService.ts": "37", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\work-report\\page.tsx": "38", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\after-sales\\page.tsx": "39", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\analytics\\page.tsx": "40", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\credit\\page.tsx": "41", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\customers\\page.tsx": "42", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\delivery\\page.tsx": "43", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\invoices\\page.tsx": "44", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\layout.tsx": "45", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\components\\AddProductModal.tsx": "46", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\components\\ProductionOrderTable.tsx": "47", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\hooks\\useProductionOrders.ts": "48", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\page.tsx": "49", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\payments\\page.tsx": "50", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\layout.tsx": "51", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\materials\\page.tsx": "52", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\page.tsx": "53", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\products\\page.tsx": "54", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\BaseOrderDetailModal.tsx": "55", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\configs\\productionOrderConfig.tsx": "56", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\configs\\salesOrderConfig.tsx": "57", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\fieldRenderers.tsx": "58", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\index.ts": "59", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\types.ts": "60", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\ProductWeightDisplay.tsx": "61", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\SyncStatusIndicator.tsx": "62", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\UnifiedTagRenderer.tsx": "63", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\VirtualizedTable.tsx": "64", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\EventCommunicationMonitor.tsx": "65", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\MemoryMonitorCard.tsx": "66", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\MoldNumberDebugger.tsx": "67", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\FormSelect.tsx": "68", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\Header.tsx": "69", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\MainLayout.tsx": "70", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\Sidebar.tsx": "71", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\MessageQueueDebugger.tsx": "72", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\production\\RealTimeSyncStatus.tsx": "73", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\providers\\AntdProvider.tsx": "74", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\providers\\DataInitializationProvider.tsx": "75", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\SafeSelect.tsx": "76", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\AddOrderModal.tsx": "77", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductCreateModal.tsx": "78", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductInfoSection.tsx": "79", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductSelectModal.tsx": "80", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\__tests__\\OrderChangeModal.test.tsx": "81", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\shared\\ProductForm.tsx": "82", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\config\\dataAccessConfig.ts": "83", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\config\\validationRules.ts": "84", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\constants\\productionConstants.ts": "85", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\data\\seedData.ts": "86", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useDataAccessMonitor.ts": "87", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useDebouncedCallback.ts": "88", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useEventListener.ts": "89", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useFormValueFixer.ts": "90", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useOptimizedProductionData.ts": "91", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useOrdersData.ts": "92", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionDataAdapter.ts": "93", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionDataSync.ts": "94", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionErrorHandler.ts": "95", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionOperations.ts": "96", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionRealTimeSync.ts": "97", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionStatistics.ts": "98", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useTimer.ts": "99", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useWarehousePermissions.ts": "100", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useWorkstationRealtime.ts": "101", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\__tests__\\productionHooks.test.ts": "102", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\__tests__\\useOptimizedProductionData.test.ts": "103", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\BusinessIdGenerator.ts": "104", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\CacheDebugger.ts": "105", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\strategies.ts": "106", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\types.ts": "107", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\CostCalculationDataAccessService.ts": "108", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\CustomerDataAccessService.ts": "109", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessLayer.ts": "110", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessManager.ts": "111", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessPerformanceMonitor.ts": "112", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataChangeNotifier.ts": "113", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataConsistencyService.ts": "114", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataSyncService.ts": "115", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\EmployeeDataAccessService.ts": "116", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\InventoryDataAccessService.ts": "117", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\OrderDataAccessService.ts": "118", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\PerformanceOptimizer.ts": "119", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductDataAccessService.ts": "120", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductionOrderDataAccessService.ts": "121", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductionWorkOrderDataAccessService.ts": "122", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkstationDataAccessService.ts": "123", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkstationStorage.ts": "124", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkTimeDataAccessService.ts": "125", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\__tests__\\DataAccessManager.test.ts": "126", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataInitializationService.ts": "127", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataModelSyncService.ts": "128", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventCommunicationDebugger.ts": "129", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventErrorHandler.ts": "130", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventListenerManager.ts": "131", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\strategies.ts": "132", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\types.ts": "133", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\inventoryValueCalculationService.ts": "134", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\mrpService.ts": "135", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderCancellationService.ts": "136", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderDeliveryDateChangeService.ts": "137", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderQuantityChangeService.ts": "138", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\productInventorySyncService.ts": "139", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\productTypeBindingService.ts": "140", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\IncrementalSyncService.ts": "141", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\OfflineDataCache.ts": "142", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\RealtimeDataSyncService.ts": "143", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\RealtimeSyncManager.ts": "144", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\SyncErrorHandler.ts": "145", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\WorkstationRealtimeService.ts": "146", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\clearSchedulingData.ts": "147", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\SameMoldPrioritySchedulingService.ts": "148", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\SchedulingStateManager.ts": "149", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testSameMoldGrouping.ts": "150", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testSchedulingAlgorithm.ts": "151", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testWorkstationDistribution.ts": "152", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testWorkTimeCalculation.ts": "153", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\verifySchedulingFix.ts": "154", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\__tests__\\SameMoldPrioritySchedulingService.test.ts": "155", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\validation\\OrderValidationService.ts": "156", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\validation\\WorkstationDataConsistencyValidator.ts": "157", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\workstation\\WorkstationUpdateService.ts": "158", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\employeeStore.ts": "159", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\masterDataStore.ts": "160", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\productionStore.ts": "161", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\productionUIStore.ts": "162", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\sales\\useCustomerStore.ts": "163", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\sales\\useSalesCompositeStore.ts": "164", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\useProductionUIStore.ts": "165", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\warehouseStore.ts": "166", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\workTimeStore.ts": "167", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\__tests__\\useProductionUIStore.test.ts": "168", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\useAppStore.ts": "169", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\index.ts": "170", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\standardStoreInterface.ts": "171", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\architectureCompliance.ts": "172", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\batchNumberMigration.ts": "173", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\businessRuleValidator.ts": "174", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\dataTransformers.ts": "175", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\errorHandlers.ts": "176", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\index.ts": "177", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\productionOrderRules.ts": "178", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\statusTransitionManager.ts": "179", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\timestampGenerator.ts": "180", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\transactionManager.ts": "181", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\calculations\\amountCalculator.ts": "182", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\calculations\\index.ts": "183", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\concurrencyControl.ts": "184", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\DataAccessComplianceChecker.ts": "185", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataAccessErrorHandler.ts": "186", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataModelConsistency.ts": "187", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataSyncVerification.ts": "188", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dateUtils.ts": "189", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\duplicateExecutionPrevention.ts": "190", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formatters\\index.ts": "191", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formatters\\statusFormatter.tsx": "192", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formUtils.ts": "193", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\index.ts": "194", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\orderStatusTransitions.ts": "195", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\htmlSanitizer.ts": "196", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\inputValidator.ts": "197", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\safeEventManager.ts": "198", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\testSchedulingBoardData.ts": "199", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\UnifiedLogger.ts": "200", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validation\\workstationValidation.ts": "201", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validationUtils.ts": "202", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validators\\formValidators.ts": "203", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validators\\index.ts": "204", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\workOrderGenerator.ts": "205", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\workstationQueueCleaner.ts": "206", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\basic.test.ts": "207", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\businessRuleValidator.test.ts": "208", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\dataTransformers.test.ts": "209", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\errorHandlers.test.ts": "210", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\index.test.ts": "211", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\simple.test.ts": "212", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\statusTransitionManager.test.ts": "213", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\timestampGenerator.test.ts": "214", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\transactionManager.test.ts": "215", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\orderNumberValidation.test.ts": "216", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\batch\\BatchOperations.test.ts": "217", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\configuration\\SystemConfig.test.ts": "218", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\dataAccess\\DataAccessMonitoring.test.ts": "219", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\hooks\\useProductionDataAdapter.test.ts": "220", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\CrossModule.integration.test.ts": "221", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\DataAccess.integration.test.ts": "222", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\HookIntegration.test.tsx": "223", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\OrderManagement.e2e.test.ts": "224", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\Phase4EndToEndTest.test.ts": "225", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\StateManagementIntegration.test.tsx": "226", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\CachePerformanceComparison.test.ts": "227", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\DataAccess.performance.test.ts": "228", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\StateManagementPerformance.test.ts": "229", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\production\\ProductionReadinessTest.test.ts": "230", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\CacheIntegration.test.ts": "231", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\DataAccessManager.test.ts": "232", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\PerformanceOptimizer.test.ts": "233", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\ProductionOrderDataAccessService.test.ts": "234", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\WorkstationDataAccessService.test.ts": "235", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\events\\EventCommunicationOptimization.test.ts": "236", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\setup.ts": "237", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\DataAccessCompliantStateManager.ts": "238", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\LegacyCompatibilityAdapter.ts": "239", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\UnifiedStateTransitionService.ts": "240", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\unified\\UnifiedOrderStatusManager.ts": "241", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\performance.d.ts": "242", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\stateTransitionTypes.ts": "243", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\statusTransitionManagerDemo.ts": "244", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedCalculation.ts": "245", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedIdManager.ts": "246", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedStateTransition.ts": "247", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\styles\\antdHelpers.ts": "248", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\styles\\constants.ts": "249", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\styles\\theme.ts": "250", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\css-modules.d.ts": "251", "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\styles\\__tests__\\antdHelpers.test.ts": "252"}, {"size": 9784, "mtime": 1753955013146, "results": "253", "hashOfConfig": "254"}, {"size": 17066, "mtime": 1753110188437, "results": "255", "hashOfConfig": "254"}, {"size": 10910, "mtime": 1753110281265, "results": "256", "hashOfConfig": "254"}, {"size": 200, "mtime": 1750663972000, "results": "257", "hashOfConfig": "254"}, {"size": 9577, "mtime": 1753954867890, "results": "258", "hashOfConfig": "254"}, {"size": 7679, "mtime": 1753110359903, "results": "259", "hashOfConfig": "254"}, {"size": 198, "mtime": 1750664248000, "results": "260", "hashOfConfig": "254"}, {"size": 13303, "mtime": 1752636568070, "results": "261", "hashOfConfig": "254"}, {"size": 687, "mtime": 1753462581535, "results": "262", "hashOfConfig": "254"}, {"size": 11936, "mtime": 1752636568072, "results": "263", "hashOfConfig": "254"}, {"size": 32672, "mtime": 1753596544526, "results": "264", "hashOfConfig": "254"}, {"size": 201, "mtime": 1750676474000, "results": "265", "hashOfConfig": "254"}, {"size": 14901, "mtime": 1753597860953, "results": "266", "hashOfConfig": "254"}, {"size": 5389, "mtime": 1750676500000, "results": "267", "hashOfConfig": "254"}, {"size": 31923, "mtime": 1753597668284, "results": "268", "hashOfConfig": "254"}, {"size": 135, "mtime": 1750663842000, "results": "269", "hashOfConfig": "254"}, {"size": 202, "mtime": 1750664068000, "results": "270", "hashOfConfig": "254"}, {"size": 9130, "mtime": 1752636568076, "results": "271", "hashOfConfig": "254"}, {"size": 26876, "mtime": 1752676456709, "results": "272", "hashOfConfig": "254"}, {"size": 11598, "mtime": 1750783880000, "results": "273", "hashOfConfig": "254"}, {"size": 22365, "mtime": 1752676485529, "results": "274", "hashOfConfig": "254"}, {"size": 6532, "mtime": 1752636638340, "results": "275", "hashOfConfig": "254"}, {"size": 201, "mtime": 1750664192000, "results": "276", "hashOfConfig": "254"}, {"size": 839, "mtime": 1753589546202, "results": "277", "hashOfConfig": "254"}, {"size": 14643, "mtime": 1753673540477, "results": "278", "hashOfConfig": "254"}, {"size": 31861, "mtime": 1753767020331, "results": "279", "hashOfConfig": "254"}, {"size": 21099, "mtime": 1753960067381, "results": "280", "hashOfConfig": "254"}, {"size": 10580, "mtime": 1753251806457, "results": "281", "hashOfConfig": "254"}, {"size": 13053, "mtime": 1752683170218, "results": "282", "hashOfConfig": "254"}, {"size": 5641, "mtime": 1753959623707, "results": "283", "hashOfConfig": "254"}, {"size": 36999, "mtime": 1753959687438, "results": "284", "hashOfConfig": "254"}, {"size": 20200, "mtime": 1753959780375, "results": "285", "hashOfConfig": "254"}, {"size": 41930, "mtime": 1753959791345, "results": "286", "hashOfConfig": "254"}, {"size": 207, "mtime": 1753110003286, "results": "287", "hashOfConfig": "254"}, {"size": 9122, "mtime": 1753111302759, "results": "288", "hashOfConfig": "254"}, {"size": 7409, "mtime": 1753111563102, "results": "289", "hashOfConfig": "254"}, {"size": 7355, "mtime": 1753111627741, "results": "290", "hashOfConfig": "254"}, {"size": 13186, "mtime": 1753112108584, "results": "291", "hashOfConfig": "254"}, {"size": 32932, "mtime": 1751471092404, "results": "292", "hashOfConfig": "254"}, {"size": 19721, "mtime": 1751519299779, "results": "293", "hashOfConfig": "254"}, {"size": 27895, "mtime": 1751907540694, "results": "294", "hashOfConfig": "254"}, {"size": 29281, "mtime": 1753596711203, "results": "295", "hashOfConfig": "254"}, {"size": 34635, "mtime": 1751624415640, "results": "296", "hashOfConfig": "254"}, {"size": 32378, "mtime": 1751520259608, "results": "297", "hashOfConfig": "254"}, {"size": 196, "mtime": 1750664022000, "results": "298", "hashOfConfig": "254"}, {"size": 6942, "mtime": 1753959960146, "results": "299", "hashOfConfig": "254"}, {"size": 9615, "mtime": 1752130586035, "results": "300", "hashOfConfig": "254"}, {"size": 5360, "mtime": 1752636568003, "results": "301", "hashOfConfig": "254"}, {"size": 50078, "mtime": 1753959856051, "results": "302", "hashOfConfig": "254"}, {"size": 23839, "mtime": 1751947848231, "results": "303", "hashOfConfig": "254"}, {"size": 200, "mtime": 1750664130000, "results": "304", "hashOfConfig": "254"}, {"size": 30710, "mtime": 1751471092422, "results": "305", "hashOfConfig": "254"}, {"size": 12125, "mtime": 1751523982521, "results": "306", "hashOfConfig": "254"}, {"size": 25330, "mtime": 1753595901403, "results": "307", "hashOfConfig": "254"}, {"size": 6953, "mtime": 1753425443052, "results": "308", "hashOfConfig": "254"}, {"size": 6420, "mtime": 1753425502395, "results": "309", "hashOfConfig": "254"}, {"size": 10388, "mtime": 1753425659050, "results": "310", "hashOfConfig": "254"}, {"size": 5689, "mtime": 1753425387183, "results": "311", "hashOfConfig": "254"}, {"size": 610, "mtime": 1753425459068, "results": "312", "hashOfConfig": "254"}, {"size": 4056, "mtime": 1753590154028, "results": "313", "hashOfConfig": "254"}, {"size": 6634, "mtime": 1753683040589, "results": "314", "hashOfConfig": "254"}, {"size": 5985, "mtime": 1752636638473, "results": "315", "hashOfConfig": "254"}, {"size": 10019, "mtime": 1753687909984, "results": "316", "hashOfConfig": "254"}, {"size": 12022, "mtime": 1753590236232, "results": "317", "hashOfConfig": "254"}, {"size": 11262, "mtime": 1753590395478, "results": "318", "hashOfConfig": "254"}, {"size": 7352, "mtime": 1753590431356, "results": "319", "hashOfConfig": "254"}, {"size": 8051, "mtime": 1751960054721, "results": "320", "hashOfConfig": "254"}, {"size": 4042, "mtime": 1753110084804, "results": "321", "hashOfConfig": "254"}, {"size": 4899, "mtime": 1753954670655, "results": "322", "hashOfConfig": "254"}, {"size": 1362, "mtime": 1753954505118, "results": "323", "hashOfConfig": "254"}, {"size": 8637, "mtime": 1753954602820, "results": "324", "hashOfConfig": "254"}, {"size": 8979, "mtime": 1752124680580, "results": "325", "hashOfConfig": "254"}, {"size": 10356, "mtime": 1753116519437, "results": "326", "hashOfConfig": "254"}, {"size": 1809, "mtime": 1752934163849, "results": "327", "hashOfConfig": "254"}, {"size": 4054, "mtime": 1752931470909, "results": "328", "hashOfConfig": "254"}, {"size": 2815, "mtime": 1753116837300, "results": "329", "hashOfConfig": "254"}, {"size": 15798, "mtime": 1753597147530, "results": "330", "hashOfConfig": "254"}, {"size": 18626, "mtime": 1752683812925, "results": "331", "hashOfConfig": "254"}, {"size": 11005, "mtime": 1753270264271, "results": "332", "hashOfConfig": "254"}, {"size": 11506, "mtime": 1752687579634, "results": "333", "hashOfConfig": "254"}, {"size": 5510, "mtime": 1753078696827, "results": "334", "hashOfConfig": "254"}, {"size": 13582, "mtime": 1752202638541, "results": "335", "hashOfConfig": "254"}, {"size": 4739, "mtime": 1753590910292, "results": "336", "hashOfConfig": "254"}, {"size": 7685, "mtime": 1753591071270, "results": "337", "hashOfConfig": "254"}, {"size": 1496, "mtime": 1753334284832, "results": "338", "hashOfConfig": "254"}, {"size": 22430, "mtime": 1752636638344, "results": "339", "hashOfConfig": "254"}, {"size": 5566, "mtime": 1753591186472, "results": "340", "hashOfConfig": "254"}, {"size": 4555, "mtime": 1753591512966, "results": "341", "hashOfConfig": "254"}, {"size": 12669, "mtime": 1753591597013, "results": "342", "hashOfConfig": "254"}, {"size": 2548, "mtime": 1752636568007, "results": "343", "hashOfConfig": "254"}, {"size": 16200, "mtime": 1753591898082, "results": "344", "hashOfConfig": "254"}, {"size": 7274, "mtime": 1753462713838, "results": "345", "hashOfConfig": "254"}, {"size": 17272, "mtime": 1753183526475, "results": "346", "hashOfConfig": "254"}, {"size": 9257, "mtime": 1753119081974, "results": "347", "hashOfConfig": "254"}, {"size": 8438, "mtime": 1753119255181, "results": "348", "hashOfConfig": "254"}, {"size": 14458, "mtime": 1753119734439, "results": "349", "hashOfConfig": "254"}, {"size": 12749, "mtime": 1753346402967, "results": "350", "hashOfConfig": "254"}, {"size": 9417, "mtime": 1753120263369, "results": "351", "hashOfConfig": "254"}, {"size": 10202, "mtime": 1753592123980, "results": "352", "hashOfConfig": "254"}, {"size": 4411, "mtime": 1751526108817, "results": "353", "hashOfConfig": "254"}, {"size": 10015, "mtime": 1753120524778, "results": "354", "hashOfConfig": "254"}, {"size": 10810, "mtime": 1753183656800, "results": "355", "hashOfConfig": "254"}, {"size": 12052, "mtime": 1752598511992, "results": "356", "hashOfConfig": "254"}, {"size": 9026, "mtime": 1753025744693, "results": "357", "hashOfConfig": "254"}, {"size": 9903, "mtime": 1753592257121, "results": "358", "hashOfConfig": "254"}, {"size": 8607, "mtime": 1753426737026, "results": "359", "hashOfConfig": "254"}, {"size": 4874, "mtime": 1753447681518, "results": "360", "hashOfConfig": "254"}, {"size": 16889, "mtime": 1753294654982, "results": "361", "hashOfConfig": "254"}, {"size": 10679, "mtime": 1753343790701, "results": "362", "hashOfConfig": "254"}, {"size": 9946, "mtime": 1753332732442, "results": "363", "hashOfConfig": "254"}, {"size": 80655, "mtime": 1753936993496, "results": "364", "hashOfConfig": "254"}, {"size": 17353, "mtime": 1753592493225, "results": "365", "hashOfConfig": "254"}, {"size": 20235, "mtime": 1753376754066, "results": "366", "hashOfConfig": "254"}, {"size": 11053, "mtime": 1753295847419, "results": "367", "hashOfConfig": "254"}, {"size": 15293, "mtime": 1753295924820, "results": "368", "hashOfConfig": "254"}, {"size": 12086, "mtime": 1753344120473, "results": "369", "hashOfConfig": "254"}, {"size": 14671, "mtime": 1753592558658, "results": "370", "hashOfConfig": "254"}, {"size": 17319, "mtime": 1753592637902, "results": "371", "hashOfConfig": "254"}, {"size": 11970, "mtime": 1753592806709, "results": "372", "hashOfConfig": "254"}, {"size": 13288, "mtime": 1753343385089, "results": "373", "hashOfConfig": "254"}, {"size": 29284, "mtime": 1753344449529, "results": "374", "hashOfConfig": "254"}, {"size": 30723, "mtime": 1753637955696, "results": "375", "hashOfConfig": "254"}, {"size": 31941, "mtime": 1753687446307, "results": "376", "hashOfConfig": "254"}, {"size": 12356, "mtime": 1753184660310, "results": "377", "hashOfConfig": "254"}, {"size": 17699, "mtime": 1753296145425, "results": "378", "hashOfConfig": "254"}, {"size": 11145, "mtime": 1753346287058, "results": "379", "hashOfConfig": "254"}, {"size": 10920, "mtime": 1752932955093, "results": "380", "hashOfConfig": "254"}, {"size": 9026, "mtime": 1753129802023, "results": "381", "hashOfConfig": "254"}, {"size": 13279, "mtime": 1753432845142, "results": "382", "hashOfConfig": "254"}, {"size": 21084, "mtime": 1753593098613, "results": "383", "hashOfConfig": "254"}, {"size": 16488, "mtime": 1753432480423, "results": "384", "hashOfConfig": "254"}, {"size": 11492, "mtime": 1753432218773, "results": "385", "hashOfConfig": "254"}, {"size": 7537, "mtime": 1753448215808, "results": "386", "hashOfConfig": "254"}, {"size": 10992, "mtime": 1753129887123, "results": "387", "hashOfConfig": "254"}, {"size": 17637, "mtime": 1753182767916, "results": "388", "hashOfConfig": "254"}, {"size": 9842, "mtime": 1753278708677, "results": "389", "hashOfConfig": "254"}, {"size": 17349, "mtime": 1753163754987, "results": "390", "hashOfConfig": "254"}, {"size": 9966, "mtime": 1753279011742, "results": "391", "hashOfConfig": "254"}, {"size": 14078, "mtime": 1753164171559, "results": "392", "hashOfConfig": "254"}, {"size": 14251, "mtime": 1752636568038, "results": "393", "hashOfConfig": "254"}, {"size": 9863, "mtime": 1752636568040, "results": "394", "hashOfConfig": "254"}, {"size": 8213, "mtime": 1752931368309, "results": "395", "hashOfConfig": "254"}, {"size": 10570, "mtime": 1753258490583, "results": "396", "hashOfConfig": "254"}, {"size": 8784, "mtime": 1752636638375, "results": "397", "hashOfConfig": "254"}, {"size": 7448, "mtime": 1752645059013, "results": "398", "hashOfConfig": "254"}, {"size": 9898, "mtime": 1752940477480, "results": "399", "hashOfConfig": "254"}, {"size": 7314, "mtime": 1753203286040, "results": "400", "hashOfConfig": "254"}, {"size": 45354, "mtime": 1753637091920, "results": "401", "hashOfConfig": "254"}, {"size": 7074, "mtime": 1753088327988, "results": "402", "hashOfConfig": "254"}, {"size": 5554, "mtime": 1753252196090, "results": "403", "hashOfConfig": "254"}, {"size": 6964, "mtime": 1753172024370, "results": "404", "hashOfConfig": "254"}, {"size": 4988, "mtime": 1753172863387, "results": "405", "hashOfConfig": "254"}, {"size": 3659, "mtime": 1753174191502, "results": "406", "hashOfConfig": "254"}, {"size": 4697, "mtime": 1753089147119, "results": "407", "hashOfConfig": "254"}, {"size": 13434, "mtime": 1752266933158, "results": "408", "hashOfConfig": "254"}, {"size": 20714, "mtime": 1753593288555, "results": "409", "hashOfConfig": "254"}, {"size": 11259, "mtime": 1753203314465, "results": "410", "hashOfConfig": "254"}, {"size": 16808, "mtime": 1753370080981, "results": "411", "hashOfConfig": "254"}, {"size": 7123, "mtime": 1753348031690, "results": "412", "hashOfConfig": "254"}, {"size": 9559, "mtime": 1753347069671, "results": "413", "hashOfConfig": "254"}, {"size": 13815, "mtime": 1753347205084, "results": "414", "hashOfConfig": "254"}, {"size": 12010, "mtime": 1752599799373, "results": "415", "hashOfConfig": "254"}, {"size": 10587, "mtime": 1753593348653, "results": "416", "hashOfConfig": "254"}, {"size": 14473, "mtime": 1753169826753, "results": "417", "hashOfConfig": "254"}, {"size": 15378, "mtime": 1753347175520, "results": "418", "hashOfConfig": "254"}, {"size": 19819, "mtime": 1753162490093, "results": "419", "hashOfConfig": "254"}, {"size": 9205, "mtime": 1753207611478, "results": "420", "hashOfConfig": "254"}, {"size": 13502, "mtime": 1752598565747, "results": "421", "hashOfConfig": "254"}, {"size": 1449, "mtime": 1750663890000, "results": "422", "hashOfConfig": "254"}, {"size": 77504, "mtime": 1753934336265, "results": "423", "hashOfConfig": "254"}, {"size": 5331, "mtime": 1752199782988, "results": "424", "hashOfConfig": "254"}, {"size": 9878, "mtime": 1753593489808, "results": "425", "hashOfConfig": "254"}, {"size": 5751, "mtime": 1753162734635, "results": "426", "hashOfConfig": "254"}, {"size": 14988, "mtime": 1753276559106, "results": "427", "hashOfConfig": "254"}, {"size": 12587, "mtime": 1753942428136, "results": "428", "hashOfConfig": "254"}, {"size": 13280, "mtime": 1753943513983, "results": "429", "hashOfConfig": "254"}, {"size": 2768, "mtime": 1753672005552, "results": "430", "hashOfConfig": "254"}, {"size": 8938, "mtime": 1753895390153, "results": "431", "hashOfConfig": "254"}, {"size": 26595, "mtime": 1753942231246, "results": "432", "hashOfConfig": "254"}, {"size": 5191, "mtime": 1753593586880, "results": "433", "hashOfConfig": "254"}, {"size": 13601, "mtime": 1753960105037, "results": "434", "hashOfConfig": "254"}, {"size": 8060, "mtime": 1753270177859, "results": "435", "hashOfConfig": "254"}, {"size": 535, "mtime": 1753270727671, "results": "436", "hashOfConfig": "254"}, {"size": 9532, "mtime": 1752931063725, "results": "437", "hashOfConfig": "254"}, {"size": 13488, "mtime": 1753203664315, "results": "438", "hashOfConfig": "254"}, {"size": 4263, "mtime": 1753895901895, "results": "439", "hashOfConfig": "254"}, {"size": 9982, "mtime": 1752636568061, "results": "440", "hashOfConfig": "254"}, {"size": 8127, "mtime": 1753175935226, "results": "441", "hashOfConfig": "254"}, {"size": 3699, "mtime": 1753120607827, "results": "442", "hashOfConfig": "254"}, {"size": 4198, "mtime": 1753125293677, "results": "443", "hashOfConfig": "254"}, {"size": 641, "mtime": 1753270744908, "results": "444", "hashOfConfig": "254"}, {"size": 7413, "mtime": 1753270353937, "results": "445", "hashOfConfig": "254"}, {"size": 9796, "mtime": 1753025804174, "results": "446", "hashOfConfig": "254"}, {"size": 1131, "mtime": 1753593920067, "results": "447", "hashOfConfig": "254"}, {"size": 6012, "mtime": 1753326520640, "results": "448", "hashOfConfig": "254"}, {"size": 2794, "mtime": 1753671480334, "results": "449", "hashOfConfig": "254"}, {"size": 8431, "mtime": 1753671684327, "results": "450", "hashOfConfig": "254"}, {"size": 5209, "mtime": 1753896163704, "results": "451", "hashOfConfig": "254"}, {"size": 3832, "mtime": 1753180301276, "results": "452", "hashOfConfig": "254"}, {"size": 3071, "mtime": 1753334272827, "results": "453", "hashOfConfig": "254"}, {"size": 3025, "mtime": 1753685242417, "results": "454", "hashOfConfig": "254"}, {"size": 7986, "mtime": 1753332776460, "results": "455", "hashOfConfig": "254"}, {"size": 8296, "mtime": 1753276644867, "results": "456", "hashOfConfig": "254"}, {"size": 402, "mtime": 1753270759925, "results": "457", "hashOfConfig": "254"}, {"size": 9081, "mtime": 1753180636246, "results": "458", "hashOfConfig": "254"}, {"size": 12936, "mtime": 1753180811711, "results": "459", "hashOfConfig": "254"}, {"size": 7464, "mtime": 1753287450857, "results": "460", "hashOfConfig": "254"}, {"size": 8711, "mtime": 1753946105560, "results": "461", "hashOfConfig": "254"}, {"size": 10410, "mtime": 1753286896501, "results": "462", "hashOfConfig": "254"}, {"size": 7648, "mtime": 1753287410272, "results": "463", "hashOfConfig": "254"}, {"size": 7837, "mtime": 1753286932569, "results": "464", "hashOfConfig": "254"}, {"size": 8256, "mtime": 1753287547287, "results": "465", "hashOfConfig": "254"}, {"size": 13028, "mtime": 1753934091360, "results": "466", "hashOfConfig": "254"}, {"size": 9646, "mtime": 1753294747577, "results": "467", "hashOfConfig": "254"}, {"size": 8743, "mtime": 1753896126729, "results": "468", "hashOfConfig": "254"}, {"size": 6977, "mtime": 1753025884170, "results": "469", "hashOfConfig": "254"}, {"size": 18950, "mtime": 1753944066315, "results": "470", "hashOfConfig": "254"}, {"size": 11602, "mtime": 1753944640062, "results": "471", "hashOfConfig": "254"}, {"size": 11618, "mtime": 1753944723095, "results": "472", "hashOfConfig": "254"}, {"size": 9901, "mtime": 1753945480206, "results": "473", "hashOfConfig": "254"}, {"size": 14763, "mtime": 1753945563874, "results": "474", "hashOfConfig": "254"}, {"size": 13425, "mtime": 1753375633015, "results": "475", "hashOfConfig": "254"}, {"size": 9358, "mtime": 1752640831372, "results": "476", "hashOfConfig": "254"}, {"size": 12869, "mtime": 1753375569895, "results": "477", "hashOfConfig": "254"}, {"size": 14171, "mtime": 1753370765605, "results": "478", "hashOfConfig": "254"}, {"size": 12423, "mtime": 1752598743592, "results": "479", "hashOfConfig": "254"}, {"size": 10509, "mtime": 1753370835593, "results": "480", "hashOfConfig": "254"}, {"size": 11164, "mtime": 1753375676930, "results": "481", "hashOfConfig": "254"}, {"size": 13436, "mtime": 1752598798485, "results": "482", "hashOfConfig": "254"}, {"size": 11996, "mtime": 1753370963443, "results": "483", "hashOfConfig": "254"}, {"size": 6140, "mtime": 1753345001176, "results": "484", "hashOfConfig": "254"}, {"size": 8589, "mtime": 1752640486549, "results": "485", "hashOfConfig": "254"}, {"size": 9905, "mtime": 1752640593542, "results": "486", "hashOfConfig": "254"}, {"size": 10899, "mtime": 1752745552656, "results": "487", "hashOfConfig": "254"}, {"size": 11027, "mtime": 1752640428765, "results": "488", "hashOfConfig": "254"}, {"size": 13147, "mtime": 1753594438226, "results": "489", "hashOfConfig": "254"}, {"size": 5049, "mtime": 1753286990271, "results": "490", "hashOfConfig": "254"}, {"size": 22951, "mtime": 1753898090263, "results": "491", "hashOfConfig": "254"}, {"size": 15777, "mtime": 1753898174617, "results": "492", "hashOfConfig": "254"}, {"size": 23667, "mtime": 1753897895005, "results": "493", "hashOfConfig": "254"}, {"size": 16391, "mtime": 1753900331284, "results": "494", "hashOfConfig": "254"}, {"size": 638, "mtime": 1753944332686, "results": "495", "hashOfConfig": "254"}, {"size": 11541, "mtime": 1753897973245, "results": "496", "hashOfConfig": "254"}, {"size": 5306, "mtime": 1753897234473, "results": "497", "hashOfConfig": "254"}, {"size": 10946, "mtime": 1753897535458, "results": "498", "hashOfConfig": "254"}, {"size": 9749, "mtime": 1753897483917, "results": "499", "hashOfConfig": "254"}, {"size": 8308, "mtime": 1753897402187, "results": "500", "hashOfConfig": "254"}, {"size": 5453, "mtime": 1753951908812, "results": "501", "hashOfConfig": "254"}, {"size": 4544, "mtime": 1753953011879, "results": "502", "hashOfConfig": "254"}, {"size": 5522, "mtime": 1753952979815, "results": "503", "hashOfConfig": "254"}, {"size": 7381, "mtime": 1753953265497, "results": "504", "hashOfConfig": "254"}, {"size": 7195, "mtime": 1753953076361, "results": "505", "hashOfConfig": "254"}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tpykxl", {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1082", "messages": "1083", "suppressedMessages": "1084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1085", "messages": "1086", "suppressedMessages": "1087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1088", "messages": "1089", "suppressedMessages": "1090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1091", "messages": "1092", "suppressedMessages": "1093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1094", "messages": "1095", "suppressedMessages": "1096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1118", "messages": "1119", "suppressedMessages": "1120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1121", "messages": "1122", "suppressedMessages": "1123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1124", "messages": "1125", "suppressedMessages": "1126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1127", "messages": "1128", "suppressedMessages": "1129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1130", "messages": "1131", "suppressedMessages": "1132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1133", "messages": "1134", "suppressedMessages": "1135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1136", "messages": "1137", "suppressedMessages": "1138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1139", "messages": "1140", "suppressedMessages": "1141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1142", "messages": "1143", "suppressedMessages": "1144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1145", "messages": "1146", "suppressedMessages": "1147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1148", "messages": "1149", "suppressedMessages": "1150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1151", "messages": "1152", "suppressedMessages": "1153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1154", "messages": "1155", "suppressedMessages": "1156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1157", "messages": "1158", "suppressedMessages": "1159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1160", "messages": "1161", "suppressedMessages": "1162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1163", "messages": "1164", "suppressedMessages": "1165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1166", "messages": "1167", "suppressedMessages": "1168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1169", "messages": "1170", "suppressedMessages": "1171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1172", "messages": "1173", "suppressedMessages": "1174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1175", "messages": "1176", "suppressedMessages": "1177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1178", "messages": "1179", "suppressedMessages": "1180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1181", "messages": "1182", "suppressedMessages": "1183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1184", "messages": "1185", "suppressedMessages": "1186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1187", "messages": "1188", "suppressedMessages": "1189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1190", "messages": "1191", "suppressedMessages": "1192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1193", "messages": "1194", "suppressedMessages": "1195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1196", "messages": "1197", "suppressedMessages": "1198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1199", "messages": "1200", "suppressedMessages": "1201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1202", "messages": "1203", "suppressedMessages": "1204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1205", "messages": "1206", "suppressedMessages": "1207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1208", "messages": "1209", "suppressedMessages": "1210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1211", "messages": "1212", "suppressedMessages": "1213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1214", "messages": "1215", "suppressedMessages": "1216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1217", "messages": "1218", "suppressedMessages": "1219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1220", "messages": "1221", "suppressedMessages": "1222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1223", "messages": "1224", "suppressedMessages": "1225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1226", "messages": "1227", "suppressedMessages": "1228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1229", "messages": "1230", "suppressedMessages": "1231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1232", "messages": "1233", "suppressedMessages": "1234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1235", "messages": "1236", "suppressedMessages": "1237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1238", "messages": "1239", "suppressedMessages": "1240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1241", "messages": "1242", "suppressedMessages": "1243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1244", "messages": "1245", "suppressedMessages": "1246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1247", "messages": "1248", "suppressedMessages": "1249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1250", "messages": "1251", "suppressedMessages": "1252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1253", "messages": "1254", "suppressedMessages": "1255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1256", "messages": "1257", "suppressedMessages": "1258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1259", "messages": "1260", "suppressedMessages": "1261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\admin\\data-management\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\admin\\integration-test\\page.tsx", ["1262", "1263", "1264", "1265"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\api\\products\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\data-status\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\finance\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\finance\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\employees\\components\\DepartmentManagement.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\employees\\page.tsx", ["1266"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\materials\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\master-data\\product-models\\page.tsx", ["1267"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\procurement\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\procurement\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\cost-calculation\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\cost-calculation\\utils\\costCalculationEngine.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\hot-press-board\\page.tsx", ["1268"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\hot-press-board\\utils\\hotPressEngine.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionOrderDetailModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionOrdersList.tsx", ["1269", "1270", "1271"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionSchedulingBoard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\ProductionWorkOrdersList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\SchedulingConfigModal.tsx", ["1272", "1273"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\SchedulingResultModal.tsx", ["1274", "1275", "1276", "1277"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkstationCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkstationManagementTab.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\components\\WorkTimeManagementTab.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\orders\\page.tsx", ["1278", "1279"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\queue-cleaner\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\services\\eventDrivenIntegration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\services\\improvedIntegrationService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\work-report\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\after-sales\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\analytics\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\credit\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\customers\\page.tsx", ["1280"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\delivery\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\invoices\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\components\\AddProductModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\components\\ProductionOrderTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\hooks\\useProductionOrders.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\orders\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\sales\\payments\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\materials\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\warehouse\\products\\page.tsx", ["1281"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\BaseOrderDetailModal.tsx", ["1282"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\configs\\productionOrderConfig.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\configs\\salesOrderConfig.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\fieldRenderers.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\OrderDetailModal\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\ProductWeightDisplay.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\SyncStatusIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\UnifiedTagRenderer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\common\\VirtualizedTable.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\EventCommunicationMonitor.tsx", ["1283"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\MemoryMonitorCard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\debug\\MoldNumberDebugger.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\FormSelect.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\MainLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\MessageQueueDebugger.tsx", ["1284"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\production\\RealTimeSyncStatus.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\providers\\AntdProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\providers\\DataInitializationProvider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\SafeSelect.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\AddOrderModal.tsx", ["1285"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductCreateModal.tsx", ["1286"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductInfoSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\ProductSelectModal.tsx", ["1287", "1288"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\sales\\__tests__\\OrderChangeModal.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\components\\shared\\ProductForm.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\config\\dataAccessConfig.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\config\\validationRules.ts", ["1289"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\constants\\productionConstants.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\data\\seedData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useDataAccessMonitor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useDebouncedCallback.ts", ["1290"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useEventListener.ts", ["1291", "1292", "1293", "1294", "1295"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useFormValueFixer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useOptimizedProductionData.ts", ["1296", "1297"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useOrdersData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionDataAdapter.ts", ["1298"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionDataSync.ts", ["1299", "1300"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionErrorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionOperations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionRealTimeSync.ts", ["1301", "1302"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useProductionStatistics.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useTimer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useWarehousePermissions.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\useWorkstationRealtime.ts", ["1303"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\__tests__\\productionHooks.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\hooks\\__tests__\\useOptimizedProductionData.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\BusinessIdGenerator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\CacheDebugger.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\strategies.ts", ["1304"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\cache\\types.ts", ["1305"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\CostCalculationDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\CustomerDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessLayer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataAccessPerformanceMonitor.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataChangeNotifier.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataConsistencyService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\DataSyncService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\EmployeeDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\InventoryDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\OrderDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\PerformanceOptimizer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductionOrderDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\ProductionWorkOrderDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkstationDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkstationStorage.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\WorkTimeDataAccessService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataAccess\\__tests__\\DataAccessManager.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataInitializationService.ts", ["1306"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\dataModelSyncService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventCommunicationDebugger.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventErrorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\EventListenerManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\strategies.ts", ["1307"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\events\\types.ts", ["1308"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\inventoryValueCalculationService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\mrpService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderCancellationService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderDeliveryDateChangeService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\OrderQuantityChangeService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\productInventorySyncService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\productTypeBindingService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\IncrementalSyncService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\OfflineDataCache.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\RealtimeDataSyncService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\RealtimeSyncManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\SyncErrorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\realtime\\WorkstationRealtimeService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\clearSchedulingData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\SameMoldPrioritySchedulingService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\SchedulingStateManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testSameMoldGrouping.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testSchedulingAlgorithm.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testWorkstationDistribution.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\testWorkTimeCalculation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\verifySchedulingFix.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\scheduling\\__tests__\\SameMoldPrioritySchedulingService.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\validation\\OrderValidationService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\validation\\WorkstationDataConsistencyValidator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\workstation\\WorkstationUpdateService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\employeeStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\masterDataStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\productionStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\productionUIStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\sales\\useCustomerStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\sales\\useSalesCompositeStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\useProductionUIStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\warehouseStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\workTimeStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\modules\\__tests__\\useProductionUIStore.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\store\\useAppStore.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\standardStoreInterface.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\architectureCompliance.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\batchNumberMigration.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\businessRuleValidator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\dataTransformers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\errorHandlers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\productionOrderRules.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\statusTransitionManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\timestampGenerator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\transactionManager.ts", [], ["1309"], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\calculations\\amountCalculator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\calculations\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\concurrencyControl.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\DataAccessComplianceChecker.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataAccessErrorHandler.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataModelConsistency.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dataSyncVerification.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\dateUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\duplicateExecutionPrevention.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formatters\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formatters\\statusFormatter.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\formUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\orderStatusTransitions.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\htmlSanitizer.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\inputValidator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\security\\safeEventManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\testSchedulingBoardData.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\UnifiedLogger.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validation\\workstationValidation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validationUtils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validators\\formValidators.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\validators\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\workOrderGenerator.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\workstationQueueCleaner.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\basic.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\businessRuleValidator.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\dataTransformers.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\errorHandlers.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\index.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\simple.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\statusTransitionManager.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\timestampGenerator.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\business\\transactionManager.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\__tests__\\orderNumberValidation.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\batch\\BatchOperations.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\configuration\\SystemConfig.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\dataAccess\\DataAccessMonitoring.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\hooks\\useProductionDataAdapter.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\CrossModule.integration.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\DataAccess.integration.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\HookIntegration.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\OrderManagement.e2e.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\Phase4EndToEndTest.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\integration\\StateManagementIntegration.test.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\CachePerformanceComparison.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\DataAccess.performance.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\performance\\StateManagementPerformance.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\production\\ProductionReadinessTest.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\CacheIntegration.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\DataAccessManager.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\PerformanceOptimizer.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\ProductionOrderDataAccessService.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\dataAccess\\WorkstationDataAccessService.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\services\\events\\EventCommunicationOptimization.test.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\__tests__\\setup.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\DataAccessCompliantStateManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\LegacyCompatibilityAdapter.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\stateTransition\\UnifiedStateTransitionService.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\services\\unified\\UnifiedOrderStatusManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\performance.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\stateTransitionTypes.ts", ["1310"], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\statusTransitionManagerDemo.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedCalculation.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedIdManager.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\business\\unifiedStateTransition.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\styles\\antdHelpers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\styles\\constants.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\styles\\theme.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\types\\css-modules.d.ts", [], [], "C:\\Users\\<USER>\\Desktop\\erp软件\\src\\utils\\styles\\__tests__\\antdHelpers.test.ts", [], [], {"ruleId": "1311", "severity": 2, "message": "1312", "line": 482, "column": 32, "nodeType": "1313", "messageId": "1314", "suggestions": "1315"}, {"ruleId": "1311", "severity": 2, "message": "1312", "line": 482, "column": 36, "nodeType": "1313", "messageId": "1314", "suggestions": "1316"}, {"ruleId": "1311", "severity": 2, "message": "1312", "line": 482, "column": 40, "nodeType": "1313", "messageId": "1314", "suggestions": "1317"}, {"ruleId": "1311", "severity": 2, "message": "1312", "line": 482, "column": 44, "nodeType": "1313", "messageId": "1314", "suggestions": "1318"}, {"ruleId": "1319", "severity": 1, "message": "1320", "line": 133, "column": 6, "nodeType": "1321", "endLine": 133, "endColumn": 8, "suggestions": "1322"}, {"ruleId": "1319", "severity": 1, "message": "1323", "line": 84, "column": 6, "nodeType": "1321", "endLine": 84, "endColumn": 8, "suggestions": "1324"}, {"ruleId": "1325", "severity": 2, "message": "1326", "line": 526, "column": 19, "nodeType": "1327", "messageId": "1328", "endLine": 540, "endColumn": 32}, {"ruleId": "1329", "severity": 2, "message": "1330", "line": 37, "column": 6, "nodeType": "1331", "messageId": "1332", "endLine": 142, "endColumn": 3}, {"ruleId": "1329", "severity": 2, "message": "1330", "line": 159, "column": 67, "nodeType": "1331", "messageId": "1332", "endLine": 476, "endColumn": 3}, {"ruleId": "1319", "severity": 1, "message": "1333", "line": 366, "column": 6, "nodeType": "1321", "endLine": 366, "endColumn": 37, "suggestions": "1334"}, {"ruleId": "1311", "severity": 2, "message": "1312", "line": 313, "column": 33, "nodeType": "1313", "messageId": "1314", "suggestions": "1335"}, {"ruleId": "1311", "severity": 2, "message": "1312", "line": 313, "column": 37, "nodeType": "1313", "messageId": "1314", "suggestions": "1336"}, {"ruleId": "1311", "severity": 2, "message": "1312", "line": 284, "column": 27, "nodeType": "1313", "messageId": "1314", "suggestions": "1337"}, {"ruleId": "1311", "severity": 2, "message": "1312", "line": 284, "column": 31, "nodeType": "1313", "messageId": "1314", "suggestions": "1338"}, {"ruleId": "1311", "severity": 2, "message": "1312", "line": 287, "column": 27, "nodeType": "1313", "messageId": "1314", "suggestions": "1339"}, {"ruleId": "1311", "severity": 2, "message": "1312", "line": 287, "column": 36, "nodeType": "1313", "messageId": "1314", "suggestions": "1340"}, {"ruleId": "1319", "severity": 1, "message": "1341", "line": 341, "column": 6, "nodeType": "1321", "endLine": 341, "endColumn": 24, "suggestions": "1342"}, {"ruleId": "1319", "severity": 1, "message": "1343", "line": 413, "column": 6, "nodeType": "1321", "endLine": 413, "endColumn": 8, "suggestions": "1344"}, {"ruleId": "1319", "severity": 1, "message": "1345", "line": 314, "column": 6, "nodeType": "1321", "endLine": 314, "endColumn": 8, "suggestions": "1346"}, {"ruleId": "1319", "severity": 1, "message": "1347", "line": 151, "column": 6, "nodeType": "1321", "endLine": 151, "endColumn": 8, "suggestions": "1348"}, {"ruleId": "1319", "severity": 1, "message": "1349", "line": 166, "column": 6, "nodeType": "1321", "endLine": 166, "endColumn": 58, "suggestions": "1350"}, {"ruleId": "1319", "severity": 1, "message": "1351", "line": 57, "column": 6, "nodeType": "1321", "endLine": 57, "endColumn": 24, "suggestions": "1352"}, {"ruleId": "1319", "severity": 1, "message": "1353", "line": 157, "column": 6, "nodeType": "1321", "endLine": 157, "endColumn": 37, "suggestions": "1354"}, {"ruleId": "1319", "severity": 1, "message": "1355", "line": 330, "column": 6, "nodeType": "1321", "endLine": 330, "endColumn": 12, "suggestions": "1356"}, {"ruleId": "1319", "severity": 1, "message": "1357", "line": 185, "column": 6, "nodeType": "1321", "endLine": 185, "endColumn": 12, "suggestions": "1358"}, {"ruleId": "1319", "severity": 1, "message": "1359", "line": 228, "column": 6, "nodeType": "1321", "endLine": 228, "endColumn": 12, "suggestions": "1360"}, {"ruleId": "1319", "severity": 1, "message": "1361", "line": 235, "column": 6, "nodeType": "1321", "endLine": 235, "endColumn": 42, "suggestions": "1362"}, {"ruleId": "1363", "severity": 1, "message": "1364", "line": 278, "column": 1, "nodeType": "1365", "endLine": 289, "endColumn": 2}, {"ruleId": "1319", "severity": 1, "message": "1366", "line": 37, "column": 29, "nodeType": "1367", "endLine": 37, "endColumn": 40}, {"ruleId": "1319", "severity": 1, "message": "1368", "line": 138, "column": 6, "nodeType": "1321", "endLine": 138, "endColumn": 117, "suggestions": "1369"}, {"ruleId": "1319", "severity": 1, "message": "1370", "line": 233, "column": 6, "nodeType": "1321", "endLine": 233, "endColumn": 67, "suggestions": "1371"}, {"ruleId": "1372", "severity": 2, "message": "1373", "line": 317, "column": 5, "nodeType": "1367", "endLine": 317, "endColumn": 27}, {"ruleId": "1372", "severity": 2, "message": "1374", "line": 319, "column": 5, "nodeType": "1367", "endLine": 319, "endColumn": 29}, {"ruleId": "1372", "severity": 2, "message": "1375", "line": 321, "column": 5, "nodeType": "1367", "endLine": 321, "endColumn": 24}, {"ruleId": "1319", "severity": 1, "message": "1376", "line": 168, "column": 8, "nodeType": "1321", "endLine": 168, "endColumn": 22, "suggestions": "1377"}, {"ruleId": "1319", "severity": 1, "message": "1378", "line": 479, "column": 6, "nodeType": "1321", "endLine": 479, "endColumn": 27, "suggestions": "1379"}, {"ruleId": "1319", "severity": 1, "message": "1380", "line": 169, "column": 8, "nodeType": "1321", "endLine": 169, "endColumn": 30, "suggestions": "1381"}, {"ruleId": "1319", "severity": 1, "message": "1382", "line": 210, "column": 6, "nodeType": "1321", "endLine": 210, "endColumn": 50, "suggestions": "1383"}, {"ruleId": "1319", "severity": 1, "message": "1384", "line": 253, "column": 6, "nodeType": "1321", "endLine": 253, "endColumn": 62, "suggestions": "1385"}, {"ruleId": "1319", "severity": 1, "message": "1386", "line": 222, "column": 6, "nodeType": "1321", "endLine": 222, "endColumn": 115, "suggestions": "1387"}, {"ruleId": "1319", "severity": 1, "message": "1388", "line": 406, "column": 25, "nodeType": "1367", "endLine": 406, "endColumn": 32}, {"ruleId": "1319", "severity": 1, "message": "1389", "line": 218, "column": 20, "nodeType": "1367", "endLine": 218, "endColumn": 27}, {"ruleId": "1363", "severity": 1, "message": "1364", "line": 305, "column": 1, "nodeType": "1365", "endLine": 310, "endColumn": 2}, {"ruleId": "1363", "severity": 1, "message": "1364", "line": 236, "column": 1, "nodeType": "1365", "endLine": 238, "endColumn": 2}, {"ruleId": "1363", "severity": 1, "message": "1364", "line": 348, "column": 1, "nodeType": "1365", "endLine": 358, "endColumn": 2}, {"ruleId": "1363", "severity": 1, "message": "1364", "line": 486, "column": 1, "nodeType": "1365", "endLine": 492, "endColumn": 2}, {"ruleId": "1363", "severity": 1, "message": "1364", "line": 350, "column": 1, "nodeType": "1365", "endLine": 355, "endColumn": 2}, {"ruleId": "1390", "severity": 2, "message": "1391", "line": 387, "column": 10, "nodeType": "1392", "endLine": 387, "endColumn": 22, "suppressions": "1393"}, {"ruleId": "1363", "severity": 1, "message": "1364", "line": 446, "column": 1, "nodeType": "1365", "endLine": 459, "endColumn": 2}, "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["1394", "1395", "1396", "1397"], ["1398", "1399", "1400", "1401"], ["1402", "1403", "1404", "1405"], ["1406", "1407", "1408", "1409"], "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadDepartments' and 'loadEmployees'. Either include them or remove the dependency array.", "ArrayExpression", ["1410"], "React Hook useEffect has a missing dependency: 'refreshProductModels'. Either include it or remove the dependency array.", ["1411"], "react/jsx-key", "Missing \"key\" prop for element in array", "JSXElement", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "react/display-name", "Component definition is missing display name", "CallExpression", "noDisplayName", "React Hook useMemo has an unnecessary dependency: 'onStatusChange'. Either exclude it or remove the dependency array.", ["1412"], ["1413", "1414", "1415", "1416"], ["1417", "1418", "1419", "1420"], ["1421", "1422", "1423", "1424"], ["1425", "1426", "1427", "1428"], ["1429", "1430", "1431", "1432"], ["1433", "1434", "1435", "1436"], "React Hook useCallback has missing dependencies: 'message' and 'refreshData'. Either include them or remove the dependency array.", ["1437"], "React Hook useEffect has missing dependencies: 'message' and 'refreshData'. Either include them or remove the dependency array.", ["1438"], "React Hook useEffect has a missing dependency: 'refreshCustomers'. Either include it or remove the dependency array.", ["1439"], "React Hook useEffect has a missing dependency: 'loadInventoryData'. Either include it or remove the dependency array.", ["1440"], "React Hook useMemo has a missing dependency: 'config'. Either include it or remove the dependency array.", ["1441"], "React Hook useEffect has a missing dependency: 'updateDebugInfo'. Either include it or remove the dependency array.", ["1442"], "React Hook useEffect has a missing dependency: 'collectDebugInfo'. Either include it or remove the dependency array.", ["1443"], "React Hook useEffect has missing dependencies: 'form', 'generateOrderNumber', and 'loadInitialData'. Either include them or remove the dependency array.", ["1444"], "React Hook useEffect has a missing dependency: 'initializeForm'. Either include it or remove the dependency array.", ["1445"], "React Hook useEffect has a missing dependency: 'loadProducts'. Either include it or remove the dependency array.", ["1446"], "React Hook useEffect has a missing dependency: 'filterProducts'. Either include it or remove the dependency array.", ["1447"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "Identifier", "React Hook useEffect has a missing dependency: 'listener'. Either include it or remove the dependency array.", ["1448"], "React Hook useEffect has a missing dependency: 'options'. Either include it or remove the dependency array.", ["1449"], "react-hooks/rules-of-hooks", "React Hook \"useWindowEventListener\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "React Hook \"useDocumentEventListener\" is called conditionally. React Hooks must be called in the exact same order in every component render.", "React Hook \"useDOMEventListener\" is called conditionally. React Hooks must be called in the exact same order in every component render. Did you accidentally call a React Hook after an early return?", "React Hook useCallback has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["1450"], "React Hook useEffect has missing dependencies: 'performance.dataLoadTime' and 'performance.renderTime'. Either include them or remove the dependency array.", ["1451"], "React Hook useCallback has missing dependencies: 'refreshOrders' and 'refreshWorkOrders'. Either include them or remove the dependency array.", ["1452"], "React Hook useCallback has a missing dependency: 'checkForChanges'. Either include it or remove the dependency array.", ["1453"], "React Hook useCallback has a missing dependency: 'disconnect'. Either include it or remove the dependency array.", ["1454"], "React Hook useCallback has missing dependencies: 'handleCacheInvalidation' and 'showDataChangeMessage'. Either include them or remove the dependency array.", ["1455"], "The ref value 'eventListenersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'eventListenersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "The ref value 'listenersRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'listenersRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "@next/next/no-assign-module-variable", "Do not assign to the variable `module`. See: https://nextjs.org/docs/messages/no-assign-module-variable", "VariableDeclaration", ["1456"], {"messageId": "1457", "data": "1458", "fix": "1459", "desc": "1460"}, {"messageId": "1457", "data": "1461", "fix": "1462", "desc": "1463"}, {"messageId": "1457", "data": "1464", "fix": "1465", "desc": "1466"}, {"messageId": "1457", "data": "1467", "fix": "1468", "desc": "1469"}, {"messageId": "1457", "data": "1470", "fix": "1471", "desc": "1460"}, {"messageId": "1457", "data": "1472", "fix": "1473", "desc": "1463"}, {"messageId": "1457", "data": "1474", "fix": "1475", "desc": "1466"}, {"messageId": "1457", "data": "1476", "fix": "1477", "desc": "1469"}, {"messageId": "1457", "data": "1478", "fix": "1479", "desc": "1460"}, {"messageId": "1457", "data": "1480", "fix": "1481", "desc": "1463"}, {"messageId": "1457", "data": "1482", "fix": "1483", "desc": "1466"}, {"messageId": "1457", "data": "1484", "fix": "1485", "desc": "1469"}, {"messageId": "1457", "data": "1486", "fix": "1487", "desc": "1460"}, {"messageId": "1457", "data": "1488", "fix": "1489", "desc": "1463"}, {"messageId": "1457", "data": "1490", "fix": "1491", "desc": "1466"}, {"messageId": "1457", "data": "1492", "fix": "1493", "desc": "1469"}, {"desc": "1494", "fix": "1495"}, {"desc": "1496", "fix": "1497"}, {"desc": "1498", "fix": "1499"}, {"messageId": "1457", "data": "1500", "fix": "1501", "desc": "1460"}, {"messageId": "1457", "data": "1502", "fix": "1503", "desc": "1463"}, {"messageId": "1457", "data": "1504", "fix": "1505", "desc": "1466"}, {"messageId": "1457", "data": "1506", "fix": "1507", "desc": "1469"}, {"messageId": "1457", "data": "1508", "fix": "1509", "desc": "1460"}, {"messageId": "1457", "data": "1510", "fix": "1511", "desc": "1463"}, {"messageId": "1457", "data": "1512", "fix": "1513", "desc": "1466"}, {"messageId": "1457", "data": "1514", "fix": "1515", "desc": "1469"}, {"messageId": "1457", "data": "1516", "fix": "1517", "desc": "1460"}, {"messageId": "1457", "data": "1518", "fix": "1519", "desc": "1463"}, {"messageId": "1457", "data": "1520", "fix": "1521", "desc": "1466"}, {"messageId": "1457", "data": "1522", "fix": "1523", "desc": "1469"}, {"messageId": "1457", "data": "1524", "fix": "1525", "desc": "1460"}, {"messageId": "1457", "data": "1526", "fix": "1527", "desc": "1463"}, {"messageId": "1457", "data": "1528", "fix": "1529", "desc": "1466"}, {"messageId": "1457", "data": "1530", "fix": "1531", "desc": "1469"}, {"messageId": "1457", "data": "1532", "fix": "1533", "desc": "1460"}, {"messageId": "1457", "data": "1534", "fix": "1535", "desc": "1463"}, {"messageId": "1457", "data": "1536", "fix": "1537", "desc": "1466"}, {"messageId": "1457", "data": "1538", "fix": "1539", "desc": "1469"}, {"messageId": "1457", "data": "1540", "fix": "1541", "desc": "1460"}, {"messageId": "1457", "data": "1542", "fix": "1543", "desc": "1463"}, {"messageId": "1457", "data": "1544", "fix": "1545", "desc": "1466"}, {"messageId": "1457", "data": "1546", "fix": "1547", "desc": "1469"}, {"desc": "1548", "fix": "1549"}, {"desc": "1550", "fix": "1551"}, {"desc": "1552", "fix": "1553"}, {"desc": "1554", "fix": "1555"}, {"desc": "1556", "fix": "1557"}, {"desc": "1558", "fix": "1559"}, {"desc": "1560", "fix": "1561"}, {"desc": "1562", "fix": "1563"}, {"desc": "1564", "fix": "1565"}, {"desc": "1566", "fix": "1567"}, {"desc": "1568", "fix": "1569"}, {"desc": "1570", "fix": "1571"}, {"desc": "1572", "fix": "1573"}, {"desc": "1574", "fix": "1575"}, {"desc": "1576", "fix": "1577"}, {"desc": "1578", "fix": "1579"}, {"desc": "1580", "fix": "1581"}, {"desc": "1582", "fix": "1583"}, {"desc": "1584", "fix": "1585"}, {"kind": "1586", "justification": "1587"}, "replaceWithAlt", {"alt": "1588"}, {"range": "1589", "text": "1590"}, "Replace with `&quot;`.", {"alt": "1591"}, {"range": "1592", "text": "1593"}, "Replace with `&ldquo;`.", {"alt": "1594"}, {"range": "1595", "text": "1596"}, "Replace with `&#34;`.", {"alt": "1597"}, {"range": "1598", "text": "1599"}, "Replace with `&rdquo;`.", {"alt": "1588"}, {"range": "1600", "text": "1601"}, {"alt": "1591"}, {"range": "1602", "text": "1603"}, {"alt": "1594"}, {"range": "1604", "text": "1605"}, {"alt": "1597"}, {"range": "1606", "text": "1607"}, {"alt": "1588"}, {"range": "1608", "text": "1609"}, {"alt": "1591"}, {"range": "1610", "text": "1611"}, {"alt": "1594"}, {"range": "1612", "text": "1613"}, {"alt": "1597"}, {"range": "1614", "text": "1615"}, {"alt": "1588"}, {"range": "1616", "text": "1617"}, {"alt": "1591"}, {"range": "1618", "text": "1619"}, {"alt": "1594"}, {"range": "1620", "text": "1621"}, {"alt": "1597"}, {"range": "1622", "text": "1623"}, "Update the dependencies array to be: [loadDepartments, loadEmployees]", {"range": "1624", "text": "1625"}, "Update the dependencies array to be: [refreshProductModels]", {"range": "1626", "text": "1627"}, "Update the dependencies array to be: [onOrderDetail]", {"range": "1628", "text": "1629"}, {"alt": "1588"}, {"range": "1630", "text": "1631"}, {"alt": "1591"}, {"range": "1632", "text": "1633"}, {"alt": "1594"}, {"range": "1634", "text": "1635"}, {"alt": "1597"}, {"range": "1636", "text": "1637"}, {"alt": "1588"}, {"range": "1638", "text": "1639"}, {"alt": "1591"}, {"range": "1640", "text": "1641"}, {"alt": "1594"}, {"range": "1642", "text": "1643"}, {"alt": "1597"}, {"range": "1644", "text": "1645"}, {"alt": "1588"}, {"range": "1646", "text": "1647"}, {"alt": "1591"}, {"range": "1648", "text": "1649"}, {"alt": "1594"}, {"range": "1650", "text": "1651"}, {"alt": "1597"}, {"range": "1652", "text": "1653"}, {"alt": "1588"}, {"range": "1654", "text": "1655"}, {"alt": "1591"}, {"range": "1656", "text": "1657"}, {"alt": "1594"}, {"range": "1658", "text": "1659"}, {"alt": "1597"}, {"range": "1660", "text": "1661"}, {"alt": "1588"}, {"range": "1662", "text": "1663"}, {"alt": "1591"}, {"range": "1664", "text": "1665"}, {"alt": "1594"}, {"range": "1666", "text": "1667"}, {"alt": "1597"}, {"range": "1668", "text": "1669"}, {"alt": "1588"}, {"range": "1670", "text": "1671"}, {"alt": "1591"}, {"range": "1672", "text": "1673"}, {"alt": "1594"}, {"range": "1674", "text": "1675"}, {"alt": "1597"}, {"range": "1676", "text": "1677"}, "Update the dependencies array to be: [message, productionOrders.length, refreshData]", {"range": "1678", "text": "1679"}, "Update the dependencies array to be: [message, refreshData]", {"range": "1680", "text": "1681"}, "Update the dependencies array to be: [refreshCustomers]", {"range": "1682", "text": "1683"}, "Update the dependencies array to be: [loadInventoryData]", {"range": "1684", "text": "1685"}, "Update the dependencies array to be: [actionButtons, onClose, config, order]", {"range": "1686", "text": "1687"}, "Update the dependencies array to be: [debuggerInstance, updateDebugInfo]", {"range": "1688", "text": "1689"}, "Update the dependencies array to be: [visible, pendingOrders.length, collectDebugInfo]", {"range": "1690", "text": "1691"}, "Update the dependencies array to be: [form, generateOrderNumber, loadInitialData, open]", {"range": "1692", "text": "1693"}, "Update the dependencies array to be: [initializeForm, open]", {"range": "1694", "text": "1695"}, "Update the dependencies array to be: [loadProducts, open]", {"range": "1696", "text": "1697"}, "Update the dependencies array to be: [searchText, statusFilter, products, filterProducts]", {"range": "1698", "text": "1699"}, "Update the dependencies array to be: [listenerId, opts.autoCleanup, opts.trackMemory, opts.useNewManager, opts.priority, opts.timeout, isMonitoring, listener]", {"range": "1700", "text": "1701"}, "Update the dependencies array to be: [element, eventType, handler, opts.trackMemory, isMonitoring, options]", {"range": "1702", "text": "1703"}, "Update the dependencies array to be: [onDataChange, refresh]", {"range": "1704", "text": "1705"}, "Update the dependencies array to be: [onPerformanceUpdate, performance.dataLoadTime, performance.renderTime]", {"range": "1706", "text": "1707"}, "Update the dependencies array to be: [onRealTimeDataChange, refreshOrders, refreshWorkOrders]", {"range": "1708", "text": "1709"}, "Update the dependencies array to be: [enabled, pollingInterval, checkForChanges, handleDataChange]", {"range": "1710", "text": "1711"}, "Update the dependencies array to be: [disconnect, syncMethod, connectWebSocket, connectSSE, startPolling]", {"range": "1712", "text": "1713"}, "Update the dependencies array to be: [isInitialized, eventTypes, onConnectionChange, autoInvalidateCache, onDataChange, showSyncMessages, handleCacheInvalidation, showDataChangeMessage, onError]", {"range": "1714", "text": "1715"}, "directive", "", "&quot;", [15030, 15054], "2. 审核订单，状态从&quot;未审核\"变更为\"已审核\"", "&ldquo;", [15030, 15054], "2. 审核订单，状态从&ldquo;未审核\"变更为\"已审核\"", "&#34;", [15030, 15054], "2. 审核订单，状态从&#34;未审核\"变更为\"已审核\"", "&rdquo;", [15030, 15054], "2. 审核订单，状态从&rdquo;未审核\"变更为\"已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核&quot;变更为\"已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核&ldquo;变更为\"已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核&#34;变更为\"已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核&rdquo;变更为\"已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为&quot;已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为&ldquo;已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为&#34;已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为&rdquo;已审核\"", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为\"已审核&quot;", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为\"已审核&ldquo;", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为\"已审核&#34;", [15030, 15054], "2. 审核订单，状态从\"未审核\"变更为\"已审核&rdquo;", [4120, 4122], "[loadDepartments, loadEmployees]", [2177, 2179], "[refreshProductModels]", [10831, 10862], "[onOrderDetail]", [9234, 9253], "排程完成后，工单状态将变更为&quot;已排程\"", [9234, 9253], "排程完成后，工单状态将变更为&ldquo;已排程\"", [9234, 9253], "排程完成后，工单状态将变更为&#34;已排程\"", [9234, 9253], "排程完成后，工单状态将变更为&rdquo;已排程\"", [9234, 9253], "排程完成后，工单状态将变更为\"已排程&quot;", [9234, 9253], "排程完成后，工单状态将变更为\"已排程&ldquo;", [9234, 9253], "排程完成后，工单状态将变更为\"已排程&#34;", [9234, 9253], "排程完成后，工单状态将变更为\"已排程&rdquo;", [7406, 7420], "• 工单状态：仍为&quot;待开始\"", [7406, 7420], "• 工单状态：仍为&ldquo;待开始\"", [7406, 7420], "• 工单状态：仍为&#34;待开始\"", [7406, 7420], "• 工单状态：仍为&rdquo;待开始\"", [7406, 7420], "• 工单状态：仍为\"待开始&quot;", [7406, 7420], "• 工单状态：仍为\"待开始&ldquo;", [7406, 7420], "• 工单状态：仍为\"待开始&#34;", [7406, 7420], "• 工单状态：仍为\"待开始&rdquo;", [7544, 7568], "点击&quot;确认应用排程结果\"后才会正式应用所有更改。", [7544, 7568], "点击&ldquo;确认应用排程结果\"后才会正式应用所有更改。", [7544, 7568], "点击&#34;确认应用排程结果\"后才会正式应用所有更改。", [7544, 7568], "点击&rdquo;确认应用排程结果\"后才会正式应用所有更改。", [7544, 7568], "点击\"确认应用排程结果&quot;后才会正式应用所有更改。", [7544, 7568], "点击\"确认应用排程结果&ldquo;后才会正式应用所有更改。", [7544, 7568], "点击\"确认应用排程结果&#34;后才会正式应用所有更改。", [7544, 7568], "点击\"确认应用排程结果&rdquo;后才会正式应用所有更改。", [12205, 12223], "[message, productionOrders.length, refreshData]", [14085, 14087], "[message, refreshData]", [8109, 8111], "[refreshCustomers]", [4702, 4704], "[loadInventoryData]", [4482, 4534], "[actionButtons, onClose, config, order]", [1790, 1808], "[debuggerInstance, updateDebugInfo]", [4428, 4459], "[visible, pendingOrders.length, collectDebugInfo]", [9130, 9136], "[form, generateOrderNumber, loadInitialData, open]", [5325, 5331], "[initializeForm, open]", [5920, 5926], "[loadProducts, open]", [6005, 6041], "[searchText, statusFilter, products, filterProducts]", [4477, 4588], "[listenerId, opts.autoCleanup, opts.trackMemory, opts.useNewManager, opts.priority, opts.timeout, isMonitoring, listener]", [6859, 6920], "[element, eventType, handler, opts.trackMemory, isMonitoring, options]", [5170, 5184], "[on<PERSON><PERSON><PERSON><PERSON><PERSON>, refresh]", [14707, 14728], "[onPerformanceUpdate, performance.dataLoadTime, performance.renderTime]", [5634, 5656], "[onRealTimeDataChange, refreshOrders, refreshWorkOrders]", [5279, 5323], "[enabled, pollingInterval, checkForChanges, handleDataChange]", [6229, 6285], "[disconnect, syncMethod, connectWebSocket, connectSSE, startPolling]", [6034, 6143], "[isInitialized, eventTypes, onConnectionChange, autoInvalidateCache, onDataChange, showSyncMessages, handleCacheInvalidation, showDataChangeMessage, onError]"]