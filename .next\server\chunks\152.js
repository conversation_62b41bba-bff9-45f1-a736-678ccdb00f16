exports.id=152,exports.ids=[152],exports.modules={54649:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var l=n(65651),r=n(3729);let o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M257.7 752c2 0 4-.2 6-.5L431.9 722c2-.4 3.9-1.3 5.3-2.8l423.9-423.9a9.96 9.96 0 000-14.1L694.9 114.9c-1.9-1.9-4.4-2.9-7.1-2.9s-5.2 1-7.1 2.9L256.8 538.8c-1.5 1.5-2.4 3.3-2.8 5.3l-29.5 168.2a33.5 33.5 0 009.4 29.8c6.6 6.4 14.9 9.9 23.8 9.9zm67.4-174.4L687.8 215l73.3 73.3-362.7 362.6-88.9 15.7 15.6-89zM880 836H144c-17.7 0-32 14.3-32 32v36c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-36c0-17.7-14.3-32-32-32z"}}]},name:"edit",theme:"outlined"};var a=n(49809);let i=r.forwardRef(function(e,t){return r.createElement(a.Z,(0,l.Z)({},e,{ref:t,icon:o}))})},90152:(e,t,n)=>{"use strict";n.d(t,{default:()=>ex});var l=n(3729),r=n(72375),o=n(54649),a=n(34132),i=n.n(a),c=n(70242),s=n(89299),u=n(17981),d=n(80595),p=n(24773),f=n(67862),m=n(2533),g=n(84893),b=n(99601),y=n(51410),v=n(65651);let h={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 170h-60c-4.4 0-8 3.6-8 8v518H310v-73c0-6.7-7.8-10.5-13-6.3l-141.9 112a8 8 0 000 12.6l141.9 112c5.3 4.2 13 .4 13-6.3v-75h498c35.3 0 64-28.7 64-64V178c0-4.4-3.6-8-8-8z"}}]},name:"enter",theme:"outlined"};var x=n(49809),O=l.forwardRef(function(e,t){return l.createElement(x.Z,(0,v.Z)({},e,{ref:t,icon:h}))}),E=n(21029),w=n(29545),S=n(16879),j=n(22989),C=n(13165),k=n(45603),$=n(92959);let R=(e,t,n,l)=>{let{titleMarginBottom:r,fontWeightStrong:o}=l;return{marginBottom:r,color:n,fontWeight:o,fontSize:e,lineHeight:t}},T=e=>{let t={};return[1,2,3,4,5].forEach(n=>{t[`
      h${n}&,
      div&-h${n},
      div&-h${n} > textarea,
      h${n}
    `]=R(e[`fontSizeHeading${n}`],e[`lineHeightHeading${n}`],e.colorTextHeading,e)}),t},Z=e=>{let{componentCls:t}=e;return{"a&, a":Object.assign(Object.assign({},(0,j.Nd)(e)),{userSelect:"text",[`&[disabled], &${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:active, &:hover":{color:e.colorTextDisabled},"&:active":{pointerEvents:"none"}}})}},I=e=>({code:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.2em 0.1em",fontSize:"85%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3},kbd:{margin:"0 0.2em",paddingInline:"0.4em",paddingBlock:"0.15em 0.1em",fontSize:"90%",fontFamily:e.fontFamilyCode,background:"rgba(150, 150, 150, 0.06)",border:"1px solid rgba(100, 100, 100, 0.2)",borderBottomWidth:2,borderRadius:3},mark:{padding:0,backgroundColor:k.EV[2]},"u, ins":{textDecoration:"underline",textDecorationSkipInk:"auto"},"s, del":{textDecoration:"line-through"},strong:{fontWeight:600},"ul, ol":{marginInline:0,marginBlock:"0 1em",padding:0,li:{marginInline:"20px 0",marginBlock:0,paddingInline:"4px 0",paddingBlock:0}},ul:{listStyleType:"circle",ul:{listStyleType:"disc"}},ol:{listStyleType:"decimal"},"pre, blockquote":{margin:"1em 0"},pre:{padding:"0.4em 0.6em",whiteSpace:"pre-wrap",wordWrap:"break-word",background:"rgba(150, 150, 150, 0.1)",border:"1px solid rgba(100, 100, 100, 0.2)",borderRadius:3,fontFamily:e.fontFamilyCode,code:{display:"inline",margin:0,padding:0,fontSize:"inherit",fontFamily:"inherit",background:"transparent",border:0}},blockquote:{paddingInline:"0.6em 0",paddingBlock:0,borderInlineStart:"4px solid rgba(100, 100, 100, 0.2)",opacity:.85}}),D=e=>{let{componentCls:t,paddingSM:n}=e;return{"&-edit-content":{position:"relative","div&":{insetInlineStart:e.calc(e.paddingSM).mul(-1).equal(),marginTop:e.calc(n).mul(-1).equal(),marginBottom:`calc(1em - ${(0,$.bf)(n)})`},[`${t}-edit-content-confirm`]:{position:"absolute",insetInlineEnd:e.calc(e.marginXS).add(2).equal(),insetBlockEnd:e.marginXS,color:e.colorIcon,fontWeight:"normal",fontSize:e.fontSize,fontStyle:"normal",pointerEvents:"none"},textarea:{margin:"0!important",MozTransition:"none",height:"1em"}}}},H=e=>({[`${e.componentCls}-copy-success`]:{[`
    &,
    &:hover,
    &:focus`]:{color:e.colorSuccess}},[`${e.componentCls}-copy-icon-only`]:{marginInlineStart:0}}),M=()=>({[`
  a&-ellipsis,
  span&-ellipsis
  `]:{display:"inline-block",maxWidth:"100%"},"&-ellipsis-single-line":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis","a&, span&":{verticalAlign:"bottom"},"> code":{paddingBlock:0,maxWidth:"calc(100% - 1.2em)",display:"inline-block",overflow:"hidden",textOverflow:"ellipsis",verticalAlign:"bottom",boxSizing:"content-box"}},"&-ellipsis-multiple-line":{display:"-webkit-box",overflow:"hidden",WebkitLineClamp:3,WebkitBoxOrient:"vertical"}}),z=e=>{let{componentCls:t,titleMarginTop:n}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({color:e.colorText,wordBreak:"break-word",lineHeight:e.lineHeight,[`&${t}-secondary`]:{color:e.colorTextDescription},[`&${t}-success`]:{color:e.colorSuccessText},[`&${t}-warning`]:{color:e.colorWarningText},[`&${t}-danger`]:{color:e.colorErrorText,"a&:active, a&:focus":{color:e.colorErrorTextActive},"a&:hover":{color:e.colorErrorTextHover}},[`&${t}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed",userSelect:"none"},[`
        div&,
        p
      `]:{marginBottom:"1em"}},T(e)),{[`
      & + h1${t},
      & + h2${t},
      & + h3${t},
      & + h4${t},
      & + h5${t}
      `]:{marginTop:n},[`
      div,
      ul,
      li,
      p,
      h1,
      h2,
      h3,
      h4,
      h5`]:{[`
        + h1,
        + h2,
        + h3,
        + h4,
        + h5
        `]:{marginTop:n}}}),I(e)),Z(e)),{[`
        ${t}-expand,
        ${t}-collapse,
        ${t}-edit,
        ${t}-copy
      `]:Object.assign(Object.assign({},(0,j.Nd)(e)),{marginInlineStart:e.marginXXS})}),D(e)),H(e)),M()),{"&-rtl":{direction:"rtl"}})}},B=(0,C.I$)("Typography",e=>[z(e)],()=>({titleMarginTop:"1.2em",titleMarginBottom:"0.5em"})),P=e=>{let{prefixCls:t,"aria-label":n,className:r,style:o,direction:a,maxLength:c,autoSize:s=!0,value:u,onSave:d,onCancel:p,onEnd:f,component:m,enterIcon:g=l.createElement(O,null)}=e,b=l.useRef(null),y=l.useRef(!1),v=l.useRef(null),[h,x]=l.useState(u);l.useEffect(()=>{x(u)},[u]),l.useEffect(()=>{var e;if(null===(e=b.current)||void 0===e?void 0:e.resizableTextArea){let{textArea:e}=b.current.resizableTextArea;e.focus();let{length:t}=e.value;e.setSelectionRange(t,t)}},[]);let j=()=>{d(h.trim())},[C,k,$]=B(t),R=i()(t,`${t}-edit-content`,{[`${t}-rtl`]:"rtl"===a,[`${t}-${m}`]:!!m},r,k,$);return C(l.createElement("div",{className:R,style:o},l.createElement(S.Z,{ref:b,maxLength:c,value:h,onChange:({target:e})=>{x(e.value.replace(/[\n\r]/g,""))},onKeyDown:({keyCode:e})=>{y.current||(v.current=e)},onKeyUp:({keyCode:e,ctrlKey:t,altKey:n,metaKey:l,shiftKey:r})=>{v.current!==e||y.current||t||n||l||r||(e===E.Z.ENTER?(j(),null==f||f()):e===E.Z.ESC&&p())},onCompositionStart:()=>{y.current=!0},onCompositionEnd:()=>{y.current=!1},onBlur:()=>{j()},"aria-label":n,rows:1,autoSize:s}),null!==g?(0,w.Tm)(g,{className:`${t}-edit-content-confirm`}):null))};var N=n(12300),L=n.n(N),A=n(67827);let W=(e,t=!1)=>t&&null==e?[]:Array.isArray(e)?e:[e],F=({copyConfig:e,children:t})=>{let[n,r]=l.useState(!1),[o,a]=l.useState(!1),i=l.useRef(null),c=()=>{i.current&&clearTimeout(i.current)},s={};return e.format&&(s.format=e.format),l.useEffect(()=>c,[]),{copied:n,copyLoading:o,onClick:(0,A.Z)(n=>(function(e,t,n,l){return new(n||(n=Promise))(function(r,o){function a(e){try{c(l.next(e))}catch(e){o(e)}}function i(e){try{c(l.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?r(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(a,i)}c((l=l.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var l;null==n||n.preventDefault(),null==n||n.stopPropagation(),a(!0);try{let o="function"==typeof e.text?yield e.text():e.text;L()(o||W(t,!0).join("")||"",s),a(!1),r(!0),c(),i.current=setTimeout(()=>{r(!1)},3e3),null===(l=e.onCopy)||void 0===l||l.call(e,n)}catch(e){throw a(!1),e}}))}};function U(e,t){return l.useMemo(()=>{let n=!!e;return[n,Object.assign(Object.assign({},t),n&&"object"==typeof e?e:null)]},[e])}let V=e=>{let t=(0,l.useRef)(void 0);return(0,l.useEffect)(()=>{t.current=e}),t.current},q=(e,t,n)=>(0,l.useMemo)(()=>!0===e?{title:null!=t?t:n}:(0,l.isValidElement)(e)?{title:e}:"object"==typeof e?Object.assign({title:null!=t?t:n},e):{title:e},[e,t,n]);var X=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]]);return n};let G=l.forwardRef((e,t)=>{let{prefixCls:n,component:r="article",className:o,rootClassName:a,setContentRef:c,children:s,direction:u,style:d}=e,p=X(e,["prefixCls","component","className","rootClassName","setContentRef","children","direction","style"]),{getPrefixCls:m,direction:b,className:y,style:v}=(0,g.dj)("typography"),h=c?(0,f.sQ)(t,c):t,x=m("typography",n),[O,E,w]=B(x),S=i()(x,y,{[`${x}-rtl`]:"rtl"===(null!=u?u:b)},o,a,E,w),j=Object.assign(Object.assign({},v),d);return O(l.createElement(r,Object.assign({className:S,style:j,ref:h},p),s))});var K=n(97147);let Q={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"}}]},name:"copy",theme:"outlined"};var _=l.forwardRef(function(e,t){return l.createElement(x.Z,(0,v.Z)({},e,{ref:t,icon:Q}))}),J=n(31529);function Y(e){return!1===e?[!1,!1]:Array.isArray(e)?e:[e]}function ee(e,t,n){return!0===e||void 0===e?t:e||n&&t}let et=e=>["string","number"].includes(typeof e),en=({prefixCls:e,copied:t,locale:n,iconOnly:r,tooltips:o,icon:a,tabIndex:c,onCopy:s,loading:u})=>{let d=Y(o),p=Y(a),{copied:f,copy:m}=null!=n?n:{},g=t?f:m,b=ee(d[t?1:0],g),v="string"==typeof b?b:g;return l.createElement(y.Z,{title:b},l.createElement("button",{type:"button",className:i()(`${e}-copy`,{[`${e}-copy-success`]:t,[`${e}-copy-icon-only`]:r}),onClick:s,"aria-label":v,tabIndex:c},t?ee(p[1],l.createElement(K.Z,null),!0):ee(p[0],u?l.createElement(J.Z,null):l.createElement(_,null),!0)))},el=l.forwardRef(({style:e,children:t},n)=>{let r=l.useRef(null);return l.useImperativeHandle(n,()=>({isExceed:()=>{let e=r.current;return e.scrollHeight>e.clientHeight},getHeight:()=>r.current.clientHeight})),l.createElement("span",{"aria-hidden":!0,ref:r,style:Object.assign({position:"fixed",display:"block",left:0,top:0,pointerEvents:"none",backgroundColor:"rgba(255, 0, 0, 0.65)"},e)},t)}),er=e=>e.reduce((e,t)=>e+(et(t)?String(t).length:1),0);function eo(e,t){let n=0,l=[];for(let r=0;r<e.length;r+=1){if(n===t)return l;let o=e[r],a=n+(et(o)?String(o).length:1);if(a>t){let e=t-n;return l.push(String(o).slice(0,e)),l}l.push(o),n=a}return e}let ea={display:"-webkit-box",overflow:"hidden",WebkitBoxOrient:"vertical"};function ei(e){let{enableMeasure:t,width:n,text:o,children:a,rows:i,expanded:c,miscDeps:d,onEllipsis:p}=e,f=l.useMemo(()=>(0,s.Z)(o),[o]),m=l.useMemo(()=>er(f),[o]),g=l.useMemo(()=>a(f,!1),[o]),[b,y]=l.useState(null),v=l.useRef(null),h=l.useRef(null),x=l.useRef(null),O=l.useRef(null),E=l.useRef(null),[w,S]=l.useState(!1),[j,C]=l.useState(0),[k,$]=l.useState(0),[R,T]=l.useState(null);(0,u.Z)(()=>{t&&n&&m?C(1):C(0)},[n,o,i,t,f]),(0,u.Z)(()=>{var e,t,n,l;if(1===j)C(2),T(h.current&&getComputedStyle(h.current).whiteSpace);else if(2===j){let r=!!(null===(e=x.current)||void 0===e?void 0:e.isExceed());C(r?3:4),y(r?[0,m]:null),S(r);let o=(null===(t=x.current)||void 0===t?void 0:t.getHeight())||0;$(Math.max(o,(1===i?0:(null===(n=O.current)||void 0===n?void 0:n.getHeight())||0)+((null===(l=E.current)||void 0===l?void 0:l.getHeight())||0))+1),p(r)}},[j]);let Z=b?Math.ceil((b[0]+b[1])/2):0;(0,u.Z)(()=>{var e;let[t,n]=b||[0,0];if(t!==n){let l=((null===(e=v.current)||void 0===e?void 0:e.getHeight())||0)>k,r=Z;n-t==1&&(r=l?t:n),y(l?[t,r]:[r,n])}},[b,Z]);let I=l.useMemo(()=>{if(!t)return a(f,!1);if(3!==j||!b||b[0]!==b[1]){let e=a(f,!1);return[4,0].includes(j)?e:l.createElement("span",{style:Object.assign(Object.assign({},ea),{WebkitLineClamp:i})},e)}return a(c?f:eo(f,b[0]),w)},[c,j,b,f].concat((0,r.Z)(d))),D={width:n,margin:0,padding:0,whiteSpace:"nowrap"===R?"normal":"inherit"};return l.createElement(l.Fragment,null,I,2===j&&l.createElement(l.Fragment,null,l.createElement(el,{style:Object.assign(Object.assign(Object.assign({},D),ea),{WebkitLineClamp:i}),ref:x},g),l.createElement(el,{style:Object.assign(Object.assign(Object.assign({},D),ea),{WebkitLineClamp:i-1}),ref:O},g),l.createElement(el,{style:Object.assign(Object.assign(Object.assign({},D),ea),{WebkitLineClamp:1}),ref:E},a([],!0))),3===j&&b&&b[0]!==b[1]&&l.createElement(el,{style:Object.assign(Object.assign({},D),{top:400}),ref:v},a(eo(f,Z),!0)),1===j&&l.createElement("span",{style:{whiteSpace:"inherit"},ref:h}))}let ec=({enableEllipsis:e,isEllipsis:t,children:n,tooltipProps:r})=>(null==r?void 0:r.title)&&e?l.createElement(y.Z,Object.assign({open:!!t&&void 0},r),n):n;var es=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]]);return n};let eu=["delete","mark","code","underline","strong","keyboard","italic"],ed=l.forwardRef((e,t)=>{var n;let{prefixCls:a,className:v,style:h,type:x,disabled:O,children:E,ellipsis:w,editable:S,copyable:j,component:C,title:k}=e,$=es(e,["prefixCls","className","style","type","disabled","children","ellipsis","editable","copyable","component","title"]),{getPrefixCls:R,direction:T}=l.useContext(g.E_),[Z]=(0,b.Z)("Text"),I=l.useRef(null),D=l.useRef(null),H=R("typography",a),M=(0,p.Z)($,eu),[z,B]=U(S),[N,L]=(0,d.Z)(!1,{value:B.editing}),{triggerType:A=["icon"]}=B,W=e=>{var t;e&&(null===(t=B.onStart)||void 0===t||t.call(B)),L(e)},X=V(N);(0,u.Z)(()=>{var e;!N&&X&&(null===(e=D.current)||void 0===e||e.focus())},[N]);let K=e=>{null==e||e.preventDefault(),W(!0)},[Q,_]=U(j),{copied:J,copyLoading:Y,onClick:ee}=F({copyConfig:_,children:E}),[el,er]=l.useState(!1),[eo,ea]=l.useState(!1),[ed,ep]=l.useState(!1),[ef,em]=l.useState(!1),[eg,eb]=l.useState(!0),[ey,ev]=U(w,{expandable:!1,symbol:e=>e?null==Z?void 0:Z.collapse:null==Z?void 0:Z.expand}),[eh,ex]=(0,d.Z)(ev.defaultExpanded||!1,{value:ev.expanded}),eO=ey&&(!eh||"collapsible"===ev.expandable),{rows:eE=1}=ev,ew=l.useMemo(()=>eO&&(void 0!==ev.suffix||ev.onEllipsis||ev.expandable||z||Q),[eO,ev,z,Q]);(0,u.Z)(()=>{ey&&!ew&&(er((0,m.G)("webkitLineClamp")),ea((0,m.G)("textOverflow")))},[ew,ey]);let[eS,ej]=l.useState(eO),eC=l.useMemo(()=>!ew&&(1===eE?eo:el),[ew,eo,el]);(0,u.Z)(()=>{ej(eC&&eO)},[eC,eO]);let ek=eO&&(eS?ef:ed),e$=eO&&1===eE&&eS,eR=eO&&eE>1&&eS,eT=(e,t)=>{var n;ex(t.expanded),null===(n=ev.onExpand)||void 0===n||n.call(ev,e,t)},[eZ,eI]=l.useState(0),eD=e=>{var t;ep(e),ed!==e&&(null===(t=ev.onEllipsis)||void 0===t||t.call(ev,e))};l.useEffect(()=>{let e=I.current;if(ey&&eS&&e){let t=function(e){let t=document.createElement("em");e.appendChild(t);let n=e.getBoundingClientRect(),l=t.getBoundingClientRect();return e.removeChild(t),n.left>l.left||l.right>n.right||n.top>l.top||l.bottom>n.bottom}(e);ef!==t&&em(t)}},[ey,eS,E,eR,eg,eZ]),l.useEffect(()=>{let e=I.current;if("undefined"==typeof IntersectionObserver||!e||!eS||!eO)return;let t=new IntersectionObserver(()=>{eb(!!e.offsetParent)});return t.observe(e),()=>{t.disconnect()}},[eS,eO]);let eH=q(ev.tooltip,B.text,E),eM=l.useMemo(()=>{if(ey&&!eS)return[B.text,E,k,eH.title].find(et)},[ey,eS,k,eH.title,ek]);if(N)return l.createElement(P,{value:null!==(n=B.text)&&void 0!==n?n:"string"==typeof E?E:"",onSave:e=>{var t;null===(t=B.onChange)||void 0===t||t.call(B,e),W(!1)},onCancel:()=>{var e;null===(e=B.onCancel)||void 0===e||e.call(B),W(!1)},onEnd:B.onEnd,prefixCls:H,className:v,style:h,direction:T,component:C,maxLength:B.maxLength,autoSize:B.autoSize,enterIcon:B.enterIcon});let ez=()=>{let{expandable:e,symbol:t}=ev;return e?l.createElement("button",{type:"button",key:"expand",className:`${H}-${eh?"collapse":"expand"}`,onClick:e=>eT(e,{expanded:!eh}),"aria-label":eh?Z.collapse:null==Z?void 0:Z.expand},"function"==typeof t?t(eh):t):null},eB=()=>{if(!z)return;let{icon:e,tooltip:t,tabIndex:n}=B,r=(0,s.Z)(t)[0]||(null==Z?void 0:Z.edit),a="string"==typeof r?r:"";return A.includes("icon")?l.createElement(y.Z,{key:"edit",title:!1===t?"":r},l.createElement("button",{type:"button",ref:D,className:`${H}-edit`,onClick:K,"aria-label":a,tabIndex:n},e||l.createElement(o.Z,{role:"button"}))):null},eP=()=>Q?l.createElement(en,Object.assign({key:"copy"},_,{prefixCls:H,copied:J,locale:Z,onCopy:ee,loading:Y,iconOnly:null==E})):null,eN=e=>[e&&ez(),eB(),eP()],eL=e=>[e&&!eh&&l.createElement("span",{"aria-hidden":!0,key:"ellipsis"},"..."),ev.suffix,eN(e)];return l.createElement(c.Z,{onResize:({offsetWidth:e})=>{eI(e)},disabled:!eO},n=>l.createElement(ec,{tooltipProps:eH,enableEllipsis:eO,isEllipsis:ek},l.createElement(G,Object.assign({className:i()({[`${H}-${x}`]:x,[`${H}-disabled`]:O,[`${H}-ellipsis`]:ey,[`${H}-ellipsis-single-line`]:e$,[`${H}-ellipsis-multiple-line`]:eR},v),prefixCls:a,style:Object.assign(Object.assign({},h),{WebkitLineClamp:eR?eE:void 0}),component:C,ref:(0,f.sQ)(n,I,t),direction:T,onClick:A.includes("text")?K:void 0,"aria-label":null==eM?void 0:eM.toString(),title:k},M),l.createElement(ei,{enableMeasure:eO&&!eS,text:E,rows:eE,width:eZ,onEllipsis:eD,expanded:eh,miscDeps:[J,eh,Y,z,Q,Z].concat((0,r.Z)(eu.map(t=>e[t])))},(t,n)=>(function({mark:e,code:t,underline:n,delete:r,strong:o,keyboard:a,italic:i},c){let s=c;function u(e,t){t&&(s=l.createElement(e,{},s))}return u("strong",o),u("u",n),u("del",r),u("code",t),u("mark",e),u("kbd",a),u("i",i),s})(e,l.createElement(l.Fragment,null,t.length>0&&n&&!eh&&eM?l.createElement("span",{key:"show-content","aria-hidden":!0},t):t,eL(n)))))))});var ep=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]]);return n};let ef=l.forwardRef((e,t)=>{var{ellipsis:n,rel:r}=e,o=ep(e,["ellipsis","rel"]);let a=Object.assign(Object.assign({},o),{rel:void 0===r&&"_blank"===o.target?"noopener noreferrer":r});return delete a.navigate,l.createElement(ed,Object.assign({},a,{ref:t,ellipsis:!!n,component:"a"}))}),em=l.forwardRef((e,t)=>l.createElement(ed,Object.assign({ref:t},e,{component:"div"})));var eg=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]]);return n};let eb=l.forwardRef((e,t)=>{var{ellipsis:n}=e,r=eg(e,["ellipsis"]);let o=l.useMemo(()=>n&&"object"==typeof n?(0,p.Z)(n,["expandable","rows"]):n,[n]);return l.createElement(ed,Object.assign({ref:t},r,{ellipsis:o,component:"span"}))});var ey=function(e,t){var n={};for(var l in e)Object.prototype.hasOwnProperty.call(e,l)&&0>t.indexOf(l)&&(n[l]=e[l]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,l=Object.getOwnPropertySymbols(e);r<l.length;r++)0>t.indexOf(l[r])&&Object.prototype.propertyIsEnumerable.call(e,l[r])&&(n[l[r]]=e[l[r]]);return n};let ev=[1,2,3,4,5],eh=l.forwardRef((e,t)=>{let{level:n=1}=e,r=ey(e,["level"]),o=ev.includes(n)?`h${n}`:"h1";return l.createElement(ed,Object.assign({ref:t},r,{component:o}))});G.Text=eb,G.Link=ef,G.Title=eh,G.Paragraph=em;let ex=G},12300:(e,t,n)=>{"use strict";var l=n(80514),r={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,t){var n,o,a,i,c,s,u,d,p=!1;t||(t={}),a=t.debug||!1;try{if(c=l(),s=document.createRange(),u=document.getSelection(),(d=document.createElement("span")).textContent=e,d.ariaHidden="true",d.style.all="unset",d.style.position="fixed",d.style.top=0,d.style.clip="rect(0, 0, 0, 0)",d.style.whiteSpace="pre",d.style.webkitUserSelect="text",d.style.MozUserSelect="text",d.style.msUserSelect="text",d.style.userSelect="text",d.addEventListener("copy",function(n){if(n.stopPropagation(),t.format){if(n.preventDefault(),void 0===n.clipboardData){a&&console.warn("unable to use e.clipboardData"),a&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var l=r[t.format]||r.default;window.clipboardData.setData(l,e)}else n.clipboardData.clearData(),n.clipboardData.setData(t.format,e)}t.onCopy&&(n.preventDefault(),t.onCopy(n.clipboardData))}),document.body.appendChild(d),s.selectNodeContents(d),u.addRange(s),!document.execCommand("copy"))throw Error("copy command was unsuccessful");p=!0}catch(l){a&&console.error("unable to copy using execCommand: ",l),a&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(t.format||"text",e),t.onCopy&&t.onCopy(window.clipboardData),p=!0}catch(l){a&&console.error("unable to copy using clipboardData: ",l),a&&console.error("falling back to prompt"),n="message"in t?t.message:"Copy to clipboard: #{key}, Enter",o=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",i=n.replace(/#{\s*key\s*}/g,o),window.prompt(i,e)}}finally{u&&("function"==typeof u.removeRange?u.removeRange(s):u.removeAllRanges()),d&&document.body.removeChild(d),c()}return p}},80514:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var t=document.activeElement,n=[],l=0;l<e.rangeCount;l++)n.push(e.getRangeAt(l));switch(t.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":t.blur();break;default:t=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||n.forEach(function(t){e.addRange(t)}),t&&t.focus()}}}};