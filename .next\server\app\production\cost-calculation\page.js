(()=>{var e={};e.id=8522,e.ids=[8522],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},82361:e=>{"use strict";e.exports=require("events")},44478:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=a(50482),r=a(69108),i=a(62563),o=a.n(i),l=a(68300),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);a.d(t,n);let c=["",{children:["production",{children:["cost-calculation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,23057)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\cost-calculation\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,18223)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,14499)),"C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\erp软件\\src\\app\\production\\cost-calculation\\page.tsx"],m="/production/cost-calculation/page",u={require:a,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/production/cost-calculation/page",pathname:"/production/cost-calculation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89712:(e,t,a)=>{Promise.resolve().then(a.bind(a,82124))},4818:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(65651),r=a(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"};var o=a(49809);let l=r.forwardRef(function(e,t){return r.createElement(o.Z,(0,s.Z)({},e,{ref:t,icon:i}))})},55741:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(65651),r=a(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M888 792H200V168c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v688c0 4.4 3.6 8 8 8h752c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm-600-80h56c4.4 0 8-3.6 8-8V560c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v144c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V384c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v320c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V462c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v242c0 4.4 3.6 8 8 8zm152 0h56c4.4 0 8-3.6 8-8V304c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v400c0 4.4 3.6 8 8 8z"}}]},name:"bar-chart",theme:"outlined"};var o=a(49809);let l=r.forwardRef(function(e,t){return r.createElement(o.Z,(0,s.Z)({},e,{ref:t,icon:i}))})},32646:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(65651),r=a(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M251.2 387H320v68.8c0 1.8 1.8 3.2 4 3.2h48c2.2 0 4-1.4 4-3.3V387h68.8c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H376v-68.8c0-1.8-1.8-3.2-4-3.2h-48c-2.2 0-4 1.4-4 3.2V331h-68.8c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm328 0h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 265h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 104h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm-195.7-81l61.2-74.9c4.3-5.2.7-13.1-5.9-13.1H388c-2.3 0-4.5 1-5.9 2.9l-34 41.6-34-41.6a7.85 7.85 0 00-5.9-2.9h-50.9c-6.6 0-10.2 7.9-5.9 13.1l61.2 74.9-62.7 76.8c-4.4 5.2-.8 13.1 5.8 13.1h50.8c2.3 0 4.5-1 5.9-2.9l35.5-43.5 35.5 43.5c1.5 1.8 3.7 2.9 5.9 2.9h50.8c6.6 0 10.2-7.9 5.9-13.1L383.5 675zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-36 732H180V180h664v664z"}}]},name:"calculator",theme:"outlined"};var o=a(49809);let l=r.forwardRef(function(e,t){return r.createElement(o.Z,(0,s.Z)({},e,{ref:t,icon:i}))})},98021:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(65651),r=a(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"};var o=a(49809);let l=r.forwardRef(function(e,t){return r.createElement(o.Z,(0,s.Z)({},e,{ref:t,icon:i}))})},15595:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(65651),r=a(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"};var o=a(49809);let l=r.forwardRef(function(e,t){return r.createElement(o.Z,(0,s.Z)({},e,{ref:t,icon:i}))})},73716:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(65651),r=a(3729);let i={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M864 518H506V160c0-4.4-3.6-8-8-8h-26a398.46 398.46 0 00-282.8 117.1 398.19 398.19 0 00-85.7 127.1A397.61 397.61 0 0072 552a398.46 398.46 0 00117.1 282.8c36.7 36.7 79.5 65.6 127.1 85.7A397.61 397.61 0 00472 952a398.46 398.46 0 00282.8-117.1c36.7-36.7 65.6-79.5 85.7-127.1A397.61 397.61 0 00872 552v-26c0-4.4-3.6-8-8-8zM705.7 787.8A331.59 331.59 0 01470.4 884c-88.1-.4-170.9-34.9-233.2-97.2C174.5 724.1 140 640.7 140 552c0-88.7 34.5-172.1 97.2-234.8 54.6-54.6 124.9-87.9 200.8-95.5V586h364.3c-7.7 76.3-41.3 147-96.6 201.8zM952 462.4l-2.6-28.2c-8.5-92.1-49.4-179-115.2-244.6A399.4 399.4 0 00589 74.6L560.7 72c-4.7-.4-8.7 3.2-8.7 7.9V464c0 4.4 3.6 8 8 8l384-1c4.7 0 8.4-4 8-8.6zm-332.2-58.2V147.6a332.24 332.24 0 01166.4 89.8c45.7 45.6 77 103.6 90 166.1l-256.4.7z"}}]},name:"pie-chart",theme:"outlined"};var o=a(49809);let l=r.forwardRef(function(e,t){return r.createElement(o.Z,(0,s.Z)({},e,{ref:t,icon:i}))})},82124:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>F});var s=a(95344),r=a(3729),i=a(97854),o=a(97557),l=a(32979),n=a(7618),c=a(10707),d=a(11157),m=a(39470),u=a(63724),h=a(27976),x=a(83984),p=a(43896),g=a(87049),j=a(53869),f=a(36527),v=a(14223),y=a(45850),C=a(16408),Z=a(284),M=a(6025),w=a(96266),P=a(3745),b=a(32646),N=a(98021),I=a(44670),k=a(55741),A=a(75397),S=a(4818),R=a(15595),q=a(73716);class V{static calculateFormingCost(e,t){let a=e.reduce((e,t)=>e+t.completedMolds,0),s=t.formingPiecePrice,r=new Map;e.forEach(e=>{let t=r.get(e.operatorId)||{molds:0,operatorName:e.operatorName};t.molds+=e.completedMolds,r.set(e.operatorId,t)});let i=Array.from(r.entries()).map(([e,t])=>({operatorId:e,operatorName:t.operatorName,completedMolds:t.molds,pieceRate:s,wage:t.molds*s}));return{totalMolds:a,pieceRate:s,totalWage:i.reduce((e,t)=>e+t.wage,0),operatorRecords:i}}static calculateHotPressCost(e,t){let a=e.filter(e=>e.isActive),s=a.reduce((e,t)=>e+t.bindingMolds,0),r=t.hotPressPiecePrice,i=a.map(e=>({employeeId:e.employeeId,employeeName:e.employeeName,bindingMolds:e.bindingMolds,pieceRate:r,wage:e.bindingMolds*r}));return{totalMolds:s,pieceRate:r,totalWage:i.reduce((e,t)=>e+t.wage,0),employeeBindings:i}}static calculateMaterialCost(e,t,a,s){let r=t*a/1e6;return{materialCode:`MAT-${e}`,materialName:`${e}原料`,usedQuantity:r,unitPrice:s,totalCost:r*s}}static executeFullCostCalculation(e,t,a,s,r=250,i=1500){let o=this.calculateFormingCost(a,t),l=this.calculateHotPressCost(s,t),n=this.calculateMaterialCost(t.modelCode,o.totalMolds,r,i),c=o.totalWage+l.totalWage+n.totalCost,d=o.totalMolds>0?c/o.totalMolds:0,m=o.totalMolds*t.productPrice,u=m-c;return{id:`cost-${e}-${Date.now()}`,calculationDate:new Date().toISOString().split("T")[0],productionOrderId:e,productModelCode:t.modelCode,totalMolds:o.totalMolds,formingCost:o,hotPressCost:l,materialCost:n,totalCost:Math.round(100*c)/100,unitCost:Math.round(100*d)/100,totalRevenue:Math.round(1e3*m)/1e3,totalProfit:Math.round(1e3*u)/1e3,profitMargin:Math.round(100*(m>0?u/m*100:0))/100,createdAt:new Date().toISOString()}}static performCostReconciliation(e,t,a,s,r,i){let o=t.reduce((e,t)=>e+t.bindingMolds,0),l=a+s,n=o-l,c=l>0?100*Math.abs(n/l):0,d=c<=5;return{id:`reconciliation-${e}-${Date.now()}`,reconciliationDate:new Date().toISOString().split("T")[0],productModelCode:e,hotPressQuantityTotal:o,warehouseQuantity:a,onSiteQuantity:s,bindingQuantityTotal:o,quantityVariance:n,variancePercentage:Math.round(100*c)/100,isVarianceAcceptable:d,varianceReason:d?void 0:`数量差异超过5%阈值，实际差异${c.toFixed(2)}%`,materialTheoreticalUsage:r,materialActualUsage:i,materialVariance:i-r,status:"pending",createdAt:new Date().toISOString()}}static generateCostAnalysisReport(e){let t=e.reduce((e,t)=>e+t.totalMolds,0),a=e.reduce((e,t)=>e+t.totalCost,0),s=e.reduce((e,t)=>e+t.formingCost.totalWage,0),r=e.reduce((e,t)=>e+t.hotPressCost.totalWage,0),i=e.reduce((e,t)=>e+t.materialCost.totalCost,0),o={formingCostRatio:a>0?s/a*100:0,hotPressCostRatio:a>0?r/a*100:0,materialCostRatio:a>0?i/a*100:0},l=new Map;e.forEach(e=>{let t=e.calculationDate,a=l.get(t)||{totalCost:0,totalMolds:0};a.totalCost+=e.totalCost,a.totalMolds+=e.totalMolds,l.set(t,a)});let n=Array.from(l.entries()).map(([e,t])=>({period:e,unitCost:t.totalMolds>0?t.totalCost/t.totalMolds:0,totalMolds:t.totalMolds})).sort((e,t)=>e.period.localeCompare(t.period));return{totalProduction:t,totalCost:Math.round(100*a)/100,averageUnitCost:Math.round(100*(t>0?a/t:0))/100,costBreakdown:{formingCostRatio:Math.round(100*o.formingCostRatio)/100,hotPressCostRatio:Math.round(100*o.hotPressCostRatio)/100,materialCostRatio:Math.round(100*o.materialCostRatio)/100},trends:n}}static detectCostAnomalies(e,t){let a=e.unitCost,s=t>0?(a-t)/t*100:0;return Math.abs(s)>20?{hasAnomaly:!0,anomalyType:s>0?"high_cost":"low_efficiency",anomalyMessage:`单位成本异常：当前${a.toFixed(2)}元/模，偏离历史平均${Math.abs(s).toFixed(1)}%`,suggestions:s>0?["检查材料用量是否超标","核实计件工资计算","分析生产效率"]:["确认数据准确性","分析效率提升原因"]}:{hasAnomaly:!1}}}let{Option:z}=i.default,{RangePicker:D}=o.default,F=()=>{let{modal:e}=l.Z.useApp(),[t]=n.Z.useForm(),[a,o]=(0,r.useState)(!1),[D,F]=(0,r.useState)(!1),[T,Q]=(0,r.useState)(!1),[_,E]=(0,r.useState)("calculations"),[U]=(0,r.useState)([{id:"1",modelCode:"JJS-0001",modelName:"精密机械组件A",formingMold:"M-JX-05",formingMoldQuantity:4,hotPressMold:"M-RY-12",hotPressMoldQuantity:2,piecesPerMold:4,formingPiecePrice:8,hotPressPiecePrice:2.5,productPrice:.15,productWeight:12.5,boxSpecification:"30\xd720\xd715 cm",packingQuantity:100,status:"active",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"},{id:"2",modelCode:"JJS-0002",modelName:"电子控制器B",formingMold:"M-JX-08",formingMoldQuantity:6,hotPressMold:"M-RY-15",hotPressMoldQuantity:3,piecesPerMold:6,formingPiecePrice:12,hotPressPiecePrice:3.5,productPrice:.258,productWeight:18.6,boxSpecification:"25\xd715\xd78 cm",packingQuantity:80,status:"active",createdAt:"2024-01-01T00:00:00Z",updatedAt:"2024-01-01T00:00:00Z"}]),[B,O]=(0,r.useState)([{id:"cost-001",calculationDate:"2024-01-15",productionOrderId:"order-001",productModelCode:"JJS-0001",totalMolds:1500,formingCost:{totalMolds:1500,pieceRate:8,totalWage:12e3,operatorRecords:[{operatorId:"OP001",operatorName:"张师傅",completedMolds:800,pieceRate:8,wage:6400},{operatorId:"OP002",operatorName:"李师傅",completedMolds:700,pieceRate:8,wage:5600}]},hotPressCost:{totalMolds:1500,pieceRate:2.5,totalWage:3750,employeeBindings:[{employeeId:"EMP001",employeeName:"王师傅",bindingMolds:800,pieceRate:2.5,wage:2e3},{employeeId:"EMP002",employeeName:"赵师傅",bindingMolds:700,pieceRate:2.5,wage:1750}]},materialCost:{materialCode:"MAT-CP-202",materialName:"CP-202原料",usedQuantity:.375,unitPrice:1500,totalCost:562.5},totalCost:16312.5,unitCost:10.875,createdAt:"2024-01-15T18:00:00Z"}]),[$,H]=(0,r.useState)([{id:"reconciliation-001",reconciliationDate:"2024-01-15",productModelCode:"JJS-0001",hotPressQuantityTotal:1500,warehouseQuantity:1450,onSiteQuantity:45,bindingQuantityTotal:1500,quantityVariance:5,variancePercentage:.33,isVarianceAcceptable:!0,materialTheoreticalUsage:375,materialActualUsage:380,materialVariance:5,status:"pending",createdAt:"2024-01-15T18:00:00Z"}]),W=V.generateCostAnalysisReport(B),J=async a=>{o(!0);try{let e=U.find(e=>e.modelCode===a.productModelCode);if(!e)throw Error("产品型号不存在");let s=[{id:"report-001",workstationId:"W1",operatorId:a.operatorId||"OP001",operatorName:a.operatorName||"操作员",reportTime:new Date().toISOString(),completedMolds:a.totalMolds,reportType:"normal",isValidated:!0,createdAt:new Date().toISOString()}],r=[{id:"binding-001",employeeId:a.employeeId||"EMP001",employeeName:a.employeeName||"员工",employeeCode:"E001",taskId:"task-001",batchNumber:"BATCH-001",bindingTime:new Date().toISOString(),scanMethod:"qr_code",bindingMolds:a.totalMolds,isActive:!0,createdAt:new Date().toISOString()}],i=V.executeFullCostCalculation(a.productionOrderId,e,s,r,a.materialUsageRate||250,a.materialUnitPrice||1500);O(e=>[i,...e]),F(!1),t.resetFields()}catch(t){e.error({title:"计算失败",content:"成本计算过程中发生错误，请重试"})}finally{o(!1)}},L=async a=>{o(!0);try{let e=[{id:"binding-001",employeeId:"EMP001",employeeName:"员工1",employeeCode:"E001",taskId:"task-001",batchNumber:"BATCH-001",bindingTime:new Date().toISOString(),scanMethod:"qr_code",bindingMolds:a.hotPressQuantity,isActive:!0,createdAt:new Date().toISOString()}],s=V.performCostReconciliation(a.productModelCode,e,a.warehouseQuantity,a.onSiteQuantity,a.theoreticalMaterialUsage,a.actualMaterialUsage);H(e=>[s,...e]),Q(!1),t.resetFields()}catch(t){e.error({title:"对账失败",content:"成本对账过程中发生错误，请重试"})}finally{o(!1)}};return(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx("div",{className:"page-header",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(b.Z,{className:"text-2xl text-purple-600 mr-3"}),(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"page-title",children:"成本计算"}),s.jsx("p",{className:"page-description",children:"生产成本计算和对账系统"})]})]}),(0,s.jsxs)(c.Z,{children:[s.jsx(d.ZP,{icon:s.jsx(N.Z,{}),children:"导出报表"}),s.jsx(d.ZP,{icon:s.jsx(I.Z,{}),onClick:()=>window.location.reload(),children:"刷新数据"})]})]})}),(0,s.jsxs)(u.Z,{gutter:[16,16],children:[s.jsx(h.Z,{xs:24,sm:6,children:s.jsx(x.Z,{children:s.jsx(p.Z,{title:"总生产量",value:W.totalProduction,suffix:"模",valueStyle:{color:"#1890ff"},prefix:s.jsx(k.Z,{})})})}),s.jsx(h.Z,{xs:24,sm:6,children:s.jsx(x.Z,{children:s.jsx(p.Z,{title:"总成本",value:W.totalCost,suffix:"元",valueStyle:{color:"#52c41a"},prefix:s.jsx(A.Z,{}),precision:2})})}),s.jsx(h.Z,{xs:24,sm:6,children:s.jsx(x.Z,{children:s.jsx(p.Z,{title:"平均单位成本",value:W.averageUnitCost,suffix:"元/模",valueStyle:{color:"#722ed1"},prefix:s.jsx(S.Z,{}),precision:3})})}),s.jsx(h.Z,{xs:24,sm:6,children:s.jsx(x.Z,{children:s.jsx(p.Z,{title:"待审核对账",value:$.filter(e=>"pending"===e.status).length,suffix:"项",valueStyle:{color:"#fa8c16"},prefix:s.jsx(R.Z,{})})})})]}),s.jsx(x.Z,{title:"成本结构分析",children:(0,s.jsxs)(u.Z,{gutter:[16,16],children:[s.jsx(h.Z,{xs:24,lg:12,children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("span",{children:"成型成本"}),(0,s.jsxs)("span",{className:"font-medium",children:[W.costBreakdown.formingCostRatio.toFixed(1),"%"]})]}),s.jsx(g.Z,{percent:W.costBreakdown.formingCostRatio,strokeColor:"#1890ff",showInfo:!1})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("span",{children:"热压成本"}),(0,s.jsxs)("span",{className:"font-medium",children:[W.costBreakdown.hotPressCostRatio.toFixed(1),"%"]})]}),s.jsx(g.Z,{percent:W.costBreakdown.hotPressCostRatio,strokeColor:"#52c41a",showInfo:!1})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("span",{children:"材料成本"}),(0,s.jsxs)("span",{className:"font-medium",children:[W.costBreakdown.materialCostRatio.toFixed(1),"%"]})]}),s.jsx(g.Z,{percent:W.costBreakdown.materialCostRatio,strokeColor:"#faad14",showInfo:!1})]})]})}),s.jsx(h.Z,{xs:24,lg:12,children:(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[s.jsx("h4",{className:"font-medium mb-3",children:"成本分析建议"}),(0,s.jsxs)("ul",{className:"space-y-2 text-sm",children:[s.jsx("li",{children:"• 成型成本占比最高，建议优化生产效率"}),s.jsx("li",{children:"• 材料成本相对稳定，关注原料价格波动"}),s.jsx("li",{children:"• 热压成本占比较低，工艺效率良好"}),s.jsx("li",{children:"• 建议定期对比历史数据，识别成本趋势"})]})]})})]})}),s.jsx(x.Z,{children:s.jsx(j.default,{activeKey:_,onChange:E,tabBarExtraContent:(0,s.jsxs)(c.Z,{children:[s.jsx(d.ZP,{type:"primary",icon:s.jsx(b.Z,{}),onClick:()=>F(!0),children:"新建成本计算"}),s.jsx(d.ZP,{icon:s.jsx(q.Z,{}),onClick:()=>Q(!0),children:"成本对账"})]}),items:[{key:"calculations",label:"成本计算记录",children:s.jsx(f.Z,{columns:[{title:"计算日期",dataIndex:"calculationDate",key:"calculationDate",width:100},{title:"产品型号",dataIndex:"productModelCode",key:"productModelCode",width:100},{title:"总模数",dataIndex:"totalMolds",key:"totalMolds",width:80,render:e=>`${e} 模`},{title:"成型成本",key:"formingCost",width:100,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"font-medium",children:["\xa5",t.formingCost.totalWage.toFixed(2)]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["\xa5",t.formingCost.pieceRate,"/模"]})]})},{title:"热压成本",key:"hotPressCost",width:100,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"font-medium",children:["\xa5",t.hotPressCost.totalWage.toFixed(2)]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["\xa5",t.hotPressCost.pieceRate,"/模"]})]})},{title:"材料成本",key:"materialCost",width:100,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"font-medium",children:["\xa5",t.materialCost.totalCost.toFixed(2)]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:[t.materialCost.usedQuantity.toFixed(3),"吨"]})]})},{title:"总成本",dataIndex:"totalCost",key:"totalCost",width:100,render:e=>(0,s.jsxs)("div",{className:"font-medium text-blue-600",children:["\xa5",e.toFixed(2)]})},{title:"总收入",dataIndex:"totalRevenue",key:"totalRevenue",width:100,render:e=>s.jsx("div",{className:"font-medium text-green-600",children:e?`\xa5${e.toFixed(3)}`:"-"})},{title:"总利润",dataIndex:"totalProfit",key:"totalProfit",width:100,render:e=>s.jsx("div",{className:`font-medium ${e&&e>=0?"text-green-600":"text-red-600"}`,children:e?`\xa5${e.toFixed(3)}`:"-"})},{title:"利润率",dataIndex:"profitMargin",key:"profitMargin",width:100,render:e=>s.jsx("div",{className:`font-medium ${e&&e>=0?"text-green-600":"text-red-600"}`,children:e?`${e.toFixed(1)}%`:"-"})},{title:"单位成本",dataIndex:"unitCost",key:"unitCost",width:100,render:e=>(0,s.jsxs)("div",{className:"font-medium text-purple-600",children:["\xa5",e.toFixed(3),"/模"]})},{title:"操作",key:"action",width:100,render:(e,t)=>s.jsx(c.Z,{size:"small",children:s.jsx(d.ZP,{type:"text",size:"small",icon:s.jsx(w.Z,{}),children:"详情"})})}],dataSource:B,rowKey:"id",pagination:{pageSize:10},scroll:{x:1e3}})},{key:"reconciliations",label:"对账记录",children:s.jsx(f.Z,{columns:[{title:"对账日期",dataIndex:"reconciliationDate",key:"reconciliationDate",width:100},{title:"产品型号",dataIndex:"productModelCode",key:"productModelCode",width:100},{title:"数量对账",key:"quantityReconciliation",width:150,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("span",{children:["热压: ",t.hotPressQuantityTotal]}),s.jsx("span",{className:"mx-2",children:"|"}),(0,s.jsxs)("span",{children:["入库: ",t.warehouseQuantity]})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["差异: ",t.quantityVariance," (",t.variancePercentage,"%)"]})]})},{title:"材料对账",key:"materialReconciliation",width:120,render:(e,t)=>(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-sm",children:["差异: ",t.materialVariance,"kg"]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["实际: ",t.materialActualUsage,"kg"]})]})},{title:"对账状态",key:"reconciliationStatus",width:120,render:(e,t)=>(0,s.jsxs)("div",{children:[s.jsx(m.Z,{status:t.isVarianceAcceptable?"success":"error",text:t.isVarianceAcceptable?"正常":"异常"}),s.jsx("div",{className:"text-xs text-gray-500 mt-1",children:"pending"===t.status?"待审核":"reviewed"===t.status?"已审核":"已批准"})]})},{title:"操作",key:"action",width:100,render:(e,t)=>s.jsx(c.Z,{size:"small",children:s.jsx(d.ZP,{type:"text",size:"small",icon:s.jsx(P.Z,{}),children:"审核"})})}],dataSource:$,rowKey:"id",pagination:{pageSize:10},scroll:{x:800}})},{key:"trends",label:"成本趋势",children:(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(v.Z,{message:"成本趋势分析",description:"基于历史数据分析成本变化趋势，帮助优化生产成本控制",type:"info",showIcon:!0}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded",children:[s.jsx("h4",{className:"font-medium mb-3",children:"趋势数据"}),s.jsx(y.Z,{dataSource:W.trends,renderItem:e=>s.jsx(y.Z.Item,{children:(0,s.jsxs)("div",{className:"flex justify-between items-center w-full",children:[s.jsx("span",{children:e.period}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"font-medium",children:["\xa5",e.unitCost.toFixed(3),"/模"]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[e.totalMolds," 模"]})]})]})})})]})]})}]})}),s.jsx(C.Z,{title:"新建成本计算",open:D,onCancel:()=>F(!1),onOk:()=>t.submit(),confirmLoading:a,width:800,children:(0,s.jsxs)(n.Z,{form:t,layout:"vertical",onFinish:J,children:[(0,s.jsxs)(u.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(n.Z.Item,{label:"生产订单ID",name:"productionOrderId",rules:[{required:!0,message:"请输入生产订单ID"}],children:s.jsx(Z.default,{placeholder:"请输入生产订单ID"})})}),s.jsx(h.Z,{span:12,children:s.jsx(n.Z.Item,{label:"产品型号",name:"productModelCode",rules:[{required:!0,message:"请选择产品型号"}],children:s.jsx(i.default,{placeholder:"请选择产品型号",children:U.map(e=>(0,s.jsxs)(z,{value:e.modelCode,children:[e.modelCode," - ",e.modelName]},e.modelCode))})})})]}),(0,s.jsxs)(u.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(n.Z.Item,{label:"总模数",name:"totalMolds",rules:[{required:!0,message:"请输入总模数"}],children:s.jsx(M.Z,{className:"w-full",placeholder:"请输入总模数",min:1,addonAfter:"模"})})}),s.jsx(h.Z,{span:12,children:s.jsx(n.Z.Item,{label:"材料使用率",name:"materialUsageRate",initialValue:250,children:s.jsx(M.Z,{className:"w-full",placeholder:"材料使用率",min:1,addonAfter:"克/模"})})})]}),(0,s.jsxs)(u.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(n.Z.Item,{label:"材料单价",name:"materialUnitPrice",initialValue:1500,children:s.jsx(M.Z,{className:"w-full",placeholder:"材料单价",min:1,addonAfter:"元/吨"})})}),s.jsx(h.Z,{span:12,children:s.jsx(n.Z.Item,{label:"操作员姓名",name:"operatorName",children:s.jsx(Z.default,{placeholder:"操作员姓名（可选）"})})})]})]})}),s.jsx(C.Z,{title:"成本对账",open:T,onCancel:()=>Q(!1),onOk:()=>t.submit(),confirmLoading:a,width:600,children:(0,s.jsxs)(n.Z,{form:t,layout:"vertical",onFinish:L,children:[s.jsx(n.Z.Item,{label:"产品型号",name:"productModelCode",rules:[{required:!0,message:"请选择产品型号"}],children:s.jsx(i.default,{placeholder:"请选择产品型号",children:U.map(e=>(0,s.jsxs)(z,{value:e.modelCode,children:[e.modelCode," - ",e.modelName]},e.modelCode))})}),(0,s.jsxs)(u.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(n.Z.Item,{label:"热压数量",name:"hotPressQuantity",rules:[{required:!0,message:"请输入热压数量"}],children:s.jsx(M.Z,{className:"w-full",placeholder:"热压数量",min:1,addonAfter:"模"})})}),s.jsx(h.Z,{span:12,children:s.jsx(n.Z.Item,{label:"入库数量",name:"warehouseQuantity",rules:[{required:!0,message:"请输入入库数量"}],children:s.jsx(M.Z,{className:"w-full",placeholder:"入库数量",min:0,addonAfter:"模"})})})]}),(0,s.jsxs)(u.Z,{gutter:16,children:[s.jsx(h.Z,{span:12,children:s.jsx(n.Z.Item,{label:"场地暂存",name:"onSiteQuantity",rules:[{required:!0,message:"请输入场地暂存数量"}],children:s.jsx(M.Z,{className:"w-full",placeholder:"场地暂存数量",min:0,addonAfter:"模"})})}),s.jsx(h.Z,{span:12,children:s.jsx(n.Z.Item,{label:"理论材料用量",name:"theoreticalMaterialUsage",rules:[{required:!0,message:"请输入理论材料用量"}],children:s.jsx(M.Z,{className:"w-full",placeholder:"理论材料用量",min:0,addonAfter:"kg"})})})]}),s.jsx(n.Z.Item,{label:"实际材料用量",name:"actualMaterialUsage",rules:[{required:!0,message:"请输入实际材料用量"}],children:s.jsx(M.Z,{className:"w-full",placeholder:"实际材料用量",min:0,addonAfter:"kg"})})]})})]})}},23057:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>o});let s=(0,a(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\erp软件\src\app\production\cost-calculation\page.tsx`),{__esModule:r,$$typeof:i}=s,o=s.default},18223:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(25036),r=a(38834);function i({children:e}){return s.jsx(r.Z,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[1638,1880,8811,3984,7883,7779,8699,2079,6527,6879,7618,284,2345,7049,7557,8243,1248,6274,996,6133],()=>a(44478));module.exports=s})();