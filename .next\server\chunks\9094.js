"use strict";exports.id=9094,exports.ids=[9094],exports.modules={39426:(e,t,r)=>{r.d(t,{Xe:()=>l});var a=r(95344);r(3729);var s=r(90152),i=r(90377),n=r(87049);let{Text:o}=s.default,l={datetime:e=>{if(!e)return l.empty("未设置");try{let t="string"==typeof e?new Date(e):e;return a.jsx("span",{children:t.toLocaleString()})}catch(e){return l.empty("无效时间")}},date:e=>{if(!e)return l.empty("未设置");try{let t="string"==typeof e?new Date(e):e;return a.jsx("span",{children:t.toLocaleDateString()})}catch(e){return l.empty("无效日期")}},currency:(e,t="\xa5")=>null==e?l.empty("0.00"):(0,a.jsxs)("span",{style:{fontWeight:"bold",color:"#3f8600"},children:[t,e.toLocaleString()]}),quantity:(e,t="个")=>null==e?l.empty("0"):(0,a.jsxs)("span",{children:[e.toLocaleString()," ",t]}),status:(e,t)=>{if(!e)return l.empty("未知状态");let r=t[e]||{color:"default",text:e};return a.jsx(i.Z,{color:r.color,icon:r.icon,children:r.text})},progress:(e,t)=>{if(0===t)return l.empty("无数据");let r=Math.round(e/t*100);return a.jsx(n.Z,{percent:r,size:"small",status:100===r?"success":"active",format:()=>`${e}/${t}`})},empty:(e="无")=>a.jsx(o,{type:"secondary",children:e}),highlight:(e,t="info")=>e?a.jsx(o,{style:{color:{success:"#52c41a",warning:"#faad14",error:"#ff4d4f",info:"#1890ff"}[t],fontWeight:"bold"},children:e}):l.empty()}},51360:(e,t,r)=>{r.d(t,{ZP:()=>g});var a=r(95344),s=r(3729),i=r(90152),n=r(10707),o=r(11157),l=r(13113),c=r(67383),d=r(16408),h=r(39426);let{Title:u}=i.default,g=function({open:e,order:t,onClose:r,config:i,className:g,style:m}){let y=(0,s.useMemo)(()=>{if(!t)return"";let e="function"==typeof i.title?i.title(t):i.title,r=i.getStatus(t);return i.statusConfig[r],(0,a.jsxs)(n.Z,{children:[a.jsx("span",{children:e}),h.Xe.status(r,i.statusConfig)]})},[t,i]),R=(0,s.useMemo)(()=>t&&i.actions?i.actions.filter(e=>!e.hidden||!e.hidden(t)).map(e=>{let r="function"==typeof e.loading?e.loading(t):e.loading,s="function"==typeof e.disabled?e.disabled(t):e.disabled,i="function"==typeof e.text?e.text(t):e.text;return a.jsx(o.ZP,{type:e.type,icon:e.icon,loading:r,disabled:s,danger:e.danger,style:e.style,onClick:()=>e.onClick(t),children:i},e.key)}):[],[t,i.actions]),p=(e,t)=>"string"==typeof t&&t.includes(".")?t.split(".").reduce((e,t)=>e?.[t],e):e[t],E=e=>null==e||""===e?h.Xe.empty():"number"==typeof e&&e>1e9?h.Xe.datetime(new Date(e)):"string"==typeof e&&/^\d{4}-\d{2}-\d{2}/.test(e)?h.Xe.datetime(e):a.jsx("span",{children:String(e)}),v=(0,s.useMemo)(()=>{let e=[...R,a.jsx(o.ZP,{onClick:r,children:"关闭"},"close")];return i.customFooter&&t?i.customFooter(t,e):e},[R,r,i.customFooter,t]);return t?a.jsx(d.Z,{title:y,open:e,onCancel:r,footer:v,width:i.width||800,className:g,style:m,children:(0,a.jsxs)("div",{style:{padding:"16px 0"},children:[i.sections.map((e,r)=>{if(!t||e.hidden&&e.hidden(t))return null;let s=e.fields.filter(e=>!e.hidden||!e.hidden(t));return 0!==s.length||e.customContent?(0,a.jsxs)("div",{children:[r>0&&a.jsx(l.Z,{}),a.jsx(u,{level:5,children:e.title}),s.length>0&&a.jsx(c.Z,{column:e.columns||2,bordered:!1!==e.bordered,size:e.size||"small",children:s.map((e,r)=>{let s=p(t,e.key),i=e.render?e.render(s,t):E(s);return a.jsx(c.Z.Item,{label:e.label,span:e.span,children:i},r)})}),e.customContent&&e.customContent(t)]},r):null}),i.customContent&&i.customContent(t)]})}):null}},82366:(e,t,r)=>{r.d(t,{e:()=>i});var a=r(3729),s=r(37637);let i=e=>{let t={interval:6e4,enabled:!0,showDetails:!1,...e},[r,i]=(0,a.useState)({metrics:null,cacheStats:null,isMonitoring:!1,lastUpdateTime:null,error:null}),n=(0,a.useCallback)(async()=>{try{let e=s.dataAccessManager.getPerformanceMetrics(),r=s.dataAccessManager.getCacheStatistics();i(t=>({...t,metrics:e,cacheStats:r,lastUpdateTime:Date.now(),error:null})),t.showDetails&&console.log("DataAccessManager监控数据:",{响应时间:e.averageResponseTime+"ms",缓存命中率:(100*r.hitRate).toFixed(2)+"%",缓存大小:r.size,总请求数:e.totalCalls})}catch(e){i(t=>({...t,error:e instanceof Error?e.message:"监控数据获取失败"})),console.error("DataAccessManager监控数据获取失败:",e)}},[t.showDetails]),o=(0,a.useCallback)((e,t)=>{try{switch(e){case"service":if(t){let e=s.dataAccessManager.clearServiceCache(t);console.log(`清理了 ${e} 个${t}服务缓存`)}break;case"dataType":t&&(s.dataAccessManager.clearDataTypeCache(t),console.log(`清理了${t}数据类型缓存`));break;default:s.dataAccessManager.clearAllCache(),console.log("清理了所有缓存")}n()}catch(e){console.error("缓存清理失败:",e)}},[n]),l=(0,a.useCallback)(()=>{try{return[]}catch(e){return console.error("获取性能告警失败:",e),[]}},[]),c=(0,a.useCallback)(e=>{if(0===e)return"0 B";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["B","KB","MB","GB"][t]},[]),d=(0,a.useCallback)(e=>(100*e).toFixed(2)+"%",[]);return(0,a.useEffect)(()=>{if(!t.enabled)return;i(e=>({...e,isMonitoring:!0})),n();let e=setInterval(n,t.interval);return()=>{clearInterval(e),i(e=>({...e,isMonitoring:!1}))}},[t.enabled,t.interval,n]),{...r,updateMonitoringData:n,clearCache:o,getPerformanceAlerts:l,formatMemorySize:c,formatPercentage:d,isHealthy:!!r.cacheStats&&r.cacheStats.hitRate>.3,needsOptimization:!!r.metrics&&r.metrics.averageResponseTime>1e3}}},6487:(e,t,r)=>{r.d(t,{Yy:()=>l,y1:()=>o});var a=r(3729),s=r(63908),i=r.n(s),n=r(82366);let o=(e,t,r,s)=>{let{isMonitoring:o}=(0,n.e)({interval:6e4,enabled:!1}),l=(0,a.useRef)(null),c=(0,a.useCallback)(i()(e,t,{leading:s?.leading??!1,trailing:s?.trailing??!0,maxWait:s?.maxWait}),r);return(0,a.useEffect)(()=>(l.current=c,()=>{l.current&&(l.current.cancel(),l.current=null)}),[c]),(0,a.useEffect)(()=>()=>{l.current&&(l.current.cancel(),l.current=null)},[]),c},l=(e,t=300,r=[])=>o(e,t,[e,...r],{leading:!1,trailing:!0})},81037:(e,t,r)=>{r.d(t,{el:()=>p});var a,s,i,n,o=r(3729),l=r(43471),c=r(82366);(function(e){e.CONNECTION_ERROR="connection_error",e.LISTENER_ERROR="listener_error",e.PUBLISH_ERROR="publish_error",e.SERIALIZATION_ERROR="serialization_error",e.TIMEOUT_ERROR="timeout_error",e.PERMISSION_ERROR="permission_error",e.NETWORK_ERROR="network_error",e.CONFIG_ERROR="config_error"})(a||(a={})),function(e){e.LOW="low",e.MEDIUM="medium",e.HIGH="high",e.CRITICAL="critical"}(s||(s={})),function(e){e.FIXED="fixed",e.EXPONENTIAL="exponential",e.LINEAR="linear",e.CUSTOM="custom"}(i||(i={})),function(e){e.NONE="none",e.POLLING="polling",e.BACKUP_SERVICE="backup_service",e.LOCAL_CACHE="local_cache",e.SILENT_FAIL="silent_fail"}(n||(n={}));let d={FAST:{strategy:i.EXPONENTIAL,maxRetries:3,baseDelay:100,maxDelay:1e3,backoffMultiplier:2,jitterFactor:.1},STANDARD:{strategy:i.EXPONENTIAL,maxRetries:5,baseDelay:500,maxDelay:5e3,backoffMultiplier:2,jitterFactor:.2},SLOW:{strategy:i.EXPONENTIAL,maxRetries:3,baseDelay:2e3,maxDelay:3e4,backoffMultiplier:3,jitterFactor:.3},LINEAR:{strategy:i.LINEAR,maxRetries:10,baseDelay:1e3,maxDelay:1e4,backoffMultiplier:1,jitterFactor:.1},FIXED:{strategy:i.FIXED,maxRetries:5,baseDelay:3e3,maxDelay:3e3,backoffMultiplier:1,jitterFactor:0}},h={POLLING:{strategy:n.POLLING,triggerErrorTypes:[a.CONNECTION_ERROR,a.NETWORK_ERROR,a.TIMEOUT_ERROR],timeout:3e4,options:{pollingInterval:5e3,maxPollingAttempts:10}},LOCAL_CACHE:{strategy:n.LOCAL_CACHE,triggerErrorTypes:[a.CONNECTION_ERROR,a.NETWORK_ERROR],timeout:6e4,options:{cacheExpiry:3e5,maxCacheSize:1e3}},BACKUP_SERVICE:{strategy:n.BACKUP_SERVICE,triggerErrorTypes:[a.CONNECTION_ERROR,a.TIMEOUT_ERROR],timeout:15e3,options:{backupEndpoint:"/api/backup/events",healthCheckInterval:1e4}},SILENT_FAIL:{strategy:n.SILENT_FAIL,triggerErrorTypes:[a.PERMISSION_ERROR,a.CONFIG_ERROR],timeout:0,options:{logLevel:"warn"}}},u={[a.CONNECTION_ERROR]:{enabled:!0,retry:d.STANDARD,fallback:h.POLLING,reporting:{enabled:!0,batchSize:10,interval:3e4},monitoring:{enabled:!0,interval:5e3,thresholds:{maxErrorRate:.1,maxResponseTime:5e3,maxMemoryUsage:.8}}},[a.LISTENER_ERROR]:{enabled:!0,retry:d.FAST,fallback:h.SILENT_FAIL,reporting:{enabled:!0,batchSize:5,interval:1e4},monitoring:{enabled:!0,interval:1e3,thresholds:{maxErrorRate:.05,maxResponseTime:1e3,maxMemoryUsage:.7}}},[a.PUBLISH_ERROR]:{enabled:!0,retry:d.STANDARD,fallback:h.LOCAL_CACHE,reporting:{enabled:!0,batchSize:20,interval:6e4},monitoring:{enabled:!0,interval:2e3,thresholds:{maxErrorRate:.02,maxResponseTime:2e3,maxMemoryUsage:.6}}},[a.SERIALIZATION_ERROR]:{enabled:!0,retry:d.FAST,fallback:h.SILENT_FAIL,reporting:{enabled:!0,batchSize:1,interval:5e3},monitoring:{enabled:!0,interval:1e3,thresholds:{maxErrorRate:.01,maxResponseTime:500,maxMemoryUsage:.5}}},[a.TIMEOUT_ERROR]:{enabled:!0,retry:d.SLOW,fallback:h.POLLING,reporting:{enabled:!0,batchSize:15,interval:45e3},monitoring:{enabled:!0,interval:3e3,thresholds:{maxErrorRate:.15,maxResponseTime:1e4,maxMemoryUsage:.8}}},[a.PERMISSION_ERROR]:{enabled:!0,retry:{...d.FAST,maxRetries:1},fallback:h.SILENT_FAIL,reporting:{enabled:!0,batchSize:1,interval:1e3},monitoring:{enabled:!0,interval:1e3,thresholds:{maxErrorRate:.01,maxResponseTime:1e3,maxMemoryUsage:.5}}},[a.NETWORK_ERROR]:{enabled:!0,retry:d.STANDARD,fallback:h.LOCAL_CACHE,reporting:{enabled:!0,batchSize:25,interval:6e4},monitoring:{enabled:!0,interval:5e3,thresholds:{maxErrorRate:.2,maxResponseTime:8e3,maxMemoryUsage:.8}}},[a.CONFIG_ERROR]:{enabled:!0,retry:{...d.FAST,maxRetries:1},fallback:h.SILENT_FAIL,reporting:{enabled:!0,batchSize:1,interval:1e3},monitoring:{enabled:!0,interval:1e3,thresholds:{maxErrorRate:.01,maxResponseTime:500,maxMemoryUsage:.5}}}};s.LOW,s.MEDIUM,s.HIGH,s.CRITICAL;class g{static calculateRetryDelay(e,t,r){let a;switch(t.strategy){case i.FIXED:a=t.baseDelay;break;case i.LINEAR:a=t.baseDelay*e;break;case i.EXPONENTIAL:a=t.baseDelay*Math.pow(t.backoffMultiplier,e-1);break;case i.CUSTOM:a=t.customDelayFn?t.customDelayFn(e,t.baseDelay):t.baseDelay;break;default:a=t.baseDelay}if(t.jitterFactor>0){let e=a*t.jitterFactor*(Math.random()-.5);a+=e}return Math.min(a,t.maxDelay)}static shouldTriggerFallback(e,t,r){return!!r.triggerErrorTypes.includes(e)&&t>=(({[a.CONNECTION_ERROR]:3,[a.NETWORK_ERROR]:5,[a.TIMEOUT_ERROR]:2,[a.LISTENER_ERROR]:10,[a.PUBLISH_ERROR]:3,[a.SERIALIZATION_ERROR]:1,[a.PERMISSION_ERROR]:1,[a.CONFIG_ERROR]:1})[e]||3)}static calculateListenerHealthScore(e,t,r,a){let s=e+t;return 0===s?100:Math.round(e/s*40+Math.max(0,30-r/100*30)+Math.max(0,20-20*a)+Math.max(0,10-t/s*100))}static recommendStrategy(e,t){let r=t.filter(e=>Date.now()-e.timestamp<3e5&&!e.success).length/Math.max(1,t.length),a=u[e],s=a.retry,i=a.fallback,n=.8;return r>.5?(s={...s,maxRetries:Math.max(1,s.maxRetries-2),baseDelay:2*s.baseDelay},n=.6):r<.1&&(s={...s,maxRetries:s.maxRetries+1,baseDelay:Math.max(100,s.baseDelay/2)},n=.9),{retryConfig:s,fallbackConfig:i,confidence:n}}}class m{constructor(e){this.errorHistory=[],this.listenerStates=new Map,this.retryTimers=new Map,this.fallbackStates=new Map,this.alerts=[],this.config={enabled:!0,retry:u[a.CONNECTION_ERROR].retry,fallback:u[a.CONNECTION_ERROR].fallback,reporting:{enabled:!0,batchSize:10,interval:3e4},monitoring:{enabled:!0,interval:5e3,thresholds:{maxErrorRate:.1,maxResponseTime:5e3,maxMemoryUsage:.8}},...e},this.config.monitoring.enabled&&this.startMonitoring()}async handleError(e,t,r){let a=Date.now(),s=this.createEventError(e,t),i=r?{...this.config,...r}:this.getConfigForErrorType(s.type);this.recordError(s),t.listenerId&&this.updateListenerState(t.listenerId,!1,Date.now()-a);let n=await this.executeErrorHandling(s,i);return this.recordHandlingResult(s,n),this.checkAndGenerateAlerts(s,n),n}createEventError(e,t){let r=this.classifyError(e),a=this.determineSeverity(r,t);return{id:this.generateErrorId(),type:r,severity:a,message:e.message,originalError:e,context:t,timestamp:Date.now(),retryable:this.isRetryable(r),retryCount:0,maxRetries:this.getConfigForErrorType(r).retry.maxRetries,stack:e.stack}}classifyError(e){let t=e.message.toLowerCase(),r=e.name.toLowerCase();return t.includes("network")||t.includes("fetch")?a.NETWORK_ERROR:t.includes("timeout")||r.includes("timeout")?a.TIMEOUT_ERROR:t.includes("connection")||t.includes("websocket")?a.CONNECTION_ERROR:t.includes("permission")||t.includes("unauthorized")?a.PERMISSION_ERROR:t.includes("serialize")||t.includes("json")?a.SERIALIZATION_ERROR:t.includes("config")||t.includes("configuration")?a.CONFIG_ERROR:t.includes("listener")||t.includes("callback")?a.LISTENER_ERROR:a.PUBLISH_ERROR}determineSeverity(e,t){let r={[a.CONNECTION_ERROR]:s.HIGH,[a.NETWORK_ERROR]:s.MEDIUM,[a.TIMEOUT_ERROR]:s.MEDIUM,[a.LISTENER_ERROR]:s.LOW,[a.PUBLISH_ERROR]:s.MEDIUM,[a.SERIALIZATION_ERROR]:s.LOW,[a.PERMISSION_ERROR]:s.HIGH,[a.CONFIG_ERROR]:s.CRITICAL}[e];"critical-service"===t.component&&(r=s.CRITICAL);let i=this.getRecentErrors(e,3e5);return i.length>10?r=s.CRITICAL:i.length>5&&(r=s.HIGH),r}async executeErrorHandling(e,t){let r=Date.now();return this.shouldRetry(e,t.retry)?this.scheduleRetry(e,t.retry):this.shouldFallback(e,t.fallback)?await this.executeFallback(e,t.fallback):{success:!1,action:"ignore",message:`错误已记录但无法处理: ${e.message}`,duration:Date.now()-r}}shouldRetry(e,t){return e.retryable&&e.retryCount<t.maxRetries&&e.retryCount<e.maxRetries}scheduleRetry(e,t){let r=g.calculateRetryDelay(e.retryCount+1,t,e.type),a=Date.now()+r,s=`${e.id}_retry`;this.retryTimers.has(s)&&clearTimeout(this.retryTimers.get(s));let i=setTimeout(()=>{this.executeRetry(e),this.retryTimers.delete(s)},r);return this.retryTimers.set(s,i),{success:!0,action:"retry",nextRetryAt:a,message:`已安排在 ${r}ms 后重试 (第 ${e.retryCount+1} 次)`,duration:0}}async executeRetry(e){e.retryCount++;try{this.emitRetryEvent(e)}catch(t){await this.handleError(t,e.context)}}shouldFallback(e,t){return g.shouldTriggerFallback(e.type,this.getErrorCount(e.type,e.context.listenerId),t)}async executeFallback(e,t){let r=Date.now(),a=`${e.context.listenerId||"global"}_${e.type}`;this.fallbackStates.set(a,{active:!0,strategy:t.strategy,startTime:Date.now()});try{switch(t.strategy){case"polling":await this.activatePollingFallback(e,t);break;case"local_cache":await this.activateLocalCacheFallback(e,t);break;case"backup_service":await this.activateBackupServiceFallback(e,t);break;case"silent_fail":console.warn(`[EventErrorHandler] 静默失败: ${e.message}`)}return{success:!0,action:"fallback",fallbackStrategy:t.strategy,message:`已激活降级策略: ${t.strategy}`,duration:Date.now()-r}}catch(e){return{success:!1,action:"escalate",message:`降级策略执行失败: ${e.message}`,duration:Date.now()-r}}}async activatePollingFallback(e,t){let{pollingInterval:r=5e3,maxPollingAttempts:a=10}=t.options;console.log(`[EventErrorHandler] 激活轮询降级，间隔: ${r}ms`),this.emitFallbackEvent("polling_activated",{errorType:e.type,pollingInterval:r,maxPollingAttempts:a})}async activateLocalCacheFallback(e,t){console.log(`[EventErrorHandler] 激活本地缓存降级`),this.emitFallbackEvent("local_cache_activated",{errorType:e.type,cacheOptions:t.options})}async activateBackupServiceFallback(e,t){console.log(`[EventErrorHandler] 激活备用服务降级`),this.emitFallbackEvent("backup_service_activated",{errorType:e.type,backupEndpoint:t.options.backupEndpoint})}getConfigForErrorType(e){return u[e]||this.config}isRetryable(e){return![a.PERMISSION_ERROR,a.CONFIG_ERROR,a.SERIALIZATION_ERROR].includes(e)}generateErrorId(){return`err_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}recordError(e){this.errorHistory.length>1e3&&(this.errorHistory=this.errorHistory.slice(-500))}recordHandlingResult(e,t){this.errorHistory.push({error:e,handlingResult:t,timestamp:Date.now()})}updateListenerState(e,t,r){let a=this.listenerStates.get(e);a||(a={id:e,errorCount:0,successCount:0,lastActivity:Date.now(),averageResponseTime:0,memoryUsage:0,health:{listenerId:e,status:"healthy",lastActivity:Date.now(),errorCount:0,successCount:0,averageResponseTime:0,memoryUsage:0,healthScore:100}},this.listenerStates.set(e,a)),t?a.successCount++:a.errorCount++,a.lastActivity=Date.now();let s=a.successCount+a.errorCount;a.averageResponseTime=(a.averageResponseTime*(s-1)+r)/s,a.health.healthScore=g.calculateListenerHealthScore(a.successCount,a.errorCount,a.averageResponseTime,a.memoryUsage),a.health.healthScore>80?a.health.status="healthy":a.health.healthScore>60?a.health.status="warning":a.health.healthScore>20?a.health.status="error":a.health.status="dead"}getRecentErrors(e,t){let r=Date.now()-t;return this.errorHistory.filter(t=>t.error.type===e&&t.timestamp>r)}getErrorCount(e,t){return this.errorHistory.filter(r=>r.error.type===e&&(!t||r.error.context.listenerId===t)).length}checkAndGenerateAlerts(e,t){e.severity===s.CRITICAL&&this.generateAlert({id:`alert_${Date.now()}`,type:"error",level:"critical",message:`严重错误: ${e.message}`,metrics:{errorCount:this.getErrorCount(e.type)},suggestedActions:["检查系统状态","联系技术支持"],timestamp:Date.now(),acknowledged:!1});let r=this.getRecentErrors(e.type,3e5);r.length>10&&this.generateAlert({id:`alert_freq_${Date.now()}`,type:"performance",level:"warning",message:`错误频率过高: ${e.type} (${r.length} 次/5分钟)`,metrics:{errorRate:r.length/5},suggestedActions:["检查网络连接","检查服务状态"],timestamp:Date.now(),acknowledged:!1})}generateAlert(e){this.alerts.push(e),this.alerts.length>100&&(this.alerts=this.alerts.slice(-50)),console.warn(`[EventErrorHandler] ${e.level.toUpperCase()}: ${e.message}`)}startMonitoring(){setInterval(()=>{this.performHealthCheck(),this.cleanupExpiredStates()},this.config.monitoring.interval)}performHealthCheck(){this.listenerStates.forEach((e,t)=>{Date.now()-e.lastActivity>3e5&&(e.health.status="dead",e.health.healthScore=0)})}cleanupExpiredStates(){let e=Date.now();this.fallbackStates.forEach((t,r)=>{e-t.startTime>36e5&&this.fallbackStates.delete(r)}),this.retryTimers.forEach((e,t)=>{})}emitRetryEvent(e){console.log(`[EventErrorHandler] 重试事件: ${e.type} - ${e.message}`)}emitFallbackEvent(e,t){console.log(`[EventErrorHandler] 降级事件: ${e}`,t)}getErrorStatistics(){let e=Date.now(),t=this.errorHistory.filter(t=>e-t.timestamp<864e5),r={};return Object.values(a).forEach(e=>{r[e]=t.filter(t=>t.error.type===e).length}),{totalErrors:t.length,errorsByType:r,activeListeners:this.listenerStates.size,healthyListeners:Array.from(this.listenerStates.values()).filter(e=>"healthy"===e.health.status).length,activeFallbacks:this.fallbackStates.size,pendingRetries:this.retryTimers.size}}getListenerHealthStates(){return Array.from(this.listenerStates.values()).map(e=>e.health)}getAlerts(){return[...this.alerts]}clearAlerts(){this.alerts=[]}getErrorHistory(e=100){return this.errorHistory.slice(-e)}async manualRetry(e){let t=this.errorHistory.find(t=>t.error.id===e);return t?(await this.executeRetry(t.error),{success:!0,action:"retry",message:"已手动触发重试",duration:0}):{success:!1,action:"ignore",message:"未找到指定的错误记录",duration:0}}deactivateFallback(e,t){let r=`${e}_${t}`,a=this.fallbackStates.delete(r);return a&&this.emitFallbackEvent("fallback_deactivated",{listenerId:e,errorType:t}),a}resetListenerState(e){let t=this.listenerStates.get(e);return!!t&&(t.errorCount=0,t.successCount=0,t.averageResponseTime=0,t.health.healthScore=100,t.health.status="healthy",t.health.errorCount=0,t.health.successCount=0,!0)}getConfig(){return{...this.config}}updateConfig(e){this.config={...this.config,...e}}destroy(){this.retryTimers.forEach(e=>clearTimeout(e)),this.retryTimers.clear(),this.errorHistory=[],this.listenerStates.clear(),this.fallbackStates.clear(),this.alerts=[],console.log("[EventErrorHandler] 错误处理器已销毁")}}class y{constructor(e){this.listeners=new Map,this.eventHistory=[],this.isDestroyed=!1,this.errorHandler=e||new m,this.metrics={totalEvents:0,successfulEvents:0,failedEvents:0,errorRate:0,averageResponseTime:0,activeListeners:0,deadListeners:0,memoryUsage:0,networkUsage:0},this.startMonitoring()}registerListener(e){if(this.isDestroyed)throw Error("EventListenerManager has been destroyed");this.validateListenerConfig(e),this.listeners.has(e.id)&&(console.warn(`[EventListenerManager] 监听器 ${e.id} 已存在，将被替换`),this.unregisterListener(e.id));let t={id:e.id,config:e,registeredAt:Date.now(),lastActivity:Date.now(),eventCount:0,errorCount:0,averageResponseTime:0,isActive:!0},r=this.wrapListenerCallback(t);return t.config.callback=r,this.listeners.set(e.id,t),this.updateMetrics(),console.log(`[EventListenerManager] 注册监听器: ${e.id}`),e.id}unregisterListener(e){let t=this.listeners.get(e);if(!t)return!1;if(t.cleanup)try{t.cleanup()}catch(t){console.error(`[EventListenerManager] 清理监听器 ${e} 时出错:`,t)}return t.isActive=!1,this.listeners.delete(e),this.updateMetrics(),console.log(`[EventListenerManager] 注销监听器: ${e}`),!0}async publishEvent(e){let t;if(this.isDestroyed)throw Error("EventListenerManager has been destroyed");let r=Date.now(),a=this.generateEventId(),s=!0;try{let i=this.getTargetListeners(e);if(0===i.length)return console.warn(`[EventListenerManager] 没有找到事件 ${e.type} 的监听器`),!1;let n=i.map(t=>this.publishToListener(t,e)),o=(await Promise.allSettled(n)).filter(e=>"rejected"===e.status);return o.length>0&&(s=!1,t=Error(`${o.length} 个监听器发布失败`)),this.recordEventPublish({id:a,type:e.type,timestamp:r,success:s,duration:Date.now()-r,targetListeners:i.map(e=>e.id),error:t}),s}catch(i){return t=i,s=!1,this.recordEventPublish({id:a,type:e.type,timestamp:r,success:!1,duration:Date.now()-r,targetListeners:[],error:t}),await this.errorHandler.handleError(t,{eventType:e.type,component:"EventListenerManager"}),!1}}getTargetListeners(e){let t=Array.from(this.listeners.values());return e.targetListeners&&e.targetListeners.length>0?t.filter(t=>e.targetListeners.includes(t.id)&&t.isActive&&t.config.enabled):t.filter(t=>t.config.eventTypes.includes(e.type)&&t.isActive&&t.config.enabled)}async publishToListener(e,t){let r=Date.now();try{let a=t.options?.timeout||e.config.timeout||5e3,s=new Promise((e,t)=>{setTimeout(()=>t(Error("Listener timeout")),a)}),i=Promise.resolve(e.config.callback(t.data));await Promise.race([i,s]),this.updateListenerStats(e,!0,Date.now()-r)}catch(a){throw this.updateListenerStats(e,!1,Date.now()-r),await this.errorHandler.handleError(a,{eventType:t.type,listenerId:e.id,component:"EventListenerManager"}),a}}wrapListenerCallback(e){let t=e.config.callback;return async r=>{let a=Date.now();try{e.lastActivity=Date.now();let s=await t(r);return this.updateListenerStats(e,!0,Date.now()-a),s}catch(r){this.updateListenerStats(e,!1,Date.now()-a);let t={listenerId:e.id,component:"EventListener",metadata:{eventTypes:e.config.eventTypes,priority:e.config.priority}};throw await this.errorHandler.handleError(r,t),r}}}updateListenerStats(e,t,r){e.eventCount++,e.lastActivity=Date.now(),t?e.averageResponseTime=(e.averageResponseTime*(e.eventCount-1)+r)/e.eventCount:e.errorCount++}validateListenerConfig(e){if(!e.id)throw Error("监听器ID不能为空");if(!e.eventTypes||0===e.eventTypes.length)throw Error("监听器必须指定至少一个事件类型");if("function"!=typeof e.callback)throw Error("监听器回调必须是函数");if(e.priority<0||e.priority>10)throw Error("监听器优先级必须在0-10之间");if(e.timeout<0)throw Error("监听器超时时间不能为负数")}recordEventPublish(e){this.eventHistory.push(e),this.eventHistory.length>1e3&&(this.eventHistory=this.eventHistory.slice(-500)),this.updateMetrics()}updateMetrics(){let e=Date.now(),t=this.eventHistory.filter(t=>e-t.timestamp<3e5);this.metrics.totalEvents=t.length,this.metrics.successfulEvents=t.filter(e=>e.success).length,this.metrics.failedEvents=t.filter(e=>!e.success).length,this.metrics.errorRate=this.metrics.totalEvents>0?this.metrics.failedEvents/this.metrics.totalEvents:0,this.metrics.averageResponseTime=t.length>0?t.reduce((e,t)=>e+t.duration,0)/t.length:0,this.metrics.activeListeners=Array.from(this.listeners.values()).filter(e=>e.isActive).length,this.metrics.deadListeners=Array.from(this.listeners.values()).filter(t=>!t.isActive||e-t.lastActivity>3e5).length,this.metrics.memoryUsage=this.estimateMemoryUsage()}estimateMemoryUsage(){return(1024*this.listeners.size+512*this.eventHistory.length)/1048576}generateEventId(){return`evt_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}startMonitoring(){let e=setInterval(()=>{if(this.isDestroyed){clearInterval(e);return}this.performHealthCheck(),this.cleanupInactiveListeners(),this.updateMetrics()},3e4)}performHealthCheck(){let e=Date.now();this.listeners.forEach((t,r)=>{let a=e-t.lastActivity;a>3e5&&t.isActive&&console.warn(`[EventListenerManager] 监听器 ${r} 长时间无活动 (${Math.round(a/1e3)}秒)`)})}cleanupInactiveListeners(){let e=Date.now(),t=[];this.listeners.forEach((r,a)=>{(!r.isActive||e-r.lastActivity>36e5)&&t.push(a)}),t.forEach(e=>{console.log(`[EventListenerManager] 清理非活跃监听器: ${e}`),this.unregisterListener(e)})}getListenerHealthStates(){return Array.from(this.listeners.values()).map(e=>{let t;let r=e.eventCount,a=Math.round(60*(r>0?(r-e.errorCount)/r:1)+Math.max(0,30-e.averageResponseTime/100*30)+(e.isActive?10:0));return t=e.isActive?a>80?"healthy":a>60?"warning":"error":"dead",{listenerId:e.id,status:t,lastActivity:e.lastActivity,errorCount:e.errorCount,successCount:e.eventCount-e.errorCount,averageResponseTime:e.averageResponseTime,memoryUsage:0,healthScore:a}})}getMetrics(){return{...this.metrics}}getDebugInfo(){let e=this.getListenerHealthStates(),t=this.eventHistory.slice(-20);return{listeners:Array.from(this.listeners.values()).map(t=>({id:t.id,eventTypes:t.config.eventTypes,status:t.isActive?"active":"inactive",health:e.find(e=>e.listenerId===t.id)})),recentEvents:t.map(e=>({type:e.type,timestamp:e.timestamp,success:e.success,duration:e.duration})),errorStats:this.errorHandler.getErrorStatistics().errorsByType,metrics:this.metrics,alerts:this.errorHandler.getAlerts()}}getListeners(){return Array.from(this.listeners.values()).map(e=>({id:e.id,eventTypes:e.config.eventTypes,isActive:e.isActive,registeredAt:e.registeredAt,eventCount:e.eventCount,errorCount:e.errorCount}))}resetListenerStats(e){let t=this.listeners.get(e);return!!t&&(t.eventCount=0,t.errorCount=0,t.averageResponseTime=0,t.lastActivity=Date.now(),!0)}destroy(){this.isDestroyed||(Array.from(this.listeners.keys()).forEach(e=>this.unregisterListener(e)),this.listeners.clear(),this.eventHistory=[],this.errorHandler.destroy(),this.isDestroyed=!0,console.log("[EventListenerManager] 事件监听器管理器已销毁"))}}let R=new y(new m),p=(e,t,r)=>{let{isMonitoring:a}=(0,c.e)({interval:6e4,enabled:!1}),s=(0,o.useRef)(null),i={autoCleanup:!0,trackMemory:!0,useNewManager:!1,priority:5,timeout:5e3,...r};(0,o.useEffect)(()=>{if(s.current=t,!i.useNewManager)return l.dataChangeNotifier.registerListener(e,t),i.trackMemory&&a&&console.log(`Event listener registered: ${e}`,{manager:"legacy"}),()=>{i.autoCleanup&&l.dataChangeNotifier.unregisterListener(e),s.current=null};{let r={id:e,eventTypes:["order_created","order_updated","order_deleted","production_order_created","production_order_updated","production_order_deleted","production_work_order_created","production_work_order_updated","production_work_order_deleted"],callback:async e=>{switch(e.type||e.eventType){case"order_created":t.onOrderCreated?.(e.data);break;case"order_updated":t.onOrderUpdated?.(e.data);break;case"order_deleted":t.onOrderDeleted?.(e.data);break;case"production_order_created":t.onProductionOrderCreated?.(e.data);break;case"production_order_updated":t.onProductionOrderUpdated?.(e.data);break;case"production_order_deleted":t.onProductionOrderDeleted?.(e.data);break;case"production_work_order_created":t.onProductionWorkOrderCreated?.(e.data);break;case"production_work_order_updated":t.onProductionWorkOrderUpdated?.(e.data);break;case"production_work_order_deleted":t.onProductionWorkOrderDeleted?.(e.data)}},priority:i.priority,enabled:!0,timeout:i.timeout};return R.registerListener(r),i.trackMemory&&a&&console.log(`Event listener registered: ${e}`,{manager:"new",priority:i.priority,timeout:i.timeout}),()=>{i.autoCleanup&&R.unregisterListener(e),s.current=null}}},[e,i.autoCleanup,i.trackMemory,i.useNewManager,i.priority,i.timeout,a]),(0,o.useEffect)(()=>()=>{s.current&&(l.dataChangeNotifier.unregisterListener(e),s.current=null)},[e])}},51221:(e,t,r)=>{r.d(t,{Ro:()=>a});let a=async(e,t)=>{try{let t=await e();if("success"===t.status)return t.data||null;return t.code,t.message,t.message,null}catch(e){return e instanceof Error?e.message:String(e),null}}}};